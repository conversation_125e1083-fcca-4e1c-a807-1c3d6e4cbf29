@use "../../../styles/mixins.scss";

.statistics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  color: var(--tk-primary);
  margin: 0.5rem 0;

  @include mixins.respond-below(lg) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include mixins.respond-below(md) {
    grid-template-columns: 1fr;
  }
}

.statistic {
  display: flex;
  align-items: center;
  padding: 1.25rem !important;
  transition: all 0.2s ease-in-out;
  margin-bottom: 0 !important;
  height: 100%;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .leftPanel {
    flex: 1;
    display: grid;
    grid-template-rows: auto auto;
    min-height: 65px;
    gap: 5px;

    .number {
      font-size: 1.75rem;
      font-weight: 600;
      letter-spacing: -0.02em;
      line-height: 1.2;
      align-self: end;

      @include mixins.respond-below(md) {
        font-size: 1.5rem;
      }
    }

    .description {
      font-size: 1rem;
      color: var(--tk-color-gray-dark);
      font-weight: 500;
      align-self: start;
    }
  }

  .rightPanel {
    margin-left: 1rem;
    align-self: flex-start;

    .icon {
      width: 42px;
      height: 42px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      transition: transform 0.2s ease;

      @include mixins.respond-below(md) {
        width: 36px;
        height: 36px;
        border-radius: 10px;
      }

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  @include mixins.respond-below(sm) {
    padding: 1rem !important;

    .leftPanel {
      min-height: 55px;

      .number {
        font-size: 1.25rem;
      }

      .description {
        font-size: 0.9125rem;
      }
    }
  }
}
