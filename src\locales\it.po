msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-04 12:37-0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: it\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-05-04 12:37-0800\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Italian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/components/layouts/Event/index.tsx:189
msgid "- Click to Publish"
msgstr "- Clicca per pubblicare"

#: src/components/layouts/Event/index.tsx:191
msgid "- Click to Unpublish"
msgstr "- <PERSON><PERSON>ca per annullare la pubblicazione"

#: src/components/layouts/Event/index.tsx:143
#~ msgid "..."
#~ msgstr "..."

#: src/components/common/NoResultsSplash/index.tsx:14
msgid "'There\\'s nothing to show yet'"
msgstr "'Non c'è ancora nulla da mostrare'"

#: src/components/modals/SendMessageModal/index.tsx:159
#~ msgid ""
#~ "\"\"Due to the high risk of spam, we require manual verification before you can send messages.\n"
#~ "\"\"Please contact us to request access."
#~ msgstr ""
#~ "A causa dell'elevato rischio di spam, richiediamo una verifica manuale prima di poter inviare messaggi.\n"
#~ "Contattaci per richiedere l'accesso."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
#~ msgid ""
#~ "\"\"If you have an account with us, you will receive an email with instructions on how to reset your\n"
#~ "\"\"password."
#~ msgstr ""
#~ "Se hai un account con noi, riceverai un'email con le istruzioni su come reimpostare la tua\n"
#~ "password."

#: src/components/common/QuestionsTable/index.tsx:267
#~ msgid "{0, plural, one {# hidden question} other {# hidden questions}}"
#~ msgstr "{0, plural, one {# domanda nascosta} other {# domande nascoste}}"

#: src/components/common/ReportTable/index.tsx:303
#: src/locales.ts:37
msgid "{0}"
msgstr "{0}"

#: src/components/common/QuestionsTable/index.tsx:267
#~ msgid "{0} {1}"
#~ msgstr "{0} {1}"

#: src/components/layouts/CheckIn/index.tsx:82
msgid "{0} <0>checked in</0> successfully"
msgstr "{0} <0>registrato</0> con successo"

#: src/components/layouts/CheckIn/index.tsx:106
msgid "{0} <0>checked out</0> successfully"
msgstr "{0} <0>uscita registrata</0> con successo"

#: src/components/routes/event/Webhooks/index.tsx:24
msgid "{0} Active Webhooks"
msgstr "{0} Webhook Attivi"

#: src/components/routes/product-widget/SelectProducts/index.tsx:412
msgid "{0} available"
msgstr "{0} disponibili"

#: src/components/common/AttendeeCheckInTable/index.tsx:155
#~ msgid "{0} checked in"
#~ msgstr "{0} registrati"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
msgid "{0} created successfully"
msgstr "{0} creato con successo"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
msgid "{0} updated successfully"
msgstr "{0} aggiornato con successo"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:57
msgid "{0}'s Events"
msgstr "Eventi di {0}"

#: src/components/common/QuestionsTable/index.tsx:267
#~ msgid "{0}{1}"
#~ msgstr "{0}{1}"

#: src/components/layouts/CheckIn/index.tsx:449
msgid "{0}/{1} checked in"
msgstr "{0}/{1} registrati"

#: src/components/common/Countdown/index.tsx:68
msgid "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{days} giorni, {hours} ore, {minutes} minuti e {seconds} secondi"

#: src/components/common/WebhookTable/index.tsx:79
msgid "{eventCount} events"
msgstr "{eventCount} eventi"

#: src/components/common/Countdown/index.tsx:70
msgid "{hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{hours} ore, {minutes} minuti e {seconds} secondi"

#: src/components/modals/RefundOrderModal/index.tsx:121
#~ msgid "{message}"
#~ msgstr "{message}"

#: src/components/common/Countdown/index.tsx:72
msgid "{minutes} minutes and {seconds} seconds"
msgstr "{minutes} minuti e {seconds} secondi"

#: src/components/routes/welcome/index.tsx:59
msgid "{organizerName}'s first event"
msgstr "Primo evento di {organizerName}"

#: src/components/common/ReportTable/index.tsx:256
#~ msgid "{title}"
#~ msgstr "{title}"

#: src/components/common/CapacityAssignmentList/index.tsx:47
msgid "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"
msgstr "<0>Le assegnazioni di capacità ti permettono di gestire la capacità tra biglietti o un intero evento. Ideale per eventi di più giorni, workshop e altro, dove il controllo delle presenze è cruciale.</0><1>Ad esempio, puoi associare un'assegnazione di capacità con i biglietti <2>Primo Giorno</2> e <3>Tutti i Giorni</3>. Una volta raggiunta la capacità, entrambi i biglietti smetteranno automaticamente di essere disponibili per la vendita.</1>"

#: src/components/common/CheckInListList/index.tsx:49
msgid "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"
msgstr "<0>Le liste di check-in aiutano a gestire l'ingresso dei partecipanti al tuo evento. Puoi associare più biglietti a una lista di check-in e assicurarti che solo coloro con biglietti validi possano entrare.</0>"

#: src/components/common/WidgetEditor/index.tsx:324
msgid "<0>https://</0>your-website.com"
msgstr "<0>https://</0>il-tuo-sito-web.com"

#: src/components/forms/ProductForm/index.tsx:287
msgid "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"
msgstr "<0>Inserisci il prezzo escluse tasse e commissioni.</0><1>Tasse e commissioni possono essere aggiunte qui sotto.</1>"

#: src/components/forms/ProductForm/index.tsx:304
msgid "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"
msgstr "<0>Il numero di prodotti disponibili per questo prodotto</0><1>Questo valore può essere sovrascritto se ci sono <2>Limiti di Capacità</2> associati a questo prodotto.</1>"

#: src/components/forms/TicketForm/index.tsx:249
#~ msgid "<0>The number of tickets available for this ticket</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this ticket.</1>"
#~ msgstr "<0>Il numero di biglietti disponibili per questo biglietto</0><1>Questo valore può essere sovrascritto se ci sono <2>Limiti di Capacità</2> associati a questo biglietto.</1>"

#: src/components/common/WebhookTable/index.tsx:205
msgid "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"
msgstr "<0>I webhook notificano istantaneamente i servizi esterni quando si verificano eventi, come l'aggiunta di un nuovo partecipante al tuo CRM o alla mailing list al momento della registrazione, garantendo un'automazione senza interruzioni.</0><1>Utilizza servizi di terze parti come <2>Zapier</2>, <3>IFTTT</3> o <4>Make</4> per creare flussi di lavoro personalizzati e automatizzare le attività.</1>"

#: src/components/routes/event/GettingStarted/index.tsx:134
msgid "⚡️ Set up your event"
msgstr "⚡️ Configura il tuo evento"

#: src/components/routes/event/GettingStarted/index.tsx:190
msgid "✉️ Confirm your email address"
msgstr "✉️ Conferma il tuo indirizzo email"

#: src/components/routes/event/GettingStarted/index.tsx:56
#~ msgid "🎉 Congratulation on creating an event!"
#~ msgstr "🎉 Congratulazioni per aver creato un evento!"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "🎉 Congratulations on creating an event!"
#~ msgstr "🎉 Congratulazioni per aver creato un evento!"

#: src/components/routes/event/GettingStarted/index.tsx:67
#~ msgid "🎟️ Add products"
#~ msgstr "🎟️ Aggiungi prodotti"

#: src/components/routes/event/GettingStarted/index.tsx:119
msgid "🎟️ Add tickets"
msgstr "🎟️ Aggiungi biglietti"

#: src/components/routes/event/GettingStarted/index.tsx:162
msgid "🎨 Customize your event page"
msgstr "🎨 Personalizza la pagina del tuo evento"

#: src/components/routes/event/GettingStarted/index.tsx:147
msgid "💳 Connect with Stripe"
msgstr "💳 Connetti con Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:52
#~ msgid "📢 Promote your event"
#~ msgstr "📢 Promuovi il tuo evento"

#: src/components/routes/event/GettingStarted/index.tsx:176
msgid "🚀 Set your event live"
msgstr "🚀 Metti online il tuo evento"

#: src/components/common/Countdown/index.tsx:34
msgid "0 minutes and 0 seconds"
msgstr "0 minuti e 0 secondi"

#: src/components/forms/TaxAndFeeForm/index.tsx:73
#~ msgid "0.50 for $0.50"
#~ msgstr "0.50 per $0.50"

#: src/components/routes/event/Webhooks/index.tsx:23
msgid "1 Active Webhook"
msgstr "1 Webhook Attivo"

#: src/components/forms/TaxAndFeeForm/index.tsx:73
#~ msgid "1.75 for 1.75%"
#~ msgstr "1.75 per 1.75%"

#: src/components/modals/RefundOrderModal/index.tsx:105
msgid "10.00"
msgstr "10.00"

#: src/components/routes/event/settings.tsx:118
#~ msgid "10001"
#~ msgstr "10001"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:123
msgid "123 Main Street"
msgstr "Via Roma 123"

#: src/components/common/WidgetEditor/index.tsx:226
msgid "20"
msgstr "20"

#: src/components/routes/welcome/index.tsx:91
msgid "2024-01-01 10:00"
msgstr "2024-01-01 10:00"

#: src/components/routes/welcome/index.tsx:97
msgid "2024-01-01 18:00"
msgstr "2024-01-01 18:00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:147
msgid "94103"
msgstr "00100"

#: src/components/forms/QuestionForm/index.tsx:138
msgid "A date input. Perfect for asking for a date of birth etc."
msgstr "Un campo data. Perfetto per chiedere una data di nascita ecc."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
msgid "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."
msgstr "Un {type} predefinito viene applicato automaticamente a tutti i nuovi prodotti. Puoi sovrascrivere questa impostazione per ogni singolo prodotto."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
#~ msgid "A default {type} is automaticaly applied to all new tickets. You can override this on a per ticket basis."
#~ msgstr "Un {type} predefinito viene applicato automaticamente a tutti i nuovi biglietti. Puoi sovrascrivere questa impostazione per ogni singolo biglietto."

#: src/components/forms/QuestionForm/index.tsx:126
msgid "A Dropdown input allows only one selection"
msgstr "Un menu a tendina consente una sola selezione"

#: src/components/forms/TaxAndFeeForm/index.tsx:20
msgid "A fee, like a booking fee or a service fee"
msgstr "Una commissione, come una commissione di prenotazione o una commissione di servizio"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
msgid "A fixed amount per product. E.g, $0.50 per product"
msgstr "Un importo fisso per prodotto. Es. $0,50 per prodotto"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
#~ msgid "A fixed amount per ticket. E.g, $0.50 per ticket"
#~ msgstr "Un importo fisso per biglietto. Es. $0,50 per biglietto"

#: src/components/forms/QuestionForm/index.tsx:108
msgid "A multi line text input"
msgstr "Un campo di testo a più righe"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
msgid "A percentage of the product price. E.g., 3.5% of the product price"
msgstr "Una percentuale del prezzo del prodotto. Es. 3,5% del prezzo del prodotto"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
#~ msgid "A percentage of the ticket price. E.g., 3.5% of the ticket price"
#~ msgstr "Una percentuale del prezzo del biglietto. Es. 3,5% del prezzo del biglietto"

#: src/components/forms/PromoCodeForm/index.tsx:37
msgid "A promo code with no discount can be used to reveal hidden products."
msgstr "Un codice promozionale senza sconto può essere utilizzato per rivelare prodotti nascosti."

#: src/components/forms/PromoCodeForm/index.tsx:36
#~ msgid "A promo code with no discount can be used to reveal hidden tickets."
#~ msgstr "Un codice promozionale senza sconto può essere utilizzato per rivelare biglietti nascosti."

#: src/components/forms/QuestionForm/index.tsx:114
#~ msgid "A Radio option allows has multiple options but only one can be selected."
#~ msgstr "Un'opzione Radio ha più opzioni ma solo una può essere selezionata."

#: src/components/forms/QuestionForm/index.tsx:114
#~ msgid "A Radio Option allows only one selection"
#~ msgstr "Un'opzione Radio consente una sola selezione"

#: src/components/forms/QuestionForm/index.tsx:120
msgid "A Radio option has multiple options but only one can be selected."
msgstr "Un'opzione Radio ha più opzioni ma solo una può essere selezionata."

#: src/components/routes/welcome/index.tsx:85
#~ msgid "A short description of Awesome Events Ltd."
#~ msgstr "Una breve descrizione di Awesome Events Ltd."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:69
msgid "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"
msgstr "Una breve descrizione dell'evento che verrà visualizzata nei risultati dei motori di ricerca e quando condiviso sui social media. Per impostazione predefinita, verrà utilizzata la descrizione dell'evento"

#: src/components/forms/QuestionForm/index.tsx:102
msgid "A single line text input"
msgstr "Un campo di testo a singola riga"

#: src/components/forms/QuestionForm/index.tsx:92
#~ msgid "A single question per attendee. E.g, What is your preferred meal?"
#~ msgstr "Una singola domanda per partecipante. Es. Qual è il tuo pasto preferito?"

#: src/components/forms/QuestionForm/index.tsx:86
#~ msgid "A single question per order. E.g, What is your company name?"
#~ msgstr "Una singola domanda per ordine. Es. Qual è il nome della tua azienda?"

#: src/components/forms/QuestionForm/index.tsx:87
msgid "A single question per order. E.g, What is your shipping address?"
msgstr "Una singola domanda per ordine. Es. Qual è il tuo indirizzo di spedizione?"

#: src/components/forms/QuestionForm/index.tsx:93
msgid "A single question per product. E.g, What is your t-shirt size?"
msgstr "Una singola domanda per prodotto. Es. Qual è la tua taglia di maglietta?"

#: src/components/forms/TaxAndFeeForm/index.tsx:14
msgid "A standard tax, like VAT or GST"
msgstr "Un'imposta standard, come IVA o GST"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:76
msgid "About"
msgstr "Informazioni"

#: src/components/common/GlobalMenu/index.tsx:25
#~ msgid "About hi.events"
#~ msgstr "Informazioni su hi.events"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:211
msgid "About Stripe Connect"
msgstr "Informazioni su Stripe Connect"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:79
#~ msgid "About the event"
#~ msgstr "Informazioni sull'evento"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:91
msgid "Accept bank transfers, checks, or other offline payment methods"
msgstr "Accetta bonifici bancari, assegni o altri metodi di pagamento offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:86
msgid "Accept credit card payments with Stripe"
msgstr "Accetta pagamenti con carta di credito tramite Stripe"

#: src/components/routes/auth/AcceptInvitation/index.tsx:126
msgid "Accept Invitation"
msgstr "Accetta Invito"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:125
msgid "Access Denied"
msgstr "Accesso Negato"

#: src/components/forms/TicketForm/index.tsx:168
#~ msgid "Access to the VIP area..."
#~ msgstr "Accesso all'area VIP..."

#: src/components/routes/account/ManageAccount/index.tsx:24
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:53
msgid "Account"
msgstr "Account"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:70
#~ msgid "Account Email"
#~ msgstr "Email dell'Account"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:62
msgid "Account Name"
msgstr "Nome Account"

#: src/components/common/GlobalMenu/index.tsx:32
#: src/components/routes/account/ManageAccount/index.tsx:19
msgid "Account Settings"
msgstr "Impostazioni Account"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:42
msgid "Account updated successfully"
msgstr "Account aggiornato con successo"

#: src/components/common/AttendeeTable/index.tsx:158
#: src/components/common/ProductsTable/SortableProduct/index.tsx:267
#: src/components/common/QuestionsTable/index.tsx:108
msgid "Actions"
msgstr "Azioni"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "Activate"
msgstr "Attiva"

#: src/components/forms/CheckInListForm/index.tsx:47
msgid "Activation date"
msgstr "Data di attivazione"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:19
#: src/components/modals/EditUserModal/index.tsx:110
msgid "Active"
msgstr "Attivo"

#: src/components/routes/event/attendees.tsx:59
#~ msgid "Add"
#~ msgstr "Aggiungi"

#: src/components/routes/event/GettingStarted/index.tsx:44
#~ msgid "Add a cover image, description, and more to make your event stand out."
#~ msgstr "Aggiungi un'immagine di copertina, una descrizione e altro per far risaltare il tuo evento."

#: src/components/forms/CheckInListForm/index.tsx:39
msgid "Add a description for this check-in list"
msgstr "Aggiungi una descrizione per questa lista di check-in"

#: src/components/modals/ManageAttendeeModal/index.tsx:131
msgid "Add any notes about the attendee. These will not be visible to the attendee."
msgstr "Aggiungi eventuali note sul partecipante. Queste non saranno visibili al partecipante."

#: src/components/modals/ManageAttendeeModal/index.tsx:133
msgid "Add any notes about the attendee..."
msgstr "Aggiungi eventuali note sul partecipante..."

#: src/components/modals/ManageOrderModal/index.tsx:165
msgid "Add any notes about the order. These will not be visible to the customer."
msgstr "Aggiungi eventuali note sull'ordine. Queste non saranno visibili al cliente."

#: src/components/modals/ManageOrderModal/index.tsx:169
msgid "Add any notes about the order..."
msgstr "Aggiungi eventuali note sull'ordine..."

#: src/components/forms/QuestionForm/index.tsx:202
msgid "Add description"
msgstr "Aggiungi descrizione"

#: src/components/routes/event/GettingStarted/index.tsx:85
#~ msgid "Add event details and and manage event settings."
#~ msgstr "Aggiungi dettagli dell'evento e gestisci le impostazioni dell'evento."

#: src/components/routes/event/GettingStarted/index.tsx:137
msgid "Add event details and manage event settings."
msgstr "Aggiungi i dettagli dell'evento e gestisci le impostazioni dell'evento."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:153
msgid "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"
msgstr "Aggiungi istruzioni per i pagamenti offline (es. dettagli del bonifico bancario, dove inviare gli assegni, scadenze di pagamento)"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add More products"
#~ msgstr "Aggiungi altri prodotti"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add More tickets"
msgstr "Aggiungi altri biglietti"

#: src/components/common/HeadingCard/index.tsx:27
msgid "Add New"
msgstr "Aggiungi Nuovo"

#: src/components/forms/QuestionForm/index.tsx:68
msgid "Add Option"
msgstr "Aggiungi Opzione"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:68
msgid "Add Product"
msgstr "Aggiungi Prodotto"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:51
msgid "Add Product to Category"
msgstr "Aggiungi Prodotto alla Categoria"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add products"
#~ msgstr "Aggiungi prodotti"

#: src/components/common/QuestionsTable/index.tsx:281
msgid "Add question"
msgstr "Aggiungi domanda"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:18
msgid "Add Tax or Fee"
msgstr "Aggiungi Tassa o Commissione"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add tickets"
msgstr "Aggiungi biglietti"

#: src/components/forms/ProductForm/index.tsx:337
msgid "Add tier"
msgstr "Aggiungi livello"

#: src/components/common/AddEventToCalendarButton/index.tsx:112
#: src/components/common/AddEventToCalendarButton/index.tsx:120
msgid "Add to Calendar"
msgstr "Aggiungi al Calendario"

#: src/components/common/WebhookTable/index.tsx:223
#: src/components/routes/event/Webhooks/index.tsx:35
msgid "Add Webhook"
msgstr "Aggiungi Webhook"

#: src/components/forms/EventForm/index.tsx:82
#~ msgid "Additional Details"
#~ msgstr "Dettagli Aggiuntivi"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:219
msgid "Additional Information"
msgstr "Informazioni Aggiuntive"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/forms/ProductForm/index.tsx:348
msgid "Additional Options"
msgstr "Opzioni Aggiuntive"

#: src/components/common/OrderDetails/index.tsx:96
#: src/components/forms/QuestionForm/index.tsx:130
msgid "Address"
msgstr "Indirizzo"

#: src/components/common/CheckoutQuestion/index.tsx:156
msgid "Address line 1"
msgstr "Indirizzo riga 1"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:122
#: src/components/routes/product-widget/CollectInformation/index.tsx:319
#: src/components/routes/product-widget/CollectInformation/index.tsx:320
msgid "Address Line 1"
msgstr "Indirizzo Riga 1"

#: src/components/common/CheckoutQuestion/index.tsx:159
msgid "Address line 2"
msgstr "Indirizzo riga 2"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:127
#: src/components/routes/product-widget/CollectInformation/index.tsx:324
#: src/components/routes/product-widget/CollectInformation/index.tsx:325
msgid "Address Line 2"
msgstr "Indirizzo Riga 2"

#: src/components/modals/EditUserModal/index.tsx:49
#: src/components/modals/InviteUserModal/index.tsx:41
msgid "Admin"
msgstr "Amministratore"

#: src/components/modals/EditUserModal/index.tsx:51
#: src/components/modals/InviteUserModal/index.tsx:43
msgid "Admin users have full access to events and account settings."
msgstr "Gli amministratori hanno accesso completo agli eventi e alle impostazioni dell'account."

#: src/components/layouts/Event/index.tsx:53
#~ msgid "Affiliates"
#~ msgstr "Affiliati"

#: src/components/common/MessageList/index.tsx:21
msgid "All attendees"
msgstr "Tutti i partecipanti"

#: src/components/modals/SendMessageModal/index.tsx:187
msgid "All attendees of this event"
msgstr "Tutti i partecipanti di questo evento"

#: src/components/routes/events/Dashboard/index.tsx:52
#~ msgid "All Events"
#~ msgstr "Tutti gli Eventi"

#: src/components/common/PromoCodeTable/index.tsx:131
msgid "All Products"
msgstr "Tutti i Prodotti"

#: src/components/common/PromoCodeTable/index.tsx:130
#: src/components/forms/PromoCodeForm/index.tsx:67
#~ msgid "All Tickets"
#~ msgstr "Tutti i Biglietti"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:157
msgid "Allow attendees associated with unpaid orders to check in"
msgstr "Consenti ai partecipanti associati a ordini non pagati di effettuare il check-in"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:82
msgid "Allow search engine indexing"
msgstr "Consenti l'indicizzazione dei motori di ricerca"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:81
msgid "Allow search engines to index this event"
msgstr "Consenti ai motori di ricerca di indicizzare questo evento"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:78
msgid "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."
msgstr "Ci siamo quasi! Stiamo solo aspettando che il tuo pagamento venga elaborato. Questo dovrebbe richiedere solo pochi secondi.."

#: src/components/routes/auth/register.tsx:68
#~ msgid "Already have an account?"
#~ msgstr "Hai già un account?"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:77
msgid "Amazing, Event, Keywords..."
msgstr "Fantastico, Evento, Parole chiave..."

#: src/components/common/OrdersTable/index.tsx:207
#: src/components/forms/TaxAndFeeForm/index.tsx:71
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:36
msgid "Amount"
msgstr "Importo"

#: src/components/forms/PromoCodeForm/index.tsx:53
#~ msgid "amount in {0}"
#~ msgstr "importo in {0}"

#: src/components/modals/CreateAttendeeModal/index.tsx:165
msgid "Amount paid ({0})"
msgstr "Importo pagato ({0})"

#: src/components/modals/CreateAttendeeModal/index.tsx:81
#~ msgid "Amount paid ${0}"
#~ msgstr "Importo pagato ${0}"

#: src/mutations/useExportAnswers.ts:45
msgid "An error occurred while checking export status."
msgstr "Si è verificato un errore durante il controllo dello stato di esportazione."

#: src/components/common/ErrorDisplay/index.tsx:18
msgid "An error occurred while loading the page"
msgstr "Si è verificato un errore durante il caricamento della pagina"

#: src/components/common/QuestionsTable/index.tsx:148
msgid "An error occurred while sorting the questions. Please try again or refresh the page"
msgstr "Si è verificato un errore durante l'ordinamento delle domande. Riprova o aggiorna la pagina"

#: src/components/common/TicketsTable/index.tsx:47
#~ msgid "An error occurred while sorting the tickets. Please try again or refresh the page"
#~ msgstr "Si è verificato un errore durante l'ordinamento dei biglietti. Riprova o aggiorna la pagina"

#: src/components/routes/welcome/index.tsx:111
#~ msgid "An event is the actual event you are hosting"
#~ msgstr "Un evento è l'evento effettivo che stai organizzando"

#: src/components/routes/welcome/index.tsx:75
#~ msgid "An event is the actual event you are hosting. You can add more details later."
#~ msgstr "Un evento è l'evento effettivo che stai organizzando. Puoi aggiungere più dettagli in seguito."

#: src/components/routes/welcome/index.tsx:75
msgid "An event is the gathering or occasion you’re organizing. You can add more details later."
msgstr "An event is the gathering or occasion you’re organizing. You can add more details later."

#: src/components/routes/welcome/index.tsx:23
msgid "An organizer is the company or person who is hosting the event"
msgstr "Un organizzatore è l'azienda o la persona che ospita l'evento"

#: src/components/forms/StripeCheckoutForm/index.tsx:40
msgid "An unexpected error occurred."
msgstr "Si è verificato un errore imprevisto."

#: src/hooks/useFormErrorResponseHandler.tsx:48
msgid "An unexpected error occurred. Please try again."
msgstr "Si è verificato un errore imprevisto. Per favore riprova."

#: src/components/common/QuestionAndAnswerList/index.tsx:97
msgid "Answer updated successfully."
msgstr "Risposta aggiornata con successo."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
msgid "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
msgstr "Qualsiasi richiesta dai possessori di prodotti verrà inviata a questo indirizzo email. Questo sarà anche utilizzato come indirizzo \"rispondi a\" per tutte le email inviate da questo evento"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
#~ msgid "Any queries from ticket holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
#~ msgstr "Qualsiasi richiesta dai possessori di biglietti verrà inviata a questo indirizzo email. Questo sarà anche utilizzato come indirizzo \"rispondi a\" per tutte le email inviate da questo evento"

#: src/components/common/WidgetEditor/index.tsx:213
msgid "Appearance"
msgstr "Aspetto"

#: src/components/routes/product-widget/SelectProducts/index.tsx:500
msgid "applied"
msgstr "applicato"

#: src/components/common/CapacityAssignmentList/index.tsx:97
msgid "Applies to {0} products"
msgstr "Si applica a {0} prodotti"

#: src/components/common/CapacityAssignmentList/index.tsx:97
#~ msgid "Applies to {0} tickets"
#~ msgstr "Si applica a {0} biglietti"

#: src/components/common/CapacityAssignmentList/index.tsx:98
msgid "Applies to 1 product"
msgstr "Si applica a 1 prodotto"

#: src/components/common/CapacityAssignmentList/index.tsx:98
#~ msgid "Applies to 1 ticket"
#~ msgstr "Si applica a 1 biglietto"

#: src/components/common/FilterModal/index.tsx:253
msgid "Apply"
msgstr "Applica"

#: src/components/routes/product-widget/SelectProducts/index.tsx:528
msgid "Apply Promo Code"
msgstr "Applica Codice Promozionale"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
msgid "Apply this {type} to all new products"
msgstr "Applica questo {type} a tutti i nuovi prodotti"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
#~ msgid "Apply this {type} to all new tickets"
#~ msgstr "Applica questo {type} a tutti i nuovi biglietti"

#: src/components/common/EventCard/index.tsx:167
msgid "Archive event"
msgstr "Archivia evento"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:32
msgid "Archived"
msgstr "Archiviati"

#: src/components/routes/events/Dashboard/index.tsx:59
msgid "Archived Events"
msgstr "Eventi Archiviati"

#: src/components/common/AttendeeTable/index.tsx:76
msgid "Are you sure you want to activate this attendee?"
msgstr "Sei sicuro di voler attivare questo partecipante?"

#: src/components/common/EventCard/index.tsx:58
msgid "Are you sure you want to archive this event?"
msgstr "Sei sicuro di voler archiviare questo evento?"

#: src/components/common/AttendeeTable/index.tsx:78
#~ msgid "Are you sure you want to cancel this attendee? This will void their product"
#~ msgstr "Sei sicuro di voler cancellare questo partecipante? Questo annullerà il loro prodotto"

#: src/components/common/AttendeeTable/index.tsx:77
msgid "Are you sure you want to cancel this attendee? This will void their ticket"
msgstr "Sei sicuro di voler cancellare questo partecipante? Questo annullerà il loro biglietto"

#: src/components/common/PromoCodeTable/index.tsx:37
msgid "Are you sure you want to delete this promo code?"
msgstr "Sei sicuro di voler eliminare questo codice promozionale?"

#: src/components/common/QuestionsTable/index.tsx:164
msgid "Are you sure you want to delete this question?"
msgstr "Sei sicuro di voler eliminare questa domanda?"

#: src/components/common/WebhookTable/index.tsx:122
msgid "Are you sure you want to delete this webhook?"
msgstr "Sei sicuro di voler eliminare questo webhook?"

#: src/components/layouts/Event/index.tsx:68
#: src/components/routes/event/EventDashboard/index.tsx:64
msgid "Are you sure you want to make this event draft? This will make the event invisible to the public"
msgstr "Sei sicuro di voler rendere questo evento una bozza? Questo renderà l'evento invisibile al pubblico"

#: src/components/layouts/Event/index.tsx:69
#: src/components/routes/event/EventDashboard/index.tsx:65
msgid "Are you sure you want to make this event public? This will make the event visible to the public"
msgstr "Sei sicuro di voler rendere questo evento pubblico? Questo renderà l'evento visibile al pubblico"

#: src/components/common/EventCard/index.tsx:59
msgid "Are you sure you want to restore this event? It will be restored as a draft event."
msgstr "Sei sicuro di voler ripristinare questo evento? Verrà ripristinato come bozza."

#: src/components/common/CapacityAssignmentList/index.tsx:163
msgid "Are you sure you would like to delete this Capacity Assignment?"
msgstr "Sei sicuro di voler eliminare questa Assegnazione di Capacità?"

#: src/components/common/CheckInListList/index.tsx:183
msgid "Are you sure you would like to delete this Check-In List?"
msgstr "Sei sicuro di voler eliminare questa Lista di Check-In?"

#: src/components/forms/QuestionForm/index.tsx:90
#~ msgid "Ask once per attendee"
#~ msgstr "Chiedi una volta per partecipante"

#: src/components/forms/QuestionForm/index.tsx:85
msgid "Ask once per order"
msgstr "Chiedi una volta per ordine"

#: src/components/forms/QuestionForm/index.tsx:91
msgid "Ask once per product"
msgstr "Chiedi una volta per prodotto"

#: src/components/modals/CreateWebhookModal/index.tsx:33
msgid "At least one event type must be selected"
msgstr "Deve essere selezionato almeno un tipo di evento"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Attendee"
msgstr "Partecipante"

#: src/components/forms/WebhookForm/index.tsx:94
msgid "Attendee Cancelled"
msgstr "Partecipante Cancellato"

#: src/components/forms/WebhookForm/index.tsx:82
msgid "Attendee Created"
msgstr "Partecipante Creato"

#: src/components/modals/ManageAttendeeModal/index.tsx:147
msgid "Attendee Details"
msgstr "Dettagli Partecipante"

#: src/components/layouts/AuthLayout/index.tsx:73
msgid "Attendee Management"
msgstr "Gestione Partecipanti"

#: src/components/layouts/CheckIn/index.tsx:150
msgid "Attendee not found"
msgstr "Partecipante non trovato"

#: src/components/modals/ManageAttendeeModal/index.tsx:153
msgid "Attendee Notes"
msgstr "Note Partecipante"

#: src/components/common/QuestionsTable/index.tsx:369
msgid "Attendee questions"
msgstr "Domande partecipante"

#: src/components/common/QuestionsTable/index.tsx:346
#~ msgid "Attendee questions are asked once per attendee. By default, people are asked for their first name, last name, and email address."
#~ msgstr "Le domande per i partecipanti vengono poste una volta per partecipante. Per impostazione predefinita, alle persone vengono chiesti nome, cognome e indirizzo email."

#: src/components/common/QuestionsTable/index.tsx:244
#~ msgid "Attendee questions are asked once per attendee. By default, we ask for the attendee's first name, last name, and email address."
#~ msgstr "Le domande per i partecipanti vengono poste una volta per partecipante. Per impostazione predefinita, chiediamo nome, cognome e indirizzo email del partecipante."

#: src/components/modals/ManageAttendeeModal/index.tsx:172
msgid "Attendee Ticket"
msgstr "Biglietto Partecipante"

#: src/components/forms/WebhookForm/index.tsx:88
msgid "Attendee Updated"
msgstr "Partecipante Aggiornato"

#: src/components/common/OrdersTable/index.tsx:206
#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
#: src/components/common/StatBoxes/index.tsx:27
#: src/components/layouts/Event/index.tsx:99
#: src/components/modals/ManageOrderModal/index.tsx:126
#: src/components/routes/event/attendees.tsx:59
msgid "Attendees"
msgstr "Partecipanti"

#: src/components/routes/event/attendees.tsx:43
msgid "Attendees Exported"
msgstr "Partecipanti Esportati"

#: src/components/routes/event/EventDashboard/index.tsx:239
msgid "Attendees Registered"
msgstr "Partecipanti Registrati"

#: src/components/modals/SendMessageModal/index.tsx:146
#~ msgid "Attendees with a specific product"
#~ msgstr "Partecipanti con un prodotto specifico"

#: src/components/modals/SendMessageModal/index.tsx:183
msgid "Attendees with a specific ticket"
msgstr "Partecipanti con un biglietto specifico"

#: src/components/common/WidgetEditor/index.tsx:233
msgid "Auto Resize"
msgstr "Ridimensionamento Automatico"

#: src/components/layouts/AuthLayout/index.tsx:88
#~ msgid "Auto Workflow"
#~ msgstr "Flusso di Lavoro Automatico"

#: src/components/layouts/AuthLayout/index.tsx:79
msgid "Automated entry management with multiple check-in lists and real-time validation"
msgstr "Gestione automatizzata degli ingressi con liste di check-in multiple e convalida in tempo reale"

#: src/components/common/WidgetEditor/index.tsx:236
msgid "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."
msgstr "Ridimensiona automaticamente l'altezza del widget in base al contenuto. Quando disabilitato, il widget riempirà l'altezza del contenitore."

#: src/components/common/TicketsTable/index.tsx:103
#~ msgid "Availability"
#~ msgstr "Disponibilità"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
#~ msgid "Avg Discount/Order"
#~ msgstr "Sconto Medio/Ordine"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
#~ msgid "Avg Order Value"
#~ msgstr "Valore Medio Ordine"

#: src/components/common/OrderStatusBadge/index.tsx:15
#: src/components/modals/SendMessageModal/index.tsx:231
msgid "Awaiting offline payment"
msgstr "In attesa di pagamento offline"

#: src/components/routes/event/orders.tsx:25
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:43
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:66
msgid "Awaiting Offline Payment"
msgstr "In Attesa di Pagamento Offline"

#: src/components/layouts/CheckIn/index.tsx:263
msgid "Awaiting payment"
msgstr "In attesa di pagamento"

#: src/components/common/AttendeeTicket/index.tsx:67
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:40
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:65
msgid "Awaiting Payment"
msgstr "In Attesa di Pagamento"

#: src/components/routes/welcome/index.tsx:84
msgid "Awesome Event"
msgstr "Evento Fantastico"

#: src/components/forms/OrganizerForm/index.tsx:59
#~ msgid "Awesome Events Ltd."
#~ msgstr "Awesome Events Srl."

#: src/components/forms/OrganizerForm/index.tsx:28
msgid "Awesome Organizer Ltd."
msgstr "Awesome Organizer Srl."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:42
msgid "Back to all events"
msgstr "Torna a tutti gli eventi"

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:212
#~ msgid "Back to event homepage"
#~ msgstr "Torna alla homepage dell'evento"

#: src/components/layouts/Checkout/index.tsx:73
#: src/components/routes/product-widget/CollectInformation/index.tsx:249
#: src/components/routes/product-widget/CollectInformation/index.tsx:261
msgid "Back to event page"
msgstr "Torna alla pagina dell'evento"

#: src/components/routes/auth/ForgotPassword/index.tsx:59
#: src/components/routes/auth/ResetPassword/index.tsx:66
msgid "Back to login"
msgstr "Torna al login"

#: src/components/routes/event/HomepageDesigner/index.tsx:83
#~ msgid "Background color"
#~ msgstr "Colore di sfondo"

#: src/components/common/WidgetEditor/index.tsx:175
msgid "Background Color"
msgstr "Colore di Sfondo"

#: src/components/routes/event/HomepageDesigner/index.tsx:144
msgid "Background Type"
msgstr "Tipo di Sfondo"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#~ msgid "Basic Details"
#~ msgstr "Dettagli di Base"

#: src/components/modals/EditUserModal/index.tsx:85
#~ msgid "Because you are the account owner, you cannot change your role or status."
#~ msgstr "Poiché sei il proprietario dell'account, non puoi modificare il tuo ruolo o stato."

#: src/components/modals/SendMessageModal/index.tsx:278
msgid "Before you send!"
msgstr "Prima di inviare!"

#: src/components/routes/event/GettingStarted/index.tsx:19
#~ msgid "Before you're event can go live, there's a few thing to do."
#~ msgstr "Prima che il tuo evento possa andare online, ci sono alcune cose da fare."

#: src/components/routes/event/GettingStarted/index.tsx:60
#~ msgid "Before your event can go live, there are a few things you need to do."
#~ msgstr "Prima che il tuo evento possa andare online, ci sono alcune cose che devi fare."

#: src/components/routes/event/GettingStarted/index.tsx:92
msgid "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."
msgstr "Prima che il tuo evento possa andare online, ci sono alcune cose da fare. Completa tutti i passaggi qui sotto per iniziare."

#: src/components/routes/auth/Register/index.tsx:66
#~ msgid "Begin selling products in minutes"
#~ msgstr "Inizia a vendere prodotti in pochi minuti"

#: src/components/routes/auth/Register/index.tsx:49
#~ msgid "Begin selling tickets in minutes"
#~ msgstr "Inizia a vendere biglietti in pochi minuti"

#: src/components/routes/account/ManageAccount/index.tsx:32
#: src/components/routes/account/ManageAccount/sections/BillingSettings/index.tsx:10
#: src/components/routes/account/ManageAccount/sections/BillingSettings/index.tsx:14
#~ msgid "Billing"
#~ msgstr "Fatturazione"

#: src/components/routes/product-widget/CollectInformation/index.tsx:313
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:142
msgid "Billing Address"
msgstr "Indirizzo di Fatturazione"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:167
msgid "Billing Settings"
msgstr "Impostazioni di Fatturazione"

#: src/components/layouts/AuthLayout/index.tsx:83
#~ msgid "Brand Control"
#~ msgstr "Controllo del Brand"

#: src/components/common/LanguageSwitcher/index.tsx:28
msgid "Brazilian Portuguese"
msgstr "Portoghese Brasiliano"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:87
#~ msgid "Button color"
#~ msgstr "Colore pulsante"

#: src/components/routes/auth/Register/index.tsx:133
msgid "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."
msgstr "Registrandoti accetti i nostri <0>Termini di Servizio</0> e la <1>Privacy Policy</1>."

#: src/components/forms/TaxAndFeeForm/index.tsx:52
msgid "Calculation Type"
msgstr "Tipo di Calcolo"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:140
msgid "California"
msgstr "California"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:175
msgid "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."
msgstr "L'autorizzazione della fotocamera è stata negata. <0>Richiedi Autorizzazione</0> di nuovo, o se questo non funziona, dovrai <1>concedere a questa pagina</1> l'accesso alla tua fotocamera nelle impostazioni del browser."

#: src/components/routes/events/Dashboard/dashboard.tsx:18
#~ msgid "Can't load events"
#~ msgstr "Impossibile caricare gli eventi"

#: src/components/common/AttendeeTable/index.tsx:182
#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/QuestionAndAnswerList/index.tsx:149
#: src/components/layouts/CheckIn/index.tsx:225
#: src/utilites/confirmationDialog.tsx:11
msgid "Cancel"
msgstr "Annulla"

#: src/components/routes/profile/ManageProfile/index.tsx:126
msgid "Cancel email change"
msgstr "Annulla cambio email"

#: src/components/common/OrdersTable/index.tsx:190
#: src/components/modals/RefundOrderModal/index.tsx:112
msgid "Cancel order"
msgstr "Annulla ordine"

#: src/components/modals/CancelOrderModal/index.tsx:59
msgid "Cancel Order"
msgstr "Annulla Ordine"

#: src/components/modals/CancelOrderModal/index.tsx:44
msgid "Cancel Order {0}"
msgstr "Annulla Ordine {0}"

#: src/components/common/AttendeeDetails/index.tsx:32
#~ msgid "Canceled"
#~ msgstr "Annullato"

#: src/components/modals/CancelOrderModal/index.tsx:54
msgid "Canceling will cancel all products associated with this order, and release the products back into the available pool."
msgstr "L'annullamento cancellerà tutti i prodotti associati a questo ordine e rilascerà i prodotti nel pool disponibile."

#: src/components/modals/CancelOrderModal/index.tsx:54
#~ msgid "Canceling will cancel all tickets associated with this order, and release the tickets back into the available pool."
#~ msgstr "L'annullamento cancellerà tutti i biglietti associati a questo ordine e rilascerà i biglietti nel pool disponibile."

#: src/components/common/AttendeeTicket/index.tsx:61
#: src/components/common/OrderStatusBadge/index.tsx:12
#: src/components/routes/event/orders.tsx:24
msgid "Cancelled"
msgstr "Annullato"

#: src/components/layouts/CheckIn/index.tsx:173
msgid "Cannot Check In"
msgstr "Impossibile Effettuare il Check In"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:42
#: src/components/layouts/Event/index.tsx:103
msgid "Capacity"
msgstr "Capacità"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:36
msgid "Capacity Assignment created successfully"
msgstr "Assegnazione di Capacità creata con successo"

#: src/components/common/CapacityAssignmentList/index.tsx:31
msgid "Capacity Assignment deleted successfully"
msgstr "Assegnazione di Capacità eliminata con successo"

#: src/components/routes/event/CapacityAssignments/index.tsx:32
msgid "Capacity Management"
msgstr "Gestione della Capacità"

#: src/components/modals/CreateProductCategoryModal/index.tsx:56
msgid "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."
msgstr "Le categorie ti permettono di raggruppare i prodotti. Ad esempio, potresti avere una categoria per \"Biglietti\" e un'altra per \"Merchandise\"."

#: src/components/forms/ProductForm/index.tsx:267
msgid "Categories help you organize your products. This title will be displayed on the public event page."
msgstr "Le categorie ti aiutano a organizzare i tuoi prodotti. Questo titolo verrà visualizzato sulla pagina pubblica dell'evento."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:91
msgid "Categories reordered successfully."
msgstr "Categorie riordinate con successo."

#: src/components/routes/event/products.tsx:96
msgid "Category"
msgstr "Categoria"

#: src/components/modals/CreateProductCategoryModal/index.tsx:37
msgid "Category Created Successfully"
msgstr "Categoria Creata con Successo"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Change Cover"
msgstr "Cambia Copertina"

#: src/components/routes/profile/ManageProfile/index.tsx:201
msgid "Change password"
msgstr "Cambia password"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check in"
#~ msgstr "registra ingresso"

#: src/components/layouts/CheckIn/index.tsx:180
msgid "Check In"
msgstr "Registra Ingresso"

#: src/components/layouts/CheckIn/index.tsx:193
msgid "Check in {0} {1}"
msgstr "Registra ingresso di {0} {1}"

#: src/components/layouts/CheckIn/index.tsx:218
msgid "Check in and mark order as paid"
msgstr "Registra ingresso e segna l'ordine come pagato"

#: src/components/layouts/CheckIn/index.tsx:209
msgid "Check in only"
msgstr "Solo registrazione ingresso"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check out"
#~ msgstr "registra uscita"

#: src/components/layouts/CheckIn/index.tsx:177
msgid "Check Out"
msgstr "Registra Uscita"

#: src/components/layouts/Checkout/index.tsx:106
msgid "Check out this event!"
msgstr "Dai un'occhiata a questo evento!"

#: src/components/common/EventCard/index.tsx:156
msgid "Check-in"
msgstr "Registrazione"

#: src/components/layouts/Event/index.tsx:52
#~ msgid "Check-In"
#~ msgstr "Registrazione"

#: src/components/forms/WebhookForm/index.tsx:100
msgid "Check-in Created"
msgstr "Registrazione Creata"

#: src/components/forms/WebhookForm/index.tsx:106
msgid "Check-in Deleted"
msgstr "Registrazione Eliminata"

#: src/components/modals/CreateCheckInListModal/index.tsx:40
msgid "Check-In List created successfully"
msgstr "Lista di Registrazione creata con successo"

#: src/components/common/CheckInListList/index.tsx:33
msgid "Check-In List deleted successfully"
msgstr "Lista di Registrazione eliminata con successo"

#: src/components/layouts/CheckIn/index.tsx:326
msgid "Check-in list has expired"
msgstr "La lista di registrazione è scaduta"

#: src/components/layouts/CheckIn/index.tsx:343
msgid "Check-in list is not active"
msgstr "La lista di registrazione non è attiva"

#: src/components/layouts/CheckIn/index.tsx:311
msgid "Check-in list not found"
msgstr "Lista di registrazione non trovata"

#: src/components/layouts/Event/index.tsx:104
#: src/components/routes/event/CheckInLists/index.tsx:32
msgid "Check-In Lists"
msgstr "Liste di Registrazione"

#: src/components/common/CheckInListList/index.tsx:162
msgid "Check-In URL copied to clipboard"
msgstr "URL di Registrazione copiato negli appunti"

#: src/components/forms/QuestionForm/index.tsx:114
msgid "Checkbox options allow multiple selections"
msgstr "Le opzioni di casella di controllo consentono selezioni multiple"

#: src/components/forms/QuestionForm/index.tsx:112
msgid "Checkboxes"
msgstr "Caselle di controllo"

#: src/components/common/AttendeeDetails/index.tsx:29
msgid "Checked In"
msgstr "Registrato"

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "Checked in successfully"
#~ msgstr "Registrazione effettuata con successo"

#: src/components/layouts/Checkout/index.tsx:83
#: src/components/routes/event/Settings/index.tsx:40
msgid "Checkout"
msgstr "Pagamento"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:57
#~ msgid "Checkout Messaging"
#~ msgstr "Messaggi di Pagamento"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:61
msgid "Checkout Settings"
msgstr "Impostazioni di Pagamento"

#: src/locales.ts:46
#~ msgid "Chinese"
#~ msgstr "Cinese"

#: src/components/common/LanguageSwitcher/index.tsx:30
msgid "Chinese (Simplified)"
msgstr "Cinese (Semplificato)"

#: src/components/common/LanguageSwitcher/index.tsx:32
msgid "Chinese (Traditional)"
msgstr "Cinese (Tradizionale)"

#: src/components/routes/event/HomepageDesigner/index.tsx:133
msgid "Choose a color for your background"
msgstr "Scegli un colore per lo sfondo"

#: src/components/modals/ChooseAccountModal/index.tsx:13
msgid "Choose an account"
msgstr "Scegli un account"

#: src/components/routes/event/settings.tsx:32
#: src/components/routes/event/settings.tsx:75
#~ msgid "Choose what notifications you want to receive"
#~ msgstr "Scegli quali notifiche vuoi ricevere"

#: src/components/common/CheckoutQuestion/index.tsx:162
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:134
#: src/components/routes/product-widget/CollectInformation/index.tsx:333
#: src/components/routes/product-widget/CollectInformation/index.tsx:334
msgid "City"
msgstr "Città"

#: src/components/common/SearchBar/index.tsx:75
msgid "Clear Search Text"
msgstr "Cancella Testo di Ricerca"

#: src/components/routes/product-widget/SelectProducts/index.tsx:288
#~ msgid "click here"
#~ msgstr "clicca qui"

#: src/components/common/PromoCodeTable/index.tsx:98
msgid "Click to copy"
msgstr "Clicca per copiare"

#: src/components/routes/product-widget/SelectProducts/index.tsx:533
msgid "close"
msgstr "chiudi"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:186
#: src/components/modals/RefundOrderModal/index.tsx:125
#: src/components/routes/product-widget/SelectProducts/index.tsx:534
msgid "Close"
msgstr "Chiudi"

#: src/components/layouts/Event/index.tsx:281
msgid "Close sidebar"
msgstr "Chiudi barra laterale"

#: src/components/common/PromoCodeTable/index.tsx:69
#: src/components/forms/PromoCodeForm/index.tsx:34
msgid "Code"
msgstr "Codice"

#: src/components/modals/CreatePromoCodeModal/index.tsx:26
#: src/components/modals/EditPromoCodeModal/index.tsx:38
msgid "Code must be between 3 and 50 characters long"
msgstr "Il codice deve essere compreso tra 3 e 50 caratteri"

#: src/components/forms/ProductForm/index.tsx:401
msgid "Collapse this product when the event page is initially loaded"
msgstr "Comprimi questo prodotto quando la pagina dell'evento viene caricata inizialmente"

#: src/components/forms/TicketForm/index.tsx:346
#~ msgid "Collapse this ticket when the event page is initially loaded"
#~ msgstr "Comprimi questo biglietto quando la pagina dell'evento viene caricata inizialmente"

#: src/components/routes/event/HomepageDesigner/index.tsx:131
msgid "Color"
msgstr "Colore"

#: src/components/common/WidgetEditor/index.tsx:21
msgid "Color must be a valid hex color code. Example: #ffffff"
msgstr "Il colore deve essere un codice colore esadecimale valido. Esempio: #ffffff"

#: src/components/common/WidgetEditor/index.tsx:166
#: src/components/routes/event/HomepageDesigner/index.tsx:124
msgid "Colors"
msgstr "Colori"

#: src/components/layouts/Event/index.tsx:158
msgid "Coming Soon"
msgstr "Prossimamente"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
msgid "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"
msgstr "Parole chiave separate da virgole che descrivono l'evento. Queste saranno utilizzate dai motori di ricerca per aiutare a categorizzare e indicizzare l'evento"

#: src/components/routes/product-widget/CollectInformation/index.tsx:453
msgid "Complete Order"
msgstr "Completa Ordine"

#: src/components/routes/product-widget/CollectInformation/index.tsx:223
msgid "Complete payment"
msgstr "Completa pagamento"

#: src/components/routes/product-widget/Payment/index.tsx:131
msgid "Complete Payment"
msgstr "Completa Pagamento"

#: src/components/layouts/AuthLayout/index.tsx:68
#~ msgid "Complete Store"
#~ msgstr "Negozio Completo"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:202
#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Complete Stripe Setup"
msgstr "Completa Configurazione Stripe"

#: src/components/routes/event/EventDashboard/index.tsx:127
msgid "Complete these steps to start selling tickets for your event."
msgstr "Completa questi passaggi per iniziare a vendere biglietti per il tuo evento."

#: src/components/modals/SendMessageModal/index.tsx:230
#: src/components/routes/event/GettingStarted/index.tsx:70
#: src/components/routes/event/orders.tsx:23
msgid "Completed"
msgstr "Completato"

#: src/components/common/StatBoxes/index.tsx:51
msgid "Completed orders"
msgstr "Ordini completati"

#: src/components/routes/event/EventDashboard/index.tsx:237
msgid "Completed Orders"
msgstr "Ordini Completati"

#: src/components/common/WidgetEditor/index.tsx:287
msgid "Component Code"
msgstr "Codice Componente"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:35
msgid "Configured Discount"
msgstr "Sconto Configurato"

#: src/utilites/confirmationDialog.tsx:11
msgid "Confirm"
msgstr "Conferma"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:51
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:56
msgid "Confirm Email Change"
msgstr "Conferma Cambio Email"

#: src/components/routes/profile/ManageProfile/index.tsx:199
msgid "Confirm New Password"
msgstr "Conferma Nuova Password"

#: src/components/routes/auth/Register/index.tsx:115
msgid "Confirm password"
msgstr "Conferma password"

#: src/components/routes/auth/AcceptInvitation/index.tsx:112
#: src/components/routes/auth/Register/index.tsx:114
#: src/components/routes/auth/ResetPassword/index.tsx:60
msgid "Confirm Password"
msgstr "Conferma Password"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:36
msgid "Confirming email address..."
msgstr "Conferma indirizzo email in corso..."

#: src/components/routes/event/GettingStarted/index.tsx:13
#~ msgid "Congratulation on creating an event!"
#~ msgstr "Congratulation on creating an event!"

#: src/components/routes/event/GettingStarted/index.tsx:88
msgid "Congratulations on creating an event!"
msgstr "Congratulazioni per aver creato un evento!"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:173
msgid "Connect Documentation"
msgstr "Documentazione di Connessione"

#: src/components/routes/event/EventDashboard/index.tsx:192
msgid "Connect payment processing"
msgstr "Connetti elaborazione pagamenti"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:52
#~ msgid "Connect Stripe"
#~ msgstr "Connect Stripe"

#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Connect to Stripe"
msgstr "Connetti a Stripe"

#: src/components/layouts/AuthLayout/index.tsx:89
msgid "Connect with CRM and automate tasks using webhooks and integrations"
msgstr "Connetti con CRM e automatizza le attività utilizzando webhook e integrazioni"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:201
#: src/components/routes/event/GettingStarted/index.tsx:154
msgid "Connect with Stripe"
msgstr "Connetti con Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:150
msgid "Connect your Stripe account to start receiving payments."
msgstr "Connetti il tuo account Stripe per iniziare a ricevere pagamenti."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:148
msgid "Connected to Stripe"
msgstr "Connesso a Stripe"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:98
msgid "Connection Details"
msgstr "Dettagli di Connessione"

#: src/components/routes/event/settings.tsx:243
#~ msgid "Contact email"
#~ msgstr "Contact email"

#: src/components/modals/SendMessageModal/index.tsx:167
msgid "Contact Support"
msgstr "Contatta il Supporto"

#: src/components/modals/SendMessageModal/index.tsx:158
msgid "Contact us to enable messaging"
msgstr "Contattaci per abilitare la messaggistica"

#: src/components/routes/event/HomepageDesigner/index.tsx:155
msgid "Content background color"
msgstr "Colore di sfondo del contenuto"

#: src/components/common/WidgetEditor/index.tsx:31
#: src/components/common/WidgetEditor/index.tsx:217
#: src/components/layouts/Checkout/CheckoutFooter/index.tsx:38
#: src/components/routes/product-widget/CollectInformation/index.tsx:444
#: src/components/routes/product-widget/SelectProducts/index.tsx:486
msgid "Continue"
msgstr "Continua"

#: src/components/routes/event/HomepageDesigner/index.tsx:164
msgid "Continue button text"
msgstr "Testo pulsante continua"

#: src/components/common/WidgetEditor/index.tsx:216
msgid "Continue Button Text"
msgstr "Testo Pulsante Continua"

#: src/components/modals/CreateEventModal/index.tsx:153
msgid "Continue Event Setup"
msgstr "Continua Configurazione Evento"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Continue set up"
msgstr "Continua configurazione"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
#~ msgid "Continue Stripe Connect Setup"
#~ msgstr "Continua Configurazione Stripe Connect"

#: src/components/routes/product-widget/SelectProducts/index.tsx:337
msgid "Continue to Checkout"
msgstr "Procedi al pagamento"

#: src/components/routes/product-widget/CollectInformation/index.tsx:380
#~ msgid "Continue To Payment"
#~ msgstr "Continua Al Pagamento"

#: src/components/common/AttendeeTicket/index.tsx:91
#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:243
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copied"
msgstr "Copiato"

#: src/components/common/PromoCodeTable/index.tsx:106
msgid "copied to clipboard"
msgstr "copiato negli appunti"

#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copy"
msgstr "Copia"

#: src/components/common/CheckInListList/index.tsx:156
msgid "Copy Check-In URL"
msgstr "Copia URL di Check-In"

#: src/components/routes/product-widget/CollectInformation/index.tsx:306
msgid "Copy details to all attendees"
msgstr "Copia dettagli a tutti i partecipanti"

#: src/components/common/AttendeeTicket/index.tsx:91
msgid "Copy Link"
msgstr "Copia Link"

#: src/components/common/PromoCodeTable/index.tsx:177
#: src/components/modals/ShareModal/index.tsx:243
msgid "Copy URL"
msgstr "Copia URL"

#: src/components/common/CheckoutQuestion/index.tsx:174
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:152
#: src/components/routes/product-widget/CollectInformation/index.tsx:354
msgid "Country"
msgstr "Paese"

#: src/components/routes/event/HomepageDesigner/index.tsx:117
msgid "Cover"
msgstr "Copertina"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:74
#~ msgid "Cover Image"
#~ msgstr "Immagine di Copertina"

#: src/components/routes/event/attendees.tsx:71
#: src/components/routes/event/products.tsx:74
msgid "Create"
msgstr "Crea"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
msgid "Create {0}"
msgstr "Crea {0}"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:59
msgid "Create a Product"
msgstr "Crea un Prodotto"

#: src/components/common/PromoCodeTable/index.tsx:57
msgid "Create a Promo Code"
msgstr "Crea un Codice Promozionale"

#: src/components/modals/CreateCheckInListModal/index.tsx:63
msgid "Create a Ticket"
msgstr "Crea un Biglietto"

#: src/components/routes/auth/Register/index.tsx:69
msgid "Create an account or <0>{0}</0> to get started"
msgstr "Crea un account o <0>{0}</0> per iniziare"

#: src/components/modals/CreateEventModal/index.tsx:120
msgid "create an organizer"
msgstr "crea un organizzatore"

#: src/components/layouts/AuthLayout/index.tsx:27
msgid "Create and customize your event page instantly"
msgstr "Crea e personalizza la tua pagina evento istantaneamente"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Create Attendee"
msgstr "Crea Partecipante"

#: src/components/common/CapacityAssignmentList/index.tsx:63
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:68
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:79
#: src/components/routes/event/CapacityAssignments/index.tsx:46
msgid "Create Capacity Assignment"
msgstr "Crea Assegnazione di Capacità"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:128
#: src/components/common/ProductsTable/SortableCategory/index.tsx:129
msgid "Create category"
msgstr "Crea categoria"

#: src/components/modals/CreateProductCategoryModal/index.tsx:45
#: src/components/modals/CreateProductCategoryModal/index.tsx:60
msgid "Create Category"
msgstr "Crea Categoria"

#: src/components/common/CheckInListList/index.tsx:61
#: src/components/modals/CreateCheckInListModal/index.tsx:72
#: src/components/modals/CreateCheckInListModal/index.tsx:82
#: src/components/routes/event/CheckInLists/index.tsx:46
msgid "Create Check-In List"
msgstr "Crea Lista di Check-In"

#: src/components/common/NoEventsBlankSlate/index.tsx:27
#: src/components/modals/CreateEventModal/index.tsx:85
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:81
#: src/components/routes/welcome/index.tsx:108
msgid "Create Event"
msgstr "Crea Evento"

#: src/components/routes/events/Dashboard/index.tsx:91
msgid "Create new"
msgstr "Crea nuovo"

#: src/components/forms/OrganizerForm/index.tsx:111
#: src/components/modals/CreateEventModal/index.tsx:93
#: src/components/modals/CreateOrganizerModal/index.tsx:16
msgid "Create Organizer"
msgstr "Crea Organizzatore"

#: src/components/modals/CreateProductModal/index.tsx:80
#: src/components/modals/CreateProductModal/index.tsx:88
msgid "Create Product"
msgstr "Crea Prodotto"

#: src/components/routes/event/GettingStarted/index.tsx:70
#~ msgid "Create products for your event, set prices, and manage available quantity."
#~ msgstr "Crea prodotti per il tuo evento, imposta i prezzi e gestisci la quantità disponibile."

#: src/components/modals/CreatePromoCodeModal/index.tsx:51
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
msgid "Create Promo Code"
msgstr "Crea Codice Promozionale"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "Create promo codes"
#~ msgstr "Crea codici promozionali"

#: src/components/routes/event/GettingStarted/index.tsx:55
#~ msgid "Create promo codes to offer discounts to your attendees."
#~ msgstr "Crea codici promozionali per offrire sconti ai tuoi partecipanti."

#: src/components/modals/CreateQuestionModal/index.tsx:69
#: src/components/modals/CreateQuestionModal/index.tsx:74
msgid "Create Question"
msgstr "Crea Domanda"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:41
msgid "Create Tax or Fee"
msgstr "Crea Tassa o Commissione"

#: src/components/modals/CreateTicketModal/index.tsx:83
#: src/components/modals/CreateTicketModal/index.tsx:91
#: src/components/routes/event/tickets.tsx:50
#~ msgid "Create Ticket"
#~ msgstr "Crea Biglietto"

#: src/components/routes/event/GettingStarted/index.tsx:122
msgid "Create tickets for your event, set prices, and manage available quantity."
msgstr "Crea biglietti per il tuo evento, imposta i prezzi e gestisci la quantità disponibile."

#: src/components/modals/CreateWebhookModal/index.tsx:57
#: src/components/modals/CreateWebhookModal/index.tsx:67
msgid "Create Webhook"
msgstr "Crea Webhook"

#: src/components/common/OrdersTable/index.tsx:208
#: src/components/common/OrdersTable/index.tsx:281
msgid "Created"
msgstr "Creato"

#: src/components/forms/OrganizerForm/index.tsx:45
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:73
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:100
msgid "Currency"
msgstr "Valuta"

#: src/components/routes/profile/ManageProfile/index.tsx:191
msgid "Current Password"
msgstr "Password Attuale"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:159
msgid "Custom Maps URL"
msgstr "URL Maps Personalizzato"

#: src/components/common/ReportTable/index.tsx:53
msgid "Custom Range"
msgstr "Intervallo Personalizzato"

#: src/components/common/OrdersTable/index.tsx:205
msgid "Customer"
msgstr "Cliente"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:55
msgid "Customize the email and notification settings for this event"
msgstr "Personalizza le impostazioni di email e notifiche per questo evento"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
#~ msgid "Customize the email settings for this event"
#~ msgstr "Personalizza le impostazioni email per questo evento"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:72
#~ msgid "Customize the event homepage and checkout experience"
#~ msgstr "Personalizza la homepage dell'evento e l'esperienza di checkout"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:62
msgid "Customize the event homepage and checkout messaging"
msgstr "Personalizza i messaggi della homepage dell'evento e del checkout"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:70
msgid "Customize the miscellaneous settings for this event"
msgstr "Personalizza le impostazioni varie per questo evento"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:57
msgid "Customize the SEO settings for this event"
msgstr "Personalizza le impostazioni SEO per questo evento"

#: src/components/routes/event/GettingStarted/index.tsx:169
msgid "Customize your event page"
msgstr "Personalizza la pagina del tuo evento"

#: src/components/layouts/AuthLayout/index.tsx:84
msgid "Customize your event page and widget design to match your brand perfectly"
msgstr "Personalizza la pagina del tuo evento e il design del widget per adattarli perfettamente al tuo brand"

#: src/components/routes/event/GettingStarted/index.tsx:165
msgid "Customize your event page to match your brand and style."
msgstr "Personalizza la pagina del tuo evento per adattarla al tuo brand e stile."

#: src/components/routes/event/Reports/index.tsx:23
msgid "Daily Sales Report"
msgstr "Report Vendite Giornaliere"

#: src/components/routes/event/Reports/index.tsx:24
msgid "Daily sales, tax, and fee breakdown"
msgstr "Ripartizione giornaliera di vendite, tasse e commissioni"

#: src/components/common/CapacityAssignmentList/index.tsx:156
#: src/components/common/CheckInListList/index.tsx:176
#: src/components/common/OrdersTable/index.tsx:186
#: src/components/common/ProductsTable/SortableProduct/index.tsx:287
#: src/components/common/PromoCodeTable/index.tsx:181
#: src/components/common/QuestionsTable/index.tsx:115
#: src/components/common/WebhookTable/index.tsx:116
msgid "Danger zone"
msgstr "Danger zone"

#: src/components/common/AttendeeTable/index.tsx:179
msgid "Danger Zone"
msgstr "Danger Zone"

#: src/components/layouts/Event/index.tsx:89
msgid "Dashboard"
msgstr "Dashboard"

#: src/components/common/OrderDetails/index.tsx:39
#: src/components/forms/QuestionForm/index.tsx:136
msgid "Date"
msgstr "Data"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:40
#~ msgid "Date & Time"
#~ msgstr "Data e Ora"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:38
msgid "Day one capacity"
msgstr "Capacità primo giorno"

#: src/components/forms/CheckInListForm/index.tsx:21
#~ msgid "Day one check-in list"
#~ msgstr "Lista check-in primo giorno"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#~ msgid "Deactivate user"
#~ msgstr "Disattiva utente"

#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/TaxAndFeeList/index.tsx:73
msgid "Delete"
msgstr "Elimina"

#: src/components/common/CapacityAssignmentList/index.tsx:159
msgid "Delete Capacity"
msgstr "Elimina Capacità"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:146
#: src/components/common/ProductsTable/SortableCategory/index.tsx:147
msgid "Delete category"
msgstr "Elimina categoria"

#: src/components/common/CheckInListList/index.tsx:179
msgid "Delete Check-In List"
msgstr "Elimina Lista Check-In"

#: src/components/common/PromoCodeTable/index.tsx:185
msgid "Delete code"
msgstr "Elimina codice"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:113
msgid "Delete Cover"
msgstr "Elimina Copertina"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:98
msgid "Delete Image"
msgstr "Elimina Immagine"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:292
msgid "Delete product"
msgstr "Elimina prodotto"

#: src/components/common/QuestionsTable/index.tsx:120
msgid "Delete question"
msgstr "Elimina domanda"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:199
#~ msgid "Delete ticket"
#~ msgstr "Elimina biglietto"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#~ msgid "Delete user"
#~ msgstr "Elimina utente"

#: src/components/common/WebhookTable/index.tsx:127
msgid "Delete webhook"
msgstr "Elimina webhook"

#: src/components/forms/ProductForm/index.tsx:258
#: src/components/forms/TaxAndFeeForm/index.tsx:81
#: src/components/modals/CreateEventModal/index.tsx:135
#: src/components/modals/DuplicateEventModal/index.tsx:84
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:78
msgid "Description"
msgstr "Descrizione"

#: src/components/forms/CheckInListForm/index.tsx:36
msgid "Description for check-in staff"
msgstr "Descrizione per il personale di check-in"

#: src/components/modals/CreateEventModal/index.tsx:40
#~ msgid "Description should be less than 50,000 characters"
#~ msgstr "La descrizione deve essere inferiore a 50.000 caratteri"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Details"
msgstr "Dettagli"

#: src/components/common/PromoCodeTable/index.tsx:167
#~ msgid "Disable code"
#~ msgstr "Disabilita codice"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
#~ msgid "Disable this capacity track capacity without stopping product sales"
#~ msgstr "Disabilita il monitoraggio di questa capacità senza interrompere le vendite dei prodotti"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:26
#~ msgid "Disable this capacity track capacity without stopping ticket sales"
#~ msgstr "Disabilita il monitoraggio di questa capacità senza interrompere le vendite dei biglietti"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
msgid "Disabling this capacity will track sales but not stop them when the limit is reached"
msgstr "Disabilitando questa capacità verranno monitorate le vendite ma non verranno interrotte quando viene raggiunto il limite"

#: src/components/common/PromoCodeTable/index.tsx:70
msgid "Discount"
msgstr "Sconto"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount %"
msgstr "Sconto %"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount in {0}"
msgstr "Sconto in {0}"

#: src/components/forms/PromoCodeForm/index.tsx:43
msgid "Discount Type"
msgstr "Tipo di Sconto"

#: src/components/routes/product-widget/SelectProducts/index.tsx:297
#~ msgid "Dismiss"
#~ msgstr "Chiudi"

#: src/components/routes/product-widget/SelectProducts/index.tsx:354
msgid "Dismiss this message"
msgstr "Ignora questo messaggio"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:190
msgid "Document Label"
msgstr "Etichetta Documento"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:222
msgid "Documentation"
msgstr "Documentazione"

#: src/components/layouts/AuthLayout/index.tsx:19
#~ msgid "Does not exist"
#~ msgstr "Non esiste"

#: src/components/routes/auth/Login/index.tsx:57
msgid "Don't have an account?   <0>Sign up</0>"
msgstr "Non hai un account?   <0>Registrati</0>"

#: src/components/routes/auth/Login/index.tsx:72
#~ msgid "Don't have an account?   <0>Sign Up</0>"
#~ msgstr "Non hai un account?   <0>Registrati</0>"

#: src/components/forms/ProductForm/index.tsx:157
msgid "Donation / Pay what you'd like product"
msgstr "Donazione / Prodotto a offerta libera"

#: src/components/forms/TicketForm/index.tsx:139
#~ msgid "Donation / Pay what you'd like ticket"
#~ msgstr "Donazione / Biglietto a offerta libera"

#: src/components/routes/ticket-widget/SelectTickets/Prices/Tiered/index.tsx:59
#~ msgid "Donation amount"
#~ msgstr "Importo donazione"

#: src/components/forms/TicketForm/index.tsx:139
#~ msgid "Donation Ticket"
#~ msgstr "Biglietto Donazione"

#: src/components/common/AddEventToCalendarButton/index.tsx:137
msgid "Download .ics"
msgstr "Scarica .ics"

#: src/components/common/DownloadCsvButton/index.tsx:48
msgid "Download CSV"
msgstr "Scarica CSV"

#: src/components/common/OrdersTable/index.tsx:162
msgid "Download invoice"
msgstr "Scarica fattura"

#: src/components/layouts/Checkout/index.tsx:126
msgid "Download Invoice"
msgstr "Scarica Fattura"

#: src/components/modals/ShareModal/index.tsx:224
msgid "Download QR Code"
msgstr "Scarica Codice QR"

#: src/components/routes/ticket-widget/OrderSummaryAndTickets/index.tsx:91
#~ msgid "Download Tickets PDF"
#~ msgstr "Scarica PDF Biglietti"

#: src/components/common/OrdersTable/index.tsx:111
#: src/components/layouts/Checkout/index.tsx:49
msgid "Downloading Invoice"
msgstr "Scaricamento Fattura in corso"

#: src/components/layouts/Event/index.tsx:188
msgid "Draft"
msgstr "Bozza"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:83
msgid "Drag and drop or click"
msgstr "Trascina e rilascia o clicca"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Drag to sort"
#~ msgstr "Trascina per ordinare"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:83
#~ msgid "Drop an image, or click here to replace the Cover Image"
#~ msgstr "Rilascia un'immagine, o clicca qui per sostituire l'Immagine di Copertina"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:84
#~ msgid "Drop an image, or click here to upload the Cover Image"
#~ msgstr "Rilascia un'immagine, o clicca qui per caricare l'Immagine di Copertina"

#: src/components/forms/QuestionForm/index.tsx:124
msgid "Dropdown selection"
msgstr "Selezione a tendina"

#: src/components/modals/SendMessageModal/index.tsx:159
msgid ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."
msgstr ""
"A causa dell'alto rischio di spam, è necessaria una verifica manuale prima di poter inviare messaggi.\n"
"Contattaci per richiedere l'accesso."

#: src/components/modals/DuplicateEventModal/index.tsx:124
msgid "Duplicate Capacity Assignments"
msgstr "Duplica Assegnazioni di Capacità"

#: src/components/modals/DuplicateEventModal/index.tsx:128
msgid "Duplicate Check-In Lists"
msgstr "Duplica Liste di Check-In"

#: src/components/common/EventCard/index.tsx:162
msgid "Duplicate event"
msgstr "Duplica evento"

#: src/components/modals/DuplicateEventModal/index.tsx:69
#: src/components/modals/DuplicateEventModal/index.tsx:142
msgid "Duplicate Event"
msgstr "Duplica Evento"

#: src/components/modals/DuplicateEventModal/index.tsx:132
msgid "Duplicate Event Cover Image"
msgstr "Duplica Immagine di Copertina Evento"

#: src/components/modals/DuplicateEventModal/index.tsx:103
msgid "Duplicate Options"
msgstr "Opzioni di Duplicazione"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:285
#: src/components/modals/DuplicateProductModal/index.tsx:109
#: src/components/modals/DuplicateProductModal/index.tsx:113
msgid "Duplicate Product"
msgstr "Duplica Prodotto"

#: src/components/modals/DuplicateEventModal/index.tsx:108
msgid "Duplicate Products"
msgstr "Duplica Prodotti"

#: src/components/modals/DuplicateEventModal/index.tsx:120
msgid "Duplicate Promo Codes"
msgstr "Duplica Codici Promozionali"

#: src/components/modals/DuplicateEventModal/index.tsx:112
msgid "Duplicate Questions"
msgstr "Duplica Domande"

#: src/components/modals/DuplicateEventModal/index.tsx:116
msgid "Duplicate Settings"
msgstr "Duplica Impostazioni"

#: src/components/modals/DuplicateEventModal/index.tsx:107
#~ msgid "Duplicate Tickets"
#~ msgstr "Duplica Biglietti"

#: src/components/modals/DuplicateEventModal/index.tsx:137
msgid "Duplicate Webhooks"
msgstr "Duplica Webhook"

#: src/components/common/LanguageSwitcher/index.tsx:24
msgid "Dutch"
msgstr "Olandese"

#: src/components/forms/ProductForm/index.tsx:78
msgid "Early bird"
msgstr "Prevendita"

#: src/components/common/TaxAndFeeList/index.tsx:65
#: src/components/modals/ManageAttendeeModal/index.tsx:221
#: src/components/modals/ManageOrderModal/index.tsx:203
msgid "Edit"
msgstr "Modifica"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
msgid "Edit {0}"
msgstr "Modifica {0}"

#: src/components/common/QuestionAndAnswerList/index.tsx:159
msgid "Edit Answer"
msgstr "Modifica Risposta"

#: src/components/common/AttendeeTable/index.tsx:174
#~ msgid "Edit attendee"
#~ msgstr "Modifica partecipante"

#: src/components/modals/EditAttendeeModal/index.tsx:72
#: src/components/modals/EditAttendeeModal/index.tsx:110
#~ msgid "Edit Attendee"
#~ msgstr "Modifica Partecipante"

#: src/components/common/CapacityAssignmentList/index.tsx:146
msgid "Edit Capacity"
msgstr "Modifica Capacità"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:66
#: src/components/modals/EditCapacityAssignmentModal/index.tsx:74
msgid "Edit Capacity Assignment"
msgstr "Modifica Assegnazione di Capacità"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:137
#: src/components/common/ProductsTable/SortableCategory/index.tsx:138
msgid "Edit category"
msgstr "Modifica categoria"

#: src/components/common/CheckInListList/index.tsx:148
#: src/components/modals/EditCheckInListModal/index.tsx:69
#: src/components/modals/EditCheckInListModal/index.tsx:91
msgid "Edit Check-In List"
msgstr "Modifica Lista di Check-In"

#: src/components/common/PromoCodeTable/index.tsx:170
msgid "Edit Code"
msgstr "Modifica Codice"

#: src/components/modals/EditOrganizerModal/index.tsx:63
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:51
msgid "Edit Organizer"
msgstr "Modifica Organizzatore"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:280
#: src/components/modals/EditProductModal/index.tsx:100
#: src/components/modals/EditProductModal/index.tsx:108
msgid "Edit Product"
msgstr "Modifica Prodotto"

#: src/components/modals/EditProductCategoryModal/index.tsx:62
#: src/components/modals/EditProductCategoryModal/index.tsx:70
msgid "Edit Product Category"
msgstr "Modifica Categoria Prodotto"

#: src/components/modals/EditPromoCodeModal/index.tsx:79
#: src/components/modals/EditPromoCodeModal/index.tsx:84
msgid "Edit Promo Code"
msgstr "Modifica Codice Promozionale"

#: src/components/common/QuestionsTable/index.tsx:112
msgid "Edit question"
msgstr "Modifica domanda"

#: src/components/modals/EditQuestionModal/index.tsx:95
#: src/components/modals/EditQuestionModal/index.tsx:101
msgid "Edit Question"
msgstr "Modifica Domanda"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:191
#: src/components/modals/EditTicketModal/index.tsx:96
#: src/components/modals/EditTicketModal/index.tsx:104
#~ msgid "Edit Ticket"
#~ msgstr "Modifica Biglietto"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Edit user"
msgstr "Modifica utente"

#: src/components/modals/EditUserModal/index.tsx:62
#: src/components/modals/EditUserModal/index.tsx:120
msgid "Edit User"
msgstr "Modifica Utente"

#: src/components/common/WebhookTable/index.tsx:104
msgid "Edit webhook"
msgstr "Modifica webhook"

#: src/components/modals/EditWebhookModal/index.tsx:66
#: src/components/modals/EditWebhookModal/index.tsx:87
msgid "Edit Webhook"
msgstr "Modifica Webhook"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 2.50 for $2.50"
msgstr "es. 2.50 per $2.50"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 23.5 for 23.5%"
msgstr "es. 23.5 per 23.5%"

#: src/components/common/AttendeeDetails/index.tsx:21
#: src/components/common/AttendeeTable/index.tsx:109
#: src/components/common/OrderDetails/index.tsx:31
#: src/components/forms/OrganizerForm/index.tsx:32
#: src/components/modals/EditUserModal/index.tsx:80
#: src/components/modals/InviteUserModal/index.tsx:61
#: src/components/routes/auth/AcceptInvitation/index.tsx:98
#: src/components/routes/auth/Login/index.tsx:68
#: src/components/routes/auth/Register/index.tsx:97
#: src/components/routes/event/Settings/index.tsx:52
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:117
#: src/components/routes/profile/ManageProfile/index.tsx:138
msgid "Email"
msgstr "Email"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
msgid "Email & Notification Settings"
msgstr "Impostazioni Email e Notifiche"

#: src/components/modals/CreateAttendeeModal/index.tsx:132
#: src/components/modals/ManageAttendeeModal/index.tsx:114
#: src/components/modals/ManageOrderModal/index.tsx:157
msgid "Email address"
msgstr "Indirizzo email"

#: src/components/common/QuestionsTable/index.tsx:220
#: src/components/common/QuestionsTable/index.tsx:221
#: src/components/routes/product-widget/CollectInformation/index.tsx:298
#: src/components/routes/product-widget/CollectInformation/index.tsx:299
#: src/components/routes/product-widget/CollectInformation/index.tsx:408
#: src/components/routes/product-widget/CollectInformation/index.tsx:409
msgid "Email Address"
msgstr "Indirizzo Email"

#: src/components/routes/profile/ManageProfile/index.tsx:77
msgid "Email change cancelled successfully"
msgstr "Modifica email annullata con successo"

#: src/components/routes/profile/ManageProfile/index.tsx:116
msgid "Email change pending"
msgstr "Modifica email in attesa"

#: src/components/routes/event/settings.tsx:72
#~ msgid "Email Configuration"
#~ msgstr "Configurazione Email"

#: src/components/routes/profile/ManageProfile/index.tsx:151
msgid "Email confirmation resent"
msgstr "Conferma email inviata nuovamente"

#: src/components/routes/profile/ManageProfile/index.tsx:90
msgid "Email confirmation resent successfully"
msgstr "Conferma email inviata nuovamente con successo"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:66
msgid "Email footer message"
msgstr "Messaggio piè di pagina email"

#: src/components/routes/profile/ManageProfile/index.tsx:141
msgid "Email not verified"
msgstr "Email non verificata"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:53
#~ msgid "Email Settings"
#~ msgstr "Impostazioni Email"

#: src/components/common/WidgetEditor/index.tsx:272
msgid "Embed Code"
msgstr "Codice di Incorporamento"

#: src/components/common/WidgetEditor/index.tsx:260
msgid "Embed Script"
msgstr "Script di Incorporamento"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:181
msgid "Enable Invoicing"
msgstr "Abilita Fatturazione"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:21
msgid "Enable this capacity to stop product sales when the limit is reached"
msgstr "Abilita questa capacità per interrompere le vendite dei prodotti quando viene raggiunto il limite"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:20
#~ msgid "Enable this capacity to stop ticket sales when the limit is reached"
#~ msgstr "Abilita questa capacità per interrompere le vendite dei biglietti quando viene raggiunto il limite"

#: src/components/forms/WebhookForm/index.tsx:19
msgid "Enabled"
msgstr "Abilitato"

#: src/components/modals/CreateEventModal/index.tsx:149
#: src/components/modals/DuplicateEventModal/index.tsx:98
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:92
#: src/components/routes/welcome/index.tsx:96
msgid "End Date"
msgstr "Data Fine"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:26
#: src/components/common/EventStatusBadge/index.tsx:14
msgid "Ended"
msgstr "Terminato"

#: src/components/routes/events/Dashboard/index.tsx:57
msgid "Ended Events"
msgstr "Eventi Terminati"

#: src/components/common/LanguageSwitcher/index.tsx:16
#: src/components/common/LanguageSwitcher/index.tsx:50
#: src/components/modals/CreateAttendeeModal/index.tsx:145
#: src/components/routes/profile/ManageProfile/index.tsx:173
msgid "English"
msgstr "Inglese"

#: src/components/modals/CreateAttendeeModal/index.tsx:170
msgid "Enter an amount excluding taxes and fees."
msgstr "Inserisci un importo escluse tasse e commissioni."

#: src/components/common/OrdersTable/index.tsx:119
#: src/components/layouts/Checkout/index.tsx:57
msgid "Error"
msgstr "Errore"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:27
msgid "Error confirming email address"
msgstr "Errore durante la conferma dell'indirizzo email"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:35
msgid "Error confirming email change"
msgstr "Errore durante la conferma della modifica email"

#: src/components/modals/WebhookLogsModal/index.tsx:166
msgid "Error loading logs"
msgstr "Errore durante il caricamento dei log"

#: src/components/forms/OrganizerForm/index.tsx:46
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:74
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:101
msgid "EUR"
msgstr "EUR"

#: src/components/routes/events/Dashboard/index.tsx:103
msgid "Event"
msgstr "Evento"

#: src/components/routes/events/CreateEvent/index.tsx:45
#~ msgid "Event created successfully"
#~ msgstr "Evento creato con successo"

#: src/components/modals/CreateEventModal/index.tsx:76
#~ msgid "Event created successfully 🎉"
#~ msgstr "Evento creato con successo 🎉"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:161
msgid "Event Date"
msgstr "Data Evento"

#: src/components/routes/account/ManageAccount/sections/EventDefaultsSettings/index.tsx:5
msgid "Event Defaults"
msgstr "Impostazioni Predefinite Evento"

#: src/components/routes/event/Settings/index.tsx:28
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:276
msgid "Event Details"
msgstr "Dettagli Evento"

#: src/components/modals/DuplicateEventModal/index.tsx:58
msgid "Event duplicated successfully"
msgstr "Evento duplicato con successo"

#: src/components/layouts/Checkout/index.tsx:79
msgid "Event Homepage"
msgstr "Homepage Evento"

#: src/components/layouts/Event/index.tsx:166
#~ msgid "Event is not visible to the public"
#~ msgstr "L'evento non è visibile al pubblico"

#: src/components/layouts/Event/index.tsx:165
#~ msgid "Event is visible to the public"
#~ msgstr "L'evento è visibile al pubblico"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:85
msgid "Event location & venue details"
msgstr "Dettagli della sede e della location dell'evento"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:12
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
msgid "Event Not Available"
msgstr "Evento Non Disponibile"

#: src/components/layouts/Event/index.tsx:187
#~ msgid "Event page"
#~ msgstr "Pagina evento"

#: src/components/layouts/Event/index.tsx:212
msgid "Event Page"
msgstr "Pagina dell'evento"

#: src/components/routes/event/settings.tsx:42
#~ msgid "Event Settings"
#~ msgstr "Impostazioni Evento"

#: src/components/common/EventCard/index.tsx:70
#: src/components/layouts/Event/index.tsx:80
#: src/components/routes/event/EventDashboard/index.tsx:76
#: src/components/routes/event/GettingStarted/index.tsx:63
msgid "Event status update failed. Please try again later"
msgstr "Aggiornamento stato evento fallito. Riprova più tardi"

#: src/components/common/EventCard/index.tsx:67
#: src/components/layouts/Event/index.tsx:77
#: src/components/routes/event/EventDashboard/index.tsx:73
#: src/components/routes/event/GettingStarted/index.tsx:60
msgid "Event status updated"
msgstr "Stato evento aggiornato"


#: src/components/common/WebhookTable/index.tsx:237
#: src/components/forms/WebhookForm/index.tsx:122
msgid "Event Types"
msgstr "Tipi di Evento"

#: src/components/modals/ShareModal/index.tsx:237
msgid "Event URL"
msgstr "URL Evento"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
#~ msgid "Event wdwdeNot Available"
#~ msgstr ""

#: src/components/layouts/Event/index.tsx:226
msgid "Events"
msgstr "Eventi"

#: src/components/forms/CheckInListForm/index.tsx:56
msgid "Expiration date"
msgstr "Data di scadenza"

#: src/components/common/PromoCodeTable/index.tsx:73
msgid "Expires"
msgstr "Scade"

#: src/components/forms/PromoCodeForm/index.tsx:79
msgid "Expiry Date"
msgstr "Data di Scadenza"

#: src/components/routes/event/attendees.tsx:80
#: src/components/routes/event/orders.tsx:140
msgid "Export"
msgstr "Esporta"

#: src/components/common/QuestionsTable/index.tsx:267
msgid "Export answers"
msgstr "Esporta risposte"

#: src/mutations/useExportAnswers.ts:39
msgid "Export failed. Please try again."
msgstr "Esportazione fallita. Riprova."

#: src/mutations/useExportAnswers.ts:17
msgid "Export started. Preparing file..."
msgstr "Esportazione avviata. Preparazione file..."

#: src/components/routes/event/attendees.tsx:39
msgid "Exporting Attendees"
msgstr "Esportazione Partecipanti"

#: src/mutations/useExportAnswers.ts:33
msgid "Exporting complete. Downloading file..."
msgstr "Esportazione completata. Download file in corso..."

#: src/components/routes/event/orders.tsx:90
msgid "Exporting Orders"
msgstr "Esportazione Ordini"

#: src/components/common/AttendeeTable/index.tsx:97
msgid "Failed to cancel attendee"
msgstr "Impossibile annullare il partecipante"

#: src/components/modals/CancelOrderModal/index.tsx:33
msgid "Failed to cancel order"
msgstr "Impossibile annullare l'ordine"

#: src/components/common/QuestionsTable/index.tsx:175
msgid "Failed to delete message. Please try again."
msgstr "Impossibile eliminare il messaggio. Riprova."

#: src/components/common/OrdersTable/index.tsx:120
#: src/components/layouts/Checkout/index.tsx:58
msgid "Failed to download invoice. Please try again."
msgstr "Impossibile scaricare la fattura. Riprova."

#: src/components/routes/event/attendees.tsx:48
msgid "Failed to export attendees"
msgstr "Impossibile esportare i partecipanti"

#: src/components/routes/event/attendees.tsx:39
#~ msgid "Failed to export attendees. Please try again."
#~ msgstr "Impossibile esportare i partecipanti. Riprova."

#: src/components/routes/event/orders.tsx:99
msgid "Failed to export orders"
msgstr "Impossibile esportare gli ordini"

#: src/components/routes/event/orders.tsx:88
#~ msgid "Failed to export orders. Please try again."
#~ msgstr "Impossibile esportare gli ordini. Riprova."

#: src/components/modals/EditCheckInListModal/index.tsx:79
msgid "Failed to load Check-In List"
msgstr "Impossibile caricare la Lista di Check-In"

#: src/components/modals/EditWebhookModal/index.tsx:75
msgid "Failed to load Webhook"
msgstr "Impossibile caricare il Webhook"

#: src/components/common/AttendeeTable/index.tsx:51
#~ msgid "Failed to resend product email"
#~ msgstr "Impossibile reinviare l'email del prodotto"

#: src/components/common/AttendeeTable/index.tsx:50
msgid "Failed to resend ticket email"
msgstr "Impossibile reinviare l'email del biglietto"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:179
msgid "Failed to sort products"
msgstr "Impossibile ordinare i prodotti"

#: src/mutations/useExportAnswers.ts:15
msgid "Failed to start export job"
msgstr "Impossibile avviare il processo di esportazione"

#: src/components/common/QuestionAndAnswerList/index.tsx:102
msgid "Failed to update answer."
msgstr "Impossibile aggiornare la risposta."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:64
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:69
msgid "Failed to upload image."
msgstr "Caricamento immagine non riuscito."

#: src/components/forms/TaxAndFeeForm/index.tsx:18
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Fee"
msgstr "Commissione"

#: src/components/common/GlobalMenu/index.tsx:30
#~ msgid "Feedback"
#~ msgstr "Feedback"

#: src/components/forms/ProductForm/index.tsx:366
msgid "Fees"
msgstr "Commissioni"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:80
msgid "Fees are subject to change. You will be notified of any changes to your fee structure."
msgstr "Le commissioni sono soggette a modifiche. Sarai informato di qualsiasi modifica alla struttura delle tue commissioni."

#: src/components/routes/event/orders.tsx:121
msgid "Filter Orders"
msgstr "Filtra Ordini"

#: src/components/common/FilterModal/index.tsx:75
#: src/components/common/FilterModal/index.tsx:223
msgid "Filters"
msgstr "Filtri"

#: src/components/common/FilterModal/index.tsx:223
msgid "Filters ({activeFilterCount})"
msgstr "Filtri ({activeFilterCount})"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:206
msgid "First Invoice Number"
msgstr "Primo Numero Fattura"

#: src/components/common/QuestionsTable/index.tsx:208
#: src/components/modals/CreateAttendeeModal/index.tsx:118
#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:144
#: src/components/routes/product-widget/CollectInformation/index.tsx:284
#: src/components/routes/product-widget/CollectInformation/index.tsx:395
msgid "First name"
msgstr "Nome"

#: src/components/common/QuestionsTable/index.tsx:207
#: src/components/modals/EditUserModal/index.tsx:71
#: src/components/modals/InviteUserModal/index.tsx:57
#: src/components/routes/auth/AcceptInvitation/index.tsx:92
#: src/components/routes/auth/Register/index.tsx:83
#: src/components/routes/product-widget/CollectInformation/index.tsx:283
#: src/components/routes/product-widget/CollectInformation/index.tsx:394
#: src/components/routes/profile/ManageProfile/index.tsx:135
msgid "First Name"
msgstr "Nome"

#: src/components/routes/auth/AcceptInvitation/index.tsx:29
msgid "First name must be between 1 and 50 characters"
msgstr "Il nome deve essere compreso tra 1 e 50 caratteri"

#: src/components/common/QuestionsTable/index.tsx:349
msgid "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."
msgstr "Nome, Cognome e Indirizzo Email sono domande predefinite e sono sempre incluse nel processo di checkout."

#: src/components/common/QuestionsTable/index.tsx:327
#~ msgid "First Name, Last Name, and Email Address are default questions that are always included in the checkout process."
#~ msgstr "Nome, Cognome e Indirizzo Email sono domande predefinite che sono sempre incluse nel processo di checkout."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
msgid "First Used"
msgstr "Primo Utilizzo"

#: src/components/forms/TaxAndFeeForm/index.tsx:33
msgid "Fixed"
msgstr "Fisso"

#: src/components/forms/PromoCodeForm/index.tsx:55
msgid "Fixed amount"
msgstr "Importo fisso"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:65
msgid "Fixed Fee:"
msgstr "Commissione Fissa:"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:123
msgid "Flash is not available on this device"
msgstr "Il flash non è disponibile su questo dispositivo"

#: src/components/layouts/AuthLayout/index.tsx:58
msgid "Flexible Ticketing"
msgstr "Biglietteria Flessibile"

#: src/components/routes/auth/Login/index.tsx:80
msgid "Forgot password?"
msgstr "Password dimenticata?"

#: src/components/common/AttendeeTicket/index.tsx:40
#: src/components/common/Currency/index.tsx:90
#: src/components/common/OrderSummary/index.tsx:46
#: src/components/common/OrderSummary/index.tsx:99
#: src/components/common/ProductsTable/SortableProduct/index.tsx:93
#: src/components/common/ProductsTable/SortableProduct/index.tsx:103
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:53
msgid "Free"
msgstr "Gratuito"

#: src/components/forms/ProductForm/index.tsx:151
msgid "Free Product"
msgstr "Prodotto Gratuito"

#: src/components/forms/ProductForm/index.tsx:153
msgid "Free product, no payment information required"
msgstr "Prodotto gratuito, nessuna informazione di pagamento richiesta"

#: src/components/forms/TicketForm/index.tsx:133
#~ msgid "Free Ticket"
#~ msgstr "Biglietto Gratuito"

#: src/components/forms/TicketForm/index.tsx:135
#~ msgid "Free ticket, no payment information required"
#~ msgstr "Biglietto gratuito, nessuna informazione di pagamento richiesta"

#: src/components/common/LanguageSwitcher/index.tsx:20
msgid "French"
msgstr "Francese"

#: src/components/layouts/EventHomepage/index.tsx:34
#~ msgid "From"
#~ msgstr "Da"

#: src/components/layouts/AuthLayout/index.tsx:88
msgid "Fully Integrated"
msgstr "Completamente integrato"

#: src/components/forms/ProductForm/index.tsx:136
msgid "General"
msgstr "Generale"

#: src/components/common/LanguageSwitcher/index.tsx:14
msgid "German"
msgstr "Tedesco"

#: src/components/layouts/AuthLayout/index.tsx:35
msgid "Get started for free, no subscription fees"
msgstr "Inizia gratuitamente, nessun costo di abbonamento"

#: src/components/layouts/EventHomepage/index.tsx:42
#~ msgid "Get Tickets"
#~ msgstr "Ottieni Biglietti"

#: src/components/routes/event/EventDashboard/index.tsx:125
msgid "Get your event ready"
msgstr "Prepara il tuo evento"

#: src/components/layouts/Event/index.tsx:88
msgid "Getting Started"
msgstr "Primi Passi"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:23
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:42
msgid "Go back to profile"
msgstr "Torna al profilo"

#: src/components/routes/event/GettingStarted/index.tsx:22
#~ msgid "Go to Dashboard"
#~ msgstr "Vai alla Dashboard"

#: src/components/routes/product-widget/CollectInformation/index.tsx:239
msgid "Go to event homepage"
msgstr "Vai alla homepage dell'evento"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:53
msgid "Go to Hi.Events"
msgstr "Vai a Hi.Events"

#: src/components/common/ErrorDisplay/index.tsx:64
msgid "Go to home page"
msgstr "Vai alla pagina iniziale"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:162
msgid "Go to Stripe Dashboard"
msgstr "Vai alla Dashboard di Stripe"

#: src/components/common/AddEventToCalendarButton/index.tsx:128
msgid "Google Calendar"
msgstr "Google Calendar"

#: src/components/common/EventCard/index.tsx:133
msgid "gross sales"
msgstr "vendite lorde"

#: src/components/common/StatBoxes/index.tsx:39
msgid "Gross sales"
msgstr "Vendite lorde"

#: src/components/routes/event/EventDashboard/index.tsx:274
msgid "Gross Sales"
msgstr "Vendite Lorde"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:281
msgid "Guests"
msgstr "Ospiti"

#: src/components/routes/product-widget/SelectProducts/index.tsx:495
msgid "Have a promo code?"
msgstr "Hai un codice promozionale?"

#: src/components/forms/OrganizerForm/index.tsx:33
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/GlobalMenu/index.tsx:24
#~ msgid "Help & Support"
#~ msgstr "Aiuto e Supporto"

#: src/components/common/WidgetEditor/index.tsx:295
msgid "Here is an example of how you can use the component in your application."
msgstr "Ecco un esempio di come puoi utilizzare il componente nella tua applicazione."

#: src/components/common/WidgetEditor/index.tsx:284
msgid "Here is the React component you can use to embed the widget in your application."
msgstr "Ecco il componente React che puoi utilizzare per incorporare il widget nella tua applicazione."

#: src/components/routes/event/EventDashboard/index.tsx:101
msgid "Hi {0} 👋"
msgstr "Ciao {0} 👋"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:43
msgid "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."
msgstr "Hi.Events addebita commissioni di piattaforma per mantenere e migliorare i nostri servizi. Queste commissioni vengono automaticamente detratte da ogni transazione."

#: src/components/modals/CreateEventModal/index.tsx:130
#: src/components/modals/DuplicateEventModal/index.tsx:79
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:73
msgid "Hi.Events Conference {0}"
msgstr "Conferenza Hi.Events {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:117
msgid "Hi.Events Conference Center"
msgstr "Centro Conferenze Hi.Events"

#: src/components/layouts/AuthLayout/index.tsx:131
msgid "hi.events logo"
msgstr "logo hi.events"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:79
msgid "Hidden from public view"
msgstr "Nascosto dalla vista pubblica"

#: src/components/common/QuestionsTable/index.tsx:290
msgid "hidden question"
msgstr "domanda nascosta"

#: src/components/common/QuestionsTable/index.tsx:291
msgid "hidden questions"
msgstr "domande nascoste"

#: src/components/forms/QuestionForm/index.tsx:218
msgid "Hidden questions are only visible to the event organizer and not to the customer."
msgstr "Le domande nascoste sono visibili solo all'organizzatore dell'evento e non al cliente."

#: src/components/forms/QuestionForm/index.tsx:187
#~ msgid "Hidden will not be shown to customers."
#~ msgstr "Nascosto non sarà mostrato ai clienti."

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/routes/product-widget/SelectProducts/index.tsx:460
msgid "Hide"
msgstr "Nascondi"

#: src/components/common/AttendeeList/index.tsx:84
msgid "Hide Answers"
msgstr "Nascondi Risposte"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:89
msgid "Hide getting started page"
msgstr "Nascondi pagina di introduzione"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Hide hidden questions"
msgstr "Nascondi domande nascoste"

#: src/components/forms/ProductForm/index.tsx:399
msgid "Hide product after sale end date"
msgstr "Nascondi prodotto dopo la data di fine vendita"

#: src/components/forms/ProductForm/index.tsx:397
msgid "Hide product before sale start date"
msgstr "Nascondi prodotto prima della data di inizio vendita"

#: src/components/forms/ProductForm/index.tsx:412
msgid "Hide product unless user has applicable promo code"
msgstr "Nascondi prodotto a meno che l'utente non abbia un codice promozionale applicabile"

#: src/components/forms/ProductForm/index.tsx:405
msgid "Hide product when sold out"
msgstr "Nascondi prodotto quando esaurito"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:90
msgid "Hide the getting started page from the sidebar"
msgstr "Nascondi la pagina di introduzione dalla barra laterale"

#: src/components/forms/ProductForm/index.tsx:417
msgid "Hide this product from customers"
msgstr "Nascondi questo prodotto ai clienti"

#: src/components/forms/QuestionForm/index.tsx:219
msgid "Hide this question"
msgstr "Nascondi questa domanda"

#: src/components/forms/TicketForm/index.tsx:362
#~ msgid "Hide this ticket from customers"
#~ msgstr "Nascondi questo biglietto ai clienti"

#: src/components/forms/ProductForm/index.tsx:103
msgid "Hide this tier from users"
msgstr "Nascondi questo livello agli utenti"

#: src/components/forms/TicketForm/index.tsx:344
#~ msgid "Hide ticket after sale end date"
#~ msgstr "Nascondi biglietto dopo la data di fine vendita"

#: src/components/forms/TicketForm/index.tsx:342
#~ msgid "Hide ticket before sale start date"
#~ msgstr "Nascondi biglietto prima della data di inizio vendita"

#: src/components/forms/TicketForm/index.tsx:357
#~ msgid "Hide ticket unless user has applicable promo code"
#~ msgstr "Nascondi biglietto a meno che l'utente non abbia un codice promozionale applicabile"

#: src/components/forms/TicketForm/index.tsx:350
#~ msgid "Hide ticket when sold out"
#~ msgstr "Nascondi biglietto quando esaurito"

#: src/components/forms/ProductForm/index.tsx:101
msgid "Hiding a product will prevent users from seeing it on the event page."
msgstr "Nascondere un prodotto impedirà agli utenti di vederlo sulla pagina dell'evento."

#: src/components/forms/TicketForm/index.tsx:98
#~ msgid "Hiding a ticket will prevent users from seeing it on the event page."
#~ msgstr "Nascondere un biglietto impedirà agli utenti di vederlo sulla pagina dell'evento."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:71
#~ msgid "Homepage & Checkout"
#~ msgstr "Homepage e Checkout"

#: src/components/routes/event/settings.tsx:133
#~ msgid "Homepage & Checkout Settings"
#~ msgstr "Impostazioni Homepage e Checkout"

#: src/components/routes/event/HomepageDesigner/index.tsx:115
msgid "Homepage Design"
msgstr "Design Homepage"

#: src/components/layouts/Event/index.tsx:109
msgid "Homepage Designer"
msgstr "Designer Homepage"

#: src/components/routes/event/HomepageDesigner/index.tsx:174
msgid "Homepage Preview"
msgstr "Anteprima Homepage"

#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:145
msgid "Homer"
msgstr "Homer"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:88
#~ msgid "How many minutes the customer has to complete their order"
#~ msgstr "Quanti minuti ha il cliente per completare il proprio ordine"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
msgid "How many minutes the customer has to complete their order. We recommend at least 15 minutes"
msgstr "Quanti minuti ha il cliente per completare il proprio ordine. Consigliamo almeno 15 minuti"

#: src/components/forms/PromoCodeForm/index.tsx:84
msgid "How many times can this code be used?"
msgstr "Quante volte può essere utilizzato questo codice?"

#: src/components/common/Editor/index.tsx:75
msgid "HTML character limit exceeded: {htmlLength}/{maxLength}"
msgstr "Limite di caratteri HTML superato: {htmlLength}/{maxLength}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:160
msgid "https://example-maps-service.com/..."
msgstr "https://example-maps-service.com/..."

#: src/components/forms/WebhookForm/index.tsx:118
msgid "https://webhook-domain.com/webhook"
msgstr "https://webhook-domain.com/webhook"

#: src/components/routes/auth/AcceptInvitation/index.tsx:118
msgid "I agree to the <0>terms and conditions</0>"
msgstr "Accetto i <0>termini e condizioni</0>"

#: src/components/routes/auth/AcceptInvitation/index.tsx:108
#~ msgid "I agree to the terms and conditions"
#~ msgstr "Accetto i termini e condizioni"

#: src/components/routes/product-widget/Payment/index.tsx:106
msgid "I would like to pay using an offline method"
msgstr "Vorrei pagare utilizzando un metodo offline"

#: src/components/routes/product-widget/Payment/index.tsx:107
msgid "I would like to pay using an online method (credit card etc.)"
msgstr "Vorrei pagare utilizzando un metodo online (carta di credito ecc.)"

#: src/components/routes/product-widget/SelectProducts/index.tsx:315
msgid "If a new tab did not open automatically, please click the button below to continue to checkout."
msgstr "Se una nuova scheda non si è aperta automaticamente, clicca sul pulsante qui sotto per procedere al pagamento."

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:236
#~ msgid "If a new tab did not open, please  <0>{0}.</0>"
#~ msgstr "If a new tab did not open, please  <0>{0}.</0>"

#: src/components/routes/product-widget/SelectProducts/index.tsx:284
#~ msgid "If a new tab did not open, please  <0><1>{0}</1>.</0>"
#~ msgstr "Se non si è aperta una nuova scheda, per favore  <0><1>{0}</1>.</0>"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:149
#~ msgid "If blank, the address will be used to generate a Google map link"
#~ msgstr "If blank, the address will be used to generate a Google map link"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:158
msgid "If blank, the address will be used to generate a Google Mapa link"
msgstr "Se vuoto, l'indirizzo verrà utilizzato per generare un link a Google Maps"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:158
msgid "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."
msgstr "Se abilitato, il personale di check-in può sia segnare i partecipanti come registrati sia segnare l'ordine come pagato e registrare i partecipanti. Se disabilitato, i partecipanti associati a ordini non pagati non possono essere registrati."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:76
msgid "If enabled, the organizer will receive an email notification when a new order is placed"
msgstr "Se abilitato, l'organizzatore riceverà una notifica via email quando viene effettuato un nuovo ordine"

#: src/components/routes/profile/ManageProfile/index.tsx:122
msgid "If you did not request this change, please immediately change your password."
msgstr "Se non hai richiesto questa modifica, cambia immediatamente la tua password."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
msgid ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."
msgstr "Se hai un account con noi, riceverai un'email con le istruzioni per reimpostare la password."

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:108
msgid "Image deleted successfully"
msgstr "Immagine eliminata con successo"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:52
#~ msgid "Image dimensions must be at least 600px by 300px"
#~ msgstr "Le dimensioni dell'immagine devono essere almeno 600px per 300px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
#~ msgid "Image dimensions must be at least 900px by 450px"
#~ msgstr "Le dimensioni dell'immagine devono essere almeno 900px per 450px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
#~ msgid "Image dimensions must be between 3000px by 2000px. With a max height of 2000px and max width of 3000px"
#~ msgstr "Le dimensioni dell'immagine devono essere tra 3000px per 2000px. Con un'altezza massima di 2000px e una larghezza massima di 3000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
msgid "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"
msgstr "Le dimensioni dell'immagine devono essere tra 4000px per 4000px. Con un'altezza massima di 4000px e una larghezza massima di 4000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:49
msgid "Image must be less than 5MB"
msgstr "L'immagine deve essere inferiore a 5MB"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:63
msgid "Image uploaded successfully"
msgstr "Immagine caricata con successo"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:111
msgid "Image URL"
msgstr "URL Immagine"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
msgid "Image width must be at least 900px and height at least 50px"
msgstr "La larghezza dell'immagine deve essere almeno 900px e l'altezza almeno 50px"

#: src/components/layouts/AuthLayout/index.tsx:53
msgid "In-depth Analytics"
msgstr "Analisi approfondite"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:25
#: src/components/modals/EditUserModal/index.tsx:111
msgid "Inactive"
msgstr "Inattivo"

#: src/components/modals/EditUserModal/index.tsx:107
msgid "Inactive users cannot log in."
msgstr "Gli utenti inattivi non possono accedere."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:92
#~ msgid "Include connection details for your online event"
#~ msgstr "Includi dettagli di connessione per il tuo evento online"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:92
#~ msgid "Include connection details for your online event. These details will be after successful registration."
#~ msgstr "Includi dettagli di connessione per il tuo evento online. Questi dettagli saranno dopo la registrazione avvenuta con successo."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:92
#~ msgid "Include connection details for your online event. These details will be shown after successful registration."
#~ msgstr "Includi dettagli di connessione per il tuo evento online. Questi dettagli saranno mostrati dopo la registrazione avvenuta con successo."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee product page"
#~ msgstr "Includi dettagli di connessione per il tuo evento online. Questi dettagli saranno mostrati nella pagina di riepilogo dell'ordine e nella pagina del prodotto del partecipante"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page"
#~ msgstr "Includi dettagli di connessione per il tuo evento online. Questi dettagli saranno mostrati nella pagina di riepilogo dell'ordine e nella pagina del biglietto del partecipante"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:102
msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."
msgstr "Includi dettagli di connessione per il tuo evento online. Questi dettagli saranno mostrati nella pagina di riepilogo dell'ordine e nella pagina del biglietto del partecipante."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:54
msgid "Include tax and fees in the price"
msgstr "Includi tasse e commissioni nel prezzo"

#: src/components/common/CheckInListList/index.tsx:103
msgid "Includes {0} products"
msgstr "Include {0} prodotti"

#: src/components/common/CheckInListList/index.tsx:112
#~ msgid "Includes {0} tickets"
#~ msgstr "Include {0} biglietti"

#: src/components/common/CheckInListList/index.tsx:104
msgid "Includes 1 product"
msgstr "Include 1 prodotto"

#: src/components/common/CheckInListList/index.tsx:113
#~ msgid "Includes 1 ticket"
#~ msgstr "Include 1 biglietto"

#: src/components/common/MessageList/index.tsx:20
msgid "Individual attendees"
msgstr "Partecipanti individuali"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:100
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:121
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:137
msgid "Insert Image"
msgstr "Inserisci Immagine"

#: src/components/layouts/Event/index.tsx:54
#~ msgid "Integrations"
#~ msgstr "Integrazioni"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation resent!"
msgstr "Invito reinviato!"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation revoked!"
msgstr "Invito revocato!"

#: src/components/modals/InviteUserModal/index.tsx:54
#: src/components/modals/InviteUserModal/index.tsx:74
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invite User"
msgstr "Invita Utente"

#: src/components/common/OrdersTable/index.tsx:116
#: src/components/layouts/Checkout/index.tsx:54
msgid "Invoice downloaded successfully"
msgstr "Fattura scaricata con successo"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:252
msgid "Invoice Notes"
msgstr "Note Fattura"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:197
msgid "Invoice Numbering"
msgstr "Numerazione Fattura"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:177
msgid "Invoice Settings"
msgstr "Impostazioni Fattura"

#: src/components/modals/RefundOrderModal/index.tsx:115
msgid "Issue refund"
msgstr "Emetti rimborso"

#: src/components/common/LanguageSwitcher/index.tsx:22
msgid "Italian"
msgstr "Italiano"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Item"
msgstr "Articolo"

#: src/components/routes/auth/Register/index.tsx:84
msgid "John"
msgstr "John"

#: src/components/modals/CreateAttendeeModal/index.tsx:126
msgid "Johnson"
msgstr "Johnson"

#: src/components/modals/CreateEventModal/index.tsx:104
#~ msgid "KittenTech Conference {0}"
#~ msgstr "KittenTech Conference {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:101
#~ msgid "KittenTech Conference Center"
#~ msgstr "KittenTech Conference Center"

#: src/components/forms/ProductForm/index.tsx:77
msgid "Label"
msgstr "Etichetta"

#: src/components/common/AttendeeDetails/index.tsx:45
#: src/components/modals/CreateAttendeeModal/index.tsx:144
#: src/components/routes/profile/ManageProfile/index.tsx:172
msgid "Language"
msgstr "Lingua"

#: src/components/common/ReportTable/index.tsx:52
msgid "Last 12 months"
msgstr "Ultimi 12 mesi"

#: src/components/common/ReportTable/index.tsx:47
msgid "Last 14 days"
msgstr "Ultimi 14 giorni"

#: src/components/common/ReportTable/index.tsx:44
msgid "Last 24 hours"
msgstr "Ultime 24 ore"

#: src/components/common/ReportTable/index.tsx:48
msgid "Last 30 days"
msgstr "Ultimi 30 giorni"

#: src/components/common/ReportTable/index.tsx:45
msgid "Last 48 hours"
msgstr "Ultime 48 ore"

#: src/components/common/ReportTable/index.tsx:50
msgid "Last 6 months"
msgstr "Ultimi 6 mesi"

#: src/components/common/ReportTable/index.tsx:46
msgid "Last 7 days"
msgstr "Ultimi 7 giorni"

#: src/components/common/ReportTable/index.tsx:49
msgid "Last 90 days"
msgstr "Ultimi 90 giorni"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Last login"
msgstr "Ultimo accesso"

#: src/components/modals/CreateAttendeeModal/index.tsx:125
#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:150
msgid "Last name"
msgstr "Cognome"

#: src/components/common/QuestionsTable/index.tsx:212
#: src/components/common/QuestionsTable/index.tsx:213
#: src/components/modals/EditUserModal/index.tsx:72
#: src/components/modals/InviteUserModal/index.tsx:58
#: src/components/routes/auth/AcceptInvitation/index.tsx:94
#: src/components/routes/auth/Register/index.tsx:89
#: src/components/routes/product-widget/CollectInformation/index.tsx:289
#: src/components/routes/product-widget/CollectInformation/index.tsx:290
#: src/components/routes/product-widget/CollectInformation/index.tsx:400
#: src/components/routes/product-widget/CollectInformation/index.tsx:401
#: src/components/routes/profile/ManageProfile/index.tsx:137
msgid "Last Name"
msgstr "Cognome"

#: src/components/common/WebhookTable/index.tsx:239
msgid "Last Response"
msgstr "Ultima Risposta"

#: src/components/common/WebhookTable/index.tsx:240
msgid "Last Triggered"
msgstr "Ultimo Attivato"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
msgid "Last Used"
msgstr "Ultimo Utilizzo"

#: src/components/routes/event/EventDashboard/index.tsx:85
#~ msgid "Latest Orders"
#~ msgstr "Latest Orders"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:56
#~ msgid "Learn more about Stripe"
#~ msgstr "Learn more about Stripe"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:191
msgid "Leave blank to use the default word \"Invoice\""
msgstr "Lascia vuoto per utilizzare la parola predefinita \"Fattura\""

#: src/components/routes/welcome/index.tsx:108
#~ msgid "Let's get started by creating your first event"
#~ msgstr "Let's get started by creating your first event"

#: src/components/routes/welcome/index.tsx:20
msgid "Let's get started by creating your first organizer"
msgstr "Iniziamo creando il tuo primo organizzatore"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
#~ msgid "Link color"
#~ msgstr "Link color"

#: src/components/routes/event/EventDashboard/index.tsx:194
msgid "Link your Stripe account to receive funds from ticket sales."
msgstr "Collega il tuo account Stripe per ricevere fondi dalle vendite dei biglietti."

#: src/components/layouts/Event/index.tsx:190
msgid "Live"
msgstr "Online"

#: src/components/routes/event/Webhooks/index.tsx:21
msgid "Loading Webhooks"
msgstr "Caricamento Webhooks"

#: src/components/common/ReportTable/index.tsx:210
msgid "Loading..."
msgstr "Caricamento..."

#: src/components/routes/event/Settings/index.tsx:34
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:84
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:167
msgid "Location"
msgstr "Luogo"

#: src/components/routes/auth/Login/index.tsx:84
#: src/components/routes/auth/Register/index.tsx:71
msgid "Log in"
msgstr "Accedi"

#: src/components/routes/auth/Login/index.tsx:84
msgid "Logging in"
msgstr "Accesso in corso"

#: src/components/routes/auth/Register/index.tsx:70
#~ msgid "Login"
#~ msgstr "Login"

#: src/components/common/GlobalMenu/index.tsx:47
msgid "Logout"
msgstr "Esci"

#: src/components/common/WidgetEditor/index.tsx:331
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."

#: src/components/common/WidgetEditor/index.tsx:176
#~ msgid "Lorem ipsum..."
#~ msgstr "Lorem ipsum..."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:170
msgid "Make billing address mandatory during checkout"
msgstr "Rendi obbligatorio l'indirizzo di fatturazione durante il checkout"

#: src/components/routes/event/EventDashboard/index.tsx:172
#~ msgid "Make Event Live"
#~ msgstr "Make Event Live"

#: src/components/forms/QuestionForm/index.tsx:212
msgid "Make this question mandatory"
msgstr "Rendi obbligatoria questa domanda"

#: src/components/routes/event/EventDashboard/index.tsx:148
msgid "Make your event live"
msgstr "Rendi attivo il tuo evento"

#: src/components/common/CapacityAssignmentList/index.tsx:143
#: src/components/common/CheckInListList/index.tsx:145
#: src/components/common/EventCard/index.tsx:180
#: src/components/common/OrdersTable/index.tsx:139
#: src/components/common/OrdersTable/index.tsx:154
#: src/components/common/ProductsTable/SortableProduct/index.tsx:256
#: src/components/common/PromoCodeTable/index.tsx:167
#: src/components/layouts/Event/index.tsx:97
msgid "Manage"
msgstr "Gestisci"

#: src/components/common/AttendeeTable/index.tsx:161
msgid "Manage attendee"
msgstr "Gestisci partecipante"

#: src/components/routes/account/ManageAccount/index.tsx:32
#~ msgid "Manage billing information and view invoices"
#~ msgstr "Manage billing information and view invoices"

#: src/components/routes/account/ManageAccount/index.tsx:24
#~ msgid "Manage default settings for new events"
#~ msgstr "Manage default settings for new events"

#: src/components/common/EventCard/index.tsx:150
msgid "Manage event"
msgstr "Gestisci evento"

#: src/components/routes/account/ManageAccount/index.tsx:16
#~ msgid "Manage general account settings"
#~ msgstr "Manage general account settings"

#: src/components/common/OrdersTable/index.tsx:156
msgid "Manage order"
msgstr "Gestisci ordine"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:99
msgid "Manage payment and invoicing settings for this event."
msgstr "Gestisci le impostazioni di pagamento e fatturazione per questo evento."

#: src/components/modals/CreateAttendeeModal/index.tsx:106
#~ msgid "Manage products"
#~ msgstr "Manage products"

#: src/components/routes/profile/ManageProfile/index.tsx:101
msgid "Manage Profile"
msgstr "Gestisci Profilo"

#: src/components/routes/account/ManageAccount/index.tsx:20
#~ msgid "Manage taxes and fees which can be applied to tickets"
#~ msgstr "Manage taxes and fees which can be applied to tickets"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
msgid "Manage taxes and fees which can be applied to your products"
msgstr "Gestisci tasse e commissioni che possono essere applicate ai tuoi prodotti"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
#~ msgid "Manage taxes and fees which can be applied to your tickets"
#~ msgstr "Manage taxes and fees which can be applied to your tickets"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
msgid "Manage tickets"
msgstr "Gestisci biglietti"

#: src/components/routes/account/ManageAccount/index.tsx:28
#~ msgid "Manage users and their permissions"
#~ msgstr "Manage users and their permissions"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:54
msgid "Manage your account details and default settings"
msgstr "Gestisci i dettagli del tuo account e le impostazioni predefinite"

#: src/components/routes/account/ManageAccount/sections/BillingSettings/index.tsx:11
#~ msgid "Manage your billing and payment details"
#~ msgstr "Manage your billing and payment details"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:241
msgid "Manage your payment processing and view platform fees"
msgstr "Gestisci l'elaborazione dei pagamenti e visualizza le commissioni della piattaforma"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:91
#~ msgid "Manage your Stripe payment details"
#~ msgstr "Manage your Stripe payment details"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Manage your users and their permissions"
msgstr "Gestisci i tuoi utenti e le loro autorizzazioni"

#: src/components/forms/QuestionForm/index.tsx:211
msgid "Mandatory questions must be answered before the customer can checkout."
msgstr "Le domande obbligatorie devono essere risposte prima che il cliente possa procedere al checkout."

#: src/components/common/AttendeeTable/index.tsx:67
msgid "Manually add an Attendee"
msgstr "Aggiungi manualmente un Partecipante"

#: src/components/modals/CreateAttendeeModal/index.tsx:97
#: src/components/modals/CreateAttendeeModal/index.tsx:113
msgid "Manually Add Attendee"
msgstr "Aggiungi Manualmente Partecipante"

#: src/components/modals/CreateAttendeeModal/index.tsx:148
#~ msgid "Manually adding an attendee will adjust ticket quantity."
#~ msgstr "Manually adding an attendee will adjust ticket quantity."

#: src/components/common/OrdersTable/index.tsx:167
msgid "Mark as paid"
msgstr "Segna come pagato"

#: src/components/layouts/AuthLayout/index.tsx:83
msgid "Match Your Brand"
msgstr "Adatta al tuo brand"

#: src/components/forms/ProductForm/index.tsx:383
msgid "Maximum Per Order"
msgstr "Massimo Per Ordine"

#: src/components/common/AttendeeTable/index.tsx:166
msgid "Message attendee"
msgstr "Messaggio al partecipante"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:273
msgid "Message Attendees"
msgstr "Messaggio ai Partecipanti"

#: src/components/modals/SendMessageModal/index.tsx:165
#~ msgid "Message attendees with specific products"
#~ msgstr "Message attendees with specific products"

#: src/components/modals/SendMessageModal/index.tsx:206
msgid "Message attendees with specific tickets"
msgstr "Invia messaggio ai partecipanti con biglietti specifici"

#: src/components/layouts/AuthLayout/index.tsx:74
msgid "Message attendees, manage orders, and handle refunds all in one place"
msgstr "Invia messaggi ai partecipanti, gestisci ordini e gestisci rimborsi, tutto in un unico posto"

#: src/components/common/OrdersTable/index.tsx:158
msgid "Message buyer"
msgstr "Messaggio all'acquirente"

#: src/components/modals/SendMessageModal/index.tsx:250
msgid "Message Content"
msgstr "Contenuto del Messaggio"

#: src/components/modals/SendMessageModal/index.tsx:71
msgid "Message individual attendees"
msgstr "Invia messaggio ai singoli partecipanti"

#: src/components/modals/SendMessageModal/index.tsx:218
msgid "Message order owners with specific products"
msgstr "Invia messaggio ai proprietari degli ordini con prodotti specifici"

#: src/components/modals/SendMessageModal/index.tsx:118
msgid "Message Sent"
msgstr "Messaggio Inviato"

#: src/components/layouts/Event/index.tsx:105
#: src/components/routes/event/messages.tsx:31
msgid "Messages"
msgstr "Messaggi"

#: src/components/forms/ProductForm/index.tsx:381
msgid "Minimum Per Order"
msgstr "Minimo Per Ordine"

#: src/components/forms/ProductForm/index.tsx:285
msgid "Minimum Price"
msgstr "Prezzo Minimo"

#: src/components/routes/event/Settings/index.tsx:58
msgid "Miscellaneous"
msgstr "Varie"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:69
msgid "Miscellaneous Settings"
msgstr "Impostazioni Varie"

#: src/components/layouts/AuthLayout/index.tsx:63
msgid "Mobile Check-in"
msgstr "Check-in Mobile"

#: src/components/forms/QuestionForm/index.tsx:106
msgid "Multi line text box"
msgstr "Casella di testo multilinea"

#: src/components/forms/TicketForm/index.tsx:54
#~ msgid "Multiple price options, users can choose which to pay"
#~ msgstr "Multiple price options, users can choose which to pay"

#: src/components/forms/ProductForm/index.tsx:165
msgid "Multiple price options. Perfect for early bird products etc."
msgstr "Opzioni di prezzo multiple. Perfetto per prodotti early bird ecc."

#: src/components/forms/TicketForm/index.tsx:147
#~ msgid "Multiple price options. Perfect for early bird tickets etc."
#~ msgstr "Multiple price options. Perfect for early bird tickets etc."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:71
msgid "My amazing event description..."
msgstr "La mia fantastica descrizione dell'evento..."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:65
msgid "My amazing event title..."
msgstr "Il mio fantastico titolo dell'evento..."

#: src/components/common/GlobalMenu/index.tsx:27
msgid "My Profile"
msgstr "Il Mio Profilo"

#: src/components/common/QuestionAndAnswerList/index.tsx:178
msgid "N/A"
msgstr "N/D"

#: src/components/common/WidgetEditor/index.tsx:354
msgid "Nam placerat elementum..."
msgstr "Nam placerat elementum..."

#: src/components/common/AttendeeDetails/index.tsx:13
#: src/components/common/AttendeeTable/index.tsx:108
#: src/components/common/OrderDetails/index.tsx:23
#: src/components/forms/CapaciyAssigmentForm/index.tsx:37
#: src/components/forms/CheckInListForm/index.tsx:20
#: src/components/forms/ProductForm/index.tsx:253
#: src/components/forms/TaxAndFeeForm/index.tsx:61
#: src/components/modals/CreateEventModal/index.tsx:129
#: src/components/modals/DuplicateEventModal/index.tsx:78
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:63
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:72
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:107
#: src/components/routes/welcome/index.tsx:83
msgid "Name"
msgstr "Nome"

#: src/components/modals/CreateEventModal/index.tsx:38
msgid "Name should be less than 150 characters"
msgstr "Il nome deve essere inferiore a 150 caratteri"

#: src/components/common/QuestionAndAnswerList/index.tsx:181
#: src/components/common/QuestionAndAnswerList/index.tsx:289
msgid "Navigate to Attendee"
msgstr "Vai al Partecipante"

#: src/components/common/PromoCodeTable/index.tsx:158
#: src/components/common/WebhookTable/index.tsx:266
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Never"
msgstr "Mai"

#: src/components/routes/auth/AcceptInvitation/index.tsx:111
#: src/components/routes/auth/ResetPassword/index.tsx:59
#: src/components/routes/profile/ManageProfile/index.tsx:195
msgid "New Password"
msgstr "Nuova Password"

#: src/components/routes/event/settings.tsx:108
#: src/components/routes/event/settings.tsx:113
#~ msgid "New York"
#~ msgstr "New York"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "No"
msgstr "No"

#: src/components/common/QuestionAndAnswerList/index.tsx:104
#~ msgid "No {0} available."
#~ msgstr "No {0} available."

#: src/components/routes/event/Webhooks/index.tsx:22
msgid "No Active Webhooks"
msgstr "Nessun Webhook Attivo"

#: src/components/common/NoEventsBlankSlate/index.tsx:21
msgid "No archived events to show."
msgstr "Nessun evento archiviato da mostrare."

#: src/components/common/AttendeeList/index.tsx:23
msgid "No attendees found for this order."
msgstr "Nessun partecipante trovato per questo ordine."

#: src/components/modals/ManageOrderModal/index.tsx:132
msgid "No attendees have been added to this order."
msgstr "Nessun partecipante è stato aggiunto a questo ordine."

#: src/components/common/AttendeeTable/index.tsx:56
msgid "No Attendees to show"
msgstr "Nessun Partecipante da mostrare"

#: src/components/forms/CheckInListForm/index.tsx:48
msgid "No attendees will be able to check in before this date using this list"
msgstr "Nessun partecipante potrà effettuare il check-in prima di questa data utilizzando questa lista"

#: src/components/common/CapacityAssignmentList/index.tsx:42
msgid "No Capacity Assignments"
msgstr "Nessuna Assegnazione di Capacità"

#: src/components/common/CheckInListList/index.tsx:44
msgid "No Check-In Lists"
msgstr "Nessuna Lista di Check-In"

#: src/components/layouts/AuthLayout/index.tsx:34
msgid "No Credit Card Required"
msgstr "Nessuna Carta di Credito Richiesta"

#: src/components/common/ReportTable/index.tsx:218
msgid "No data available"
msgstr "Nessun dato disponibile"

#: src/components/common/ReportTable/index.tsx:214
msgid "No data to show. Please select a date range"
msgstr "Nessun dato da mostrare. Seleziona un intervallo di date"

#: src/components/forms/PromoCodeForm/index.tsx:47
msgid "No Discount"
msgstr "Nessuno Sconto"

#: src/components/common/NoEventsBlankSlate/index.tsx:20
msgid "No ended events to show."
msgstr "Nessun evento terminato da mostrare."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:88
#~ msgid "No events for this organizer"
#~ msgstr "No events for this organizer"

#: src/components/common/NoEventsBlankSlate/index.tsx:14
msgid "No events to show"
msgstr "Nessun evento da mostrare"

#: src/components/common/FilterModal/index.tsx:230
msgid "No filters available"
msgstr "Nessun filtro disponibile"

#: src/components/modals/WebhookLogsModal/index.tsx:177
msgid "No logs found"
msgstr "Nessun log trovato"

#: src/components/common/MessageList/index.tsx:69
msgid "No messages to show"
msgstr "Nessun messaggio da mostrare"

#: src/components/common/OrdersTable/index.tsx:63
msgid "No orders to show"
msgstr "Nessun ordine da mostrare"

#: src/components/routes/product-widget/Payment/index.tsx:76
msgid "No payment methods are currently available. Please contact the event organizer for assistance."
msgstr "Nessun metodo di pagamento è attualmente disponibile. Contatta l'organizzatore dell'evento per assistenza."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:39
msgid "No Payment Required"
msgstr "Nessun Pagamento Richiesto"

#: src/components/modals/ManageAttendeeModal/index.tsx:177
msgid "No product associated with this attendee."
msgstr "Nessun prodotto associato a questo partecipante."

#: src/components/common/ProductSelector/index.tsx:33
msgid "No products available for selection"
msgstr "Nessun prodotto disponibile per la selezione"

#: src/components/modals/CreateProductCategoryModal/index.tsx:22
msgid "No products available in this category."
msgstr "Nessun prodotto disponibile in questa categoria."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:39
msgid "No Products Yet"
msgstr "Ancora Nessun Prodotto"

#: src/components/common/PromoCodeTable/index.tsx:46
msgid "No Promo Codes to show"
msgstr "Nessun Codice Promozionale da mostrare"

#: src/components/modals/ManageAttendeeModal/index.tsx:193
msgid "No questions answered by this attendee."
msgstr "Nessuna domanda risposta da questo partecipante."

#: src/components/modals/ViewAttendeeModal/index.tsx:75
#~ msgid "No questions have been answered by this attendee."
#~ msgstr "No questions have been answered by this attendee."

#: src/components/modals/ManageOrderModal/index.tsx:119
msgid "No questions have been asked for this order."
msgstr "Nessuna domanda è stata posta per questo ordine."

#: src/components/common/WebhookTable/index.tsx:174
msgid "No response"
msgstr "Nessuna risposta"

#: src/components/common/WebhookTable/index.tsx:141
msgid "No responses yet"
msgstr "Ancora nessuna risposta"

#: src/components/common/NoResultsSplash/index.tsx:24
msgid "No results"
msgstr "Nessun risultato"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:20
msgid "No Search Results"
msgstr "Nessun Risultato di Ricerca"

#: src/components/common/NoResultsSplash/index.tsx:29
msgid "No search results."
msgstr "Nessun risultato di ricerca."

#: src/components/common/TaxAndFeeList/index.tsx:101
#~ msgid "No Taxes or Fees have been added yet."
#~ msgstr "No Taxes or Fees have been added yet."

#: src/components/common/TaxAndFeeList/index.tsx:101
msgid "No Taxes or Fees have been added."
msgstr "Nessuna Tassa o Commissione è stata aggiunta."

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:197
#~ msgid "No tickets available"
#~ msgstr "No tickets available"

#: src/components/common/TicketsTable/index.tsx:65
#~ msgid "No tickets to show"
#~ msgstr "No tickets to show"

#: src/components/modals/WebhookLogsModal/index.tsx:180
msgid "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."
msgstr "Nessun evento webhook è stato registrato per questo endpoint. Gli eventi appariranno qui una volta attivati."

#: src/components/common/WebhookTable/index.tsx:201
msgid "No Webhooks"
msgstr "Nessun Webhook"

#: src/components/common/PromoCodeTable/index.tsx:82
msgid "None"
msgstr "Nessuno"

#: src/components/common/ProductPriceAvailability/index.tsx:27
#: src/components/common/ProductPriceAvailability/index.tsx:48
msgid "Not available"
msgstr "Non disponibile"

#: src/components/common/AttendeeCheckInTable/index.tsx:125
#~ msgid "Not Checked In"
#~ msgstr "Not Checked In"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "Not On Sale"
msgstr "Non in Vendita"

#: src/components/modals/ManageAttendeeModal/index.tsx:130
#: src/components/modals/ManageOrderModal/index.tsx:164
msgid "Notes"
msgstr "Note"

#: src/components/common/ReportTable/index.tsx:240
msgid "Nothing to show yet"
msgstr "Ancora niente da mostrare"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:72
msgid "Notification Settings"
msgstr "Impostazioni Notifiche"

#: src/components/modals/RefundOrderModal/index.tsx:108
msgid "Notify buyer of refund"
msgstr "Notifica all'acquirente il rimborso"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:75
msgid "Notify organizer of new orders"
msgstr "Notifica all'organizzatore i nuovi ordini"

#: src/components/routes/welcome/index.tsx:72
msgid "Now let's create your first event"
msgstr "Ora creiamo il tuo primo evento"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:217
msgid "Number of days allowed for payment (leave blank to omit payment terms from invoices)"
msgstr "Numero di giorni consentiti per il pagamento (lasciare vuoto per omettere i termini di pagamento dalle fatture)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:199
msgid "Number Prefix"
msgstr "Prefisso Numero"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:145
msgid "Offline orders are not reflected in event statistics until the order is marked as paid."
msgstr "Gli ordini offline non sono riflessi nelle statistiche dell'evento finché l'ordine non viene contrassegnato come pagato."

#: src/components/routes/product-widget/Payment/index.tsx:66
msgid "Offline payment failed. Please try again or contact the event organizer."
msgstr "Pagamento offline fallito. Riprova o contatta l'organizzatore dell'evento."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:151
msgid "Offline Payment Instructions"
msgstr "Istruzioni per il Pagamento Offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:90
msgid "Offline Payments"
msgstr "Pagamenti Offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:139
msgid "Offline Payments Information"
msgstr "Informazioni sui Pagamenti Offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:134
msgid "Offline Payments Settings"
msgstr "Impostazioni Pagamenti Offline"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:71
msgid "On sale"
msgstr "In vendita"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "On Sale"
msgstr "In Vendita"

#: src/components/common/NoEventsBlankSlate/index.tsx:19
msgid "Once you create an event, you'll see it here."
msgstr "Una volta creato un evento, lo vedrai qui."

#: src/components/common/ReportTable/index.tsx:245
msgid "Once you start collecting data, you'll see it here."
msgstr "Una volta che inizi a raccogliere dati, li vedrai qui."

#: src/components/routes/event/GettingStarted/index.tsx:179
msgid "Once you're ready, set your event live and start selling products."
msgstr "Quando sei pronto, rendi attivo il tuo evento e inizia a vendere prodotti."

#: src/components/routes/event/GettingStarted/index.tsx:108
#~ msgid "Once you're ready, set your event live and start selling tickets."
#~ msgstr "Once you're ready, set your event live and start selling tickets."

#: src/components/common/EventStatusBadge/index.tsx:18
msgid "Ongoing"
msgstr "In Corso"

#: src/components/common/EventCard/index.tsx:118
msgid "Online event"
msgstr "Evento online"

#: src/components/common/OnlineEventDetails/index.tsx:9
msgid "Online Event Details"
msgstr "Dettagli Evento Online"

#: src/components/modals/SendMessageModal/index.tsx:279
msgid ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."
msgstr ""
"Solo le email importanti, direttamente correlate a questo evento, dovrebbero essere inviate utilizzando questo modulo.\n"
"Qualsiasi uso improprio, incluso l'invio di email promozionali, porterà al ban immediato dell'account."

#: src/components/modals/SendMessageModal/index.tsx:226
msgid "Only send to orders with these statuses"
msgstr "Invia solo agli ordini con questi stati"

#: src/components/common/CheckInListList/index.tsx:167
msgid "Open Check-In Page"
msgstr "Apri Pagina di Check-In"

#: src/components/layouts/Event/index.tsx:288
msgid "Open sidebar"
msgstr "Apri barra laterale"

#: src/components/forms/QuestionForm/index.tsx:45
msgid "Option {i}"
msgstr "Opzione {i}"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:253
msgid "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"
msgstr "Informazioni aggiuntive opzionali da visualizzare su tutte le fatture (ad es. termini di pagamento, penali per ritardo, politica di reso)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:200
msgid "Optional prefix for invoice numbers (e.g., INV-)"
msgstr "Prefisso opzionale per i numeri di fattura (ad es., FATT-)"

#: src/components/forms/QuestionForm/index.tsx:28
msgid "Options"
msgstr "Opzioni"

#: src/components/modals/CreateEventModal/index.tsx:118
msgid "or"
msgstr "o"

#: src/components/common/AttendeeTable/index.tsx:110
msgid "Order"
msgstr "Ordine"

#: src/components/common/OrdersTable/index.tsx:212
#~ msgid "Order #"
#~ msgstr "Order #"

#: src/components/forms/WebhookForm/index.tsx:76
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:63
msgid "Order Cancelled"
msgstr "Ordine Annullato"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:62
msgid "Order Completed"
msgstr "Ordine Completato"

#: src/components/forms/WebhookForm/index.tsx:52
msgid "Order Created"
msgstr "Ordine Creato"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:122
msgid "Order Date"
msgstr "Data Ordine"

#: src/components/modals/ManageAttendeeModal/index.tsx:166
#: src/components/modals/ManageOrderModal/index.tsx:86
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:266
msgid "Order Details"
msgstr "Dettagli Ordine"

#: src/components/modals/ViewOrderModal/index.tsx:30
#~ msgid "Order Details {0}"
#~ msgstr "Order Details {0}"

#: src/components/modals/CancelOrderModal/index.tsx:29
msgid "Order has been canceled and the order owner has been notified."
msgstr "L'ordine è stato annullato e il proprietario dell'ordine è stato avvisato."

#: src/components/common/OrdersTable/index.tsx:79
msgid "Order marked as paid"
msgstr "Ordine contrassegnato come pagato"

#: src/components/forms/WebhookForm/index.tsx:64
msgid "Order Marked as Paid"
msgstr "Ordine Contrassegnato come Pagato"

#: src/components/modals/ManageOrderModal/index.tsx:92
msgid "Order Notes"
msgstr "Note Ordine"

#: src/components/common/MessageList/index.tsx:23
msgid "Order owner"
msgstr "Proprietario ordine"

#: src/components/modals/SendMessageModal/index.tsx:191
msgid "Order owners with a specific product"
msgstr "Proprietari di ordini con un prodotto specifico"

#: src/components/common/MessageList/index.tsx:19
msgid "Order owners with products"
msgstr "Proprietari di ordini con prodotti"

#: src/components/common/QuestionsTable/index.tsx:313
#: src/components/common/QuestionsTable/index.tsx:355
msgid "Order questions"
msgstr "Domande ordine"

#: src/components/common/QuestionsTable/index.tsx:326
#~ msgid "Order questions are asked once per order. By default, people are asked for their first name, last name, and email address."
#~ msgstr "Order questions are asked once per order. By default, people are asked for their first name, last name, and email address."

#: src/components/common/QuestionsTable/index.tsx:226
#~ msgid "Order questions are asked once per order. By default, we ask for the first name, last name, and email address."
#~ msgstr "Order questions are asked once per order. By default, we ask for the first name, last name, and email address."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:112
msgid "Order Reference"
msgstr "Riferimento Ordine"

#: src/components/forms/WebhookForm/index.tsx:70
msgid "Order Refunded"
msgstr "Ordine Rimborsato"

#: src/components/routes/event/orders.tsx:45
msgid "Order Status"
msgstr "Stato Ordine"

#: src/components/modals/SendMessageModal/index.tsx:228
msgid "Order statuses"
msgstr "Stati ordine"

#: src/components/layouts/Checkout/CheckoutSidebar/index.tsx:28
#: src/components/modals/ManageOrderModal/index.tsx:105
msgid "Order Summary"
msgstr "Riepilogo Ordine"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:90
msgid "Order timeout"
msgstr "Timeout ordine"

#: src/components/forms/WebhookForm/index.tsx:58
msgid "Order Updated"
msgstr "Ordine Aggiornato"

#: src/components/layouts/Event/index.tsx:100
#: src/components/routes/event/orders.tsx:113
msgid "Orders"
msgstr "Ordini"

#: src/components/common/StatBoxes/index.tsx:51
#: src/components/routes/event/EventDashboard/index.tsx:105
#~ msgid "Orders Created"
#~ msgstr "Orders Created"

#: src/components/routes/event/orders.tsx:94
msgid "Orders Exported"
msgstr "Ordini Esportati"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:235
msgid "Organization Address"
msgstr "Indirizzo Organizzazione"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:226
msgid "Organization Details"
msgstr "Dettagli Organizzazione"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:228
msgid "Organization Name"
msgstr "Nome Organizzazione"

#: src/components/modals/EditUserModal/index.tsx:55
#: src/components/modals/InviteUserModal/index.tsx:47
#: src/components/routes/events/Dashboard/index.tsx:113
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:186
msgid "Organizer"
msgstr "Organizzatore"

#: src/components/routes/event/settings.tsx:231
#~ msgid "Organizer Details"
#~ msgstr "Organizer Details"

#: src/components/modals/CreateEventModal/index.tsx:41
msgid "Organizer is required"
msgstr "L'organizzatore è obbligatorio"

#: src/components/forms/OrganizerForm/index.tsx:27
msgid "Organizer Name"
msgstr "Nome Organizzatore"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
msgid "Organizers can only manage events and products. They cannot manage users, account settings or billing information."
msgstr "Gli organizzatori possono gestire solo eventi e prodotti. Non possono gestire utenti, impostazioni dell'account o informazioni di fatturazione."

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
#~ msgid "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."
#~ msgstr "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."

#: src/components/layouts/Event/index.tsx:87
msgid "Overview"
msgstr "Panoramica"

#: src/components/common/WidgetEditor/index.tsx:223
msgid "Padding"
msgstr "Spaziatura"

#: src/components/routes/event/HomepageDesigner/index.tsx:150
msgid "Page background color"
msgstr "Colore sfondo pagina"

#: src/components/common/ErrorDisplay/index.tsx:13
msgid "Page not found"
msgstr "Pagina non trovata"

#: src/components/common/StatBoxes/index.tsx:45
msgid "Page views"
msgstr "Visualizzazioni pagina"

#: src/components/forms/ProductForm/index.tsx:358
msgid "page."
msgstr "pagina."

#: src/components/modals/CreateAttendeeModal/index.tsx:180
msgid "paid"
msgstr "pagato"

#: src/components/forms/ProductForm/index.tsx:145
msgid "Paid Product"
msgstr "Prodotto a Pagamento"

#: src/components/forms/TicketForm/index.tsx:127
#~ msgid "Paid Ticket"
#~ msgstr "Paid Ticket"

#: src/components/routes/event/orders.tsx:30
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:54
msgid "Partially Refunded"
msgstr "Parzialmente Rimborsato"

#: src/components/routes/auth/Login/index.tsx:73
#: src/components/routes/auth/Register/index.tsx:106
#: src/components/routes/profile/ManageProfile/index.tsx:109
msgid "Password"
msgstr "Password"

#: src/components/routes/auth/AcceptInvitation/index.tsx:30
msgid "Password must be a minimum  of 8 characters"
msgstr "La password deve essere di almeno 8 caratteri"

#: src/components/routes/auth/Register/index.tsx:36
msgid "Password must be at least 8 characters"
msgstr "La password deve essere di almeno 8 caratteri"

#: src/components/routes/auth/ResetPassword/index.tsx:46
msgid "Password reset successfully. Please login with your new password."
msgstr "Password reimpostata con successo. Accedi con la tua nuova password."

#: src/components/routes/auth/AcceptInvitation/index.tsx:31
#: src/components/routes/auth/Register/index.tsx:37
msgid "Passwords are not the same"
msgstr "Le password non sono uguali"

#: src/components/common/WidgetEditor/index.tsx:269
msgid "Paste this where you want the widget to appear."
msgstr "Incolla questo dove vuoi che appaia il widget."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:104
msgid "Paste URL"
msgstr "Incolla URL"

#: src/components/modals/CreateAttendeeModal/index.tsx:119
msgid "Patrick"
msgstr "Patrick"

#: src/components/modals/CreateAttendeeModal/index.tsx:133
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/TicketsTable/index.tsx:138
#~ msgid "Pause Ticket"
#~ msgstr "Pause Ticket"

#: src/components/forms/WebhookForm/index.tsx:25
msgid "Paused"
msgstr "In pausa"

#: src/components/forms/StripeCheckoutForm/index.tsx:123
msgid "Payment"
msgstr "Pagamento"

#: src/components/routes/event/Settings/index.tsx:64
msgid "Payment & Invoicing"
msgstr "Pagamento e Fatturazione"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:98
msgid "Payment & Invoicing Settings"
msgstr "Impostazioni Pagamento e Fatturazione"

#: src/components/routes/account/ManageAccount/index.tsx:38
msgid "Payment & Plan"
msgstr "Pagamento e Piano"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:216
msgid "Payment Due Period"
msgstr "Periodo di Scadenza Pagamento"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:41
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:64
msgid "Payment Failed"
msgstr "Pagamento Fallito"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:228
#: src/components/routes/product-widget/Payment/PaymentMethods/Offline/index.tsx:14
msgid "Payment Instructions"
msgstr "Istruzioni di Pagamento"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:105
msgid "Payment Methods"
msgstr "Metodi di Pagamento"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:138
msgid "Payment Processing"
msgstr "Elaborazione Pagamenti"

#: src/components/common/OrderDetails/index.tsx:76
msgid "Payment provider"
msgstr "Fornitore di pagamento"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:42
msgid "Payment Received"
msgstr "Pagamento Ricevuto"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:240
msgid "Payment Settings"
msgstr "Impostazioni Pagamento"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:135
msgid "Payment Status"
msgstr "Stato Pagamento"

#: src/components/forms/StripeCheckoutForm/index.tsx:60
msgid "Payment succeeded!"
msgstr "Pagamento riuscito!"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:214
msgid "Payment Terms"
msgstr "Termini di Pagamento"

#: src/components/common/AttendeeTicket/index.tsx:70
#~ msgid "PDF"
#~ msgstr "PDF"

#: src/components/forms/PromoCodeForm/index.tsx:53
#~ msgid "percent"
#~ msgstr "percent"

#: src/components/forms/PromoCodeForm/index.tsx:51
#: src/components/forms/TaxAndFeeForm/index.tsx:27
msgid "Percentage"
msgstr "Percentuale"

#: src/components/forms/TaxAndFeeForm/index.tsx:71
msgid "Percentage Amount"
msgstr "Importo Percentuale"

#: src/components/routes/product-widget/Payment/index.tsx:122
msgid "Place Order"
msgstr "Effettua Ordine"

#: src/components/common/WidgetEditor/index.tsx:257
msgid "Place this in the <head> of your website."
msgstr "Inserisci questo nel tag <head> del tuo sito web."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:40
msgid "Platform Fees"
msgstr "Commissioni Piattaforma"

#: src/components/forms/QuestionForm/index.tsx:31
msgid "Please add at least one option"
msgstr "Aggiungi almeno un'opzione"

#: src/hooks/useFormErrorResponseHandler.tsx:18
msgid "Please check the provided information is correct"
msgstr "Verifica che le informazioni fornite siano corrette"

#: src/components/routes/auth/Login/index.tsx:41
msgid "Please check your email and password and try again"
msgstr "Controlla la tua email e password e riprova"

#: src/components/routes/auth/AcceptInvitation/index.tsx:32
#: src/components/routes/auth/Register/index.tsx:38
msgid "Please check your email is valid"
msgstr "Verifica che la tua email sia valida"

#: src/components/routes/profile/ManageProfile/index.tsx:152
msgid "Please check your email to confirm your email address"
msgstr "Controlla la tua email per confermare il tuo indirizzo email"

#: src/components/routes/auth/AcceptInvitation/index.tsx:86
msgid "Please complete the form below to accept your invitation"
msgstr "Completa il modulo sottostante per accettare il tuo invito"

#: src/components/routes/product-widget/SelectProducts/index.tsx:306
msgid "Please continue in the new tab"
msgstr "Continua nella nuova scheda"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:242
#~ msgid "Please continue your order in the new tab"
#~ msgstr "Please continue your order in the new tab"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:47
msgid "Please create a product"
msgstr "Crea un prodotto"

#: src/components/modals/CreateCheckInListModal/index.tsx:51
msgid "Please create a ticket"
msgstr "Crea un biglietto"

#: src/components/modals/SendMessageModal/index.tsx:207
#~ msgid ""
#~ "Please ensure you only send emails directly related to the order. Promotional emails\n"
#~ "should not be sent using this form."
#~ msgstr ""
#~ "Please ensure you only send emails directly related to the order. Promotional emails\n"
#~ "should not be sent using this form."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:45
msgid "Please enter a valid image URL that points to an image."
msgstr "Inserisci un URL valido che punti a un'immagine."

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:178
#~ msgid "Please enter a valid promo code"
#~ msgstr "Please enter a valid promo code"

#: src/components/modals/CreateWebhookModal/index.tsx:30
msgid "Please enter a valid URL"
msgstr "Inserisci un URL valido"

#: src/components/routes/auth/ResetPassword/index.tsx:55
msgid "Please enter your new password"
msgstr "Inserisci la tua nuova password"

#: src/components/modals/CancelOrderModal/index.tsx:52
msgid "Please Note"
msgstr "Nota Bene"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:34
msgid "Please provide an image."
msgstr "Per favore, fornisci un'immagine."

#: src/components/common/TicketsTable/index.tsx:84
#~ msgid "Please remove filters and set sorting to \"Homepage order\" to enable sorting"
#~ msgstr "Please remove filters and set sorting to \"Homepage order\" to enable sorting"

#: src/components/layouts/Checkout/index.tsx:159
msgid "Please return to the event page to start over."
msgstr "Torna alla pagina dell'evento per ricominciare."

#: src/components/modals/SendMessageModal/index.tsx:195
msgid "Please select"
msgstr "Seleziona"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:53
msgid "Please select an image."
msgstr "Per favore, seleziona un'immagine."

#: src/components/routes/product-widget/SelectProducts/index.tsx:237
msgid "Please select at least one product"
msgstr "Seleziona almeno un prodotto"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:227
#~ msgid "Please select at least one ticket"
#~ msgstr "Please select at least one ticket"

#: src/components/routes/event/attendees.tsx:49
#: src/components/routes/event/orders.tsx:100
msgid "Please try again."
msgstr "Riprova."

#: src/components/routes/profile/ManageProfile/index.tsx:142
msgid "Please verify your email address to access all features"
msgstr "Verifica il tuo indirizzo email per accedere a tutte le funzionalità"

#: src/components/routes/event/attendees.tsx:40
msgid "Please wait while we prepare your attendees for export..."
msgstr "Attendi mentre prepariamo i tuoi partecipanti per l'esportazione..."

#: src/components/common/OrdersTable/index.tsx:112
#: src/components/layouts/Checkout/index.tsx:50
msgid "Please wait while we prepare your invoice..."
msgstr "Attendi mentre prepariamo la tua fattura..."

#: src/components/routes/event/orders.tsx:91
msgid "Please wait while we prepare your orders for export..."
msgstr "Attendi mentre prepariamo i tuoi ordini per l'esportazione..."

#: src/components/common/LanguageSwitcher/index.tsx:26
msgid "Portuguese"
msgstr "Portoghese"

#: src/locales.ts:47
#~ msgid "Portuguese (Brazil)"
#~ msgstr "Portuguese (Brazil)"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:74
msgid "Post Checkout message"
msgstr "Messaggio post checkout"


#: src/components/common/EventDocumentHead/index.tsx:12
#~ msgid "Powered By"
#~ msgstr "Powered By"

#: src/components/layouts/EventHomepage/Footer/index.tsx:7
#~ msgid "Powered by <0>Hi.Events</0> 👋"
#~ msgstr "Powered by <0>Hi.Events</0> 👋"

#: src/components/common/EventDocumentHead/index.tsx:14
#~ msgid "Powered By Hi.Events"
#~ msgstr "Powered By Hi.Events"

#: src/components/forms/StripeCheckoutForm/index.tsx:137
#~ msgid "Powered by Stripe"
#~ msgstr "Powered by Stripe"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:67
msgid "Pre Checkout message"
msgstr "Messaggio pre checkout"

#: src/components/common/QuestionsTable/index.tsx:347
msgid "Preview"
msgstr "Anteprima"

#: src/components/layouts/Event/index.tsx:205
#: src/components/layouts/Event/index.tsx:209
msgid "Preview Event page"
msgstr "Anteprima pagina evento"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:236
#: src/components/forms/ProductForm/index.tsx:73
#: src/components/forms/ProductForm/index.tsx:285
msgid "Price"
msgstr "Prezzo"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:78
msgid "Price display mode"
msgstr "Modalità visualizzazione prezzo"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:88
msgid "Price not set"
msgstr "Prezzo non impostato"

#: src/components/forms/ProductForm/index.tsx:322
msgid "Price tiers"
msgstr "Livelli di prezzo"

#: src/components/forms/TicketForm/index.tsx:191
#~ msgid "Price Tiers"
#~ msgstr "Price Tiers"

#: src/components/forms/ProductForm/index.tsx:228
msgid "Price Type"
msgstr "Tipo di Prezzo"

#: src/components/common/WidgetEditor/index.tsx:182
msgid "Primary Color"
msgstr "Colore Primario"

#: src/components/routes/event/HomepageDesigner/index.tsx:156
msgid "Primary Colour"
msgstr "Colore Primario"

#: src/components/common/WidgetEditor/index.tsx:190
#: src/components/routes/event/HomepageDesigner/index.tsx:158
msgid "Primary Text Color"
msgstr "Colore Testo Primario"

#: src/components/common/AttendeeTicket/index.tsx:81
msgid "Print"
msgstr "Stampa"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:288
msgid "Print All Tickets"
msgstr "Stampa Tutti i Biglietti"

#: src/components/layouts/Checkout/index.tsx:114
msgid "Print Tickets"
msgstr "Stampa Biglietti"

#: src/components/common/AttendeeTable/index.tsx:190
#~ msgid "product"
#~ msgstr "product"

#: src/components/common/AttendeeDetails/index.tsx:37
#: src/components/modals/ManageAttendeeModal/index.tsx:119
msgid "Product"
msgstr "Prodotto"

#: src/components/forms/ProductForm/index.tsx:266
msgid "Product Category"
msgstr "Categoria Prodotto"

#: src/components/modals/EditProductCategoryModal/index.tsx:53
msgid "Product category updated successfully."
msgstr "Categoria prodotto aggiornata con successo."

#: src/components/forms/WebhookForm/index.tsx:34
msgid "Product Created"
msgstr "Prodotto Creato"

#: src/components/forms/WebhookForm/index.tsx:46
msgid "Product Deleted"
msgstr "Prodotto Eliminato"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:55
msgid "Product deleted successfully"
msgstr "Prodotto eliminato con successo"

#: src/components/common/AttendeeTable/index.tsx:50
#~ msgid "Product email has been resent to attendee"
#~ msgstr "Product email has been resent to attendee"

#: src/components/forms/ProductForm/index.tsx:236
msgid "Product Price Type"
msgstr "Tipo di Prezzo Prodotto"

#: src/components/common/QuestionsTable/index.tsx:328
msgid "Product questions"
msgstr "Domande prodotto"

#: src/components/routes/event/EventDashboard/index.tsx:217
#: src/components/routes/event/Reports/index.tsx:17
msgid "Product Sales"
msgstr "Vendite Prodotti"

#: src/components/routes/event/Reports/index.tsx:18
msgid "Product sales, revenue, and tax breakdown"
msgstr "Ripartizione vendite prodotti, ricavi e tasse"

#: src/components/common/ProductSelector/index.tsx:63
msgid "Product Tier"
msgstr "Livello Prodotto"

#: src/components/forms/ProductForm/index.tsx:214
#: src/components/forms/ProductForm/index.tsx:221
msgid "Product Type"
msgstr "Tipo di Prodotto"

#: src/components/forms/WebhookForm/index.tsx:40
msgid "Product Updated"
msgstr "Prodotto Aggiornato"

#: src/components/common/WidgetEditor/index.tsx:313
msgid "Product Widget Preview"
msgstr "Anteprima Widget Prodotto"

#: src/components/common/PromoCodeTable/index.tsx:148
msgid "Product(s)"
msgstr "Prodotto/i"

#: src/components/common/PromoCodeTable/index.tsx:72
msgid "Products"
msgstr "Prodotti"

#: src/components/common/EventCard/index.tsx:126
msgid "products sold"
msgstr "prodotti venduti"

#: src/components/common/StatBoxes/index.tsx:21
msgid "Products sold"
msgstr "Prodotti venduti"

#: src/components/routes/event/EventDashboard/index.tsx:238
msgid "Products Sold"
msgstr "Prodotti Venduti"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:178
msgid "Products sorted successfully"
msgstr "Prodotti ordinati con successo"

#: src/components/layouts/AuthLayout/index.tsx:43
msgid "Products, merchandise, and flexible pricing options"
msgstr "Prodotti, merchandising e opzioni di prezzo flessibili"

#: src/components/routes/profile/ManageProfile/index.tsx:106
msgid "Profile"
msgstr "Profilo"

#: src/components/routes/profile/ManageProfile/index.tsx:59
msgid "Profile updated successfully"
msgstr "Profilo aggiornato con successo"

#: src/components/routes/product-widget/SelectProducts/index.tsx:178
msgid "Promo {promo_code} code applied"
msgstr "Codice promo {promo_code} applicato"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:119
#~ msgid "Promo {promoCode} code applied"
#~ msgstr "Promo {promoCode} code applied"

#: src/components/common/OrderDetails/index.tsx:86
msgid "Promo code"
msgstr "Codice promo"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:20
msgid "Promo Code"
msgstr "Codice Promo"

#: src/components/forms/ProductForm/index.tsx:409
msgid "Promo Code page"
msgstr "Pagina Codice Promo"

#: src/components/routes/event/Reports/index.tsx:30
msgid "Promo code usage and discount breakdown"
msgstr "Ripartizione utilizzo codici promo e sconti"

#: src/components/layouts/Event/index.tsx:106
#: src/components/routes/event/promo-codes.tsx:31
msgid "Promo Codes"
msgstr "Codici Promo"

#: src/components/common/PromoCodeTable/index.tsx:51
msgid "Promo codes can be used to offer discounts, presale access, or provide special access to your event."
msgstr "I codici promo possono essere utilizzati per offrire sconti, accesso in prevendita o fornire accesso speciale al tuo evento."

#: src/components/routes/event/Reports/index.tsx:29
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:95
msgid "Promo Codes Report"
msgstr "Report Codici Promo"

#: src/components/forms/QuestionForm/index.tsx:189
msgid ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."
msgstr ""
"Fornisci contesto aggiuntivo o istruzioni per questa domanda. Usa questo campo per aggiungere termini\n"
"e condizioni, linee guida o qualsiasi informazione importante che i partecipanti devono conoscere prima di rispondere."

#: src/components/routes/event/EventDashboard/index.tsx:159
msgid "Publish Event"
msgstr "Pubblica Evento"

#: src/components/common/GlobalMenu/index.tsx:25
#~ msgid "Purchase License"
#~ msgstr "Purchase License"

#: src/components/modals/ShareModal/index.tsx:126
msgid "QR Code"
msgstr "Codice QR"

#: src/components/layouts/AuthLayout/index.tsx:64
msgid "QR code scanning with instant feedback and secure sharing for staff access"
msgstr "Scansione di codici QR con feedback istantaneo e condivisione sicura per l'accesso del personale"

#: src/components/forms/ProductForm/index.tsx:85
#: src/components/forms/ProductForm/index.tsx:302
msgid "Quantity Available"
msgstr "Quantità Disponibile"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
msgid "Quantity Sold"
msgstr "Quantità Venduta"

#: src/components/common/QuestionsTable/index.tsx:170
msgid "Question deleted"
msgstr "Domanda eliminata"

#: src/components/forms/QuestionForm/index.tsx:188
msgid "Question Description"
msgstr "Descrizione Domanda"

#: src/components/forms/QuestionForm/index.tsx:178
msgid "Question Title"
msgstr "Titolo Domanda"

#: src/components/common/QuestionsTable/index.tsx:275
#: src/components/layouts/Event/index.tsx:102
msgid "Questions"
msgstr "Domande"

#: src/components/modals/ManageAttendeeModal/index.tsx:184
#: src/components/modals/ManageOrderModal/index.tsx:111
msgid "Questions & Answers"
msgstr "Domande e Risposte"

#: src/components/common/QuestionsTable/index.tsx:145
msgid "Questions sorted successfully"
msgstr "Domande ordinate con successo"

#: src/components/forms/QuestionForm/index.tsx:118
msgid "Radio Option"
msgstr "Opzione Radio"

#: src/components/common/MessageList/index.tsx:59
msgid "Read less"
msgstr "Leggi meno"

#: src/components/modals/SendMessageModal/index.tsx:36
msgid "Recipient"
msgstr "Destinatario"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:110
#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:195
msgid "Redirecting to Stripe..."
msgstr "Reindirizzamento a Stripe..."

#: src/components/common/OrdersTable/index.tsx:204
#: src/components/common/OrdersTable/index.tsx:274
msgid "Reference"
msgstr "Riferimento"

#: src/components/modals/RefundOrderModal/index.tsx:104
msgid "Refund amount ({0})"
msgstr "Importo rimborso ({0})"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:52
msgid "Refund Failed"
msgstr "Rimborso Fallito"

#: src/components/common/OrdersTable/index.tsx:172
msgid "Refund order"
msgstr "Rimborsa ordine"

#: src/components/modals/RefundOrderModal/index.tsx:151
msgid "Refund Order"
msgstr "Rimborsa Ordine"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:51
msgid "Refund Pending"
msgstr "Rimborso in Attesa"

#: src/components/routes/event/orders.tsx:51
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:128
msgid "Refund Status"
msgstr "Stato Rimborso"

#: src/components/common/OrderSummary/index.tsx:80
#: src/components/common/StatBoxes/index.tsx:33
#: src/components/routes/event/orders.tsx:29
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:53
msgid "Refunded"
msgstr "Rimborsato"

#: src/components/routes/auth/Register/index.tsx:129
msgid "Register"
msgstr "Registrati"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:82
msgid "Remaining Uses"
msgstr "Utilizzi Rimanenti"

#: src/components/routes/product-widget/SelectProducts/index.tsx:504
msgid "remove"
msgstr "rimuovi"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:140
#: src/components/routes/product-widget/SelectProducts/index.tsx:505
msgid "Remove"
msgstr "Rimuovi"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
#~ msgid "Reply to email"
#~ msgstr "Reply to email"

#: src/components/layouts/Event/index.tsx:92
#: src/components/routes/event/Reports/index.tsx:39
msgid "Reports"
msgstr "Report"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:169
msgid "Require Billing Address"
msgstr "Richiedi Indirizzo di Fatturazione"

#: src/components/routes/event/GettingStarted/index.tsx:197
msgid "Resend confirmation email"
msgstr "Reinvia email di conferma"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resend email confirmation"
msgstr "Reinvia conferma email"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Resend invitation"
msgstr "Reinvia invito"

#: src/components/common/OrdersTable/index.tsx:179
msgid "Resend order email"
msgstr "Reinvia email ordine"

#: src/components/common/AttendeeTable/index.tsx:179
#~ msgid "Resend product email"
#~ msgstr "Resend product email"

#: src/components/common/AttendeeTable/index.tsx:171
msgid "Resend ticket email"
msgstr "Reinvia email biglietto"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resending..."
msgstr "Reinvio in corso..."

#: src/components/common/FilterModal/index.tsx:248
msgid "Reset"
msgstr "Reimposta"

#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Reset password"
msgstr "Reimposta password"

#: src/components/routes/auth/ResetPassword/index.tsx:54
msgid "Reset Password"
msgstr "Reimposta Password"

#: src/components/routes/auth/ForgotPassword/index.tsx:50
msgid "Reset your password"
msgstr "Reimposta la tua password"

#: src/components/common/EventCard/index.tsx:167
msgid "Restore event"
msgstr "Ripristina evento"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:50
#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:63
msgid "Return to event page"
msgstr "Torna alla pagina dell'evento"

#: src/components/layouts/Checkout/index.tsx:165
msgid "Return to Event Page"
msgstr "Torna alla Pagina dell'Evento"

#: src/components/routes/event/EventDashboard/index.tsx:249
msgid "Revenue"
msgstr "Ricavi"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Revoke invitation"
msgstr "Revoca invito"

#: src/components/modals/EditUserModal/index.tsx:93
#: src/components/modals/InviteUserModal/index.tsx:64
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Role"
msgstr "Ruolo"

#: src/components/forms/ProductForm/index.tsx:96
#: src/components/forms/ProductForm/index.tsx:391
msgid "Sale End Date"
msgstr "Data Fine Vendita"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:75
msgid "Sale ended"
msgstr "Vendita terminata"

#: src/components/forms/ProductForm/index.tsx:91
#: src/components/forms/ProductForm/index.tsx:389
msgid "Sale Start Date"
msgstr "Data Inizio Vendita"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:35
#: src/components/routes/ticket-widget/SelectTickets/Prices/Tiered/index.tsx:29
#~ msgid "Sale starts"
#~ msgstr "Sale starts"

#: src/components/common/ProductPriceAvailability/index.tsx:13
#: src/components/common/ProductPriceAvailability/index.tsx:35
msgid "Sales ended"
msgstr "Vendite terminate"

#: src/components/common/ProductPriceAvailability/index.tsx:19
#: src/components/common/ProductPriceAvailability/index.tsx:40
msgid "Sales start"
msgstr "Inizio vendite"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:135
msgid "San Francisco"
msgstr "San Francisco"

#: src/components/common/QuestionAndAnswerList/index.tsx:142
#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:80
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:114
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:96
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:166
#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:94
#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:265
#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:85
msgid "Save"
msgstr "Salva"

#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/event/HomepageDesigner/index.tsx:166
msgid "Save Changes"
msgstr "Salva Modifiche"

#: src/components/modals/EditOrganizerModal/index.tsx:70
msgid "Save Organizer"
msgstr "Salva Organizzatore"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:90
msgid "Save Settings"
msgstr "Salva Impostazioni"

#: src/components/layouts/CheckIn/index.tsx:398
#: src/components/layouts/CheckIn/index.tsx:400
msgid "Scan QR Code"
msgstr "Scansiona Codice QR"

#: src/components/modals/ShareModal/index.tsx:229
msgid "Scan this QR code to access the event page or share it with others"
msgstr "Scansiona questo codice QR per accedere alla pagina dell'evento o condividerla con altri"

#: src/components/layouts/CheckIn/index.tsx:288
#~ msgid "Seach by name, order #, attendee # or email..."
#~ msgstr "Seach by name, order #, attendee # or email..."

#: src/components/routes/event/attendees.tsx:64
msgid "Search by attendee name, email or order #..."
msgstr "Cerca per nome partecipante, email o numero ordine..."

#: src/components/routes/events/Dashboard/index.tsx:44
#~ msgid "Search by event name or description..."
#~ msgstr "Search by event name or description..."

#: src/components/routes/events/Dashboard/index.tsx:69
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:66
msgid "Search by event name..."
msgstr "Cerca per nome evento..."

#: src/components/routes/event/tickets.tsx:43
#~ msgid "Search by name or description..."
#~ msgstr "Search by name or description..."

#: src/components/routes/event/orders.tsx:126
msgid "Search by name, email, or order #..."
msgstr "Cerca per nome, email o numero ordine..."

#: src/components/layouts/CheckIn/index.tsx:394
msgid "Search by name, order #, attendee # or email..."
msgstr "Cerca per nome, numero ordine, numero partecipante o email..."

#: src/components/routes/event/promo-codes.tsx:34
msgid "Search by name..."
msgstr "Cerca per nome..."

#: src/components/routes/event/orders.tsx:46
#~ msgid "Search by order #, name or email..."
#~ msgstr "Search by order #, name or email..."

#: src/components/routes/event/products.tsx:43
#~ msgid "Search by product name..."
#~ msgstr "Search by product name..."

#: src/components/routes/event/messages.tsx:37
#~ msgid "Search by subject or body..."
#~ msgstr "Search by subject or body..."

#: src/components/routes/event/messages.tsx:34
msgid "Search by subject or content..."
msgstr "Cerca per oggetto o contenuto..."

#: src/components/routes/event/tickets.tsx:43
#~ msgid "Search by ticket name..."
#~ msgstr "Search by ticket name..."

#: src/components/routes/event/CapacityAssignments/index.tsx:37
msgid "Search capacity assignments..."
msgstr "Cerca assegnazioni di capacità..."

#: src/components/routes/event/CheckInLists/index.tsx:37
msgid "Search check-in lists..."
msgstr "Cerca liste di check-in..."

#: src/components/routes/event/products.tsx:52
msgid "Search products"
msgstr "Cerca prodotti"

#: src/components/common/SearchBar/index.tsx:36
msgid "Search..."
msgstr "Cerca..."

#: src/components/routes/event/HomepageDesigner/index.tsx:160
msgid "Secondary color"
msgstr "Colore secondario"

#: src/components/common/WidgetEditor/index.tsx:198
msgid "Secondary Color"
msgstr "Colore Secondario"

#: src/components/routes/event/HomepageDesigner/index.tsx:162
msgid "Secondary text color"
msgstr "Colore testo secondario"

#: src/components/common/WidgetEditor/index.tsx:206
msgid "Secondary Text Color"
msgstr "Colore Testo Secondario"

#: src/components/common/FilterModal/index.tsx:150
msgid "Select {0}"
msgstr "Seleziona {0}"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:214
msgid "Select Camera"
msgstr "Seleziona Fotocamera"

#: src/components/forms/ProductForm/index.tsx:269
msgid "Select category..."
msgstr "Seleziona categoria..."

#: src/components/forms/WebhookForm/index.tsx:124
msgid "Select event types"
msgstr "Seleziona tipi di evento"

#: src/components/modals/CreateEventModal/index.tsx:110
msgid "Select organizer"
msgstr "Seleziona organizzatore"

#: src/components/modals/ManageAttendeeModal/index.tsx:118
msgid "Select Product"
msgstr "Seleziona Prodotto"

#: src/components/common/ProductSelector/index.tsx:65
msgid "Select Product Tier"
msgstr "Seleziona Livello Prodotto"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:49
#: src/components/modals/SendMessageModal/index.tsx:219
msgid "Select products"
msgstr "Seleziona prodotti"

#: src/components/modals/EditUserModal/index.tsx:104
msgid "Select status"
msgstr "Seleziona stato"

#: src/components/modals/CreateAttendeeModal/index.tsx:150
msgid "Select Ticket"
msgstr "Seleziona Biglietto"

#: src/components/modals/CreateAttendeeModal/index.tsx:163
#: src/components/modals/EditAttendeeModal/index.tsx:117
#~ msgid "Select Ticket Tier"
#~ msgstr "Select Ticket Tier"

#: src/components/forms/CheckInListForm/index.tsx:26
#: src/components/modals/SendMessageModal/index.tsx:207
msgid "Select tickets"
msgstr "Seleziona biglietti"

#: src/components/layouts/EventHomepage/index.tsx:20
#~ msgid "Select Tickets"
#~ msgstr "Select Tickets"

#: src/components/common/ReportTable/index.tsx:261
msgid "Select time period"
msgstr "Seleziona periodo di tempo"

#: src/components/forms/WebhookForm/index.tsx:123
msgid "Select which events will trigger this webhook"
msgstr "Seleziona quali eventi attiveranno questo webhook"

#: src/components/forms/ProductForm/index.tsx:352
msgid "Select..."
msgstr "Seleziona..."

#: src/components/layouts/AuthLayout/index.tsx:68
msgid "Sell Anything"
msgstr "Vendi qualsiasi cosa"

#: src/components/layouts/AuthLayout/index.tsx:69
msgid "Sell merchandise alongside tickets with integrated tax and promo code support"
msgstr "Vendi merchandising insieme ai biglietti con supporto integrato per tasse e codici promozionali"

#: src/components/layouts/AuthLayout/index.tsx:42
msgid "Sell More Than Tickets"
msgstr "Vendi Più Che Biglietti"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send"
msgstr "Invia"

#: src/components/modals/SendMessageModal/index.tsx:259
msgid "Send a copy to <0>{0}</0>"
msgstr "Invia una copia a <0>{0}</0>"

#: src/components/modals/SendMessageModal/index.tsx:139
msgid "Send a message"
msgstr "Invia un messaggio"

#: src/components/modals/SendMessageModal/index.tsx:269
msgid "Send as a test. This will send the message to your email address instead of the recipients."
msgstr "Invia come test. Questo invierà il messaggio al tuo indirizzo email invece che ai destinatari."

#: src/components/modals/CreateAttendeeModal/index.tsx:184
#~ msgid "Send confirmation email"
#~ msgstr "Send confirmation email"

#: src/components/routes/event/messages.tsx:41
msgid "Send Message"
msgstr "Invia Messaggio"

#: src/components/modals/CreateAttendeeModal/index.tsx:191
#~ msgid "Send order confirmation and product email"
#~ msgstr "Send order confirmation and product email"

#: src/components/modals/CreateAttendeeModal/index.tsx:192
msgid "Send order confirmation and ticket email"
msgstr "Invia email di conferma ordine e biglietto"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send Test"
msgstr "Invia Test"

#: src/components/routes/event/Settings/index.tsx:46
msgid "SEO"
msgstr "SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:70
msgid "SEO Description"
msgstr "Descrizione SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:76
msgid "SEO Keywords"
msgstr "Parole Chiave SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:56
msgid "SEO Settings"
msgstr "Impostazioni SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:64
msgid "SEO Title"
msgstr "Titolo SEO"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "Service Fee"
msgstr "Commissione di Servizio"

#: src/components/forms/TicketForm/index.tsx:141
#~ msgid "Set a minimum price and let users donate more"
#~ msgstr "Set a minimum price and let users donate more"

#: src/components/forms/ProductForm/index.tsx:159
msgid "Set a minimum price and let users pay more if they choose"
msgstr "Imposta un prezzo minimo e permetti agli utenti di pagare di più se lo desiderano"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:207
msgid "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."
msgstr "Imposta il numero iniziale per la numerazione delle fatture. Questo non può essere modificato una volta che le fatture sono state generate."

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Set up your event"
msgstr "Configura il tuo evento"

#: src/components/routes/event/EventDashboard/index.tsx:190
#~ msgid "Set up your payment processing to receive funds from ticket sales."
#~ msgstr "Set up your payment processing to receive funds from ticket sales."

#: src/components/routes/event/GettingStarted/index.tsx:183
msgid "Set your event live"
msgstr "Metti online il tuo evento"

#: src/components/layouts/Event/index.tsx:98
#: src/components/routes/event/Settings/index.tsx:103
msgid "Settings"
msgstr "Impostazioni"

#: src/components/layouts/AuthLayout/index.tsx:26
msgid "Setup in Minutes"
msgstr "Configurazione in Minuti"

#: src/components/common/ShareIcon/index.tsx:28
#: src/components/modals/ShareModal/index.tsx:123
#: src/components/modals/ShareModal/index.tsx:141
msgid "Share"
msgstr "Condividi"

#: src/components/layouts/Event/index.tsx:251
#: src/components/modals/ShareModal/index.tsx:116
msgid "Share Event"
msgstr "Condividi Evento"

#: src/components/modals/ShareModal/index.tsx:169
msgid "Share to Facebook"
msgstr "Condividi su Facebook"

#: src/components/modals/ShareModal/index.tsx:163
msgid "Share to LinkedIn"
msgstr "Condividi su LinkedIn"

#: src/components/modals/ShareModal/index.tsx:193
msgid "Share to Pinterest"
msgstr "Condividi su Pinterest"

#: src/components/modals/ShareModal/index.tsx:187
msgid "Share to Reddit"
msgstr "Condividi su Reddit"

#: src/components/modals/ShareModal/index.tsx:148
msgid "Share to Social"
msgstr "Condividi sui Social"

#: src/components/modals/ShareModal/index.tsx:181
msgid "Share to Telegram"
msgstr "Condividi su Telegram"

#: src/components/modals/ShareModal/index.tsx:175
msgid "Share to WhatsApp"
msgstr "Condividi su WhatsApp"

#: src/components/modals/ShareModal/index.tsx:157
msgid "Share to X"
msgstr "Condividi su X"

#: src/components/modals/ShareModal/index.tsx:199
msgid "Share via Email"
msgstr "Condividi via Email"

#: src/components/forms/TaxAndFeeForm/index.tsx:85
#~ msgid "Should this {type} be applied to all new tickets?"
#~ msgstr "Should this {type} be applied to all new tickets?"

#: src/components/forms/ProductForm/index.tsx:344
msgid "Show"
msgstr "Mostra"

#: src/components/forms/ProductForm/index.tsx:403
msgid "Show available product quantity"
msgstr "Mostra quantità prodotto disponibile"

#: src/components/forms/TicketForm/index.tsx:348
#~ msgid "Show available ticket quantity"
#~ msgstr "Show available ticket quantity"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Show hidden questions"
msgstr "Mostra domande nascoste"

#: src/components/routes/product-widget/SelectProducts/index.tsx:459
msgid "Show more"
msgstr "Mostra altro"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:60
msgid "Show tax and fees separately"
msgstr "Mostra tasse e commissioni separatamente"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:76
#~ msgid "Shown to the customer after they checkout, on the order summary page"
#~ msgstr "Shown to the customer after they checkout, on the order summary page"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
msgid "Shown to the customer after they checkout, on the order summary page."
msgstr "Mostrato al cliente dopo il checkout, nella pagina di riepilogo dell'ordine."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:69
msgid "Shown to the customer before they checkout"
msgstr "Mostrato al cliente prima del checkout"

#: src/components/forms/QuestionForm/index.tsx:132
msgid "Shows common address fields, including country"
msgstr "Mostra i campi comuni dell'indirizzo, incluso il paese"

#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:151
msgid "Simpson"
msgstr "Simpson"

#: src/components/forms/QuestionForm/index.tsx:100
msgid "Single line text box"
msgstr "Casella di testo a riga singola"

#: src/components/routes/welcome/index.tsx:137
msgid "Skip this step"
msgstr "Salta questo passaggio"

#: src/components/layouts/AuthLayout/index.tsx:78
msgid "Smart Check-in"
msgstr "Check-in Intelligente"

#: src/components/layouts/AuthLayout/index.tsx:53
#~ msgid "Smart Dashboard"
#~ msgstr "Dashboard Intelligente"

#: src/components/routes/auth/Register/index.tsx:90
msgid "Smith"
msgstr "Smith"

#: src/components/common/ProductPriceAvailability/index.tsx:9
#: src/components/common/ProductPriceAvailability/index.tsx:32
msgid "Sold out"
msgstr "Esaurito"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:67
msgid "Sold Out"
msgstr "Esaurito"

#: src/components/common/ErrorDisplay/index.tsx:14
#: src/components/routes/auth/AcceptInvitation/index.tsx:47
msgid "Something went wrong"
msgstr "Qualcosa è andato storto"

#: src/components/common/TaxAndFeeList/index.tsx:39
msgid "Something went wrong while deleting the Tax or Fee"
msgstr "Qualcosa è andato storto durante l'eliminazione della Tassa o Commissione"

#: src/components/routes/auth/ForgotPassword/index.tsx:31
msgid "Something went wrong, please try again, or contact support if the problem persists"
msgstr "Qualcosa è andato storto, riprova o contatta l'assistenza se il problema persiste"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Something went wrong! Please try again"
msgstr "Qualcosa è andato storto! Riprova"

#: src/components/forms/StripeCheckoutForm/index.tsx:69
msgid "Something went wrong."
msgstr "Qualcosa è andato storto."

#: src/components/routes/profile/ManageProfile/index.tsx:80
#: src/components/routes/profile/ManageProfile/index.tsx:94
msgid "Something went wrong. Please try again."
msgstr "Qualcosa è andato storto. Riprova."

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:61
msgid "Sorry, something has gone wrong. Please restart the checkout process."
msgstr "Spiacenti, qualcosa è andato storto. Riavvia il processo di checkout."

#: src/components/routes/product-widget/CollectInformation/index.tsx:259
msgid "Sorry, something went wrong loading this page."
msgstr "Spiacenti, qualcosa è andato storto durante il caricamento di questa pagina."

#: src/components/routes/product-widget/CollectInformation/index.tsx:247
msgid "Sorry, this order no longer exists."
msgstr "Spiacenti, questo ordine non esiste più."

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:208
#~ msgid "Sorry, this promo code is invalid'"
#~ msgstr "Sorry, this promo code is invalid'"

#: src/components/routes/product-widget/SelectProducts/index.tsx:246
msgid "Sorry, this promo code is not recognized"
msgstr "Spiacenti, questo codice promo non è riconosciuto"

#: src/components/layouts/Checkout/index.tsx:26
#~ msgid "Sorry, your order has expired. Please start a new order."
#~ msgstr "Sorry, your order has expired. Please start a new order."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Sorting is disabled while filters and sorting are applied"
#~ msgstr "Sorting is disabled while filters and sorting are applied"

#: src/components/common/LanguageSwitcher/index.tsx:18
msgid "Spanish"
msgstr "Spagnolo"

#: src/components/forms/ProductForm/index.tsx:147
msgid "Standard product with a fixed price"
msgstr "Prodotto standard con prezzo fisso"

#: src/components/forms/TicketForm/index.tsx:129
#~ msgid "Standard ticket with a fixed price"
#~ msgstr "Standard ticket with a fixed price"

#: src/components/modals/CreateEventModal/index.tsx:144
#: src/components/modals/DuplicateEventModal/index.tsx:93
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:87
#: src/components/routes/welcome/index.tsx:90
msgid "Start Date"
msgstr "Data Inizio"

#: src/components/routes/event/settings.tsx:112
#~ msgid "State"
#~ msgstr "State"

#: src/components/common/CheckoutQuestion/index.tsx:165
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:139
#: src/components/routes/product-widget/CollectInformation/index.tsx:339
#: src/components/routes/product-widget/CollectInformation/index.tsx:340
msgid "State or Region"
msgstr "Stato o Regione"

#: src/components/common/AttendeeTable/index.tsx:112
#: src/components/common/OrderDetails/index.tsx:51
#: src/components/common/OrdersTable/index.tsx:209
#: src/components/common/ProductsTable/SortableProduct/index.tsx:222
#: src/components/common/WebhookTable/index.tsx:238
#: src/components/forms/CapaciyAssigmentForm/index.tsx:56
#: src/components/forms/WebhookForm/index.tsx:133
#: src/components/modals/EditUserModal/index.tsx:103
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:88
msgid "Status"
msgstr "Stato"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:85
msgid "Stripe"
msgstr "Stripe"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:48
msgid "Stripe payments are not enabled for this event."
msgstr "I pagamenti Stripe non sono abilitati per questo evento."

#: src/components/modals/SendMessageModal/index.tsx:245
msgid "Subject"
msgstr "Oggetto"

#: src/components/common/OrderSummary/index.tsx:41
msgid "Subtotal"
msgstr "Subtotale"

#: src/components/common/OrdersTable/index.tsx:115
#: src/components/layouts/Checkout/index.tsx:53
msgid "Success"
msgstr "Successo"

#: src/components/modals/EditUserModal/index.tsx:40
#: src/components/modals/InviteUserModal/index.tsx:32
msgid "Success! {0} will receive an email shortly."
msgstr "Successo! {0} riceverà un'email a breve."

#: src/components/common/AttendeeTable/index.tsx:90
msgid "Successfully {0} attendee"
msgstr "Partecipante {0} con successo"

#: src/components/common/AttendeeTable/index.tsx:51
#~ msgid "Successfully ${0} attendee"
#~ msgstr "Successfully ${0} attendee"

#: src/components/common/AttendeeCheckInTable/index.tsx:47
#: src/components/common/AttendeeCheckInTable/index.tsx:71
#~ msgid "Successfully checked <0>{0} {1}</0> {2}"
#~ msgstr "Successfully checked <0>{0} {1}</0> {2}"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:23
msgid "Successfully confirmed email address"
msgstr "Indirizzo email confermato con successo"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:31
msgid "Successfully confirmed email change"
msgstr "Modifica email confermata con successo"

#: src/components/modals/CreateAttendeeModal/index.tsx:82
msgid "Successfully created attendee"
msgstr "Partecipante creato con successo"

#: src/components/modals/CreateProductModal/index.tsx:54
msgid "Successfully Created Product"
msgstr "Prodotto Creato con Successo"

#: src/components/modals/CreatePromoCodeModal/index.tsx:39
msgid "Successfully Created Promo Code"
msgstr "Codice Promo Creato con Successo"

#: src/components/modals/CreateQuestionModal/index.tsx:45
#: src/components/modals/EditQuestionModal/index.tsx:66
msgid "Successfully Created Question"
msgstr "Domanda Creata con Successo"

#: src/components/modals/CreateTicketModal/index.tsx:50
#~ msgid "Successfully Created Ticket"
#~ msgstr "Successfully Created Ticket"

#: src/components/common/TicketsTable/index.tsx:48
#~ msgid "Successfully deleted ticket"
#~ msgstr "Successfully deleted ticket"

#: src/components/modals/DuplicateProductModal/index.tsx:95
msgid "Successfully Duplicated Product"
msgstr "Prodotto Duplicato con Successo"

#: src/components/modals/ManageAttendeeModal/index.tsx:96
msgid "Successfully updated attendee"
msgstr "Partecipante aggiornato con successo"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:47
msgid "Successfully updated Capacity Assignment"
msgstr "Assegnazione Capacità aggiornata con successo"

#: src/components/modals/EditCheckInListModal/index.tsx:49
msgid "Successfully updated Check-In List"
msgstr "Lista Check-In aggiornata con successo"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:43
msgid "Successfully Updated Email Settings"
msgstr "Impostazioni Email Aggiornate con Successo"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:54
msgid "Successfully Updated Event"
msgstr "Evento Aggiornato con Successo"

#: src/components/routes/event/HomepageDesigner/index.tsx:84
msgid "Successfully Updated Homepage Design"
msgstr "Design Homepage Aggiornato con Successo"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:50
msgid "Successfully Updated Homepage Settings"
msgstr "Impostazioni Homepage Aggiornate con Successo"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:73
msgid "Successfully Updated Location"
msgstr "Posizione Aggiornata con Successo"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:43
msgid "Successfully Updated Misc Settings"
msgstr "Impostazioni Varie Aggiornate con Successo"

#: src/components/modals/ManageOrderModal/index.tsx:74
msgid "Successfully updated order"
msgstr "Ordine aggiornato con successo"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:74
msgid "Successfully Updated Payment & Invoicing Settings"
msgstr "Impostazioni di Pagamento e Fatturazione Aggiornate con Successo"

#: src/components/modals/EditProductModal/index.tsx:89
msgid "Successfully updated product"
msgstr "Prodotto aggiornato con successo"

#: src/components/modals/EditPromoCodeModal/index.tsx:52
msgid "Successfully Updated Promo Code"
msgstr "Codice Promo Aggiornato con Successo"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:45
msgid "Successfully Updated Seo Settings"
msgstr "Impostazioni SEO Aggiornate con Successo"

#: src/components/modals/EditTicketModal/index.tsx:85
#~ msgid "Successfully updated ticket"
#~ msgstr "Successfully updated ticket"

#: src/components/modals/EditWebhookModal/index.tsx:44
msgid "Successfully updated Webhook"
msgstr "Webhook aggiornato con successo"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:128
msgid "Suite 100"
msgstr "Suite 100"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
msgid "Support Email"
msgstr "Email di Supporto"

#: src/components/layouts/AuthLayout/index.tsx:59
msgid "Support for tiered, donation-based, and product sales with customizable pricing and capacity"
msgstr "Supporto per vendite a livelli, basate su donazioni e di prodotti con prezzi e capacità personalizzabili"

#: src/components/forms/ProductForm/index.tsx:254
msgid "T-shirt"
msgstr "T-shirt"

#: src/components/forms/TaxAndFeeForm/index.tsx:12
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Tax"
msgstr "Tassa"

#: src/components/routes/account/ManageAccount/index.tsx:27
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:16
msgid "Tax & Fees"
msgstr "Tasse e Commissioni"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:243
msgid "Tax Details"
msgstr "Dettagli Fiscali"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:244
msgid "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"
msgstr "Informazioni fiscali da mostrare in fondo a tutte le fatture (es. numero di partita IVA, registrazione fiscale)"

#: src/components/common/TaxAndFeeList/index.tsx:36
msgid "Tax or Fee deleted successfully"
msgstr "Tassa o Commissione eliminata con successo"

#: src/components/forms/ProductForm/index.tsx:363
msgid "Taxes"
msgstr "Tasse"

#: src/components/routes/account/ManageAccount/index.tsx:19
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:14
#~ msgid "Taxes & Fees"
#~ msgstr "Taxes & Fees"

#: src/components/forms/ProductForm/index.tsx:351
#: src/components/forms/ProductForm/index.tsx:358
msgid "Taxes and Fees"
msgstr "Tasse e Commissioni"

#: src/components/common/TaxAndFeeList/index.tsx:102
#~ msgid "Taxes and Fees can be associated with tickets and will be added to the ticket price."
#~ msgstr "Taxes and Fees can be associated with tickets and will be added to the ticket price."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:81
#~ msgid "Text Colour"
#~ msgstr "Text Colour"

#: src/components/routes/product-widget/SelectProducts/index.tsx:138
#: src/components/routes/product-widget/SelectProducts/index.tsx:186
msgid "That promo code is invalid"
msgstr "Quel codice promo non è valido"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:78
#~ msgid "The background color for the event homepage"
#~ msgstr "The background color for the event homepage"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:98
#~ msgid "The background color for the ticket widget on the event homepage"
#~ msgstr "The background color for the ticket widget on the event homepage"

#: src/components/routes/event/settings.tsx:50
#: src/components/routes/event/settings.tsx:134
#: src/components/routes/event/settings.tsx:210
#~ msgid "The basic details of your event"
#~ msgstr "The basic details of your event"

#: src/components/layouts/CheckIn/index.tsx:316
msgid "The check-in list you are looking for does not exist."
msgstr "La lista di check-in che stai cercando non esiste."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:88
#~ msgid "The color of the buttons on the event homepage"
#~ msgstr "The color of the buttons on the event homepage"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:92
#~ msgid "The color of the links on the event homepage"
#~ msgstr "The color of the links on the event homepage"

#: src/components/forms/OrganizerForm/index.tsx:47
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:75
msgid "The default currency for your events."
msgstr "La valuta predefinita per i tuoi eventi."

#: src/components/forms/OrganizerForm/index.tsx:56
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:84
msgid "The default timezone for your events."
msgstr "Il fuso orario predefinito per i tuoi eventi."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:16
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:43
msgid "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."
msgstr "L'evento che stai cercando non è disponibile al momento. Potrebbe essere stato rimosso, scaduto o l'URL potrebbe essere errato."

#: src/components/modals/CreateAttendeeModal/index.tsx:146
msgid "The language the attendee will receive emails in."
msgstr "La lingua in cui il partecipante riceverà le email."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:22
msgid "The link you clicked is invalid."
msgstr "Il link che hai cliccato non è valido."

#: src/components/routes/event/settings.tsx:85
#~ msgid "The location of your event"
#~ msgstr "The location of your event"

#: src/components/routes/product-widget/SelectProducts/index.tsx:444
msgid "The maximum number of products for {0}is {1}"
msgstr "Il numero massimo di prodotti per {0}è {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:351
#~ msgid "The maximum numbers number of tickets for {0}is {1}"
#~ msgstr "The maximum numbers number of tickets for {0}is {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:318
#~ msgid "The maximum numbers number of tickets for Generals is {0}"
#~ msgstr "The maximum numbers number of tickets for Generals is {0}"

#: src/components/routes/event/settings.tsx:232
#~ msgid "The organizer details of your event"
#~ msgstr "The organizer details of your event"

#: src/components/common/ErrorDisplay/index.tsx:17
msgid "The page you are looking for does not exist"
msgstr "La pagina che stai cercando non esiste"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:56
msgid "The price displayed to the customer will include taxes and fees."
msgstr "Il prezzo mostrato al cliente includerà tasse e commissioni."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:62
msgid "The price displayed to the customer will not include taxes and fees. They will be shown separately"
msgstr "Il prezzo mostrato al cliente non includerà tasse e commissioni. Saranno mostrate separatamente"

#: src/components/common/WidgetEditor/index.tsx:169
msgid "The styling settings you choose apply only to copied HTML and won't be stored."
msgstr "Le impostazioni di stile che scegli si applicano solo all'HTML copiato e non saranno memorizzate."

#: src/components/forms/TicketForm/index.tsx:223
#~ msgid "The tax and fee to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "The tax and fee to apply to this ticket. You can create new taxes and fees on the"

#: src/components/forms/ProductForm/index.tsx:355
msgid "The taxes and fees to apply to this product. You can create new taxes and fees on the"
msgstr "Le tasse e commissioni da applicare a questo prodotto. Puoi creare nuove tasse e commissioni nella"

#: src/components/forms/TicketForm/index.tsx:300
#~ msgid "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
#~ msgid "The text color for the event homepage"
#~ msgstr "The text color for the event homepage"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:102
#~ msgid "The text color for the ticket widget on the event homepage"
#~ msgstr "The text color for the ticket widget on the event homepage"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
#~ msgid "The text to display in the 'Continue' button. Defaults to 'Continue'"
#~ msgstr "The text to display in the 'Continue' button. Defaults to 'Continue'"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:63
msgid "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"
msgstr "Il titolo dell'evento che verrà visualizzato nei risultati dei motori di ricerca e quando si condivide sui social media. Per impostazione predefinita, verrà utilizzato il titolo dell'evento"

#: src/components/modals/EditUserModal/index.tsx:79
#~ msgid "The user must login to change their email."
#~ msgstr "The user must login to change their email."

#: src/components/routes/product-widget/SelectProducts/index.tsx:272
msgid "There are no products available for this event"
msgstr "Non ci sono prodotti disponibili per questo evento"

#: src/components/routes/product-widget/SelectProducts/index.tsx:375
msgid "There are no products available in this category"
msgstr "Non ci sono prodotti disponibili in questa categoria"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:260
#~ msgid "There are no tickets available for this event"
#~ msgstr "There are no tickets available for this event"

#: src/components/modals/RefundOrderModal/index.tsx:137
msgid "There is a refund pending. Please wait for it to complete before requesting another refund."
msgstr "C'è un rimborso in attesa. Attendi che sia completato prima di richiedere un altro rimborso."

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:226
#~ msgid "There was an error loading this content. Please refresh the page and try again."
#~ msgstr "There was an error loading this content. Please refresh the page and try again."

#: src/components/common/OrdersTable/index.tsx:80
msgid "There was an error marking the order as paid"
msgstr "Si è verificato un errore nel contrassegnare l'ordine come pagato"

#: src/hooks/useFormErrorResponseHandler.tsx:28
msgid "There was an error processing your request. Please try again."
msgstr "Si è verificato un errore durante l'elaborazione della tua richiesta. Riprova."

#: src/components/common/OrdersTable/index.tsx:95
msgid "There was an error sending your message"
msgstr "Si è verificato un errore durante l'invio del tuo messaggio"

#: src/components/forms/QuestionForm/index.tsx:108
#~ msgid "These allow multiple selections"
#~ msgstr "These allow multiple selections"

#: src/components/common/WidgetEditor/index.tsx:86
#~ msgid "These colors are not saved in our system."
#~ msgstr "These colors are not saved in our system."

#: src/components/common/WidgetEditor/index.tsx:85
#~ msgid "These colors are not saved in our system. They are only used to generate the widget."
#~ msgstr "These colors are not saved in our system. They are only used to generate the widget."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:105
msgid "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."
msgstr "Questi dettagli saranno mostrati solo se l'ordine è completato con successo. Gli ordini in attesa di pagamento non mostreranno questo messaggio."

#: src/components/layouts/CheckIn/index.tsx:201
msgid "This attendee has an unpaid order."
msgstr "Questo partecipante ha un ordine non pagato."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:61
msgid "This category doesn't have any products yet."
msgstr "Questa categoria non ha ancora prodotti."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:112
msgid "This category is hidden from public view"
msgstr "Questa categoria è nascosta alla vista pubblica"

#: src/components/common/CheckInListList/index.tsx:75
msgid "This check-in list has expired"
msgstr "Questa lista di check-in è scaduta"

#: src/components/layouts/CheckIn/index.tsx:331
msgid "This check-in list has expired and is no longer available for check-ins."
msgstr "Questa lista di check-in è scaduta e non è più disponibile per i check-in."

#: src/components/common/CheckInListList/index.tsx:82
msgid "This check-in list is active"
msgstr "Questa lista di check-in è attiva"

#: src/components/common/CheckInListList/index.tsx:79
msgid "This check-in list is not active yet"
msgstr "Questa lista di check-in non è ancora attiva"

#: src/components/layouts/CheckIn/index.tsx:348
msgid "This check-in list is not yet active and is not available for check-ins."
msgstr "Questa lista di check-in non è ancora attiva e non è disponibile per i check-in."

#: src/components/forms/CheckInListForm/index.tsx:37
msgid "This description will be shown to the check-in staff"
msgstr "Questa descrizione sarà mostrata al personale di check-in"

#: src/components/modals/SendMessageModal/index.tsx:285
msgid "This email is not promotional and is directly related to the event."
msgstr "Questa email non è promozionale ed è direttamente correlata all'evento."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:8
#~ msgid "This event is not available at the moment. Please check back later."
#~ msgstr "This event is not available at the moment. Please check back later."

#: src/components/layouts/EventHomepage/index.tsx:49
#~ msgid "This event is not available."
#~ msgstr "This event is not available."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
msgid "This information will be shown on the payment page, order summary page, and order confirmation email."
msgstr "Queste informazioni saranno mostrate nella pagina di pagamento, nella pagina di riepilogo dell'ordine e nell'email di conferma dell'ordine."

#: src/components/forms/ProductForm/index.tsx:138
msgid "This is a general product, like a t-shirt or a mug. No ticket will be issued"
msgstr "Questo è un prodotto generico, come una maglietta o una tazza. Non verrà emesso alcun biglietto"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:91
msgid "This is an online event"
msgstr "Questo è un evento online"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
#~ msgid "This is the email address that will be used as the reply-to address for all emails sent from this event"
#~ msgstr "This is the email address that will be used as the reply-to address for all emails sent from this event"

#: src/components/forms/CheckInListForm/index.tsx:57
msgid "This list will no longer be available for check-ins after this date"
msgstr "Questa lista non sarà più disponibile per i check-in dopo questa data"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:65
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:72
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
#~ msgid "This message is how below the"
#~ msgstr "This message is how below the"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:68
msgid "This message will be included in the footer of all emails sent from this event"
msgstr "Questo messaggio sarà incluso nel piè di pagina di tutte le email inviate da questo evento"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
msgid "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"
msgstr "Questo messaggio sarà mostrato solo se l'ordine è completato con successo. Gli ordini in attesa di pagamento non mostreranno questo messaggio"

#: src/components/forms/StripeCheckoutForm/index.tsx:93
msgid "This order has already been paid."
msgstr "Questo ordine è già stato pagato."

#: src/components/routes/ticket-widget/Payment/index.tsx:40
#~ msgid "This order has already been paid. <0>View order details</0>"
#~ msgstr "This order has already been paid. <0>View order details</0>"

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:199
#~ msgid "This order has already been processed."
#~ msgstr "This order has already been processed."

#: src/components/modals/RefundOrderModal/index.tsx:141
msgid "This order has already been refunded."
msgstr "Questo ordine è già stato rimborsato."

#: src/components/routes/product-widget/CollectInformation/index.tsx:237
msgid "This order has been cancelled"
msgstr "Questo ordine è stato annullato"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:209
msgid "This order has been cancelled."
msgstr "Questo ordine è stato annullato."

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:182
#~ msgid "This order has been completed."
#~ msgstr "This order has been completed."

#: src/components/routes/product-widget/CollectInformation/index.tsx:210
msgid "This order has expired. Please start again."
msgstr "Questo ordine è scaduto. Per favore ricomincia."

#: src/components/routes/product-widget/CollectInformation/index.tsx:221
msgid "This order is awaiting payment"
msgstr "Questo ordine è in attesa di pagamento"

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:188
#~ msgid "This order is awaiting payment."
#~ msgstr "This order is awaiting payment."

#: src/components/routes/product-widget/CollectInformation/index.tsx:229
msgid "This order is complete"
msgstr "Questo ordine è completo"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:211
msgid "This order is complete."
msgstr "Questo ordine è completo."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:204
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:207
msgid "This order is processing."
msgstr "Questo ordine è in elaborazione."

#: src/components/routes/ticket-widget/OrderSummaryAndTickets/index.tsx:32
#: src/components/routes/ticket-widget/OrderSummaryAndTickets/index.tsx:48
#~ msgid "This order is processing. TODO - a nice image and poll the API"
#~ msgstr "This order is processing. TODO - a nice image and poll the API"

#: src/components/forms/StripeCheckoutForm/index.tsx:103
msgid "This order page is no longer available."
msgstr "Questa pagina dell'ordine non è più disponibile."

#: src/components/forms/ProductForm/index.tsx:415
msgid "This overrides all visibility settings and will hide the product from all customers."
msgstr "Questo sovrascrive tutte le impostazioni di visibilità e nasconderà il prodotto a tutti i clienti."

#: src/components/forms/TicketForm/index.tsx:360
#~ msgid "This overrides all visibility settings and will hide the ticket from all customers."
#~ msgstr "This overrides all visibility settings and will hide the ticket from all customers."

#: src/components/routes/ticket-widget/Payment/index.tsx:53
#~ msgid "This page has expired. <0>View order details</0>"
#~ msgstr "This page has expired. <0>View order details</0>"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:59
msgid "This product cannot be deleted because it is associated with an order. You can hide it instead."
msgstr "Questo prodotto non può essere eliminato perché è associato a un ordine. Puoi invece nasconderlo."

#: src/components/forms/ProductForm/index.tsx:132
msgid "This product is a ticket. Buyers will be issued a ticket upon purchase"
msgstr "Questo prodotto è un biglietto. Agli acquirenti verrà emesso un biglietto al momento dell'acquisto"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:215
msgid "This product is hidden from public view"
msgstr "Questo prodotto è nascosto alla vista pubblica"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:216
msgid "This product is hidden unless targeted by a Promo Code"
msgstr "Questo prodotto è nascosto a meno che non sia oggetto di un Codice Promo"

#: src/components/common/QuestionsTable/index.tsx:90
msgid "This question is only visible to the event organizer"
msgstr "Questa domanda è visibile solo all'organizzatore dell'evento"

#: src/components/routes/auth/ResetPassword/index.tsx:29
msgid "This reset password link is invalid or expired."
msgstr "Questo link per reimpostare la password non è valido o è scaduto."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:56
#~ msgid ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."
#~ msgstr ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:131
#~ msgid "This ticket is hidden from public view"
#~ msgstr "This ticket is hidden from public view"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:132
#~ msgid "This ticket is hidden unless targeted by a Promo Code"
#~ msgstr "This ticket is hidden unless targeted by a Promo Code"

#: src/components/modals/EditUserModal/index.tsx:65
msgid "This user is not active, as they have not accepted their invitation."
msgstr "Questo utente non è attivo, poiché non ha accettato il suo invito."

#: src/components/common/AttendeeTable/index.tsx:182
msgid "ticket"
msgstr "biglietto"

#: src/components/common/AttendeeTable/index.tsx:111
#: src/components/forms/ProductForm/index.tsx:130
#: src/components/modals/CreateAttendeeModal/index.tsx:151
msgid "Ticket"
msgstr "Biglietto"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:52
#~ msgid "Ticket deleted successfully"
#~ msgstr "Ticket deleted successfully"

#: src/components/common/AttendeeTable/index.tsx:49
msgid "Ticket email has been resent to attendee"
msgstr "Email del biglietto reinviata al partecipante"

#: src/components/common/MessageList/index.tsx:22
msgid "Ticket holders"
msgstr "Possessori di biglietti"

#: src/components/routes/event/products.tsx:86
msgid "Ticket or Product"
msgstr "Biglietto o prodotto"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:63
#~ msgid "Ticket page message"
#~ msgstr "Ticket page message"

#: src/components/routes/event/EventDashboard/index.tsx:53
#~ msgid "Ticket Sales"
#~ msgstr "Ticket Sales"

#: src/components/modals/CreateAttendeeModal/index.tsx:161
#: src/components/modals/EditAttendeeModal/index.tsx:115
#~ msgid "Ticket Tier"
#~ msgstr "Ticket Tier"

#: src/components/forms/TicketForm/index.tsx:189
#: src/components/forms/TicketForm/index.tsx:196
#~ msgid "Ticket Type"
#~ msgstr "Ticket Type"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:97
#~ msgid "Ticket widget background color"
#~ msgstr "Ticket widget background color"

#: src/components/common/WidgetEditor/index.tsx:311
#~ msgid "Ticket Widget Preview"
#~ msgstr "Ticket Widget Preview"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:101
#~ msgid "Ticket widget text color"
#~ msgstr "Ticket widget text color"

#: src/components/common/PromoCodeTable/index.tsx:147
#~ msgid "Ticket(s)"
#~ msgstr "Ticket(s)"

#: src/components/common/PromoCodeTable/index.tsx:71
#: src/components/layouts/Event/index.tsx:40
#: src/components/layouts/EventHomepage/index.tsx:80
#: src/components/routes/event/tickets.tsx:39
#~ msgid "Tickets"
#~ msgstr "Tickets"

#: src/components/layouts/Event/index.tsx:101
#: src/components/routes/event/products.tsx:46
msgid "Tickets & Products"
msgstr "Biglietti e prodotti"

#: src/components/routes/product-widget/PrintOrder/index.tsx:43
msgid "Tickets for"
msgstr "Biglietti per"

#: src/components/common/EventCard/index.tsx:126
#~ msgid "tickets sold"
#~ msgstr "tickets sold"

#: src/components/common/StatBoxes/index.tsx:21
#~ msgid "Tickets sold"
#~ msgstr "Tickets sold"

#: src/components/routes/event/EventDashboard/index.tsx:73
#~ msgid "Tickets Sold"
#~ msgstr "Tickets Sold"

#: src/components/common/TicketsTable/index.tsx:44
#~ msgid "Tickets sorted successfully"
#~ msgstr "Tickets sorted successfully"

#: src/components/forms/PromoCodeForm/index.tsx:59
#~ msgid "Tickets to which the promo code applies (Applies to all by default)"
#~ msgstr "Tickets to which the promo code applies (Applies to all by default)"

#: src/components/forms/ProductForm/index.tsx:67
msgid "Tier {0}"
msgstr "Livello {0}"

#: src/components/forms/ProductForm/index.tsx:163
msgid "Tiered Product"
msgstr "Prodotto a Livelli"

#: src/components/forms/ProductForm/index.tsx:240
#~ msgid ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."

#: src/components/forms/ProductForm/index.tsx:243
msgid "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."
msgstr "I prodotti a livelli ti permettono di offrire più opzioni di prezzo per lo stesso prodotto. È perfetto per prodotti in prevendita o per offrire diverse opzioni di prezzo per diversi gruppi di persone."

#: src/components/forms/TicketForm/index.tsx:145
#~ msgid "Tiered Ticket"
#~ msgstr "Tiered Ticket"

#: src/components/forms/TicketForm/index.tsx:52
#~ msgid "Tiered Ticket - Coming Soon"
#~ msgstr "Tiered Ticket - Coming Soon"

#: src/components/forms/TicketForm/index.tsx:203
#~ msgid ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."

#: src/components/forms/TicketForm/index.tsx:156
#~ msgid "Tiered tickets allow you to offer multiple price options for the same ticket. This is perfect for early bird tickets or offering different price options for different groups of people."
#~ msgstr "Tiered tickets allow you to offer multiple price options for the same ticket. This is perfect for early bird tickets or offering different price options for different groups of people."

#: src/components/layouts/Checkout/index.tsx:90
msgid "Time left:"
msgstr "Tempo rimasto:"

#: src/components/common/PromoCodeTable/index.tsx:71
msgid "Times used"
msgstr "Volte utilizzato"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:25
msgid "Times Used"
msgstr "Volte Utilizzato"

#: src/components/forms/OrganizerForm/index.tsx:54
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:82
#: src/components/routes/auth/AcceptInvitation/index.tsx:105
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:109
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:181
#: src/components/routes/profile/ManageProfile/index.tsx:161
msgid "Timezone"
msgstr "Fuso orario"

#: src/components/forms/PromoCodeForm/index.tsx:36
msgid "TIP"
msgstr "SUGGERIMENTO"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:206
msgid "Title"
msgstr "Titolo"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:182
msgid "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."
msgstr "Per ricevere pagamenti con carta di credito, devi collegare il tuo account Stripe. Stripe è il nostro partner per l'elaborazione dei pagamenti che garantisce transazioni sicure e pagamenti puntuali."

#: src/components/layouts/Event/index.tsx:108
msgid "Tools"
msgstr "Strumenti"

#: src/components/common/OrderSummary/index.tsx:92
msgid "Total"
msgstr "Totale"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:52
msgid "Total Before Discounts"
msgstr "Totale Prima degli Sconti"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:58
msgid "Total Discount Amount"
msgstr "Importo Totale Sconto"

#: src/components/routes/event/EventDashboard/index.tsx:273
msgid "Total Fees"
msgstr "Commissioni Totali"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:46
msgid "Total Gross Sales"
msgstr "Vendite Lorde Totali"

#: src/components/common/OrderDetails/index.tsx:59
#: src/components/modals/RefundOrderModal/index.tsx:74
msgid "Total order amount"
msgstr "Importo totale ordine"

#: src/components/common/OrderDetails/index.tsx:67
#: src/components/modals/RefundOrderModal/index.tsx:82
msgid "Total refunded"
msgstr "Totale rimborsato"

#: src/components/routes/event/EventDashboard/index.tsx:276
msgid "Total Refunded"
msgstr "Totale Rimborsato"

#: src/components/modals/RefundOrderModal/index.tsx:90
msgid "Total remaining"
msgstr "Totale rimanente"

#: src/components/routes/event/EventDashboard/index.tsx:275
msgid "Total Tax"
msgstr "Tasse Totali"

#: src/components/layouts/AuthLayout/index.tsx:54
msgid "Track revenue, page views, and sales with detailed analytics and exportable reports"
msgstr "Monitora ricavi, visualizzazioni di pagina e vendite con analisi dettagliate e report esportabili"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
msgid "Transaction Fee:"
msgstr "Commissione di Transazione:"

#: src/components/forms/TaxAndFeeForm/index.tsx:44
msgid "Type"
msgstr "Tipo"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "Unable to {0} attendee"
#~ msgstr "Unable to {0} attendee"

#: src/components/layouts/CheckIn/index.tsx:93
msgid "Unable to check in attendee"
msgstr "Impossibile registrare il partecipante"

#: src/components/layouts/CheckIn/index.tsx:114
msgid "Unable to check out attendee"
msgstr "Impossibile registrare l'uscita del partecipante"

#: src/components/modals/CreateProductModal/index.tsx:63
msgid "Unable to create product. Please check the your details"
msgstr "Impossibile creare il prodotto. Controlla i tuoi dati"

#: src/components/routes/product-widget/SelectProducts/index.tsx:123
msgid "Unable to create product. Please check your details"
msgstr "Impossibile creare il prodotto. Controlla i tuoi dati"

#: src/components/modals/CreateQuestionModal/index.tsx:60
msgid "Unable to create question. Please check the your details"
msgstr "Impossibile creare la domanda. Controlla i tuoi dati"

#: src/components/modals/CreateTicketModal/index.tsx:65
#: src/components/routes/ticket-widget/SelectTickets/index.tsx:117
#~ msgid "Unable to create ticket. Please check the your details"
#~ msgstr "Unable to create ticket. Please check the your details"

#: src/components/modals/DuplicateProductModal/index.tsx:103
msgid "Unable to duplicate product. Please check the your details"
msgstr "Impossibile duplicare il prodotto. Controlla i tuoi dati"

#: src/components/layouts/CheckIn/index.tsx:145
msgid "Unable to fetch attendee"
msgstr "Impossibile recuperare il partecipante"

#: src/components/modals/EditQuestionModal/index.tsx:84
msgid "Unable to update question. Please check the your details"
msgstr "Impossibile aggiornare la domanda. Controlla i tuoi dati"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:30
msgid "Unique Customers"
msgstr "Clienti Unici"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:153
msgid "United States"
msgstr "Stati Uniti"

#: src/components/common/QuestionAndAnswerList/index.tsx:284
msgid "Unknown Attendee"
msgstr "Partecipante Sconosciuto"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:43
#: src/components/forms/ProductForm/index.tsx:83
#: src/components/forms/ProductForm/index.tsx:299
#: src/components/forms/PromoCodeForm/index.tsx:82
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:78
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:84
msgid "Unlimited"
msgstr "Illimitato"

#: src/components/routes/product-widget/SelectProducts/index.tsx:407
msgid "Unlimited available"
msgstr "Disponibilità illimitata"

#: src/components/common/TicketsTable/index.tsx:106
#~ msgid "Unlimited ticket quantity available"
#~ msgstr "Unlimited ticket quantity available"

#: src/components/common/PromoCodeTable/index.tsx:126
msgid "Unlimited usages allowed"
msgstr "Utilizzi illimitati consentiti"

#: src/components/layouts/CheckIn/index.tsx:200
msgid "Unpaid Order"
msgstr "Ordine Non Pagato"

#: src/components/routes/event/EventDashboard/index.tsx:170
msgid "Unpublish Event"
msgstr "Annulla Pubblicazione Evento"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:20
#: src/components/common/EventStatusBadge/index.tsx:16
msgid "Upcoming"
msgstr "In Arrivo"

#: src/components/routes/events/Dashboard/index.tsx:55
msgid "Upcoming Events"
msgstr "Eventi in Arrivo"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Update {0}"
msgstr "Aggiorna {0}"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:66
msgid "Update event name, description and dates"
msgstr "Aggiorna nome, descrizione e date dell'evento"

#: src/components/routes/profile/ManageProfile/index.tsx:177
msgid "Update profile"
msgstr "Aggiorna profilo"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:75
#~ msgid "Upload an image to be displayed on the event page"
#~ msgstr "Upload an image to be displayed on the event page"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Upload Cover"
msgstr "Carica Copertina"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:105
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:152
msgid "Upload Image"
msgstr "Carica immagine"

#: src/components/common/WebhookTable/index.tsx:236
#: src/components/modals/ShareModal/index.tsx:129
msgid "URL"
msgstr "URL"

#: src/components/common/PromoCodeTable/index.tsx:175
msgid "URL copied to clipboard"
msgstr "URL copiato negli appunti"

#: src/components/modals/CreateWebhookModal/index.tsx:25
msgid "URL is required"
msgstr "URL è obbligatorio"

#: src/components/common/WidgetEditor/index.tsx:298
msgid "Usage Example"
msgstr "Esempio di Utilizzo"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:76
msgid "Usage Limit"
msgstr "Limite di Utilizzo"

#: src/components/routes/event/HomepageDesigner/index.tsx:139
msgid "Use a blurred version of the cover image as the background"
msgstr "Usa una versione sfocata dell'immagine di copertina come sfondo"

#: src/components/routes/event/HomepageDesigner/index.tsx:137
msgid "Use cover image"
msgstr "Usa immagine di copertina"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User"
msgstr "Utente"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User Management"
msgstr "Gestione Utenti"

#: src/components/routes/account/ManageAccount/index.tsx:32
msgid "Users"
msgstr "Utenti"

#: src/components/modals/EditUserModal/index.tsx:81
msgid "Users can change their email in <0>Profile Settings</0>"
msgstr "Gli utenti possono modificare la loro email in <0>Impostazioni Profilo</0>"

#: src/components/forms/OrganizerForm/index.tsx:55
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:83
#: src/components/routes/auth/AcceptInvitation/index.tsx:106
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:110
#: src/components/routes/profile/ManageProfile/index.tsx:162
msgid "UTC"
msgstr "UTC"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "VAT"
msgstr "IVA"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:116
msgid "Venue Name"
msgstr "Nome della Sede"

#: src/components/common/LanguageSwitcher/index.tsx:34
msgid "Vietnamese"
msgstr "Vietnamita"

#: src/components/modals/ManageAttendeeModal/index.tsx:220
#: src/components/modals/ManageOrderModal/index.tsx:200
msgid "View"
msgstr "Visualizza"

#: src/components/routes/event/Reports/index.tsx:38
msgid "View and download reports for your event. Please note, only completed orders are included in these reports."
msgstr "Visualizza e scarica i report per il tuo evento. Nota: solo gli ordini completati sono inclusi in questi report."

#: src/components/common/AttendeeList/index.tsx:84
msgid "View Answers"
msgstr "Visualizza Risposte"

#: src/components/common/AttendeeTable/index.tsx:164
#~ msgid "View attendee"
#~ msgstr "View attendee"

#: src/components/common/AttendeeList/index.tsx:103
msgid "View Attendee Details"
msgstr "Visualizza Dettagli Partecipante"

#: src/components/layouts/Checkout/index.tsx:55
#~ msgid "View event homepage"
#~ msgstr "View event homepage"

#: src/components/common/EventCard/index.tsx:145
msgid "View event page"
msgstr "Visualizza pagina evento"

#: src/components/common/MessageList/index.tsx:59
msgid "View full message"
msgstr "Visualizza messaggio completo"

#: src/components/common/WebhookTable/index.tsx:113
msgid "View logs"
msgstr "Visualizza log"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View map"
msgstr "Visualizza mappa"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View on Google Maps"
msgstr "Visualizza su Google Maps"

#: src/components/common/OrdersTable/index.tsx:111
#~ msgid "View order"
#~ msgstr "View order"

#: src/components/forms/StripeCheckoutForm/index.tsx:94
#: src/components/forms/StripeCheckoutForm/index.tsx:104
#: src/components/routes/product-widget/CollectInformation/index.tsx:231
msgid "View order details"
msgstr "Visualizza dettagli ordine"

#: src/components/forms/CheckInListForm/index.tsx:21
msgid "VIP check-in list"
msgstr "Lista check-in VIP"

#: src/components/forms/ProductForm/index.tsx:254
#~ msgid "VIP Product"
#~ msgstr "VIP Product"

#: src/components/forms/ProductForm/index.tsx:254
msgid "VIP Ticket"
msgstr "Biglietto VIP"

#: src/components/forms/ProductForm/index.tsx:394
msgid "Visibility"
msgstr "Visibilità"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:77
msgid "We could not process your payment. Please try again or contact support."
msgstr "Non è stato possibile elaborare il tuo pagamento. Riprova o contatta l'assistenza."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:53
msgid "We couldn't delete the category. Please try again."
msgstr "Non è stato possibile eliminare la categoria. Riprova."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:24
msgid "We couldn't find any tickets matching {0}"
msgstr "Non abbiamo trovato biglietti corrispondenti a {0}"

#: src/components/common/ErrorLoadingMessage/index.tsx:13
msgid "We couldn't load the data. Please try again."
msgstr "Non è stato possibile caricare i dati. Riprova."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:94
msgid "We couldn't reorder the categories. Please try again."
msgstr "Non è stato possibile riordinare le categorie. Riprova."

#: src/components/routes/event/HomepageDesigner/index.tsx:118
msgid "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"
msgstr "Consigliamo dimensioni di 2160px per 1080px e una dimensione massima del file di 5MB"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:42
#~ msgid "We use Stripe to process payments. Connect your Stripe account to start receiving payments."
#~ msgstr "We use Stripe to process payments. Connect your Stripe account to start receiving payments."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:84
msgid "We were unable to confirm your payment. Please try again or contact support."
msgstr "Non siamo riusciti a confermare il tuo pagamento. Riprova o contatta l'assistenza."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:79
msgid "We're processing your order. Please wait..."
msgstr "Stiamo elaborando il tuo ordine. Attendere prego..."

#: src/components/modals/CreateWebhookModal/index.tsx:46
msgid "Webhook created successfully"
msgstr "Webhook creato con successo"

#: src/components/common/WebhookTable/index.tsx:53
msgid "Webhook deleted successfully"
msgstr "Webhook eliminato con successo"

#: src/components/modals/WebhookLogsModal/index.tsx:151
msgid "Webhook Logs"
msgstr "Log Webhook"

#: src/components/forms/WebhookForm/index.tsx:117
msgid "Webhook URL"
msgstr "URL Webhook"

#: src/components/forms/WebhookForm/index.tsx:27
msgid "Webhook will not send notifications"
msgstr "Il webhook non invierà notifiche"

#: src/components/forms/WebhookForm/index.tsx:21
msgid "Webhook will send notifications"
msgstr "Il webhook invierà notifiche"

#: src/components/layouts/Event/index.tsx:111
#: src/components/routes/event/Webhooks/index.tsx:30
msgid "Webhooks"
msgstr "Webhooks"

#: src/components/routes/auth/AcceptInvitation/index.tsx:76
msgid "Welcome aboard! Please login to continue."
msgstr "Benvenuto a bordo! Accedi per continuare."

#: src/components/routes/auth/Login/index.tsx:55
msgid "Welcome back 👋"
msgstr "Bentornato 👋"

#: src/components/routes/event/EventDashboard/index.tsx:95
msgid "Welcome back{0} 👋"
msgstr "Bentornato{0} 👋"

#: src/components/routes/auth/Register/index.tsx:67
msgid "Welcome to Hi.Events 👋"
msgstr "Benvenuto su Hi.Events 👋"

#: src/components/routes/welcome/index.tsx:125
msgid "Welcome to Hi.Events, {0} 👋"
msgstr "Benvenuto su Hi.Events, {0} 👋"

#: src/components/forms/ProductForm/index.tsx:242
msgid "What are Tiered Products?"
msgstr "Cosa sono i Prodotti a Livelli?"

#: src/components/forms/TicketForm/index.tsx:202
#~ msgid "What are Tiered Tickets?"
#~ msgstr "What are Tiered Tickets?"

#: src/components/forms/CheckInListForm/index.tsx:50
msgid "What date should this check-in list become active?"
msgstr "In quale data questa lista di check-in dovrebbe diventare attiva?"

#: src/components/modals/CreateProductCategoryModal/index.tsx:53
msgid "What is a Category?"
msgstr "Cos'è una Categoria?"

#: src/components/forms/TicketForm/index.tsx:158
#~ msgid "What is a Tiered Ticketing?"
#~ msgstr "What is a Tiered Ticketing?"

#: src/components/routes/event/Webhooks/index.tsx:31
#~ msgid "What is a webhook?"
#~ msgstr "What is a webhook?"

#: src/components/forms/QuestionForm/index.tsx:159
msgid "What products does this code apply to?"
msgstr "What products does this code apply to?"

#: src/components/forms/PromoCodeForm/index.tsx:68
msgid "What products does this code apply to? (Applies to all by default)"
msgstr "What products does this code apply to? (Applies to all by default)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:48
msgid "What products should this capacity apply to?"
msgstr "What products should this capacity apply to?"

#: src/components/forms/PromoCodeForm/index.tsx:68
#~ msgid "What tickets does this code apply to? (Applies to all by default)"
#~ msgstr "What tickets does this code apply to? (Applies to all by default)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:47
#: src/components/forms/QuestionForm/index.tsx:159
#~ msgid "What tickets should this question be apply to?"
#~ msgstr "What tickets should this question be apply to?"

#: src/components/forms/QuestionForm/index.tsx:179
msgid "What time will you be arriving?"
msgstr "What time will you be arriving?"

#: src/components/forms/QuestionForm/index.tsx:170
msgid "What type of question is this?"
msgstr "What type of question is this?"

#: src/components/forms/WebhookForm/index.tsx:108
msgid "When a check-in is deleted"
msgstr "Quando un check-in viene eliminato"

#: src/components/forms/WebhookForm/index.tsx:84
msgid "When a new attendee is created"
msgstr "Quando viene creato un nuovo partecipante"

#: src/components/forms/WebhookForm/index.tsx:54
msgid "When a new order is created"
msgstr "Quando viene creato un nuovo ordine"

#: src/components/forms/WebhookForm/index.tsx:36
msgid "When a new product is created"
msgstr "Quando viene creato un nuovo prodotto"

#: src/components/forms/WebhookForm/index.tsx:48
msgid "When a product is deleted"
msgstr "Quando un prodotto viene eliminato"

#: src/components/forms/WebhookForm/index.tsx:42
msgid "When a product is updated"
msgstr "Quando un prodotto viene aggiornato"

#: src/components/forms/WebhookForm/index.tsx:96
msgid "When an attendee is cancelled"
msgstr "Quando un partecipante viene annullato"

#: src/components/forms/WebhookForm/index.tsx:102
msgid "When an attendee is checked in"
msgstr "Quando un partecipante effettua il check-in"

#: src/components/forms/WebhookForm/index.tsx:90
msgid "When an attendee is updated"
msgstr "Quando un partecipante viene aggiornato"

#: src/components/forms/WebhookForm/index.tsx:78
msgid "When an order is cancelled"
msgstr "Quando un ordine viene annullato"

#: src/components/forms/WebhookForm/index.tsx:66
msgid "When an order is marked as paid"
msgstr "Quando un ordine viene contrassegnato come pagato"

#: src/components/forms/WebhookForm/index.tsx:72
msgid "When an order is refunded"
msgstr "Quando un ordine viene rimborsato"

#: src/components/forms/WebhookForm/index.tsx:60
msgid "When an order is updated"
msgstr "Quando un ordine viene aggiornato"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:182
msgid "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."
msgstr "Quando abilitato, le fatture verranno generate per gli ordini di biglietti. Le fatture saranno inviate insieme all'email di conferma dell'ordine. I partecipanti possono anche scaricare le loro fatture dalla pagina di conferma dell'ordine."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:141
msgid "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."
msgstr "Quando i pagamenti offline sono abilitati, gli utenti potranno completare i loro ordini e ricevere i loro biglietti. I loro biglietti indicheranno chiaramente che l'ordine non è pagato, e lo strumento di check-in avviserà il personale di check-in se un ordine richiede il pagamento."

#: src/components/forms/CheckInListForm/index.tsx:59
msgid "When should this check-in list expire?"
msgstr "Quando dovrebbe scadere questa lista di check-in?"

#: src/components/forms/CheckInListForm/index.tsx:25
msgid "Which tickets should be associated with this check-in list?"
msgstr "Quali biglietti dovrebbero essere associati a questa lista di check-in?"

#: src/components/modals/CreateEventModal/index.tsx:107
msgid "Who is organizing this event?"
msgstr "Chi sta organizzando questo evento?"

#: src/components/modals/SendMessageModal/index.tsx:194
msgid "Who is this message to?"
msgstr "A chi è destinato questo messaggio?"

#: src/components/forms/QuestionForm/index.tsx:151
msgid "Who should be asked this question?"
msgstr "A chi dovrebbe essere posta questa domanda?"

#: src/components/forms/QuestionForm/index.tsx:163
#~ msgid "Who type of question is this?"
#~ msgstr "Who type of question is this?"

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:65
#~ msgid "Whoops! something went wrong. Please try again or contact support if the problem persists."
#~ msgstr "Whoops! something went wrong. Please try again or contact support if the problem persists."

#: src/components/layouts/Event/index.tsx:42
#~ msgid "Widget"
#~ msgstr "Widget"

#: src/components/routes/event/settings.tsx:29
#~ msgid "Widget Configuration"
#~ msgstr "Widget Configuration"

#: src/components/layouts/Event/index.tsx:110
msgid "Widget Embed"
msgstr "Incorpora Widget"

#: src/components/common/WidgetEditor/index.tsx:162
msgid "Widget Settings"
msgstr "Impostazioni Widget"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Working"
msgstr "In corso"

#: src/components/modals/CreateProductCategoryModal/index.tsx:60
#: src/components/modals/CreateProductModal/index.tsx:88
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
#: src/components/modals/CreateQuestionModal/index.tsx:74
#: src/components/modals/DuplicateEventModal/index.tsx:142
#: src/components/modals/DuplicateProductModal/index.tsx:113
#: src/components/modals/EditProductCategoryModal/index.tsx:70
#: src/components/modals/EditProductModal/index.tsx:108
#: src/components/modals/EditPromoCodeModal/index.tsx:84
#: src/components/modals/EditQuestionModal/index.tsx:101
#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/Register/index.tsx:129
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Working..."
msgstr "In corso..."

#: src/components/common/ReportTable/index.tsx:51
msgid "Year to date"
msgstr "Da inizio anno"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "Yes"
msgstr "Sì"

#: src/components/forms/ProductForm/index.tsx:376
msgid "Yes, remove them"
msgstr "Sì, rimuovili"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:77
msgid "You already scanned this ticket"
msgstr "Hai già scansionato questo biglietto"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:53
msgid "You are changing your email to <0>{0}</0>."
msgstr "Stai cambiando la tua email in <0>{0}</0>."

#: src/components/layouts/CheckIn/index.tsx:88
#: src/components/layouts/CheckIn/index.tsx:110
msgid "You are offline"
msgstr "Sei offline"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:93
#~ msgid "You can connecting using this Zoom link..."
#~ msgstr "You can connecting using this Zoom link..."

#: src/components/forms/ProductForm/index.tsx:407
msgid "You can create a promo code which targets this product on the"
msgstr "Puoi creare un codice promo che ha come target questo prodotto nella"

#: src/components/forms/TicketForm/index.tsx:352
#~ msgid "You can create a promo code which targets this ticket on the"
#~ msgstr "You can create a promo code which targets this ticket on the"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:31
#~ msgid "You can now start receiving payments through Stripe."
#~ msgstr "You can now start receiving payments through Stripe."

#: src/components/forms/ProductForm/index.tsx:208
msgid "You cannot change the product type as there are attendees associated with this product."
msgstr "Non puoi cambiare il tipo di prodotto poiché ci sono partecipanti associati a questo prodotto."

#: src/components/forms/TicketForm/index.tsx:184
#~ msgid "You cannot change the ticket type as there are attendees associated with this ticket."
#~ msgstr "You cannot change the ticket type as there are attendees associated with this ticket."

#: src/components/forms/TicketForm/index.tsx:185
#~ msgid "You cannot change the ticket type because there are already tickets sold for this ticket."
#~ msgstr "You cannot change the ticket type because there are already tickets sold for this ticket."

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "You cannot check in attendees with unpaid orders."
#~ msgstr "You cannot check in attendees with unpaid orders."

#: src/components/layouts/CheckIn/index.tsx:129
#: src/components/layouts/CheckIn/index.tsx:164
msgid "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."
msgstr "Non puoi registrare partecipanti con ordini non pagati. Questa impostazione può essere modificata nelle impostazioni dell'evento."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:40
msgid "You cannot delete the last category."
msgstr "Non puoi eliminare l'ultima categoria."

#: src/components/forms/ProductForm/index.tsx:57
msgid "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."
msgstr "Non puoi eliminare questo livello di prezzo perché ci sono già prodotti venduti per questo livello. Puoi invece nasconderlo."

#: src/components/forms/TicketForm/index.tsx:54
#~ msgid "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."
#~ msgstr "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."

#: src/components/common/QuestionsTable/index.tsx:42
#~ msgid "You cannot edit a default question"
#~ msgstr "You cannot edit a default question"

#: src/components/modals/EditUserModal/index.tsx:88
msgid "You cannot edit the role or status of the account owner."
msgstr "Non puoi modificare il ruolo o lo stato del proprietario dell'account."

#: src/components/modals/RefundOrderModal/index.tsx:132
msgid "You cannot refund a manually created order."
msgstr "Non puoi rimborsare un ordine creato manualmente."

#: src/components/common/QuestionsTable/index.tsx:252
msgid "You created a hidden question but disabled the option to show hidden questions. It has been enabled."
msgstr "Hai creato una domanda nascosta ma hai disabilitato l'opzione per mostrare le domande nascoste. È stata abilitata."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:77
#~ msgid "You do not have permission to access this page"
#~ msgstr "You do not have permission to access this page"

#: src/components/modals/ChooseAccountModal/index.tsx:14
msgid "You have access to multiple accounts. Please choose one to continue."
msgstr "Hai accesso a più account. Scegli uno per continuare."

#: src/components/routes/auth/AcceptInvitation/index.tsx:57
msgid "You have already accepted this invitation. Please login to continue."
msgstr "Hai già accettato questo invito. Accedi per continuare."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:29
#~ msgid "You have connected your Stripe account"
#~ msgstr "You have connected your Stripe account"

#: src/components/common/QuestionsTable/index.tsx:338
msgid "You have no attendee questions."
msgstr "Non hai domande per i partecipanti."

#: src/components/common/QuestionsTable/index.tsx:149
#~ msgid "You have no attendee questions. Attendee questions are asked once per attendee."
#~ msgstr "You have no attendee questions. Attendee questions are asked once per attendee."

#: src/components/common/QuestionsTable/index.tsx:323
msgid "You have no order questions."
msgstr "Non hai domande per gli ordini."

#: src/components/common/QuestionsTable/index.tsx:140
#~ msgid "You have no order questions. Order questions are asked once per order."
#~ msgstr "You have no order questions. Order questions are asked once per order."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:41
msgid "You have no pending email change."
msgstr "Non hai modifiche di email in sospeso."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:39
#~ msgid "You have not completed your Stripe Connect setup"
#~ msgstr "You have not completed your Stripe Connect setup"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:38
#~ msgid "You have not connected your Stripe account"
#~ msgstr "You have not connected your Stripe account"

#: src/components/layouts/Checkout/index.tsx:156
msgid "You have run out of time to complete your order."
msgstr "Hai esaurito il tempo per completare il tuo ordine."

#: src/components/forms/ProductForm/index.tsx:374
#~ msgid "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"
#~ msgstr "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"

#: src/components/forms/ProductForm/index.tsx:374
msgid "You have taxes and fees added to a Free Product. Would you like to remove them?"
msgstr "Hai tasse e commissioni aggiunte a un Prodotto Gratuito. Vuoi rimuoverle?"

#: src/components/forms/TicketForm/index.tsx:319
#~ msgid "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"
#~ msgstr "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"

#: src/components/common/MessageList/index.tsx:74
msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."
msgstr "Non hai ancora inviato messaggi. Puoi inviare messaggi a tutti i partecipanti o a specifici possessori di prodotti."

#: src/components/common/MessageList/index.tsx:64
#~ msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."
#~ msgstr "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."

#: src/components/modals/SendMessageModal/index.tsx:108
msgid "You must acknowledge that this email is not promotional"
msgstr "Devi riconoscere che questa email non è promozionale"

#: src/components/routes/auth/AcceptInvitation/index.tsx:33
msgid "You must agree to the terms and conditions"
msgstr "Devi accettare i termini e le condizioni"

#: src/components/routes/event/GettingStarted/index.tsx:193
msgid "You must confirm your email address before your event can go live."
msgstr "Devi confermare il tuo indirizzo email prima che il tuo evento possa andare in diretta."

#: src/components/modals/CreateAttendeeModal/index.tsx:98
msgid "You must create a ticket before you can manually add an attendee."
msgstr "Devi creare un biglietto prima di poter aggiungere manualmente un partecipante."

#: src/components/forms/ProductForm/index.tsx:60
msgid "You must have at least one price tier"
msgstr "Devi avere almeno un livello di prezzo"

#: src/components/forms/TicketForm/index.tsx:84
#~ msgid "You must have at least one tier"
#~ msgstr "You must have at least one tier"

#: src/components/modals/SendMessageModal/index.tsx:136
#~ msgid "You need to verify your account before you can send messages."
#~ msgstr "You need to verify your account before you can send messages."

#: src/components/modals/SendMessageModal/index.tsx:151
msgid "You need to verify your account email before you can send messages."
msgstr "Devi verificare l'email del tuo account prima di poter inviare messaggi."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:143
msgid "You will have to mark an order as paid manually. This can be done on the manage order page."
msgstr "Dovrai contrassegnare un ordine come pagato manualmente. Questo può essere fatto nella pagina di gestione dell'ordine."

#: src/components/modals/CreateCheckInListModal/index.tsx:55
msgid "You'll need a ticket before you can create a check-in list."
msgstr "Avrai bisogno di un biglietto prima di poter creare una lista di check-in."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
msgid "You'll need at a product before you can create a capacity assignment."
msgstr "Avrai bisogno di un prodotto prima di poter creare un'assegnazione di capacità."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
#~ msgid "You'll need at a ticket before you can create a capacity assignment."
#~ msgstr "You'll need at a ticket before you can create a capacity assignment."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:43
msgid "You'll need at least one product to get started. Free, paid or let the user decide what to pay."
msgstr "Avrai bisogno di almeno un prodotto per iniziare. Gratuito, a pagamento o lascia che l'utente decida quanto pagare."

#: src/components/common/TicketsTable/index.tsx:69
#~ msgid "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."
#~ msgstr "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:93
msgid "You're going to {0}! 🎉"
msgstr "Stai andando a {0}! 🎉"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:72
#~ msgid "Your account email in outgoing emails."
#~ msgstr "Your account email in outgoing emails."

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:64
msgid "Your account name is used on event pages and in emails."
msgstr "Il nome del tuo account è utilizzato nelle pagine degli eventi e nelle email."

#: src/components/routes/event/attendees.tsx:44
msgid "Your attendees have been exported successfully."
msgstr "I tuoi partecipanti sono stati esportati con successo."

#: src/components/common/AttendeeTable/index.tsx:61
msgid "Your attendees will appear here once they have registered for your event. You can also manually add attendees."
msgstr "I tuoi partecipanti appariranno qui una volta che si saranno registrati per il tuo evento. Puoi anche aggiungere manualmente i partecipanti."

#: src/components/common/WidgetEditor/index.tsx:329
msgid "Your awesome website 🎉"
msgstr "Il tuo fantastico sito web 🎉"

#: src/components/routes/product-widget/CollectInformation/index.tsx:276
msgid "Your Details"
msgstr "I tuoi Dettagli"

#: src/components/routes/auth/ForgotPassword/index.tsx:52
msgid "Your Email"
msgstr "La tua Email"

#: src/components/routes/profile/ManageProfile/index.tsx:118
msgid "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"
msgstr "La tua richiesta di cambio email a <0>{0}</0> è in attesa. Controlla la tua email per confermare"

#: src/components/routes/event/EventDashboard/index.tsx:150
msgid "Your event must be live before you can sell tickets to attendees."
msgstr "Il tuo evento deve essere attivo prima di poter vendere biglietti ai partecipanti."

#: src/components/routes/event/EventDashboard/index.tsx:164
#~ msgid "Your event must be live before you can sell tickets."
#~ msgstr "Your event must be live before you can sell tickets."

#: src/components/common/OrdersTable/index.tsx:88
msgid "Your message has been sent"
msgstr "Il tuo messaggio è stato inviato"

#: src/components/layouts/Checkout/index.tsx:84
msgid "Your Order"
msgstr "Il tuo Ordine"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:94
msgid "Your order has been cancelled"
msgstr "Il tuo ordine è stato annullato"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:96
msgid "Your order is awaiting payment 🏦"
msgstr "Il tuo ordine è in attesa di pagamento 🏦"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:242
#~ msgid "Your order is in progress"
#~ msgstr "Your order is in progress"

#: src/components/routes/event/orders.tsx:95
msgid "Your orders have been exported successfully."
msgstr "I tuoi ordini sono stati esportati con successo."

#: src/components/common/OrdersTable/index.tsx:66
msgid "Your orders will appear here once they start rolling in."
msgstr "I tuoi ordini appariranno qui una volta che inizieranno ad arrivare."

#: src/components/routes/auth/Login/index.tsx:74
#: src/components/routes/auth/Register/index.tsx:107
msgid "Your password"
msgstr "La tua password"

#: src/components/forms/StripeCheckoutForm/index.tsx:63
msgid "Your payment is processing."
msgstr "Il tuo pagamento è in elaborazione."

#: src/components/forms/StripeCheckoutForm/index.tsx:66
msgid "Your payment was not successful, please try again."
msgstr "Il tuo pagamento non è andato a buon fine, riprova."

#: src/components/forms/StripeCheckoutForm/index.tsx:126
msgid "Your payment was unsuccessful. Please try again."
msgstr "Il tuo pagamento non è andato a buon fine. Riprova."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#~ msgid "Your product for"
#~ msgstr "Il tuo prodotto per"

#: src/components/modals/RefundOrderModal/index.tsx:58
msgid "Your refund is processing."
msgstr "Il tuo rimborso è in elaborazione."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:153
msgid "Your Stripe account is connected and ready to process payments."
msgstr "Il tuo account Stripe è collegato e pronto per elaborare i pagamenti."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#: src/components/routes/product-widget/PrintProduct/index.tsx:42
msgid "Your ticket for"
msgstr "Il tuo biglietto per"

#: src/components/routes/event/settings.tsx:117
#~ msgid "Zip"
#~ msgstr "Zip"

#: src/components/routes/product-widget/CollectInformation/index.tsx:348
msgid "ZIP / Postal Code"
msgstr "CAP / Codice Postale"

#: src/components/common/CheckoutQuestion/index.tsx:170
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:146
msgid "Zip or Postal Code"
msgstr "CAP o Codice Postale"

#: src/components/routes/product-widget/CollectInformation/index.tsx:349
msgid "ZIP or Postal Code"
msgstr "CAP o Codice Postale"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:92
#~ msgid "Zoom link, Google Meet link, etc."
#~ msgstr "Zoom link, Google Meet link, etc."
