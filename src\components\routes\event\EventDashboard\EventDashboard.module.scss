.chartCard {
  margin-top: 20px;

  .chartCardTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
    }
  }

  .dateRange {
    color: #bdbdbd;
  }

  h2:first-child {
    margin-top: 0;
  }
}

.setupCard {
  margin-bottom: 28px;
  margin-top: 24px;
  position: relative;
  padding: 32px 28px;
  background: linear-gradient(to right, rgba(249, 250, 251, 0.6), rgba(243, 244, 246, 0.6));
  border: 1px solid #f0f0f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.dismissButton {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  color: #9e9e9e;
  background-color: rgba(255, 255, 255, 0.7);
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.9);
    color: #555;
  }
}

.setupCardContent {
  display: flex;
  flex-direction: column;
  position: relative;
}

.checklistContainer {
  width: 100%;

  h2 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
    margin-top: 0;
    color: #444;
    font-weight: 600;
    letter-spacing: -0.01em;
  }

  .setupDescription {
    margin-bottom: 1.75rem;
    color: #666;
    font-size: 1rem;
    line-height: 1.5;
    max-width: 80%;
  }
}

.checklistItems {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12px;
}

.checklistItem {
  width: calc(50% - 24px);
  margin: 0 12px 24px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid rgba(230, 230, 230, 0.9);
  transition: all 0.2s ease;

  h3 {
    font-size: 1.1rem;
    margin: 0 0 0.75rem 0;
    color: #333;
    font-weight: 600;
    display: flex;
    align-items: center;
  }

  p {
    margin: 0 0 1rem 0;
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
  }

  /* Always show a consistent layout with status indicator */
  .status-section {
    min-height: 36px;
    display: flex;
    align-items: center;
  }
}

.checkboxContainer {
  margin-right: 12px;
}

.checkbox {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  border: 2px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.6);
  transition: all 0.2s ease;

  svg {
    transform: scale(0.85);
  }
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-complete {
  background-color: rgba(52, 211, 153, 0.1);
  color: #10b981;
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

/* Tablet and smaller desktop styles */
@media (max-width: 1200px) {
  .setupCard {
    padding: 24px 20px;
  }

  .checklistContainer {
    .setupDescription {
      max-width: 100%;
    }
  }
}

/* Small tablet styles */
@media (max-width: 992px) {
  .checklistItem {
    width: 100%;
    margin-bottom: 16px;
  }

  .setupCard {
    padding: 20px 18px;
  }

  .checklistContainer h2 {
    font-size: 1.4rem;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .setupCard {
    padding: 20px 16px;
    margin-bottom: 20px;
  }

  .checklistContainer {
    h2 {
      font-size: 1.3rem;
    }

    .setupDescription {
      font-size: 0.95rem;
      margin-bottom: 1.5rem;
    }
  }

  .checklistItem {
    padding: 18px 16px;
  }

  .dismissButton {
    top: 12px;
    right: 12px;
    width: 28px;
    height: 28px;
  }
}
