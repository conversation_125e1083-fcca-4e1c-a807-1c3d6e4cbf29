msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Hi.Events\n"
"Language: de\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/components/layouts/Event/index.tsx:189
msgid "- Click to Publish"
msgstr "- Zum Veröffentlichen klicken"

#: src/components/layouts/Event/index.tsx:191
msgid "- Click to Unpublish"
msgstr "- Zum Rückgängigmachen der Veröffentlichung klicken"

#: src/components/common/NoResultsSplash/index.tsx:14
msgid "'There\\'s nothing to show yet'"
msgstr "„Hier gibt es noch nichts zu sehen“"

#: src/components/modals/SendMessageModal/index.tsx:159
#~ msgid ""
#~ "\"\"Due to the high risk of spam, we require manual verification before you can send messages.\n"
#~ "\"\"Please contact us to request access."
#~ msgstr ""
#~ "Aufgrund des hohen Spam-Risikos ist eine manuelle Verifizierung erforderlich, bevor du Nachrichten senden kannst.\n"
#~ "Bitte kontaktiere uns, um Zugriff zu beantragen."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
#~ msgid ""
#~ "\"\"If you have an account with us, you will receive an email with instructions on how to reset your\n"
#~ "\"\"password."
#~ msgstr ""
#~ "Wenn du ein Konto bei uns hast, erhältst du eine E-Mail mit Anweisungen zum Zurücksetzen deines\n"
#~ "Passworts."

#: src/components/common/ReportTable/index.tsx:303
#: src/locales.ts:37
msgid "{0}"
msgstr "{0}"

#: src/components/layouts/CheckIn/index.tsx:82
msgid "{0} <0>checked in</0> successfully"
msgstr "{0} <0>erfolgreich eingecheckt</0>"

#: src/components/layouts/CheckIn/index.tsx:106
msgid "{0} <0>checked out</0> successfully"
msgstr "{0} <0>erfolgreich ausgecheckt</0>"

#: src/components/routes/event/Webhooks/index.tsx:24
msgid "{0} Active Webhooks"
msgstr "{0} aktive Webhooks"

#: src/components/routes/product-widget/SelectProducts/index.tsx:412
msgid "{0} available"
msgstr "{0} verfügbar"

#: src/components/common/AttendeeCheckInTable/index.tsx:155
#~ msgid "{0} checked in"
#~ msgstr "{0} eingecheckt"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
msgid "{0} created successfully"
msgstr "{0} erfolgreich erstellt"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
msgid "{0} updated successfully"
msgstr "{0} erfolgreich aktualisiert"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:57
msgid "{0}'s Events"
msgstr "{0}s Veranstaltungen"

#: src/components/layouts/CheckIn/index.tsx:449
msgid "{0}/{1} checked in"
msgstr "{0}/{1} eingecheckt"

#: src/components/common/Countdown/index.tsx:68
msgid "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{days} Tage, {hours} Stunden, {minutes} Minuten und {seconds} Sekunden"

#: src/components/common/WebhookTable/index.tsx:79
msgid "{eventCount} events"
msgstr "{eventCount} Ereignisse"

#: src/components/common/Countdown/index.tsx:70
msgid "{hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{hours} Stunden, {minutes} Minuten und {seconds} Sekunden"

#: src/components/common/Countdown/index.tsx:72
msgid "{minutes} minutes and {seconds} seconds"
msgstr "{Minuten} Minuten und {Sekunden} Sekunden"

#: src/components/routes/welcome/index.tsx:59
msgid "{organizerName}'s first event"
msgstr "Erste Veranstaltung von {organizerName}"

#: src/components/common/ReportTable/index.tsx:256
#~ msgid "{title}"
#~ msgstr ""

#: src/components/common/CapacityAssignmentList/index.tsx:47
msgid "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"
msgstr "<0>Kapazitätszuweisungen ermöglichen es Ihnen, die Kapazität über Tickets oder ein ganzes Ereignis zu verwalten. Ideal für mehrtägige Veranstaltungen, Workshops und mehr, bei denen die Kontrolle der Teilnehmerzahl entscheidend ist.</0><1>Beispielsweise können Sie eine Kapazitätszuweisung mit einem <2>Tag Eins</2> und einem <3>Alle Tage</3>-Ticket verknüpfen. Sobald die Kapazität erreicht ist, werden beide Tickets automatisch nicht mehr zum Verkauf angeboten.</1>"

#: src/components/common/CheckInListList/index.tsx:49
msgid "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"
msgstr "<0>Einchecklisten helfen, den Eintritt der Teilnehmer zu verwalten. Sie können mehrere Tickets mit einer Eincheckliste verknüpfen und sicherstellen, dass nur Personen mit gültigen Tickets eintreten können.</0>"

#: src/components/common/WidgetEditor/index.tsx:324
msgid "<0>https://</0>your-website.com"
msgstr "<0>https://</0>Ihre-website.com"

#: src/components/forms/ProductForm/index.tsx:287
msgid "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"
msgstr "<0>Bitte geben Sie den Preis ohne Steuern und Gebühren ein.</0><1>Steuern und Gebühren können unten hinzugefügt werden.</1>"

#: src/components/forms/ProductForm/index.tsx:304
msgid "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"
msgstr "<0>Die Anzahl der für dieses Produkt verfügbaren Produkte</0><1>Dieser Wert kann überschrieben werden, wenn mit diesem Produkt <2>Kapazitätsgrenzen</2> verbunden sind.</1>"

#: src/components/forms/TicketForm/index.tsx:249
#~ msgid "<0>The number of tickets available for this ticket</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this ticket.</1>"
#~ msgstr "<0>Die Anzahl der verfügbaren Tickets für dieses Ticket</0><1>Dieser Wert kann überschrieben werden, wenn diesem Ticket <2>Kapazitätsgrenzen</2> zugewiesen sind.</1>"

#: src/components/common/WebhookTable/index.tsx:205
msgid "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"
msgstr "<0>Webhooks benachrichtigen externe Dienste sofort, wenn Ereignisse eintreten, z. B. wenn ein neuer Teilnehmer zu deinem CRM oder deiner Mailingliste hinzugefügt wird, um eine nahtlose Automatisierung zu gewährleisten.</0><1>Nutze Drittanbieterdienste wie <2>Zapier</2>, <3>IFTTT</3> oder <4>Make</4>, um benutzerdefinierte Workflows zu erstellen und Aufgaben zu automatisieren.</1>"

#: src/components/routes/event/GettingStarted/index.tsx:134
msgid "⚡️ Set up your event"
msgstr "⚡️ Richten Sie Ihre Veranstaltung ein"

#: src/components/routes/event/GettingStarted/index.tsx:190
msgid "✉️ Confirm your email address"
msgstr "✉️ Bestätigen Sie Ihre E-Mail-Adresse"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "🎉 Congratulations on creating an event!"
#~ msgstr "🎉 Herzlichen Glückwunsch zur Erstellung einer Veranstaltung!"

#: src/components/routes/event/GettingStarted/index.tsx:67
#~ msgid "🎟️ Add products"
#~ msgstr "🎟️ Produkte hinzufügen"

#: src/components/routes/event/GettingStarted/index.tsx:119
msgid "🎟️ Add tickets"
msgstr "🎟️ Tickets hinzufügen"

#: src/components/routes/event/GettingStarted/index.tsx:162
msgid "🎨 Customize your event page"
msgstr "🎨 Veranstaltungsseite anpassen"

#: src/components/routes/event/GettingStarted/index.tsx:147
msgid "💳 Connect with Stripe"
msgstr "💳 Mit Stripe verbinden"

#: src/components/routes/event/GettingStarted/index.tsx:176
msgid "🚀 Set your event live"
msgstr "🚀 Veröffentliche dein Event"

#: src/components/common/Countdown/index.tsx:34
msgid "0 minutes and 0 seconds"
msgstr "0 Minuten und 0 Sekunden"

#: src/components/routes/event/Webhooks/index.tsx:23
msgid "1 Active Webhook"
msgstr "1 aktiver Webhook"

#: src/components/modals/RefundOrderModal/index.tsx:105
msgid "10.00"
msgstr "10.00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:123
msgid "123 Main Street"
msgstr "Hauptstraße 123"

#: src/components/common/WidgetEditor/index.tsx:226
msgid "20"
msgstr "20"

#: src/components/routes/welcome/index.tsx:91
msgid "2024-01-01 10:00"
msgstr "01.01.2024 10:00"

#: src/components/routes/welcome/index.tsx:97
msgid "2024-01-01 18:00"
msgstr "01.01.2024 18:00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:147
msgid "94103"
msgstr "94103"

#: src/components/forms/QuestionForm/index.tsx:138
msgid "A date input. Perfect for asking for a date of birth etc."
msgstr "Ein Datumseingabefeld. Perfekt, um nach einem Geburtsdatum o.ä. zu fragen."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
msgid "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."
msgstr "Ein standardmäßiger {type} wird automatisch auf alle neuen Produkte angewendet. Sie können dies für jedes Produkt einzeln überschreiben."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
#~ msgid "A default {type} is automaticaly applied to all new tickets. You can override this on a per ticket basis."
#~ msgstr "Auf alle neuen Tickets wird automatisch ein Standard-{type} angewendet. Sie können dies für jedes Ticket einzeln überschreiben."

#: src/components/forms/QuestionForm/index.tsx:126
msgid "A Dropdown input allows only one selection"
msgstr "Eine Dropdown-Eingabe erlaubt nur eine Auswahl"

#: src/components/forms/TaxAndFeeForm/index.tsx:20
msgid "A fee, like a booking fee or a service fee"
msgstr "Eine Gebühr, beispielsweise eine Buchungsgebühr oder eine Servicegebühr"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
msgid "A fixed amount per product. E.g, $0.50 per product"
msgstr "Ein fester Betrag pro Produkt. Z.B., 0,50 $ pro Produkt"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
#~ msgid "A fixed amount per ticket. E.g, $0.50 per ticket"
#~ msgstr "Ein fester Betrag pro Ticket. Z. B. 0,50 € pro Ticket"

#: src/components/forms/QuestionForm/index.tsx:108
msgid "A multi line text input"
msgstr "Eine mehrzeilige Texteingabe"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
msgid "A percentage of the product price. E.g., 3.5% of the product price"
msgstr "Ein Prozentsatz des Produktpreises. Z.B., 3,5 % des Produktpreises"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
#~ msgid "A percentage of the ticket price. E.g., 3.5% of the ticket price"
#~ msgstr "Ein Prozentsatz des Ticketpreises. Beispiel: 3,5 % des Ticketpreises"

#: src/components/forms/PromoCodeForm/index.tsx:37
msgid "A promo code with no discount can be used to reveal hidden products."
msgstr "Ein Promo-Code ohne Rabatt kann verwendet werden, um versteckte Produkte anzuzeigen."

#: src/components/forms/PromoCodeForm/index.tsx:36
#~ msgid "A promo code with no discount can be used to reveal hidden tickets."
#~ msgstr "Mit einem Promo-Code ohne Rabatt können versteckte Tickets freigeschaltet werden."

#: src/components/forms/QuestionForm/index.tsx:120
msgid "A Radio option has multiple options but only one can be selected."
msgstr "Eine Radiooption hat mehrere Optionen, aber nur eine kann ausgewählt werden."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:69
msgid "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"
msgstr "Eine kurze Beschreibung der Veranstaltung, die in Suchmaschinenergebnissen und beim Teilen in sozialen Medien angezeigt wird. Standardmäßig wird die Veranstaltungsbeschreibung verwendet"

#: src/components/forms/QuestionForm/index.tsx:102
msgid "A single line text input"
msgstr "Eine einzeilige Texteingabe"

#: src/components/forms/QuestionForm/index.tsx:92
#~ msgid "A single question per attendee. E.g, What is your preferred meal?"
#~ msgstr "Eine einzelne Frage pro Teilnehmer. Z. B.: „Was ist Ihr Lieblingsessen?“"

#: src/components/forms/QuestionForm/index.tsx:86
#~ msgid "A single question per order. E.g, What is your company name?"
#~ msgstr "Eine einzige Frage pro Bestellung. Z. B.: Wie lautet der Name Ihres Unternehmens?"

#: src/components/forms/QuestionForm/index.tsx:87
msgid "A single question per order. E.g, What is your shipping address?"
msgstr "Eine einzelne Frage pro Bestellung. Z.B., Wie lautet Ihre Lieferadresse?"

#: src/components/forms/QuestionForm/index.tsx:93
msgid "A single question per product. E.g, What is your t-shirt size?"
msgstr "Eine einzelne Frage pro Produkt. Z.B., Welche T-Shirt-Größe haben Sie?"

#: src/components/forms/TaxAndFeeForm/index.tsx:14
msgid "A standard tax, like VAT or GST"
msgstr "Eine Standardsteuer wie Mehrwertsteuer oder GST"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:76
msgid "About"
msgstr "Über"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:211
msgid "About Stripe Connect"
msgstr "Über Stripe Connect"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:79
#~ msgid "About the event"
#~ msgstr "Über die Veranstaltung"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:91
msgid "Accept bank transfers, checks, or other offline payment methods"
msgstr "Akzeptieren Sie Banküberweisungen, Schecks oder andere Offline-Zahlungsmethoden"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:86
msgid "Accept credit card payments with Stripe"
msgstr "Akzeptieren Sie Kreditkartenzahlungen über Stripe"

#: src/components/routes/auth/AcceptInvitation/index.tsx:126
msgid "Accept Invitation"
msgstr "Einladung annehmen"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:125
msgid "Access Denied"
msgstr "Zugriff verweigert"

#: src/components/routes/account/ManageAccount/index.tsx:24
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:53
msgid "Account"
msgstr "Konto"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:62
msgid "Account Name"
msgstr "Kontoname"

#: src/components/common/GlobalMenu/index.tsx:32
#: src/components/routes/account/ManageAccount/index.tsx:19
msgid "Account Settings"
msgstr "Account Einstellungen"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:42
msgid "Account updated successfully"
msgstr "Konto erfolgreich aktualisiert"

#: src/components/common/AttendeeTable/index.tsx:158
#: src/components/common/ProductsTable/SortableProduct/index.tsx:267
#: src/components/common/QuestionsTable/index.tsx:108
msgid "Actions"
msgstr "Aktionen"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "Activate"
msgstr "Aktivieren"

#: src/components/forms/CheckInListForm/index.tsx:47
msgid "Activation date"
msgstr "Aktivierungsdatum"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:19
#: src/components/modals/EditUserModal/index.tsx:110
msgid "Active"
msgstr "Aktiv"

#: src/components/routes/event/attendees.tsx:59
#~ msgid "Add"
#~ msgstr "Hinzufügen"

#: src/components/forms/CheckInListForm/index.tsx:39
msgid "Add a description for this check-in list"
msgstr "Fügen Sie eine Beschreibung für diese Eincheckliste hinzu"

#: src/components/modals/ManageAttendeeModal/index.tsx:131
msgid "Add any notes about the attendee. These will not be visible to the attendee."
msgstr "Fügen Sie Anmerkungen über den Teilnehmer hinzu. Diese sind für den Teilnehmer nicht sichtbar."

#: src/components/modals/ManageAttendeeModal/index.tsx:133
msgid "Add any notes about the attendee..."
msgstr "Fügen Sie Anmerkungen über den Teilnehmer hinzu..."

#: src/components/modals/ManageOrderModal/index.tsx:165
msgid "Add any notes about the order. These will not be visible to the customer."
msgstr "Fügen Sie Notizen zur Bestellung hinzu. Diese sind für den Kunden nicht sichtbar."

#: src/components/modals/ManageOrderModal/index.tsx:169
msgid "Add any notes about the order..."
msgstr "Fügen Sie Notizen zur Bestellung hinzu..."

#: src/components/forms/QuestionForm/index.tsx:202
msgid "Add description"
msgstr "Beschreibung hinzufügen"

#: src/components/routes/event/GettingStarted/index.tsx:85
#~ msgid "Add event details and and manage event settings."
#~ msgstr "Fügen Sie Ereignisdetails hinzu und verwalten Sie die Ereigniseinstellungen."

#: src/components/routes/event/GettingStarted/index.tsx:137
msgid "Add event details and manage event settings."
msgstr "Füge Veranstaltungsdetails hinzu und verwalte die Einstellungen."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:153
msgid "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"
msgstr "Fügen Sie Anweisungen für Offline-Zahlungen hinzu (z. B. Überweisungsdetails, wo Schecks hingeschickt werden sollen, Zahlungsfristen)"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add More products"
#~ msgstr "Weitere Produkte hinzufügen"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add More tickets"
msgstr "Weitere Tickets hinzufügen"

#: src/components/common/HeadingCard/index.tsx:27
msgid "Add New"
msgstr "Neue hinzufügen"

#: src/components/forms/QuestionForm/index.tsx:68
msgid "Add Option"
msgstr "Option hinzufügen"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:68
msgid "Add Product"
msgstr "Produkt hinzufügen"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:51
msgid "Add Product to Category"
msgstr "Produkt zur Kategorie hinzufügen"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add products"
#~ msgstr "Produkte hinzufügen"

#: src/components/common/QuestionsTable/index.tsx:281
msgid "Add question"
msgstr "Frage hinzufügen"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:18
msgid "Add Tax or Fee"
msgstr "Steuern oder Gebühren hinzufügen"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add tickets"
msgstr "Tickets hinzufügen"

#: src/components/forms/ProductForm/index.tsx:337
msgid "Add tier"
msgstr "Ebene hinzufügen"

#: src/components/common/AddEventToCalendarButton/index.tsx:112
#: src/components/common/AddEventToCalendarButton/index.tsx:120
msgid "Add to Calendar"
msgstr "Zum Kalender hinzufügen"

#: src/components/common/WebhookTable/index.tsx:223
#: src/components/routes/event/Webhooks/index.tsx:35
msgid "Add Webhook"
msgstr "Webhook hinzufügen"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:219
msgid "Additional Information"
msgstr "Zusätzliche Informationen"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/forms/ProductForm/index.tsx:348
msgid "Additional Options"
msgstr "Zusatzoptionen"

#: src/components/common/OrderDetails/index.tsx:96
#: src/components/forms/QuestionForm/index.tsx:130
msgid "Address"
msgstr "Adresse"

#: src/components/common/CheckoutQuestion/index.tsx:156
msgid "Address line 1"
msgstr "Anschrift Zeile 1"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:122
#: src/components/routes/product-widget/CollectInformation/index.tsx:319
#: src/components/routes/product-widget/CollectInformation/index.tsx:320
msgid "Address Line 1"
msgstr "Anschrift Zeile 1"

#: src/components/common/CheckoutQuestion/index.tsx:159
msgid "Address line 2"
msgstr "Adresszeile 2"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:127
#: src/components/routes/product-widget/CollectInformation/index.tsx:324
#: src/components/routes/product-widget/CollectInformation/index.tsx:325
msgid "Address Line 2"
msgstr "Adresszeile 2"

#: src/components/modals/EditUserModal/index.tsx:49
#: src/components/modals/InviteUserModal/index.tsx:41
msgid "Admin"
msgstr "Administrator"

#: src/components/modals/EditUserModal/index.tsx:51
#: src/components/modals/InviteUserModal/index.tsx:43
msgid "Admin users have full access to events and account settings."
msgstr "Administratorbenutzer haben vollständigen Zugriff auf Ereignisse und Kontoeinstellungen."

#: src/components/layouts/Event/index.tsx:53
#~ msgid "Affiliates"
#~ msgstr "Mitgliedsorganisationen"

#: src/components/common/MessageList/index.tsx:21
msgid "All attendees"
msgstr "Alle Teilnehmer"

#: src/components/modals/SendMessageModal/index.tsx:187
msgid "All attendees of this event"
msgstr "Alle Teilnehmer dieser Veranstaltung"

#: src/components/routes/events/Dashboard/index.tsx:52
#~ msgid "All Events"
#~ msgstr "Alle Veranstaltungen"

#: src/components/common/PromoCodeTable/index.tsx:131
msgid "All Products"
msgstr "Alle Produkte"

#: src/components/common/PromoCodeTable/index.tsx:130
#: src/components/forms/PromoCodeForm/index.tsx:67
#~ msgid "All Tickets"
#~ msgstr "Alle Tickets"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:157
msgid "Allow attendees associated with unpaid orders to check in"
msgstr "Erlauben Sie Teilnehmern, die mit unbezahlten Bestellungen verbunden sind, einzuchecken"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:82
msgid "Allow search engine indexing"
msgstr "Suchmaschinenindizierung zulassen"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:81
msgid "Allow search engines to index this event"
msgstr "Suchmaschinen erlauben, dieses Ereignis zu indizieren"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:78
msgid "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."
msgstr "Fast geschafft! Wir warten nur darauf, dass Ihre Zahlung bearbeitet wird. Dies sollte nur ein paar Sekunden dauern."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:77
msgid "Amazing, Event, Keywords..."
msgstr "Erstaunlich, Ereignis, Schlüsselwörter..."

#: src/components/common/OrdersTable/index.tsx:207
#: src/components/forms/TaxAndFeeForm/index.tsx:71
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:36
msgid "Amount"
msgstr "Menge"

#: src/components/modals/CreateAttendeeModal/index.tsx:165
msgid "Amount paid ({0})"
msgstr "Bezahlter Betrag ({0})"

#: src/mutations/useExportAnswers.ts:45
msgid "An error occurred while checking export status."
msgstr "Ein Fehler ist aufgetreten beim Überprüfen des Exportstatus."

#: src/components/common/ErrorDisplay/index.tsx:18
msgid "An error occurred while loading the page"
msgstr "Beim Laden der Seite ist ein Fehler aufgetreten"

#: src/components/common/QuestionsTable/index.tsx:148
msgid "An error occurred while sorting the questions. Please try again or refresh the page"
msgstr "Beim Sortieren der Fragen ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut oder aktualisieren Sie die Seite"

#: src/components/common/TicketsTable/index.tsx:47
#~ msgid "An error occurred while sorting the tickets. Please try again or refresh the page"
#~ msgstr "Beim Sortieren der Tickets ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut oder aktualisieren Sie die Seite"

#: src/components/routes/welcome/index.tsx:75
#~ msgid "An event is the actual event you are hosting. You can add more details later."
#~ msgstr "Ein Event ist die eigentliche Veranstaltung, die Sie veranstalten. Sie können später weitere Details hinzufügen."

#: src/components/routes/welcome/index.tsx:75
msgid "An event is the gathering or occasion you’re organizing. You can add more details later."
msgstr "Eine Veranstaltung ist das Treffen oder Ereignis, das Sie organisieren. Sie können später weitere Details hinzufügen."

#: src/components/routes/welcome/index.tsx:23
msgid "An organizer is the company or person who is hosting the event"
msgstr "Ein Veranstalter ist das Unternehmen oder die Person, die die Veranstaltung ausrichtet."

#: src/components/forms/StripeCheckoutForm/index.tsx:40
msgid "An unexpected error occurred."
msgstr "Ein unerwarteter Fehler ist aufgetreten."

#: src/hooks/useFormErrorResponseHandler.tsx:48
msgid "An unexpected error occurred. Please try again."
msgstr "Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut."

#: src/components/common/QuestionAndAnswerList/index.tsx:97
msgid "Answer updated successfully."
msgstr "Antwort erfolgreich aktualisiert."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
msgid "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
msgstr "Alle Anfragen von Produktinhabern werden an diese E-Mail-Adresse gesendet. Diese wird auch als „Antwort-an“-Adresse für alle von dieser Veranstaltung gesendeten E-Mails verwendet."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
#~ msgid "Any queries from ticket holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
#~ msgstr "Alle Anfragen von Ticketinhabern werden an diese E-Mail-Adresse gesendet. Diese wird auch als „Antwortadresse“ für alle E-Mails verwendet, die von dieser Veranstaltung gesendet werden."

#: src/components/common/WidgetEditor/index.tsx:213
msgid "Appearance"
msgstr "Aussehen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:500
msgid "applied"
msgstr "angewandt"

#: src/components/common/CapacityAssignmentList/index.tsx:97
msgid "Applies to {0} products"
msgstr "Gilt für {0} Produkte"

#: src/components/common/CapacityAssignmentList/index.tsx:97
#~ msgid "Applies to {0} tickets"
#~ msgstr "Gilt für {0} Tickets"

#: src/components/common/CapacityAssignmentList/index.tsx:98
msgid "Applies to 1 product"
msgstr "Gilt für 1 Produkt"

#: src/components/common/CapacityAssignmentList/index.tsx:98
#~ msgid "Applies to 1 ticket"
#~ msgstr "Gilt für 1 Ticket"

#: src/components/common/FilterModal/index.tsx:253
msgid "Apply"
msgstr "Anwenden"

#: src/components/routes/product-widget/SelectProducts/index.tsx:528
msgid "Apply Promo Code"
msgstr "Promo-Code anwenden"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
msgid "Apply this {type} to all new products"
msgstr "Diesen {type} auf alle neuen Produkte anwenden"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
#~ msgid "Apply this {type} to all new tickets"
#~ msgstr "Wenden Sie diesen {type} auf alle neuen Tickets an"

#: src/components/common/EventCard/index.tsx:167
msgid "Archive event"
msgstr "Veranstaltung archivieren"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:32
msgid "Archived"
msgstr "Archiviert"

#: src/components/routes/events/Dashboard/index.tsx:59
msgid "Archived Events"
msgstr "Archivierte Veranstaltungen"

#: src/components/common/AttendeeTable/index.tsx:76
msgid "Are you sure you want to activate this attendee?"
msgstr "Möchten Sie diesen Teilnehmer wirklich aktivieren?"

#: src/components/common/EventCard/index.tsx:58
msgid "Are you sure you want to archive this event?"
msgstr "Sind Sie sicher, dass Sie diese Veranstaltung archivieren möchten?"

#: src/components/common/AttendeeTable/index.tsx:78
#~ msgid "Are you sure you want to cancel this attendee? This will void their product"
#~ msgstr "Sind Sie sicher, dass Sie diesen Teilnehmer stornieren möchten? Dies macht sein Produkt ungültig"

#: src/components/common/AttendeeTable/index.tsx:77
msgid "Are you sure you want to cancel this attendee? This will void their ticket"
msgstr "Möchten Sie diesen Teilnehmer wirklich stornieren? Dadurch wird sein Ticket ungültig."

#: src/components/common/PromoCodeTable/index.tsx:37
msgid "Are you sure you want to delete this promo code?"
msgstr "Möchten Sie diesen Aktionscode wirklich löschen?"

#: src/components/common/QuestionsTable/index.tsx:164
msgid "Are you sure you want to delete this question?"
msgstr "Möchten Sie diese Frage wirklich löschen?"

#: src/components/common/WebhookTable/index.tsx:122
msgid "Are you sure you want to delete this webhook?"
msgstr "Bist du sicher, dass du diesen Webhook löschen möchtest?"

#: src/components/layouts/Event/index.tsx:68
#: src/components/routes/event/EventDashboard/index.tsx:64
msgid "Are you sure you want to make this event draft? This will make the event invisible to the public"
msgstr "Möchten Sie diese Veranstaltung wirklich als Entwurf speichern? Dadurch wird die Veranstaltung für die Öffentlichkeit unsichtbar."

#: src/components/layouts/Event/index.tsx:69
#: src/components/routes/event/EventDashboard/index.tsx:65
msgid "Are you sure you want to make this event public? This will make the event visible to the public"
msgstr "Möchten Sie diese Veranstaltung wirklich öffentlich machen? Dadurch wird die Veranstaltung für die Öffentlichkeit sichtbar"

#: src/components/common/EventCard/index.tsx:59
msgid "Are you sure you want to restore this event? It will be restored as a draft event."
msgstr "Sind Sie sicher, dass Sie diese Veranstaltung wiederherstellen möchten? Es wird als Entwurf wiederhergestellt."

#: src/components/common/CapacityAssignmentList/index.tsx:163
msgid "Are you sure you would like to delete this Capacity Assignment?"
msgstr "Sind Sie sicher, dass Sie diese Kapazitätszuweisung löschen möchten?"

#: src/components/common/CheckInListList/index.tsx:183
msgid "Are you sure you would like to delete this Check-In List?"
msgstr "Möchten Sie diese Eincheckliste wirklich löschen?"

#: src/components/forms/QuestionForm/index.tsx:90
#~ msgid "Ask once per attendee"
#~ msgstr "Einmal pro Teilnehmer fragen"

#: src/components/forms/QuestionForm/index.tsx:85
msgid "Ask once per order"
msgstr "Einmal pro Bestellung anfragen"

#: src/components/forms/QuestionForm/index.tsx:91
msgid "Ask once per product"
msgstr "Einmal pro Produkt fragen"

#: src/components/modals/CreateWebhookModal/index.tsx:33
msgid "At least one event type must be selected"
msgstr "Mindestens ein Ereignistyp muss ausgewählt werden"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Attendee"
msgstr "Teilnehmer"

#: src/components/forms/WebhookForm/index.tsx:94
msgid "Attendee Cancelled"
msgstr "Teilnehmer storniert"

#: src/components/forms/WebhookForm/index.tsx:82
msgid "Attendee Created"
msgstr "Teilnehmer erstellt"

#: src/components/modals/ManageAttendeeModal/index.tsx:147
msgid "Attendee Details"
msgstr "Teilnehmerdetails"

#: src/components/layouts/AuthLayout/index.tsx:73
msgid "Attendee Management"
msgstr "Teilnehmerverwaltung"

#: src/components/layouts/CheckIn/index.tsx:150
msgid "Attendee not found"
msgstr "Teilnehmer nicht gefunden"

#: src/components/modals/ManageAttendeeModal/index.tsx:153
msgid "Attendee Notes"
msgstr "Teilnehmernotizen"

#: src/components/common/QuestionsTable/index.tsx:369
msgid "Attendee questions"
msgstr "Fragen der Teilnehmer"

#: src/components/modals/ManageAttendeeModal/index.tsx:172
msgid "Attendee Ticket"
msgstr "Teilnehmer-Ticket"

#: src/components/forms/WebhookForm/index.tsx:88
msgid "Attendee Updated"
msgstr "Teilnehmer aktualisiert"

#: src/components/common/OrdersTable/index.tsx:206
#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
#: src/components/common/StatBoxes/index.tsx:27
#: src/components/layouts/Event/index.tsx:99
#: src/components/modals/ManageOrderModal/index.tsx:126
#: src/components/routes/event/attendees.tsx:59
msgid "Attendees"
msgstr "Teilnehmer"

#: src/components/routes/event/attendees.tsx:43
msgid "Attendees Exported"
msgstr "Teilnehmer exportiert"

#: src/components/routes/event/EventDashboard/index.tsx:239
msgid "Attendees Registered"
msgstr "Registrierte Teilnehmer"

#: src/components/modals/SendMessageModal/index.tsx:146
#~ msgid "Attendees with a specific product"
#~ msgstr "Teilnehmer mit einem bestimmten Produkt"

#: src/components/modals/SendMessageModal/index.tsx:183
msgid "Attendees with a specific ticket"
msgstr "Teilnehmer mit einem bestimmten Ticket"

#: src/components/common/WidgetEditor/index.tsx:233
msgid "Auto Resize"
msgstr "Automatische Größenanpassung"

#: src/components/layouts/AuthLayout/index.tsx:88
#~ msgid "Auto Workflow"
#~ msgstr "Automatisierter Arbeitsablauf"

#: src/components/layouts/AuthLayout/index.tsx:79
msgid "Automated entry management with multiple check-in lists and real-time validation"
msgstr "Automatisierte Einlassverwaltung mit mehreren Check-in-Listen und Echtzeitvalidierung"

#: src/components/common/WidgetEditor/index.tsx:236
msgid "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."
msgstr "Passen Sie die Widgethöhe automatisch an den Inhalt an. Wenn diese Option deaktiviert ist, füllt das Widget die Höhe des Containers aus."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
#~ msgid "Avg Discount/Order"
#~ msgstr "Durchschn. Rabatt/Bestellung"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
#~ msgid "Avg Order Value"
#~ msgstr "Durchschn. Bestellwert"

#: src/components/common/OrderStatusBadge/index.tsx:15
#: src/components/modals/SendMessageModal/index.tsx:231
msgid "Awaiting offline payment"
msgstr "Warten auf Offline-Zahlung"

#: src/components/routes/event/orders.tsx:25
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:43
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:66
msgid "Awaiting Offline Payment"
msgstr "Warten auf Offline-Zahlung"

#: src/components/layouts/CheckIn/index.tsx:263
msgid "Awaiting payment"
msgstr "Zahlung ausstehend"

#: src/components/common/AttendeeTicket/index.tsx:67
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:40
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:65
msgid "Awaiting Payment"
msgstr "Zahlung steht aus"

#: src/components/routes/welcome/index.tsx:84
msgid "Awesome Event"
msgstr "Tolles Event"

#: src/components/forms/OrganizerForm/index.tsx:28
msgid "Awesome Organizer Ltd."
msgstr "Awesome Organizer Ltd."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:42
msgid "Back to all events"
msgstr "Zurück zu allen Events"

#: src/components/layouts/Checkout/index.tsx:73
#: src/components/routes/product-widget/CollectInformation/index.tsx:249
#: src/components/routes/product-widget/CollectInformation/index.tsx:261
msgid "Back to event page"
msgstr "Zurück zur Veranstaltungsseite"

#: src/components/routes/auth/ForgotPassword/index.tsx:59
#: src/components/routes/auth/ResetPassword/index.tsx:66
msgid "Back to login"
msgstr "Zurück zur Anmeldung"

#: src/components/common/WidgetEditor/index.tsx:175
msgid "Background Color"
msgstr "Hintergrundfarbe"

#: src/components/routes/event/HomepageDesigner/index.tsx:144
msgid "Background Type"
msgstr "Hintergrundtyp"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#~ msgid "Basic Details"
#~ msgstr "Grundlegende Details"

#: src/components/modals/SendMessageModal/index.tsx:278
msgid "Before you send!"
msgstr "Bevor Sie versenden!"

#: src/components/routes/event/GettingStarted/index.tsx:60
#~ msgid "Before your event can go live, there are a few things you need to do."
#~ msgstr "Bevor Ihre Veranstaltung live gehen kann, müssen Sie einige Dinge erledigen."

#: src/components/routes/event/GettingStarted/index.tsx:92
msgid "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."
msgstr "Bevor dein Event live gehen kann, musst du einige Schritte erledigen. Schließe alle folgenden Schritte ab, um zu beginnen."

#: src/components/routes/auth/Register/index.tsx:66
#~ msgid "Begin selling products in minutes"
#~ msgstr "Beginnen Sie in wenigen Minuten mit dem Verkauf von Produkten"

#: src/components/routes/auth/Register/index.tsx:49
#~ msgid "Begin selling tickets in minutes"
#~ msgstr "Beginnen Sie in wenigen Minuten mit dem Ticketverkauf"

#: src/components/routes/product-widget/CollectInformation/index.tsx:313
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:142
msgid "Billing Address"
msgstr "Rechnungsadresse"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:167
msgid "Billing Settings"
msgstr "Rechnungseinstellungen"

#: src/components/layouts/AuthLayout/index.tsx:83
#~ msgid "Brand Control"
#~ msgstr "Markenkontrolle"

#: src/components/common/LanguageSwitcher/index.tsx:28
msgid "Brazilian Portuguese"
msgstr "Brasilianisches Portugiesisch"

#: src/components/routes/auth/Register/index.tsx:133
msgid "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."
msgstr "Mit der Registrierung stimmen Sie unseren <0>Servicebedingungen</0> und <1>Datenschutzrichtlinie</1> zu."

#: src/components/forms/TaxAndFeeForm/index.tsx:52
msgid "Calculation Type"
msgstr "Berechnungstyp"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:140
msgid "California"
msgstr "Kalifornien"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:175
msgid "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."
msgstr "Die Kameraberechtigung wurde verweigert. <0>Fordern Sie die Berechtigung erneut an</0>. Wenn dies nicht funktioniert, müssen Sie dieser Seite in Ihren Browsereinstellungen <1>Zugriff auf Ihre Kamera gewähren</1>."

#: src/components/common/AttendeeTable/index.tsx:182
#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/QuestionAndAnswerList/index.tsx:149
#: src/components/layouts/CheckIn/index.tsx:225
#: src/utilites/confirmationDialog.tsx:11
msgid "Cancel"
msgstr "Stornieren"

#: src/components/routes/profile/ManageProfile/index.tsx:126
msgid "Cancel email change"
msgstr "E-Mail-Änderung abbrechen"

#: src/components/common/OrdersTable/index.tsx:190
#: src/components/modals/RefundOrderModal/index.tsx:112
msgid "Cancel order"
msgstr "Bestellung stornieren"

#: src/components/modals/CancelOrderModal/index.tsx:59
msgid "Cancel Order"
msgstr "Bestellung stornieren"

#: src/components/modals/CancelOrderModal/index.tsx:44
msgid "Cancel Order {0}"
msgstr "Bestellung stornieren {0}"

#: src/components/common/AttendeeDetails/index.tsx:32
#~ msgid "Canceled"
#~ msgstr "Abgesagt"

#: src/components/modals/CancelOrderModal/index.tsx:54
msgid "Canceling will cancel all products associated with this order, and release the products back into the available pool."
msgstr "Das Stornieren wird alle mit dieser Bestellung verbundenen Produkte stornieren und die Produkte wieder in den verfügbaren Bestand zurückgeben."

#: src/components/modals/CancelOrderModal/index.tsx:54
#~ msgid "Canceling will cancel all tickets associated with this order, and release the tickets back into the available pool."
#~ msgstr "Durch die Stornierung werden alle mit dieser Bestellung verbundenen Tickets storniert und die Tickets werden wieder in den verfügbaren Pool freigegeben."

#: src/components/common/AttendeeTicket/index.tsx:61
#: src/components/common/OrderStatusBadge/index.tsx:12
#: src/components/routes/event/orders.tsx:24
msgid "Cancelled"
msgstr "Abgesagt"

#: src/components/layouts/CheckIn/index.tsx:173
msgid "Cannot Check In"
msgstr "Check-in nicht möglich"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:42
#: src/components/layouts/Event/index.tsx:103
msgid "Capacity"
msgstr "Kapazität"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:36
msgid "Capacity Assignment created successfully"
msgstr "Kapazitätszuweisung erfolgreich erstellt"

#: src/components/common/CapacityAssignmentList/index.tsx:31
msgid "Capacity Assignment deleted successfully"
msgstr "Kapazitätszuweisung erfolgreich gelöscht"

#: src/components/routes/event/CapacityAssignments/index.tsx:32
msgid "Capacity Management"
msgstr "Kapazitätsmanagement"

#: src/components/modals/CreateProductCategoryModal/index.tsx:56
msgid "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."
msgstr "Kategorien ermöglichen es Ihnen, Produkte zusammenzufassen. Zum Beispiel könnten Sie eine Kategorie für \"Tickets\" und eine andere für \"Merchandise\" haben."

#: src/components/forms/ProductForm/index.tsx:267
msgid "Categories help you organize your products. This title will be displayed on the public event page."
msgstr "Kategorien helfen Ihnen, Ihre Produkte zu organisieren. Dieser Titel wird auf der öffentlichen Veranstaltungsseite angezeigt."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:91
msgid "Categories reordered successfully."
msgstr "Kategorien erfolgreich neu geordnet."

#: src/components/routes/event/products.tsx:96
msgid "Category"
msgstr "Kategorie"

#: src/components/modals/CreateProductCategoryModal/index.tsx:37
msgid "Category Created Successfully"
msgstr "Kategorie erfolgreich erstellt"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Change Cover"
msgstr "Cover ändern"

#: src/components/routes/profile/ManageProfile/index.tsx:201
msgid "Change password"
msgstr "Kennwort ändern"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check in"
#~ msgstr "einchecken"

#: src/components/layouts/CheckIn/index.tsx:180
msgid "Check In"
msgstr "Einchecken"

#: src/components/layouts/CheckIn/index.tsx:193
msgid "Check in {0} {1}"
msgstr "Check-in {0} {1}"

#: src/components/layouts/CheckIn/index.tsx:218
msgid "Check in and mark order as paid"
msgstr "Einchecken und Bestellung als bezahlt markieren"

#: src/components/layouts/CheckIn/index.tsx:209
msgid "Check in only"
msgstr "Nur einchecken"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check out"
#~ msgstr "Kasse"

#: src/components/layouts/CheckIn/index.tsx:177
msgid "Check Out"
msgstr "Auschecken"

#: src/components/layouts/Checkout/index.tsx:106
msgid "Check out this event!"
msgstr "Schau dir dieses Event an!"

#: src/components/common/EventCard/index.tsx:156
msgid "Check-in"
msgstr "Einchecken"

#: src/components/layouts/Event/index.tsx:52
#~ msgid "Check-In"
#~ msgstr "Einchecken"

#: src/components/forms/WebhookForm/index.tsx:100
msgid "Check-in Created"
msgstr "Check-in erstellt"

#: src/components/forms/WebhookForm/index.tsx:106
msgid "Check-in Deleted"
msgstr "Check-in gelöscht"

#: src/components/modals/CreateCheckInListModal/index.tsx:40
msgid "Check-In List created successfully"
msgstr "Eincheckliste erfolgreich erstellt"

#: src/components/common/CheckInListList/index.tsx:33
msgid "Check-In List deleted successfully"
msgstr "Eincheckliste erfolgreich gelöscht"

#: src/components/layouts/CheckIn/index.tsx:326
msgid "Check-in list has expired"
msgstr "Die Eincheckliste ist abgelaufen"

#: src/components/layouts/CheckIn/index.tsx:343
msgid "Check-in list is not active"
msgstr "Die Eincheckliste ist nicht aktiv"

#: src/components/layouts/CheckIn/index.tsx:311
msgid "Check-in list not found"
msgstr "Eincheckliste nicht gefunden"

#: src/components/layouts/Event/index.tsx:104
#: src/components/routes/event/CheckInLists/index.tsx:32
msgid "Check-In Lists"
msgstr "Einchecklisten"

#: src/components/common/CheckInListList/index.tsx:162
msgid "Check-In URL copied to clipboard"
msgstr "Eincheck-URL in die Zwischenablage kopiert"

#: src/components/forms/QuestionForm/index.tsx:114
msgid "Checkbox options allow multiple selections"
msgstr "Kontrollkästchenoptionen ermöglichen Mehrfachauswahl"

#: src/components/forms/QuestionForm/index.tsx:112
msgid "Checkboxes"
msgstr "Kontrollkästchen"

#: src/components/common/AttendeeDetails/index.tsx:29
msgid "Checked In"
msgstr "Eingecheckt"

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "Checked in successfully"
#~ msgstr "Erfolgreich eingecheckt"

#: src/components/layouts/Checkout/index.tsx:83
#: src/components/routes/event/Settings/index.tsx:40
msgid "Checkout"
msgstr "Zur Kasse"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:61
msgid "Checkout Settings"
msgstr "Checkout-Einstellungen"

#: src/locales.ts:46
#~ msgid "Chinese"
#~ msgstr "Chinesisch"

#: src/components/common/LanguageSwitcher/index.tsx:30
msgid "Chinese (Simplified)"
msgstr "Vereinfachtes Chinesisch"

#: src/components/common/LanguageSwitcher/index.tsx:32
msgid "Chinese (Traditional)"
msgstr "Chinesisch (Traditionell)"

#: src/components/routes/event/HomepageDesigner/index.tsx:133
msgid "Choose a color for your background"
msgstr "Wählen Sie eine Farbe für Ihren Hintergrund"

#: src/components/modals/ChooseAccountModal/index.tsx:13
msgid "Choose an account"
msgstr "Wähle einen Account"

#: src/components/common/CheckoutQuestion/index.tsx:162
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:134
#: src/components/routes/product-widget/CollectInformation/index.tsx:333
#: src/components/routes/product-widget/CollectInformation/index.tsx:334
msgid "City"
msgstr "Stadt"

#: src/components/common/SearchBar/index.tsx:75
msgid "Clear Search Text"
msgstr "Suchtext löschen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:288
#~ msgid "click here"
#~ msgstr "klicken Sie hier"

#: src/components/common/PromoCodeTable/index.tsx:98
msgid "Click to copy"
msgstr "Zum Kopieren klicken"

#: src/components/routes/product-widget/SelectProducts/index.tsx:533
msgid "close"
msgstr "schließen"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:186
#: src/components/modals/RefundOrderModal/index.tsx:125
#: src/components/routes/product-widget/SelectProducts/index.tsx:534
msgid "Close"
msgstr "Schließen"

#: src/components/layouts/Event/index.tsx:281
msgid "Close sidebar"
msgstr "Sidebar schließen"

#: src/components/common/PromoCodeTable/index.tsx:69
#: src/components/forms/PromoCodeForm/index.tsx:34
msgid "Code"
msgstr "Code"

#: src/components/modals/CreatePromoCodeModal/index.tsx:26
#: src/components/modals/EditPromoCodeModal/index.tsx:38
msgid "Code must be between 3 and 50 characters long"
msgstr "Der Code muss zwischen 3 und 50 Zeichen lang sein"

#: src/components/forms/ProductForm/index.tsx:401
msgid "Collapse this product when the event page is initially loaded"
msgstr "Dieses Produkt einklappen, wenn die Veranstaltungsseite initial geladen wird"

#: src/components/forms/TicketForm/index.tsx:346
#~ msgid "Collapse this ticket when the event page is initially loaded"
#~ msgstr "Dieses Ticket beim ersten Laden der Veranstaltungsseite minimieren"

#: src/components/routes/event/HomepageDesigner/index.tsx:131
msgid "Color"
msgstr "Farbe"

#: src/components/common/WidgetEditor/index.tsx:21
msgid "Color must be a valid hex color code. Example: #ffffff"
msgstr "Die Farbe muss ein gültiger Hex-Farbcode sein. Beispiel: #ffffff"

#: src/components/common/WidgetEditor/index.tsx:166
#: src/components/routes/event/HomepageDesigner/index.tsx:124
msgid "Colors"
msgstr "Farben"

#: src/components/layouts/Event/index.tsx:158
msgid "Coming Soon"
msgstr "Demnächst"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
msgid "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"
msgstr "Durch Kommas getrennte Schlüsselwörter, die das Ereignis beschreiben. Diese werden von Suchmaschinen verwendet, um das Ereignis zu kategorisieren und zu indizieren."

#: src/components/routes/product-widget/CollectInformation/index.tsx:453
msgid "Complete Order"
msgstr "Bestellung abschließen"

#: src/components/routes/product-widget/CollectInformation/index.tsx:223
msgid "Complete payment"
msgstr "Jetzt bezahlen"

#: src/components/routes/product-widget/Payment/index.tsx:131
msgid "Complete Payment"
msgstr "Jetzt bezahlen"

#: src/components/layouts/AuthLayout/index.tsx:68
#~ msgid "Complete Store"
#~ msgstr "Vollständiger Shop"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:202
#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Complete Stripe Setup"
msgstr "Stripe-Einrichtung abschließen"

#: src/components/routes/event/EventDashboard/index.tsx:127
msgid "Complete these steps to start selling tickets for your event."
msgstr "Schließen Sie diese Schritte ab, um mit dem Ticketverkauf für Ihre Veranstaltung zu beginnen."

#: src/components/modals/SendMessageModal/index.tsx:230
#: src/components/routes/event/GettingStarted/index.tsx:70
#: src/components/routes/event/orders.tsx:23
msgid "Completed"
msgstr "Vollendet"

#: src/components/common/StatBoxes/index.tsx:51
msgid "Completed orders"
msgstr "Abgeschlossene Bestellungen"

#: src/components/routes/event/EventDashboard/index.tsx:237
msgid "Completed Orders"
msgstr "Abgeschlossene Bestellungen"

#: src/components/common/WidgetEditor/index.tsx:287
msgid "Component Code"
msgstr "Komponentencode"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:35
msgid "Configured Discount"
msgstr "Konfigurierter Rabatt"

#: src/utilites/confirmationDialog.tsx:11
msgid "Confirm"
msgstr "Bestätigen"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:51
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:56
msgid "Confirm Email Change"
msgstr "E-Mail-Änderung bestätigen"

#: src/components/routes/profile/ManageProfile/index.tsx:199
msgid "Confirm New Password"
msgstr "Bestätige neues Passwort"

#: src/components/routes/auth/Register/index.tsx:115
msgid "Confirm password"
msgstr "Bestätige das Passwort"

#: src/components/routes/auth/AcceptInvitation/index.tsx:112
#: src/components/routes/auth/Register/index.tsx:114
#: src/components/routes/auth/ResetPassword/index.tsx:60
msgid "Confirm Password"
msgstr "Bestätige das Passwort"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:36
msgid "Confirming email address..."
msgstr "E-Mail-Adresse wird bestätigt …"

#: src/components/routes/event/GettingStarted/index.tsx:88
msgid "Congratulations on creating an event!"
msgstr "Herzlichen Glückwunsch zur Erstellung eines Events!"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:173
msgid "Connect Documentation"
msgstr "Verbindungsdokumentation"

#: src/components/routes/event/EventDashboard/index.tsx:192
msgid "Connect payment processing"
msgstr "Zahlungsabwicklung verbinden"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:52
#~ msgid "Connect Stripe"
#~ msgstr "Stripe verbinden"

#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Connect to Stripe"
msgstr "Mit Stripe verbinden"

#: src/components/layouts/AuthLayout/index.tsx:89
msgid "Connect with CRM and automate tasks using webhooks and integrations"
msgstr "Verbinden Sie sich mit dem CRM und automatisieren Sie Aufgaben mit Webhooks und Integrationen"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:201
#: src/components/routes/event/GettingStarted/index.tsx:154
msgid "Connect with Stripe"
msgstr "Mit Stripe verbinden"

#: src/components/routes/event/GettingStarted/index.tsx:150
msgid "Connect your Stripe account to start receiving payments."
msgstr "Verbinden Sie Ihr Stripe-Konto, um Zahlungen zu empfangen."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:148
msgid "Connected to Stripe"
msgstr "Mit Stripe verbunden"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:98
msgid "Connection Details"
msgstr "Verbindungsdetails"

#: src/components/modals/SendMessageModal/index.tsx:167
msgid "Contact Support"
msgstr "Support kontaktieren"

#: src/components/modals/SendMessageModal/index.tsx:158
msgid "Contact us to enable messaging"
msgstr "Kontaktieren Sie uns, um Nachrichten zu aktivieren"

#: src/components/routes/event/HomepageDesigner/index.tsx:155
msgid "Content background color"
msgstr "Hintergrundfarbe des Inhalts"

#: src/components/common/WidgetEditor/index.tsx:31
#: src/components/common/WidgetEditor/index.tsx:217
#: src/components/layouts/Checkout/CheckoutFooter/index.tsx:38
#: src/components/routes/product-widget/CollectInformation/index.tsx:444
#: src/components/routes/product-widget/SelectProducts/index.tsx:486
msgid "Continue"
msgstr "Weitermachen"

#: src/components/routes/event/HomepageDesigner/index.tsx:164
msgid "Continue button text"
msgstr "Text der Schaltfläche „Weiter“"

#: src/components/common/WidgetEditor/index.tsx:216
msgid "Continue Button Text"
msgstr "Text der Schaltfläche „Weiter“"

#: src/components/modals/CreateEventModal/index.tsx:153
msgid "Continue Event Setup"
msgstr "Mit der Ereigniseinrichtung fortfahren"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Continue set up"
msgstr "Weiter einrichten"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
#~ msgid "Continue Stripe Connect Setup"
#~ msgstr "Mit der Einrichtung von Stripe Connect fortfahren"

#: src/components/routes/product-widget/SelectProducts/index.tsx:337
msgid "Continue to Checkout"
msgstr "Weiter zur Kasse"

#: src/components/routes/product-widget/CollectInformation/index.tsx:380
#~ msgid "Continue To Payment"
#~ msgstr "Weiter zur Zahlung"

#: src/components/common/AttendeeTicket/index.tsx:91
#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:243
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copied"
msgstr "Kopiert"

#: src/components/common/PromoCodeTable/index.tsx:106
msgid "copied to clipboard"
msgstr "in die Zwischenablage kopiert"

#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copy"
msgstr "Kopieren"

#: src/components/common/CheckInListList/index.tsx:156
msgid "Copy Check-In URL"
msgstr "Eincheck-URL kopieren"

#: src/components/routes/product-widget/CollectInformation/index.tsx:306
msgid "Copy details to all attendees"
msgstr "Details auf alle Teilnehmer anwenden"

#: src/components/common/AttendeeTicket/index.tsx:91
msgid "Copy Link"
msgstr "Link kopieren"

#: src/components/common/PromoCodeTable/index.tsx:177
#: src/components/modals/ShareModal/index.tsx:243
msgid "Copy URL"
msgstr "URL kopieren"

#: src/components/common/CheckoutQuestion/index.tsx:174
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:152
#: src/components/routes/product-widget/CollectInformation/index.tsx:354
msgid "Country"
msgstr "Land"

#: src/components/routes/event/HomepageDesigner/index.tsx:117
msgid "Cover"
msgstr "Abdeckung"

#: src/components/routes/event/attendees.tsx:71
#: src/components/routes/event/products.tsx:74
msgid "Create"
msgstr "Erstellen"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
msgid "Create {0}"
msgstr "Erstellen Sie {0}"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:59
msgid "Create a Product"
msgstr "Ein Produkt erstellen"

#: src/components/common/PromoCodeTable/index.tsx:57
msgid "Create a Promo Code"
msgstr "Einen Promo-Code erstellen"

#: src/components/modals/CreateCheckInListModal/index.tsx:63
msgid "Create a Ticket"
msgstr "Ticket erstellen"

#: src/components/routes/auth/Register/index.tsx:69
msgid "Create an account or <0>{0}</0> to get started"
msgstr "Erstellen Sie ein Konto oder <0>{0}</0>, um loszulegen"

#: src/components/modals/CreateEventModal/index.tsx:120
msgid "create an organizer"
msgstr "einen Organizer erstellen"

#: src/components/layouts/AuthLayout/index.tsx:27
msgid "Create and customize your event page instantly"
msgstr "Erstellen und personalisieren Sie Ihre Veranstaltungsseite sofort"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Create Attendee"
msgstr "Teilnehmer erstellen"

#: src/components/common/CapacityAssignmentList/index.tsx:63
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:68
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:79
#: src/components/routes/event/CapacityAssignments/index.tsx:46
msgid "Create Capacity Assignment"
msgstr "Kapazitätszuweisung erstellen"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:128
#: src/components/common/ProductsTable/SortableCategory/index.tsx:129
msgid "Create category"
msgstr "Kategorie erstellen"

#: src/components/modals/CreateProductCategoryModal/index.tsx:45
#: src/components/modals/CreateProductCategoryModal/index.tsx:60
msgid "Create Category"
msgstr "Kategorie erstellen"

#: src/components/common/CheckInListList/index.tsx:61
#: src/components/modals/CreateCheckInListModal/index.tsx:72
#: src/components/modals/CreateCheckInListModal/index.tsx:82
#: src/components/routes/event/CheckInLists/index.tsx:46
msgid "Create Check-In List"
msgstr "Eincheckliste erstellen"

#: src/components/common/NoEventsBlankSlate/index.tsx:27
#: src/components/modals/CreateEventModal/index.tsx:85
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:81
#: src/components/routes/welcome/index.tsx:108
msgid "Create Event"
msgstr "Ereignis erstellen"

#: src/components/routes/events/Dashboard/index.tsx:91
msgid "Create new"
msgstr "Neu erstellen"

#: src/components/forms/OrganizerForm/index.tsx:111
#: src/components/modals/CreateEventModal/index.tsx:93
#: src/components/modals/CreateOrganizerModal/index.tsx:16
msgid "Create Organizer"
msgstr "Organizer erstellen"

#: src/components/modals/CreateProductModal/index.tsx:80
#: src/components/modals/CreateProductModal/index.tsx:88
msgid "Create Product"
msgstr "Produkt erstellen"

#: src/components/routes/event/GettingStarted/index.tsx:70
#~ msgid "Create products for your event, set prices, and manage available quantity."
#~ msgstr "Erstellen Sie Produkte für Ihre Veranstaltung, legen Sie Preise fest und verwalten Sie die verfügbare Menge."

#: src/components/modals/CreatePromoCodeModal/index.tsx:51
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
msgid "Create Promo Code"
msgstr "Promo-Code erstellen"

#: src/components/modals/CreateQuestionModal/index.tsx:69
#: src/components/modals/CreateQuestionModal/index.tsx:74
msgid "Create Question"
msgstr "Frage erstellen"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:41
msgid "Create Tax or Fee"
msgstr "Steuer oder Gebühr erstellen"

#: src/components/modals/CreateTicketModal/index.tsx:83
#: src/components/modals/CreateTicketModal/index.tsx:91
#: src/components/routes/event/tickets.tsx:50
#~ msgid "Create Ticket"
#~ msgstr "Ticket erstellen"

#: src/components/routes/event/GettingStarted/index.tsx:122
msgid "Create tickets for your event, set prices, and manage available quantity."
msgstr "Erstellen Sie Tickets für Ihre Veranstaltung, legen Sie Preise fest und verwalten Sie die verfügbare Menge."

#: src/components/modals/CreateWebhookModal/index.tsx:57
#: src/components/modals/CreateWebhookModal/index.tsx:67
msgid "Create Webhook"
msgstr "Webhook erstellen"

#: src/components/common/OrdersTable/index.tsx:208
#: src/components/common/OrdersTable/index.tsx:281
msgid "Created"
msgstr "Erstellt"

#: src/components/forms/OrganizerForm/index.tsx:45
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:73
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:100
msgid "Currency"
msgstr "Währung"

#: src/components/routes/profile/ManageProfile/index.tsx:191
msgid "Current Password"
msgstr "Aktuelles Passwort"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:159
msgid "Custom Maps URL"
msgstr "Benutzerdefinierte Karten-URL"

#: src/components/common/ReportTable/index.tsx:53
msgid "Custom Range"
msgstr "Benutzerdefinierter Bereich"

#: src/components/common/OrdersTable/index.tsx:205
msgid "Customer"
msgstr "Kunde"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:55
msgid "Customize the email and notification settings for this event"
msgstr "Passen Sie die E-Mail- und Benachrichtigungseinstellungen für dieses Ereignis an"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:62
msgid "Customize the event homepage and checkout messaging"
msgstr "Passen Sie die Startseite der Veranstaltung und die Nachrichten an der Kasse an"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:70
msgid "Customize the miscellaneous settings for this event"
msgstr "Passen Sie die sonstigen Einstellungen für dieses Ereignis an"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:57
msgid "Customize the SEO settings for this event"
msgstr "Passen Sie die SEO-Einstellungen für dieses Event an"

#: src/components/routes/event/GettingStarted/index.tsx:169
msgid "Customize your event page"
msgstr "Passen Sie Ihre Veranstaltungsseite an"

#: src/components/layouts/AuthLayout/index.tsx:84
msgid "Customize your event page and widget design to match your brand perfectly"
msgstr "Passen Sie Ihre Veranstaltungsseite und das Widget-Design perfekt an Ihre Marke an"

#: src/components/routes/event/GettingStarted/index.tsx:165
msgid "Customize your event page to match your brand and style."
msgstr "Passen Sie Ihre Veranstaltungsseite an Ihre Marke und Ihren Stil an."

#: src/components/routes/event/Reports/index.tsx:23
msgid "Daily Sales Report"
msgstr "Täglicher Verkaufsbericht"

#: src/components/routes/event/Reports/index.tsx:24
msgid "Daily sales, tax, and fee breakdown"
msgstr "Aufschlüsselung der täglichen Verkäufe, Steuern und Gebühren"

#: src/components/common/CapacityAssignmentList/index.tsx:156
#: src/components/common/CheckInListList/index.tsx:176
#: src/components/common/OrdersTable/index.tsx:186
#: src/components/common/ProductsTable/SortableProduct/index.tsx:287
#: src/components/common/PromoCodeTable/index.tsx:181
#: src/components/common/QuestionsTable/index.tsx:115
#: src/components/common/WebhookTable/index.tsx:116
msgid "Danger zone"
msgstr "Gefahrenzone"

#: src/components/common/AttendeeTable/index.tsx:179
msgid "Danger Zone"
msgstr "Gefahrenzone"

#: src/components/layouts/Event/index.tsx:89
msgid "Dashboard"
msgstr "Dashboard"

#: src/components/common/OrderDetails/index.tsx:39
#: src/components/forms/QuestionForm/index.tsx:136
msgid "Date"
msgstr "Datum"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:40
#~ msgid "Date & Time"
#~ msgstr "Datum & Uhrzeit"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:38
msgid "Day one capacity"
msgstr "Kapazität am ersten Tag"

#: src/components/forms/CheckInListForm/index.tsx:21
#~ msgid "Day one check-in list"
#~ msgstr "Tag eins Eincheckliste"

#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/TaxAndFeeList/index.tsx:73
msgid "Delete"
msgstr "Löschen"

#: src/components/common/CapacityAssignmentList/index.tsx:159
msgid "Delete Capacity"
msgstr "Kapazität löschen"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:146
#: src/components/common/ProductsTable/SortableCategory/index.tsx:147
msgid "Delete category"
msgstr "Kategorie löschen"

#: src/components/common/CheckInListList/index.tsx:179
msgid "Delete Check-In List"
msgstr "Eincheckliste löschen"

#: src/components/common/PromoCodeTable/index.tsx:185
msgid "Delete code"
msgstr "Code löschen"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:113
msgid "Delete Cover"
msgstr "Cover löschen"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:98
msgid "Delete Image"
msgstr "Lösche Bild"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:292
msgid "Delete product"
msgstr "Produkt löschen"

#: src/components/common/QuestionsTable/index.tsx:120
msgid "Delete question"
msgstr "Frage löschen"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:199
#~ msgid "Delete ticket"
#~ msgstr "Ticket löschen"

#: src/components/common/WebhookTable/index.tsx:127
msgid "Delete webhook"
msgstr "Webhook löschen"

#: src/components/forms/ProductForm/index.tsx:258
#: src/components/forms/TaxAndFeeForm/index.tsx:81
#: src/components/modals/CreateEventModal/index.tsx:135
#: src/components/modals/DuplicateEventModal/index.tsx:84
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:78
msgid "Description"
msgstr "Beschreibung"

#: src/components/forms/CheckInListForm/index.tsx:36
msgid "Description for check-in staff"
msgstr "Beschreibung für das Eincheckpersonal"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Details"
msgstr "Einzelheiten"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
#~ msgid "Disable this capacity track capacity without stopping product sales"
#~ msgstr "Deaktivieren Sie diese Kapazität, um Kapazitäten zu verfolgen, ohne den Produktverkauf zu stoppen"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:26
#~ msgid "Disable this capacity track capacity without stopping ticket sales"
#~ msgstr "Deaktivieren Sie diese Kapazitätsverfolgung ohne den Ticketverkauf zu stoppen"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
msgid "Disabling this capacity will track sales but not stop them when the limit is reached"
msgstr "Das Deaktivieren dieser Kapazität wird Verkäufe verfolgen, aber nicht stoppen, wenn das Limit erreicht ist"

#: src/components/common/PromoCodeTable/index.tsx:70
msgid "Discount"
msgstr "Rabatt"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount %"
msgstr "Rabatt %"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount in {0}"
msgstr "Rabatt in {0}"

#: src/components/forms/PromoCodeForm/index.tsx:43
msgid "Discount Type"
msgstr "Rabattart"

#: src/components/routes/product-widget/SelectProducts/index.tsx:297
#~ msgid "Dismiss"
#~ msgstr "Zurückweisen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:354
msgid "Dismiss this message"
msgstr "Diese Nachricht schließen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:190
msgid "Document Label"
msgstr "Dokumentenbeschriftung"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:222
msgid "Documentation"
msgstr "Dokumentation"

#: src/components/layouts/AuthLayout/index.tsx:19
#~ msgid "Does not exist"
#~ msgstr "Existiert nicht"

#: src/components/routes/auth/Login/index.tsx:57
msgid "Don't have an account?   <0>Sign up</0>"
msgstr "Sie haben kein Konto?   <0>Registrieren</0>"

#: src/components/routes/auth/Login/index.tsx:72
#~ msgid "Don't have an account?   <0>Sign Up</0>"
#~ msgstr "Sie haben noch kein Konto? <0>Registrieren</0>"

#: src/components/forms/ProductForm/index.tsx:157
msgid "Donation / Pay what you'd like product"
msgstr "Spende / Produkt mit freier Preiswahl"

#: src/components/forms/TicketForm/index.tsx:139
#~ msgid "Donation / Pay what you'd like ticket"
#~ msgstr "Spende / Bezahlen Sie, was Sie möchten Ticket"

#: src/components/common/AddEventToCalendarButton/index.tsx:137
msgid "Download .ics"
msgstr ".ics herunterladen"

#: src/components/common/DownloadCsvButton/index.tsx:48
msgid "Download CSV"
msgstr "CSV herunterladen"

#: src/components/common/OrdersTable/index.tsx:162
msgid "Download invoice"
msgstr "Rechnung herunterladen"

#: src/components/layouts/Checkout/index.tsx:126
msgid "Download Invoice"
msgstr "Rechnung herunterladen"

#: src/components/modals/ShareModal/index.tsx:224
msgid "Download QR Code"
msgstr "QR-Code herunterladen"

#: src/components/common/OrdersTable/index.tsx:111
#: src/components/layouts/Checkout/index.tsx:49
msgid "Downloading Invoice"
msgstr "Rechnung wird heruntergeladen"

#: src/components/layouts/Event/index.tsx:188
msgid "Draft"
msgstr "Entwurf"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:83
msgid "Drag and drop or click"
msgstr "Ziehen und ablegen oder klicken"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Drag to sort"
#~ msgstr "Zum Sortieren ziehen"

#: src/components/forms/QuestionForm/index.tsx:124
msgid "Dropdown selection"
msgstr "Dropdown-Auswahl"

#: src/components/modals/SendMessageModal/index.tsx:159
msgid ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."
msgstr ""
"Aufgrund des hohen Spamrisikos ist eine manuelle Verifizierung erforderlich, bevor du Nachrichten senden kannst.\n"
"Bitte kontaktiere uns, um Zugang zu beantragen."

#: src/components/modals/DuplicateEventModal/index.tsx:124
msgid "Duplicate Capacity Assignments"
msgstr "Duplizieren Sie Kapazitätszuweisungen"

#: src/components/modals/DuplicateEventModal/index.tsx:128
msgid "Duplicate Check-In Lists"
msgstr "Duplizieren Sie Check-in-Listen"

#: src/components/common/EventCard/index.tsx:162
msgid "Duplicate event"
msgstr "Ereignis duplizieren"

#: src/components/modals/DuplicateEventModal/index.tsx:69
#: src/components/modals/DuplicateEventModal/index.tsx:142
msgid "Duplicate Event"
msgstr "Ereignis duplizieren"

#: src/components/modals/DuplicateEventModal/index.tsx:132
msgid "Duplicate Event Cover Image"
msgstr "Duplizieren Sie das Titelbild des Ereignisses"

#: src/components/modals/DuplicateEventModal/index.tsx:103
msgid "Duplicate Options"
msgstr "Optionen duplizieren"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:285
#: src/components/modals/DuplicateProductModal/index.tsx:109
#: src/components/modals/DuplicateProductModal/index.tsx:113
msgid "Duplicate Product"
msgstr "Produkt duplizieren"

#: src/components/modals/DuplicateEventModal/index.tsx:108
msgid "Duplicate Products"
msgstr "Produkte duplizieren"

#: src/components/modals/DuplicateEventModal/index.tsx:120
msgid "Duplicate Promo Codes"
msgstr "Promo-Codes duplizieren"

#: src/components/modals/DuplicateEventModal/index.tsx:112
msgid "Duplicate Questions"
msgstr "Fragen duplizieren"

#: src/components/modals/DuplicateEventModal/index.tsx:116
msgid "Duplicate Settings"
msgstr "Einstellungen duplizieren"

#: src/components/modals/DuplicateEventModal/index.tsx:107
#~ msgid "Duplicate Tickets"
#~ msgstr "Tickets duplizieren"

#: src/components/modals/DuplicateEventModal/index.tsx:137
msgid "Duplicate Webhooks"
msgstr "Webhooks duplizieren"

#: src/components/common/LanguageSwitcher/index.tsx:24
msgid "Dutch"
msgstr "Niederländisch"

#: src/components/forms/ProductForm/index.tsx:78
msgid "Early bird"
msgstr "Früher Vogel"

#: src/components/common/TaxAndFeeList/index.tsx:65
#: src/components/modals/ManageAttendeeModal/index.tsx:221
#: src/components/modals/ManageOrderModal/index.tsx:203
msgid "Edit"
msgstr "Bearbeiten"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
msgid "Edit {0}"
msgstr "Bearbeiten {0}"

#: src/components/common/QuestionAndAnswerList/index.tsx:159
msgid "Edit Answer"
msgstr "Antwort bearbeiten"

#: src/components/common/AttendeeTable/index.tsx:174
#~ msgid "Edit attendee"
#~ msgstr "Teilnehmer bearbeiten"

#: src/components/modals/EditAttendeeModal/index.tsx:72
#: src/components/modals/EditAttendeeModal/index.tsx:110
#~ msgid "Edit Attendee"
#~ msgstr "Teilnehmer bearbeiten"

#: src/components/common/CapacityAssignmentList/index.tsx:146
msgid "Edit Capacity"
msgstr "Kapazität bearbeiten"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:66
#: src/components/modals/EditCapacityAssignmentModal/index.tsx:74
msgid "Edit Capacity Assignment"
msgstr "Kapazitätszuweisung bearbeiten"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:137
#: src/components/common/ProductsTable/SortableCategory/index.tsx:138
msgid "Edit category"
msgstr "Kategorie bearbeiten"

#: src/components/common/CheckInListList/index.tsx:148
#: src/components/modals/EditCheckInListModal/index.tsx:69
#: src/components/modals/EditCheckInListModal/index.tsx:91
msgid "Edit Check-In List"
msgstr "Eincheckliste bearbeiten"

#: src/components/common/PromoCodeTable/index.tsx:170
msgid "Edit Code"
msgstr "Code bearbeiten"

#: src/components/modals/EditOrganizerModal/index.tsx:63
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:51
msgid "Edit Organizer"
msgstr "Organisator bearbeiten"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:280
#: src/components/modals/EditProductModal/index.tsx:100
#: src/components/modals/EditProductModal/index.tsx:108
msgid "Edit Product"
msgstr "Produkt bearbeiten"

#: src/components/modals/EditProductCategoryModal/index.tsx:62
#: src/components/modals/EditProductCategoryModal/index.tsx:70
msgid "Edit Product Category"
msgstr "Produktkategorie bearbeiten"

#: src/components/modals/EditPromoCodeModal/index.tsx:79
#: src/components/modals/EditPromoCodeModal/index.tsx:84
msgid "Edit Promo Code"
msgstr "Aktionscode bearbeiten"

#: src/components/common/QuestionsTable/index.tsx:112
msgid "Edit question"
msgstr "Frage bearbeiten"

#: src/components/modals/EditQuestionModal/index.tsx:95
#: src/components/modals/EditQuestionModal/index.tsx:101
msgid "Edit Question"
msgstr "Frage bearbeiten"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:191
#: src/components/modals/EditTicketModal/index.tsx:96
#: src/components/modals/EditTicketModal/index.tsx:104
#~ msgid "Edit Ticket"
#~ msgstr "Ticket bearbeiten"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Edit user"
msgstr "Benutzer bearbeiten"

#: src/components/modals/EditUserModal/index.tsx:62
#: src/components/modals/EditUserModal/index.tsx:120
msgid "Edit User"
msgstr "Benutzer bearbeiten"

#: src/components/common/WebhookTable/index.tsx:104
msgid "Edit webhook"
msgstr "Webhook bearbeiten"

#: src/components/modals/EditWebhookModal/index.tsx:66
#: src/components/modals/EditWebhookModal/index.tsx:87
msgid "Edit Webhook"
msgstr "Webhook bearbeiten"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 2.50 for $2.50"
msgstr "z.B. 2,50 für 2,50 $"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 23.5 for 23.5%"
msgstr "z.B. 23,5 für 23,5 %"

#: src/components/common/AttendeeDetails/index.tsx:21
#: src/components/common/AttendeeTable/index.tsx:109
#: src/components/common/OrderDetails/index.tsx:31
#: src/components/forms/OrganizerForm/index.tsx:32
#: src/components/modals/EditUserModal/index.tsx:80
#: src/components/modals/InviteUserModal/index.tsx:61
#: src/components/routes/auth/AcceptInvitation/index.tsx:98
#: src/components/routes/auth/Login/index.tsx:68
#: src/components/routes/auth/Register/index.tsx:97
#: src/components/routes/event/Settings/index.tsx:52
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:117
#: src/components/routes/profile/ManageProfile/index.tsx:138
msgid "Email"
msgstr "Email"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
msgid "Email & Notification Settings"
msgstr "E-Mail- und Benachrichtigungseinstellungen"

#: src/components/modals/CreateAttendeeModal/index.tsx:132
#: src/components/modals/ManageAttendeeModal/index.tsx:114
#: src/components/modals/ManageOrderModal/index.tsx:157
msgid "Email address"
msgstr "E-Mail-Adresse"

#: src/components/common/QuestionsTable/index.tsx:220
#: src/components/common/QuestionsTable/index.tsx:221
#: src/components/routes/product-widget/CollectInformation/index.tsx:298
#: src/components/routes/product-widget/CollectInformation/index.tsx:299
#: src/components/routes/product-widget/CollectInformation/index.tsx:408
#: src/components/routes/product-widget/CollectInformation/index.tsx:409
msgid "Email Address"
msgstr "E-Mail-Adresse"

#: src/components/routes/profile/ManageProfile/index.tsx:77
msgid "Email change cancelled successfully"
msgstr "E-Mail-Änderung erfolgreich abgebrochen"

#: src/components/routes/profile/ManageProfile/index.tsx:116
msgid "Email change pending"
msgstr "E-Mail-Änderung ausstehend"

#: src/components/routes/profile/ManageProfile/index.tsx:151
msgid "Email confirmation resent"
msgstr "E-Mail-Bestätigung erneut gesendet"

#: src/components/routes/profile/ManageProfile/index.tsx:90
msgid "Email confirmation resent successfully"
msgstr "E-Mail-Bestätigung erfolgreich erneut gesendet"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:66
msgid "Email footer message"
msgstr "E-Mail-Fußzeilennachricht"

#: src/components/routes/profile/ManageProfile/index.tsx:141
msgid "Email not verified"
msgstr "E-Mail nicht verifiziert"

#: src/components/common/WidgetEditor/index.tsx:272
msgid "Embed Code"
msgstr "Code einbetten"

#: src/components/common/WidgetEditor/index.tsx:260
msgid "Embed Script"
msgstr "Skript einbetten"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:181
msgid "Enable Invoicing"
msgstr "Rechnungsstellung aktivieren"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:21
msgid "Enable this capacity to stop product sales when the limit is reached"
msgstr "Aktivieren Sie diese Kapazität, um den Produktverkauf zu stoppen, wenn das Limit erreicht ist"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:20
#~ msgid "Enable this capacity to stop ticket sales when the limit is reached"
#~ msgstr "Aktivieren Sie diese Kapazität, um den Ticketverkauf zu stoppen, wenn das Limit erreicht ist"

#: src/components/forms/WebhookForm/index.tsx:19
msgid "Enabled"
msgstr "Aktiviert"

#: src/components/modals/CreateEventModal/index.tsx:149
#: src/components/modals/DuplicateEventModal/index.tsx:98
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:92
#: src/components/routes/welcome/index.tsx:96
msgid "End Date"
msgstr "Endtermin"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:26
#: src/components/common/EventStatusBadge/index.tsx:14
msgid "Ended"
msgstr "Beendet"

#: src/components/routes/events/Dashboard/index.tsx:57
msgid "Ended Events"
msgstr "Beendete Veranstaltungen"

#: src/components/common/LanguageSwitcher/index.tsx:16
#: src/components/common/LanguageSwitcher/index.tsx:50
#: src/components/modals/CreateAttendeeModal/index.tsx:145
#: src/components/routes/profile/ManageProfile/index.tsx:173
msgid "English"
msgstr "Englisch"

#: src/components/modals/CreateAttendeeModal/index.tsx:170
msgid "Enter an amount excluding taxes and fees."
msgstr "Geben Sie einen Betrag ohne Steuern und Gebühren ein."

#: src/components/common/OrdersTable/index.tsx:119
#: src/components/layouts/Checkout/index.tsx:57
msgid "Error"
msgstr "Fehler"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:27
msgid "Error confirming email address"
msgstr "Fehler beim Bestätigen der E-Mail-Adresse"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:35
msgid "Error confirming email change"
msgstr "Fehler beim Bestätigen der E-Mail-Änderung"

#: src/components/modals/WebhookLogsModal/index.tsx:166
msgid "Error loading logs"
msgstr "Fehler beim Laden der Protokolle"

#: src/components/forms/OrganizerForm/index.tsx:46
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:74
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:101
msgid "EUR"
msgstr "EUR"

#: src/components/routes/events/Dashboard/index.tsx:103
msgid "Event"
msgstr "Ereignis"

#: src/components/modals/CreateEventModal/index.tsx:76
#~ msgid "Event created successfully 🎉"
#~ msgstr "Veranstaltung erfolgreich erstellt 🎉"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:161
msgid "Event Date"
msgstr "Veranstaltungsdatum"

#: src/components/routes/account/ManageAccount/sections/EventDefaultsSettings/index.tsx:5
msgid "Event Defaults"
msgstr "Ereignisstandards"

#: src/components/routes/event/Settings/index.tsx:28
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:276
msgid "Event Details"
msgstr "Veranstaltungsdetails"

#: src/components/modals/DuplicateEventModal/index.tsx:58
msgid "Event duplicated successfully"
msgstr "Ereignis erfolgreich dupliziert"

#: src/components/layouts/Checkout/index.tsx:79
msgid "Event Homepage"
msgstr "Veranstaltungsstartseite"

#: src/components/layouts/Event/index.tsx:166
#~ msgid "Event is not visible to the public"
#~ msgstr "Das Ereignis ist nicht öffentlich sichtbar"

#: src/components/layouts/Event/index.tsx:165
#~ msgid "Event is visible to the public"
#~ msgstr "Veranstaltung ist öffentlich sichtbar"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:85
msgid "Event location & venue details"
msgstr "Veranstaltungsort & Details zum Veranstaltungsort"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:12
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
msgid "Event Not Available"
msgstr "Veranstaltung nicht verfügbar"

#: src/components/layouts/Event/index.tsx:187
#~ msgid "Event page"
#~ msgstr "Veranstaltungsseite"

#: src/components/layouts/Event/index.tsx:212
msgid "Event Page"
msgstr "Veranstaltungsseite"

#: src/components/common/EventCard/index.tsx:70
#: src/components/layouts/Event/index.tsx:80
#: src/components/routes/event/EventDashboard/index.tsx:76
#: src/components/routes/event/GettingStarted/index.tsx:63
msgid "Event status update failed. Please try again later"
msgstr "Die Aktualisierung des Ereignisstatus ist fehlgeschlagen. Bitte versuchen Sie es später erneut"

#: src/components/common/EventCard/index.tsx:67
#: src/components/layouts/Event/index.tsx:77
#: src/components/routes/event/EventDashboard/index.tsx:73
#: src/components/routes/event/GettingStarted/index.tsx:60
msgid "Event status updated"
msgstr "Veranstaltungsstatus aktualisiert"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Event Ticketing by"
msgstr "Event-Ticketing von"

#: src/components/common/WebhookTable/index.tsx:237
#: src/components/forms/WebhookForm/index.tsx:122
msgid "Event Types"
msgstr "Ereignistypen"

#: src/components/modals/ShareModal/index.tsx:237
msgid "Event URL"
msgstr "Veranstaltungs-URL"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
#~ msgid "Event wdwdeNot Available"
#~ msgstr ""

#: src/components/layouts/Event/index.tsx:226
msgid "Events"
msgstr "Veranstaltungen"

#: src/components/forms/CheckInListForm/index.tsx:56
msgid "Expiration date"
msgstr "Ablaufdatum"

#: src/components/common/PromoCodeTable/index.tsx:73
msgid "Expires"
msgstr "Läuft ab"

#: src/components/forms/PromoCodeForm/index.tsx:79
msgid "Expiry Date"
msgstr "Verfallsdatum"

#: src/components/routes/event/attendees.tsx:80
#: src/components/routes/event/orders.tsx:140
msgid "Export"
msgstr "Export"

#: src/components/common/QuestionsTable/index.tsx:267
msgid "Export answers"
msgstr "Antworten exportieren"

#: src/mutations/useExportAnswers.ts:39
msgid "Export failed. Please try again."
msgstr "Export fehlgeschlagen. Bitte versuchen Sie es erneut."

#: src/mutations/useExportAnswers.ts:17
msgid "Export started. Preparing file..."
msgstr "Export gestartet. Datei wird vorbereitet..."

#: src/components/routes/event/attendees.tsx:39
msgid "Exporting Attendees"
msgstr "Teilnehmer werden exportiert"

#: src/mutations/useExportAnswers.ts:33
msgid "Exporting complete. Downloading file..."
msgstr "Export abgeschlossen. Datei wird heruntergeladen..."

#: src/components/routes/event/orders.tsx:90
msgid "Exporting Orders"
msgstr "Bestellungen werden exportiert"

#: src/components/common/AttendeeTable/index.tsx:97
msgid "Failed to cancel attendee"
msgstr "Teilnehmer konnte nicht abgesagt werden"

#: src/components/modals/CancelOrderModal/index.tsx:33
msgid "Failed to cancel order"
msgstr "Stornierung der Bestellung fehlgeschlagen"

#: src/components/common/QuestionsTable/index.tsx:175
msgid "Failed to delete message. Please try again."
msgstr "Nachricht konnte nicht gelöscht werden. Bitte versuchen Sie es erneut."

#: src/components/common/OrdersTable/index.tsx:120
#: src/components/layouts/Checkout/index.tsx:58
msgid "Failed to download invoice. Please try again."
msgstr "Rechnung konnte nicht heruntergeladen werden. Bitte versuchen Sie es erneut."

#: src/components/routes/event/attendees.tsx:48
msgid "Failed to export attendees"
msgstr "Fehler beim Export der Teilnehmer"

#: src/components/routes/event/attendees.tsx:39
#~ msgid "Failed to export attendees. Please try again."
#~ msgstr "Das Exportieren der Teilnehmer ist fehlgeschlagen. Bitte versuchen Sie es erneut."

#: src/components/routes/event/orders.tsx:99
msgid "Failed to export orders"
msgstr "Fehler beim Export der Bestellungen"

#: src/components/routes/event/orders.tsx:88
#~ msgid "Failed to export orders. Please try again."
#~ msgstr "Der Export der Bestellungen ist fehlgeschlagen. Bitte versuchen Sie es erneut."

#: src/components/modals/EditCheckInListModal/index.tsx:79
msgid "Failed to load Check-In List"
msgstr "Laden der Eincheckliste fehlgeschlagen"

#: src/components/modals/EditWebhookModal/index.tsx:75
msgid "Failed to load Webhook"
msgstr "Webhook konnte nicht geladen werden"

#: src/components/common/AttendeeTable/index.tsx:51
#~ msgid "Failed to resend product email"
#~ msgstr "Produkt-E-Mail konnte nicht erneut gesendet werden"

#: src/components/common/AttendeeTable/index.tsx:50
msgid "Failed to resend ticket email"
msgstr "Ticket-E-Mail konnte nicht erneut gesendet werden"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:179
msgid "Failed to sort products"
msgstr "Produkte konnten nicht sortiert werden"

#: src/mutations/useExportAnswers.ts:15
msgid "Failed to start export job"
msgstr "Exportauftrag konnte nicht gestartet werden"

#: src/components/common/QuestionAndAnswerList/index.tsx:102
msgid "Failed to update answer."
msgstr "Fehler beim Aktualisieren der Antwort."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:64
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:69
msgid "Failed to upload image."
msgstr "Bild-Upload fehlgeschlagen."

#: src/components/forms/TaxAndFeeForm/index.tsx:18
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Fee"
msgstr "Gebühr"

#: src/components/common/GlobalMenu/index.tsx:30
#~ msgid "Feedback"
#~ msgstr "Rückmeldung"

#: src/components/forms/ProductForm/index.tsx:366
msgid "Fees"
msgstr "Gebühren"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:80
msgid "Fees are subject to change. You will be notified of any changes to your fee structure."
msgstr "Gebühren können sich ändern. Du wirst über Änderungen deiner Gebührenstruktur benachrichtigt."

#: src/components/routes/event/orders.tsx:121
msgid "Filter Orders"
msgstr "Bestellungen filtern"

#: src/components/common/FilterModal/index.tsx:75
#: src/components/common/FilterModal/index.tsx:223
msgid "Filters"
msgstr "Filter"

#: src/components/common/FilterModal/index.tsx:223
msgid "Filters ({activeFilterCount})"
msgstr "Filter ({activeFilterCount})"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:206
msgid "First Invoice Number"
msgstr "Erste Rechnungsnummer"

#: src/components/common/QuestionsTable/index.tsx:208
#: src/components/modals/CreateAttendeeModal/index.tsx:118
#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:144
#: src/components/routes/product-widget/CollectInformation/index.tsx:284
#: src/components/routes/product-widget/CollectInformation/index.tsx:395
msgid "First name"
msgstr "Vorname"

#: src/components/common/QuestionsTable/index.tsx:207
#: src/components/modals/EditUserModal/index.tsx:71
#: src/components/modals/InviteUserModal/index.tsx:57
#: src/components/routes/auth/AcceptInvitation/index.tsx:92
#: src/components/routes/auth/Register/index.tsx:83
#: src/components/routes/product-widget/CollectInformation/index.tsx:283
#: src/components/routes/product-widget/CollectInformation/index.tsx:394
#: src/components/routes/profile/ManageProfile/index.tsx:135
msgid "First Name"
msgstr "Vorname"

#: src/components/routes/auth/AcceptInvitation/index.tsx:29
msgid "First name must be between 1 and 50 characters"
msgstr "Der Vorname muss zwischen 1 und 50 Zeichen lang sein"

#: src/components/common/QuestionsTable/index.tsx:349
msgid "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."
msgstr "Vorname, Nachname und E-Mail-Adresse sind Standardfragen und werden immer in den Bestellvorgang einbezogen."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
msgid "First Used"
msgstr "Erstmals verwendet"

#: src/components/forms/TaxAndFeeForm/index.tsx:33
msgid "Fixed"
msgstr "Fest"

#: src/components/forms/PromoCodeForm/index.tsx:55
msgid "Fixed amount"
msgstr "Fester Betrag"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:65
msgid "Fixed Fee:"
msgstr "Feste Gebühr:"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:123
msgid "Flash is not available on this device"
msgstr "Flash ist auf diesem Gerät nicht verfügbar"

#: src/components/layouts/AuthLayout/index.tsx:58
msgid "Flexible Ticketing"
msgstr "Flexibles Ticketing"

#: src/components/routes/auth/Login/index.tsx:80
msgid "Forgot password?"
msgstr "Passwort vergessen?"

#: src/components/common/AttendeeTicket/index.tsx:40
#: src/components/common/Currency/index.tsx:90
#: src/components/common/OrderSummary/index.tsx:46
#: src/components/common/OrderSummary/index.tsx:99
#: src/components/common/ProductsTable/SortableProduct/index.tsx:93
#: src/components/common/ProductsTable/SortableProduct/index.tsx:103
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:53
msgid "Free"
msgstr "Frei"

#: src/components/forms/ProductForm/index.tsx:151
msgid "Free Product"
msgstr "Kostenloses Produkt"

#: src/components/forms/ProductForm/index.tsx:153
msgid "Free product, no payment information required"
msgstr "Kostenloses Produkt, keine Zahlungsinformationen erforderlich"

#: src/components/forms/TicketForm/index.tsx:133
#~ msgid "Free Ticket"
#~ msgstr "Freikarten"

#: src/components/forms/TicketForm/index.tsx:135
#~ msgid "Free ticket, no payment information required"
#~ msgstr "Kostenloses Ticket, keine Zahlungsinformationen erforderlich"

#: src/components/common/LanguageSwitcher/index.tsx:20
msgid "French"
msgstr "Französisch"

#: src/components/layouts/AuthLayout/index.tsx:88
msgid "Fully Integrated"
msgstr "Fully Integrated"

#: src/components/forms/ProductForm/index.tsx:136
msgid "General"
msgstr "Allgemein"

#: src/components/common/LanguageSwitcher/index.tsx:14
msgid "German"
msgstr "Deutsch"

#: src/components/layouts/AuthLayout/index.tsx:35
msgid "Get started for free, no subscription fees"
msgstr "Starten Sie kostenlos, keine Abonnementgebühren"

#: src/components/routes/event/EventDashboard/index.tsx:125
msgid "Get your event ready"
msgstr "Bereiten Sie Ihre Veranstaltung vor"

#: src/components/layouts/Event/index.tsx:88
msgid "Getting Started"
msgstr "Erste Schritte"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:23
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:42
msgid "Go back to profile"
msgstr "Zurück zum Profil"

#: src/components/routes/product-widget/CollectInformation/index.tsx:239
msgid "Go to event homepage"
msgstr "Zur Event-Homepage"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:53
msgid "Go to Hi.Events"
msgstr "Gehe zu Hi.Events"

#: src/components/common/ErrorDisplay/index.tsx:64
msgid "Go to home page"
msgstr "Zur Startseite gehen"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:162
msgid "Go to Stripe Dashboard"
msgstr "Zum Stripe-Dashboard"

#: src/components/common/AddEventToCalendarButton/index.tsx:128
msgid "Google Calendar"
msgstr "Google Kalender"

#: src/components/common/EventCard/index.tsx:133
msgid "gross sales"
msgstr "Bruttoverkäufe"

#: src/components/common/StatBoxes/index.tsx:39
msgid "Gross sales"
msgstr "Bruttoumsatz"

#: src/components/routes/event/EventDashboard/index.tsx:274
msgid "Gross Sales"
msgstr "Bruttoumsatz"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:281
msgid "Guests"
msgstr "Gäste"

#: src/components/routes/product-widget/SelectProducts/index.tsx:495
msgid "Have a promo code?"
msgstr "Haben Sie einen Promo-Code?"

#: src/components/forms/OrganizerForm/index.tsx:33
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/GlobalMenu/index.tsx:24
#~ msgid "Help & Support"
#~ msgstr "Hilfe & Support"

#: src/components/common/WidgetEditor/index.tsx:295
msgid "Here is an example of how you can use the component in your application."
msgstr "Hier ist ein Beispiel, wie Sie die Komponente in Ihrer Anwendung verwenden können."

#: src/components/common/WidgetEditor/index.tsx:284
msgid "Here is the React component you can use to embed the widget in your application."
msgstr "Hier ist die React-Komponente, die Sie verwenden können, um das Widget in Ihre Anwendung einzubetten."

#: src/components/routes/event/EventDashboard/index.tsx:101
msgid "Hi {0} 👋"
msgstr "Hallo {0} 👋"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:43
msgid "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."
msgstr "Hi.Events erhebt Plattformgebühren zur Wartung und Verbesserung unserer Dienste. Diese Gebühren werden automatisch von jeder Transaktion abgezogen."

#: src/components/modals/CreateEventModal/index.tsx:130
#: src/components/modals/DuplicateEventModal/index.tsx:79
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:73
msgid "Hi.Events Conference {0}"
msgstr "Hi.Events-Konferenz {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:117
msgid "Hi.Events Conference Center"
msgstr "Hi.Events Konferenzzentrum"

#: src/components/layouts/AuthLayout/index.tsx:131
msgid "hi.events logo"
msgstr "hi.events-Logo"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:79
msgid "Hidden from public view"
msgstr "Vor der Öffentlichkeit verborgen"

#: src/components/common/QuestionsTable/index.tsx:290
msgid "hidden question"
msgstr "versteckte Frage"

#: src/components/common/QuestionsTable/index.tsx:291
msgid "hidden questions"
msgstr "versteckte Fragen"

#: src/components/forms/QuestionForm/index.tsx:218
msgid "Hidden questions are only visible to the event organizer and not to the customer."
msgstr "Versteckte Fragen sind nur für den Veranstalter und nicht für den Kunden sichtbar."

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/routes/product-widget/SelectProducts/index.tsx:460
msgid "Hide"
msgstr "Verstecken"

#: src/components/common/AttendeeList/index.tsx:84
msgid "Hide Answers"
msgstr "Antworten ausblenden"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:89
msgid "Hide getting started page"
msgstr "Seite „Erste Schritte“ ausblenden"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Hide hidden questions"
msgstr "Versteckte Fragen ausblenden"

#: src/components/forms/ProductForm/index.tsx:399
msgid "Hide product after sale end date"
msgstr "Produkt nach Verkaufsenddatum ausblenden"

#: src/components/forms/ProductForm/index.tsx:397
msgid "Hide product before sale start date"
msgstr "Produkt vor Verkaufsstartdatum ausblenden"

#: src/components/forms/ProductForm/index.tsx:412
msgid "Hide product unless user has applicable promo code"
msgstr "Produkt ausblenden, es sei denn, der Benutzer hat einen gültigen Promo-Code"

#: src/components/forms/ProductForm/index.tsx:405
msgid "Hide product when sold out"
msgstr "Produkt bei Ausverkauf ausblenden"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:90
msgid "Hide the getting started page from the sidebar"
msgstr "Blenden Sie die Seite „Erste Schritte“ in der Seitenleiste aus."

#: src/components/forms/ProductForm/index.tsx:417
msgid "Hide this product from customers"
msgstr "Dieses Produkt vor Kunden verbergen"

#: src/components/forms/QuestionForm/index.tsx:219
msgid "Hide this question"
msgstr "Diese Frage verbergen"

#: src/components/forms/TicketForm/index.tsx:362
#~ msgid "Hide this ticket from customers"
#~ msgstr "Dieses Ticket vor Kunden verbergen"

#: src/components/forms/ProductForm/index.tsx:103
msgid "Hide this tier from users"
msgstr "Diese Ebene vor Benutzern verbergen"

#: src/components/forms/TicketForm/index.tsx:344
#~ msgid "Hide ticket after sale end date"
#~ msgstr "Ticket nach Verkaufsende verbergen"

#: src/components/forms/TicketForm/index.tsx:342
#~ msgid "Hide ticket before sale start date"
#~ msgstr "Ticket vor Verkaufsbeginn verbergen"

#: src/components/forms/TicketForm/index.tsx:357
#~ msgid "Hide ticket unless user has applicable promo code"
#~ msgstr "Ticket verbergen, sofern der Benutzer nicht über einen gültigen Aktionscode verfügt"

#: src/components/forms/TicketForm/index.tsx:350
#~ msgid "Hide ticket when sold out"
#~ msgstr "Ticket bei Ausverkauf verbergen"

#: src/components/forms/ProductForm/index.tsx:101
msgid "Hiding a product will prevent users from seeing it on the event page."
msgstr "Das Ausblenden eines Produkts verhindert, dass Benutzer es auf der Veranstaltungsseite sehen."

#: src/components/forms/TicketForm/index.tsx:98
#~ msgid "Hiding a ticket will prevent users from seeing it on the event page."
#~ msgstr "Wenn Sie ein Ticket ausblenden, können Benutzer es nicht auf der Veranstaltungsseite sehen."

#: src/components/routes/event/HomepageDesigner/index.tsx:115
msgid "Homepage Design"
msgstr "Homepage-Design"

#: src/components/layouts/Event/index.tsx:109
msgid "Homepage Designer"
msgstr "Homepage Designer"

#: src/components/routes/event/HomepageDesigner/index.tsx:174
msgid "Homepage Preview"
msgstr "Homepage-Vorschau"

#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:145
msgid "Homer"
msgstr "Homer"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
msgid "How many minutes the customer has to complete their order. We recommend at least 15 minutes"
msgstr "Wie viele Minuten hat der Kunde Zeit, um seine Bestellung abzuschließen. Wir empfehlen mindestens 15 Minuten"

#: src/components/forms/PromoCodeForm/index.tsx:84
msgid "How many times can this code be used?"
msgstr "Wie oft kann dieser Code verwendet werden?"

#: src/components/common/Editor/index.tsx:75
msgid "HTML character limit exceeded: {htmlLength}/{maxLength}"
msgstr "HTML-Zeichenlimit überschritten: {htmlLength}/{maxLength}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:160
msgid "https://example-maps-service.com/..."
msgstr "https://example-maps-service.com/..."

#: src/components/forms/WebhookForm/index.tsx:118
msgid "https://webhook-domain.com/webhook"
msgstr "https://webhook-domain.com/webhook"

#: src/components/routes/auth/AcceptInvitation/index.tsx:118
msgid "I agree to the <0>terms and conditions</0>"
msgstr "Ich stimme den <0>Allgemeinen Geschäftsbedingungen</0> zu"

#: src/components/routes/product-widget/Payment/index.tsx:106
msgid "I would like to pay using an offline method"
msgstr "Ich möchte mit einer Offline-Methode bezahlen"

#: src/components/routes/product-widget/Payment/index.tsx:107
msgid "I would like to pay using an online method (credit card etc.)"
msgstr "Ich möchte mit einer Online-Methode (z. B. Kreditkarte) bezahlen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:315
msgid "If a new tab did not open automatically, please click the button below to continue to checkout."
msgstr "Wenn sich kein neues Tab automatisch geöffnet hat, klicke bitte unten auf die Schaltfläche, um mit dem Checkout fortzufahren."

#: src/components/routes/product-widget/SelectProducts/index.tsx:284
#~ msgid "If a new tab did not open, please  <0><1>{0}</1>.</0>"
#~ msgstr "Wenn keine neue Registerkarte geöffnet wurde, <0><1>{0}</1>.</0>"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:149
#~ msgid "If blank, the address will be used to generate a Google map link"
#~ msgstr "Wenn leer, wird die Adresse verwendet, um einen Google-Kartenlink zu generieren."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:158
msgid "If blank, the address will be used to generate a Google Mapa link"
msgstr "Wenn leer, wird die Adresse verwendet, um einen Google Maps-Link zu erstellen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:158
msgid "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."
msgstr "Wenn aktiviert, kann das Check-in-Personal Teilnehmer entweder als eingecheckt markieren oder die Bestellung als bezahlt markieren und die Teilnehmer einchecken. Wenn deaktiviert, können Teilnehmer mit unbezahlten Bestellungen nicht eingecheckt werden."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:76
msgid "If enabled, the organizer will receive an email notification when a new order is placed"
msgstr "Wenn aktiviert, erhält der Veranstalter eine E-Mail-Benachrichtigung, wenn eine neue Bestellung aufgegeben wird"

#: src/components/routes/profile/ManageProfile/index.tsx:122
msgid "If you did not request this change, please immediately change your password."
msgstr "Sollten Sie diese Änderung nicht veranlasst haben, ändern Sie bitte umgehend Ihr Passwort."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
msgid ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."
msgstr "Wenn du ein Konto bei uns hast, erhältst du eine E-Mail mit Anweisungen zum Zurücksetzen deines Passworts."

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:108
msgid "Image deleted successfully"
msgstr "Bild erfolgreich gelöscht"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
#~ msgid "Image dimensions must be between 3000px by 2000px. With a max height of 2000px and max width of 3000px"
#~ msgstr "Die Bildabmessungen müssen zwischen 3000px und 2000px liegen. Mit einer maximalen Höhe von 2000px und einer maximalen Breite von 3000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
msgid "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"
msgstr "Bildabmessungen müssen zwischen 4000px und 4000px liegen. Mit einer maximalen Höhe von 4000px und einer maximalen Breite von 4000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:49
msgid "Image must be less than 5MB"
msgstr "Das Bild muss kleiner als 5 MB sein."

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:63
msgid "Image uploaded successfully"
msgstr "Bild erfolgreich hochgeladen"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:111
msgid "Image URL"
msgstr "Bild-URL"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
msgid "Image width must be at least 900px and height at least 50px"
msgstr "Die Bildbreite muss mindestens 900 Pixel und die Höhe mindestens 50 Pixel betragen."

#: src/components/layouts/AuthLayout/index.tsx:53
msgid "In-depth Analytics"
msgstr "In-depth Analytics"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:25
#: src/components/modals/EditUserModal/index.tsx:111
msgid "Inactive"
msgstr "Inaktiv"

#: src/components/modals/EditUserModal/index.tsx:107
msgid "Inactive users cannot log in."
msgstr "Inaktive Benutzer können sich nicht anmelden."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee product page"
#~ msgstr "Fügen Sie Verbindungsdetails für Ihre Online-Veranstaltung hinzu. Diese Details werden auf der Bestellzusammenfassungsseite und der Teilnehmerproduktseite angezeigt."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page"
#~ msgstr "Fügen Sie Verbindungsdetails für Ihre Online-Veranstaltung hinzu. Diese Details werden auf der Bestellübersichtsseite und der Teilnehmerticketseite angezeigt."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:102
msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."
msgstr "Fügen Sie Verbindungsdetails für Ihr Online-Event hinzu. Diese Details werden auf der Bestellübersichtsseite und der Teilnehmer-Ticketseite angezeigt."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:54
msgid "Include tax and fees in the price"
msgstr "Steuern und Gebühren im Preis einbeziehen"

#: src/components/common/CheckInListList/index.tsx:103
msgid "Includes {0} products"
msgstr "Beinhaltet {0} Produkte"

#: src/components/common/CheckInListList/index.tsx:112
#~ msgid "Includes {0} tickets"
#~ msgstr "Enthält {0} Tickets"

#: src/components/common/CheckInListList/index.tsx:104
msgid "Includes 1 product"
msgstr "Beinhaltet 1 Produkt"

#: src/components/common/CheckInListList/index.tsx:113
#~ msgid "Includes 1 ticket"
#~ msgstr "Enthält 1 Ticket"

#: src/components/common/MessageList/index.tsx:20
msgid "Individual attendees"
msgstr "Einzelne Teilnehmer"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:100
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:121
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:137
msgid "Insert Image"
msgstr "Bild einfügen"

#: src/components/layouts/Event/index.tsx:54
#~ msgid "Integrations"
#~ msgstr "Integrationen"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation resent!"
msgstr "Einladung erneut verschickt!"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation revoked!"
msgstr "Einladung widerrufen!"

#: src/components/modals/InviteUserModal/index.tsx:54
#: src/components/modals/InviteUserModal/index.tsx:74
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invite User"
msgstr "Benutzer einladen"

#: src/components/common/OrdersTable/index.tsx:116
#: src/components/layouts/Checkout/index.tsx:54
msgid "Invoice downloaded successfully"
msgstr "Rechnung erfolgreich heruntergeladen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:252
msgid "Invoice Notes"
msgstr "Rechnungsnotizen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:197
msgid "Invoice Numbering"
msgstr "Rechnungsnummerierung"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:177
msgid "Invoice Settings"
msgstr "Rechnungseinstellungen"

#: src/components/modals/RefundOrderModal/index.tsx:115
msgid "Issue refund"
msgstr "Rückerstattung ausstellen"

#: src/components/common/LanguageSwitcher/index.tsx:22
msgid "Italian"
msgstr "Italienisch"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Item"
msgstr "Artikel"

#: src/components/routes/auth/Register/index.tsx:84
msgid "John"
msgstr "John"

#: src/components/modals/CreateAttendeeModal/index.tsx:126
msgid "Johnson"
msgstr "Johnson"

#: src/components/forms/ProductForm/index.tsx:77
msgid "Label"
msgstr "Etikett"

#: src/components/common/AttendeeDetails/index.tsx:45
#: src/components/modals/CreateAttendeeModal/index.tsx:144
#: src/components/routes/profile/ManageProfile/index.tsx:172
msgid "Language"
msgstr "Sprache"

#: src/components/common/ReportTable/index.tsx:52
msgid "Last 12 months"
msgstr "Letzte 12 Monate"

#: src/components/common/ReportTable/index.tsx:47
msgid "Last 14 days"
msgstr "Letzte 14 Tage"

#: src/components/common/ReportTable/index.tsx:44
msgid "Last 24 hours"
msgstr "Letzte 24 Stunden"

#: src/components/common/ReportTable/index.tsx:48
msgid "Last 30 days"
msgstr "Letzte 30 Tage"

#: src/components/common/ReportTable/index.tsx:45
msgid "Last 48 hours"
msgstr "Letzte 48 Stunden"

#: src/components/common/ReportTable/index.tsx:50
msgid "Last 6 months"
msgstr "Letzte 6 Monate"

#: src/components/common/ReportTable/index.tsx:46
msgid "Last 7 days"
msgstr "Letzte 7 Tage"

#: src/components/common/ReportTable/index.tsx:49
msgid "Last 90 days"
msgstr "Letzte 90 Tage"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Last login"
msgstr "Letzte Anmeldung"

#: src/components/modals/CreateAttendeeModal/index.tsx:125
#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:150
msgid "Last name"
msgstr "Nachname"

#: src/components/common/QuestionsTable/index.tsx:212
#: src/components/common/QuestionsTable/index.tsx:213
#: src/components/modals/EditUserModal/index.tsx:72
#: src/components/modals/InviteUserModal/index.tsx:58
#: src/components/routes/auth/AcceptInvitation/index.tsx:94
#: src/components/routes/auth/Register/index.tsx:89
#: src/components/routes/product-widget/CollectInformation/index.tsx:289
#: src/components/routes/product-widget/CollectInformation/index.tsx:290
#: src/components/routes/product-widget/CollectInformation/index.tsx:400
#: src/components/routes/product-widget/CollectInformation/index.tsx:401
#: src/components/routes/profile/ManageProfile/index.tsx:137
msgid "Last Name"
msgstr "Familienname, Nachname"

#: src/components/common/WebhookTable/index.tsx:239
msgid "Last Response"
msgstr "Letzte Antwort"

#: src/components/common/WebhookTable/index.tsx:240
msgid "Last Triggered"
msgstr "Zuletzt ausgelöst"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
msgid "Last Used"
msgstr "Zuletzt verwendet"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:56
#~ msgid "Learn more about Stripe"
#~ msgstr "Erfahren Sie mehr über Stripe"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:191
msgid "Leave blank to use the default word \"Invoice\""
msgstr "Leer lassen, um das Standardwort \"Rechnung\" zu verwenden"

#: src/components/routes/welcome/index.tsx:20
msgid "Let's get started by creating your first organizer"
msgstr "Beginnen wir mit der Erstellung Ihres ersten Organizers"

#: src/components/routes/event/EventDashboard/index.tsx:194
msgid "Link your Stripe account to receive funds from ticket sales."
msgstr "Verknüpfen Sie Ihr Stripe-Konto, um Gelder aus dem Ticketverkauf zu erhalten."

#: src/components/layouts/Event/index.tsx:190
msgid "Live"
msgstr "Live"

#: src/components/routes/event/Webhooks/index.tsx:21
msgid "Loading Webhooks"
msgstr "Webhooks werden geladen"

#: src/components/common/ReportTable/index.tsx:210
msgid "Loading..."
msgstr "Wird geladen..."

#: src/components/routes/event/Settings/index.tsx:34
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:84
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:167
msgid "Location"
msgstr "Standort"

#: src/components/routes/auth/Login/index.tsx:84
#: src/components/routes/auth/Register/index.tsx:71
msgid "Log in"
msgstr "Anmelden"

#: src/components/routes/auth/Login/index.tsx:84
msgid "Logging in"
msgstr "Einloggen"

#: src/components/routes/auth/Register/index.tsx:70
#~ msgid "Login"
#~ msgstr "Anmelden"

#: src/components/common/GlobalMenu/index.tsx:47
msgid "Logout"
msgstr "Ausloggen"

#: src/components/common/WidgetEditor/index.tsx:331
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ich habe das Element platziert …"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:170
msgid "Make billing address mandatory during checkout"
msgstr "Rechnungsadresse beim Checkout erforderlich machen"

#: src/components/routes/event/EventDashboard/index.tsx:172
#~ msgid "Make Event Live"
#~ msgstr "Veranstaltung veröffentlichen"

#: src/components/forms/QuestionForm/index.tsx:212
msgid "Make this question mandatory"
msgstr "Machen Sie diese Frage obligatorisch"

#: src/components/routes/event/EventDashboard/index.tsx:148
msgid "Make your event live"
msgstr "Machen Sie Ihre Veranstaltung live"

#: src/components/common/CapacityAssignmentList/index.tsx:143
#: src/components/common/CheckInListList/index.tsx:145
#: src/components/common/EventCard/index.tsx:180
#: src/components/common/OrdersTable/index.tsx:139
#: src/components/common/OrdersTable/index.tsx:154
#: src/components/common/ProductsTable/SortableProduct/index.tsx:256
#: src/components/common/PromoCodeTable/index.tsx:167
#: src/components/layouts/Event/index.tsx:97
msgid "Manage"
msgstr "Verwalten"

#: src/components/common/AttendeeTable/index.tsx:161
msgid "Manage attendee"
msgstr "Teilnehmer verwalten"

#: src/components/common/EventCard/index.tsx:150
msgid "Manage event"
msgstr "Veranstaltung verwalten"

#: src/components/common/OrdersTable/index.tsx:156
msgid "Manage order"
msgstr "Bestellung verwalten"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:99
msgid "Manage payment and invoicing settings for this event."
msgstr "Zahlungs- und Rechnungseinstellungen für diese Veranstaltung verwalten."

#: src/components/modals/CreateAttendeeModal/index.tsx:106
#~ msgid "Manage products"
#~ msgstr "Produkte verwalten"

#: src/components/routes/profile/ManageProfile/index.tsx:101
msgid "Manage Profile"
msgstr "Profil verwalten"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
msgid "Manage taxes and fees which can be applied to your products"
msgstr "Verwalten Sie Steuern und Gebühren, die auf Ihre Produkte angewendet werden können"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
#~ msgid "Manage taxes and fees which can be applied to your tickets"
#~ msgstr "Verwalten Sie Steuern und Gebühren, die auf Ihre Tickets erhoben werden können"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
msgid "Manage tickets"
msgstr "Tickets verwalten"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:54
msgid "Manage your account details and default settings"
msgstr "Verwalten Sie Ihre Kontodetails und Standardeinstellungen"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:241
msgid "Manage your payment processing and view platform fees"
msgstr "Verwalten Sie Ihre Zahlungsabwicklung und sehen Sie die Plattformgebühren ein"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:91
#~ msgid "Manage your Stripe payment details"
#~ msgstr "Verwalten Sie Ihre Stripe-Zahlungsdetails"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Manage your users and their permissions"
msgstr "Verwalten Sie Ihre Benutzer und deren Berechtigungen"

#: src/components/forms/QuestionForm/index.tsx:211
msgid "Mandatory questions must be answered before the customer can checkout."
msgstr "Bevor der Kunde zur Kasse gehen kann, müssen obligatorische Fragen beantwortet werden."

#: src/components/common/AttendeeTable/index.tsx:67
msgid "Manually add an Attendee"
msgstr "Einen Teilnehmer manuell hinzufügen"

#: src/components/modals/CreateAttendeeModal/index.tsx:97
#: src/components/modals/CreateAttendeeModal/index.tsx:113
msgid "Manually Add Attendee"
msgstr "Teilnehmer manuell hinzufügen"

#: src/components/modals/CreateAttendeeModal/index.tsx:148
#~ msgid "Manually adding an attendee will adjust ticket quantity."
#~ msgstr "Durch manuelles Hinzufügen eines Teilnehmers wird die Ticketanzahl angepasst."

#: src/components/common/OrdersTable/index.tsx:167
msgid "Mark as paid"
msgstr "Als bezahlt markieren"

#: src/components/layouts/AuthLayout/index.tsx:83
msgid "Match Your Brand"
msgstr "Match Your Brand"

#: src/components/forms/ProductForm/index.tsx:383
msgid "Maximum Per Order"
msgstr "Maximal pro Bestellung"

#: src/components/common/AttendeeTable/index.tsx:166
msgid "Message attendee"
msgstr "Nachricht an Teilnehmer"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:273
msgid "Message Attendees"
msgstr "Nachrichten an Teilnehmer senden"

#: src/components/modals/SendMessageModal/index.tsx:165
#~ msgid "Message attendees with specific products"
#~ msgstr "Nachricht an Teilnehmer mit bestimmten Produkten senden"

#: src/components/modals/SendMessageModal/index.tsx:206
msgid "Message attendees with specific tickets"
msgstr "Teilnehmer mit bestimmten Tickets benachrichtigen"

#: src/components/layouts/AuthLayout/index.tsx:74
msgid "Message attendees, manage orders, and handle refunds all in one place"
msgstr "Kommunizieren Sie mit Teilnehmern, verwalten Sie Bestellungen und bearbeiten Sie Rückerstattungen an einem Ort"

#: src/components/common/OrdersTable/index.tsx:158
msgid "Message buyer"
msgstr "Nachricht an den Käufer"

#: src/components/modals/SendMessageModal/index.tsx:250
msgid "Message Content"
msgstr "Nachrichteninhalt"

#: src/components/modals/SendMessageModal/index.tsx:71
msgid "Message individual attendees"
msgstr "Nachrichten an einzelne Teilnehmer senden"

#: src/components/modals/SendMessageModal/index.tsx:218
msgid "Message order owners with specific products"
msgstr "Nachricht an Bestelleigentümer mit bestimmten Produkten senden"

#: src/components/modals/SendMessageModal/index.tsx:118
msgid "Message Sent"
msgstr "Nachricht gesendet"

#: src/components/layouts/Event/index.tsx:105
#: src/components/routes/event/messages.tsx:31
msgid "Messages"
msgstr "Mitteilungen"

#: src/components/forms/ProductForm/index.tsx:381
msgid "Minimum Per Order"
msgstr "Mindestbestellwert"

#: src/components/forms/ProductForm/index.tsx:285
msgid "Minimum Price"
msgstr "Minimaler Preis"

#: src/components/routes/event/Settings/index.tsx:58
msgid "Miscellaneous"
msgstr "Sonstiges"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:69
msgid "Miscellaneous Settings"
msgstr "Verschiedene Einstellungen"

#: src/components/layouts/AuthLayout/index.tsx:63
msgid "Mobile Check-in"
msgstr "Mobiler Check-in"

#: src/components/forms/QuestionForm/index.tsx:106
msgid "Multi line text box"
msgstr "Mehrzeiliges Textfeld"

#: src/components/forms/ProductForm/index.tsx:165
msgid "Multiple price options. Perfect for early bird products etc."
msgstr "Mehrere Preisoptionen. Perfekt für Frühbucherprodukte usw."

#: src/components/forms/TicketForm/index.tsx:147
#~ msgid "Multiple price options. Perfect for early bird tickets etc."
#~ msgstr "Mehrere Preisoptionen. Perfekt für Frühbuchertickets usw."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:71
msgid "My amazing event description..."
msgstr "Meine tolle Eventbeschreibung..."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:65
msgid "My amazing event title..."
msgstr "Mein toller Veranstaltungstitel …"

#: src/components/common/GlobalMenu/index.tsx:27
msgid "My Profile"
msgstr "Mein Profil"

#: src/components/common/QuestionAndAnswerList/index.tsx:178
msgid "N/A"
msgstr "N/V"

#: src/components/common/WidgetEditor/index.tsx:354
msgid "Nam placerat elementum..."
msgstr "Ich habe das Element platziert …"

#: src/components/common/AttendeeDetails/index.tsx:13
#: src/components/common/AttendeeTable/index.tsx:108
#: src/components/common/OrderDetails/index.tsx:23
#: src/components/forms/CapaciyAssigmentForm/index.tsx:37
#: src/components/forms/CheckInListForm/index.tsx:20
#: src/components/forms/ProductForm/index.tsx:253
#: src/components/forms/TaxAndFeeForm/index.tsx:61
#: src/components/modals/CreateEventModal/index.tsx:129
#: src/components/modals/DuplicateEventModal/index.tsx:78
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:63
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:72
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:107
#: src/components/routes/welcome/index.tsx:83
msgid "Name"
msgstr "Name"

#: src/components/modals/CreateEventModal/index.tsx:38
msgid "Name should be less than 150 characters"
msgstr "Der Name sollte weniger als 150 Zeichen lang sein"

#: src/components/common/QuestionAndAnswerList/index.tsx:181
#: src/components/common/QuestionAndAnswerList/index.tsx:289
msgid "Navigate to Attendee"
msgstr "Navigieren Sie zu Teilnehmer"

#: src/components/common/PromoCodeTable/index.tsx:158
#: src/components/common/WebhookTable/index.tsx:266
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Never"
msgstr "Niemals"

#: src/components/routes/auth/AcceptInvitation/index.tsx:111
#: src/components/routes/auth/ResetPassword/index.tsx:59
#: src/components/routes/profile/ManageProfile/index.tsx:195
msgid "New Password"
msgstr "Neues Kennwort"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "No"
msgstr "NEIN"

#: src/components/common/QuestionAndAnswerList/index.tsx:104
#~ msgid "No {0} available."
#~ msgstr "Kein {0} verfügbar."

#: src/components/routes/event/Webhooks/index.tsx:22
msgid "No Active Webhooks"
msgstr "Keine aktiven Webhooks"

#: src/components/common/NoEventsBlankSlate/index.tsx:21
msgid "No archived events to show."
msgstr "Keine archivierten Veranstaltungen anzuzeigen."

#: src/components/common/AttendeeList/index.tsx:23
msgid "No attendees found for this order."
msgstr "Für diese Bestellung wurden keine Teilnehmer gefunden."

#: src/components/modals/ManageOrderModal/index.tsx:132
msgid "No attendees have been added to this order."
msgstr "Zu dieser Bestellung wurden keine Teilnehmer hinzugefügt."

#: src/components/common/AttendeeTable/index.tsx:56
msgid "No Attendees to show"
msgstr "Keine Teilnehmer zum Anzeigen"

#: src/components/forms/CheckInListForm/index.tsx:48
msgid "No attendees will be able to check in before this date using this list"
msgstr "Vor diesem Datum kann sich niemand mit dieser Liste einchecken"

#: src/components/common/CapacityAssignmentList/index.tsx:42
msgid "No Capacity Assignments"
msgstr "Keine Kapazitätszuweisungen"

#: src/components/common/CheckInListList/index.tsx:44
msgid "No Check-In Lists"
msgstr "Keine Einchecklisten"

#: src/components/layouts/AuthLayout/index.tsx:34
msgid "No Credit Card Required"
msgstr "Keine Kreditkarte erforderlich"

#: src/components/common/ReportTable/index.tsx:218
msgid "No data available"
msgstr "Keine Daten verfügbar"

#: src/components/common/ReportTable/index.tsx:214
msgid "No data to show. Please select a date range"
msgstr "Keine Daten verfügbar. Bitte wählen Sie einen Datumsbereich aus."

#: src/components/forms/PromoCodeForm/index.tsx:47
msgid "No Discount"
msgstr "Kein Rabatt"

#: src/components/common/NoEventsBlankSlate/index.tsx:20
msgid "No ended events to show."
msgstr "Keine beendeten Veranstaltungen anzuzeigen."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:88
#~ msgid "No events for this organizer"
#~ msgstr "Keine Veranstaltungen für diesen Veranstalter"

#: src/components/common/NoEventsBlankSlate/index.tsx:14
msgid "No events to show"
msgstr "Keine Ereignisse zum Anzeigen"

#: src/components/common/FilterModal/index.tsx:230
msgid "No filters available"
msgstr "Keine Filter verfügbar"

#: src/components/modals/WebhookLogsModal/index.tsx:177
msgid "No logs found"
msgstr "Keine Protokolle gefunden"

#: src/components/common/MessageList/index.tsx:69
msgid "No messages to show"
msgstr "Keine Nachrichten zum Anzeigen"

#: src/components/common/OrdersTable/index.tsx:63
msgid "No orders to show"
msgstr "Keine Bestellungen anzuzeigen"

#: src/components/routes/product-widget/Payment/index.tsx:76
msgid "No payment methods are currently available. Please contact the event organizer for assistance."
msgstr "Derzeit sind keine Zahlungsmethoden verfügbar. Bitte wenden Sie sich an den Veranstalter, um Unterstützung zu erhalten."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:39
msgid "No Payment Required"
msgstr "Keine Zahlung erforderlich"

#: src/components/modals/ManageAttendeeModal/index.tsx:177
msgid "No product associated with this attendee."
msgstr "Kein Produkt mit diesem Teilnehmer verknüpft."

#: src/components/common/ProductSelector/index.tsx:33
msgid "No products available for selection"
msgstr "Keine Produkte zur Auswahl verfügbar"

#: src/components/modals/CreateProductCategoryModal/index.tsx:22
msgid "No products available in this category."
msgstr "Keine Produkte in dieser Kategorie verfügbar."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:39
msgid "No Products Yet"
msgstr "Noch keine Produkte"

#: src/components/common/PromoCodeTable/index.tsx:46
msgid "No Promo Codes to show"
msgstr "Keine Promo-Codes anzuzeigen"

#: src/components/modals/ManageAttendeeModal/index.tsx:193
msgid "No questions answered by this attendee."
msgstr "Dieser Teilnehmer hat keine Fragen beantwortet."

#: src/components/modals/ViewAttendeeModal/index.tsx:75
#~ msgid "No questions have been answered by this attendee."
#~ msgstr "Dieser Teilnehmer hat keine Fragen beantwortet."

#: src/components/modals/ManageOrderModal/index.tsx:119
msgid "No questions have been asked for this order."
msgstr "Für diese Bestellung wurden keine Fragen gestellt."

#: src/components/common/WebhookTable/index.tsx:174
msgid "No response"
msgstr "Keine Antwort"

#: src/components/common/WebhookTable/index.tsx:141
msgid "No responses yet"
msgstr "Noch keine Antworten"

#: src/components/common/NoResultsSplash/index.tsx:24
msgid "No results"
msgstr "Keine Ergebnisse"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:20
msgid "No Search Results"
msgstr "Keine Suchergebnisse"

#: src/components/common/NoResultsSplash/index.tsx:29
msgid "No search results."
msgstr "Keine Suchergebnisse."

#: src/components/common/TaxAndFeeList/index.tsx:101
msgid "No Taxes or Fees have been added."
msgstr "Es wurden keine Steuern oder Gebühren hinzugefügt."

#: src/components/common/TicketsTable/index.tsx:65
#~ msgid "No tickets to show"
#~ msgstr "Keine Tickets vorzeigen"

#: src/components/modals/WebhookLogsModal/index.tsx:180
msgid "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."
msgstr "Für diesen Endpunkt wurden noch keine Webhook-Ereignisse aufgezeichnet. Ereignisse werden hier angezeigt, sobald sie ausgelöst werden."

#: src/components/common/WebhookTable/index.tsx:201
msgid "No Webhooks"
msgstr "Keine Webhooks"

#: src/components/common/PromoCodeTable/index.tsx:82
msgid "None"
msgstr "Keiner"

#: src/components/common/ProductPriceAvailability/index.tsx:27
#: src/components/common/ProductPriceAvailability/index.tsx:48
msgid "Not available"
msgstr "Nicht verfügbar"

#: src/components/common/AttendeeCheckInTable/index.tsx:125
#~ msgid "Not Checked In"
#~ msgstr "Nicht eingecheckt"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "Not On Sale"
msgstr "Nicht im Angebot"

#: src/components/modals/ManageAttendeeModal/index.tsx:130
#: src/components/modals/ManageOrderModal/index.tsx:164
msgid "Notes"
msgstr "Notizen"

#: src/components/common/ReportTable/index.tsx:240
msgid "Nothing to show yet"
msgstr "Noch nichts zu zeigen"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:72
msgid "Notification Settings"
msgstr "Benachrichtigungseinstellungen"

#: src/components/modals/RefundOrderModal/index.tsx:108
msgid "Notify buyer of refund"
msgstr "Käufer über Rückerstattung benachrichtigen"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:75
msgid "Notify organizer of new orders"
msgstr "Veranstalter über neue Bestellungen benachrichtigen"

#: src/components/routes/welcome/index.tsx:72
msgid "Now let's create your first event"
msgstr "Jetzt erstellen wir Ihr erstes Event"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:217
msgid "Number of days allowed for payment (leave blank to omit payment terms from invoices)"
msgstr "Anzahl der für die Zahlung zulässigen Tage (leer lassen, um Zahlungsbedingungen auf Rechnungen wegzulassen)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:199
msgid "Number Prefix"
msgstr "Nummernpräfix"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:145
msgid "Offline orders are not reflected in event statistics until the order is marked as paid."
msgstr "Offline-Bestellungen werden in der Veranstaltungsstatistik erst berücksichtigt, wenn die Bestellung als bezahlt markiert wurde."

#: src/components/routes/product-widget/Payment/index.tsx:66
msgid "Offline payment failed. Please try again or contact the event organizer."
msgstr "Offline-Zahlung fehlgeschlagen. Bitte versuchen Sie es erneut oder kontaktieren Sie den Veranstalter."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:151
msgid "Offline Payment Instructions"
msgstr "Anweisungen für Offline-Zahlungen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:90
msgid "Offline Payments"
msgstr "Offline-Zahlungen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:139
msgid "Offline Payments Information"
msgstr "Informationen zu Offline-Zahlungen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:134
msgid "Offline Payments Settings"
msgstr "Einstellungen für Offline-Zahlungen"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:71
msgid "On sale"
msgstr "Im Angebot"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "On Sale"
msgstr "Im Angebot"

#: src/components/common/NoEventsBlankSlate/index.tsx:19
msgid "Once you create an event, you'll see it here."
msgstr "Sobald Sie ein Ereignis erstellt haben, wird es hier angezeigt."

#: src/components/common/ReportTable/index.tsx:245
msgid "Once you start collecting data, you'll see it here."
msgstr "Sobald Sie Daten sammeln, werden sie hier angezeigt."

#: src/components/routes/event/GettingStarted/index.tsx:179
msgid "Once you're ready, set your event live and start selling products."
msgstr "Sobald Sie bereit sind, setzen Sie Ihre Veranstaltung live und beginnen Sie mit dem Verkauf von Produkten."

#: src/components/routes/event/GettingStarted/index.tsx:108
#~ msgid "Once you're ready, set your event live and start selling tickets."
#~ msgstr "Sobald Sie bereit sind, schalten Sie Ihre Veranstaltung live und beginnen Sie mit dem Ticketverkauf."

#: src/components/common/EventStatusBadge/index.tsx:18
msgid "Ongoing"
msgstr "Laufend"

#: src/components/common/EventCard/index.tsx:118
msgid "Online event"
msgstr "Online-Veranstaltung"

#: src/components/common/OnlineEventDetails/index.tsx:9
msgid "Online Event Details"
msgstr "Details zur Online-Veranstaltung"

#: src/components/modals/SendMessageModal/index.tsx:279
msgid ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."
msgstr ""
"Über dieses Formular sollten ausschließlich wichtige E-Mails gesendet werden, die in direktem Zusammenhang mit dieser Veranstaltung stehen.\n"
"Jeder Missbrauch, einschließlich des Versendens von Werbe-E-Mails, führt zu einer sofortigen Sperrung des Kontos."

#: src/components/modals/SendMessageModal/index.tsx:226
msgid "Only send to orders with these statuses"
msgstr "Nur an Bestellungen mit diesen Status senden"

#: src/components/common/CheckInListList/index.tsx:167
msgid "Open Check-In Page"
msgstr "Eincheckseite öffnen"

#: src/components/layouts/Event/index.tsx:288
msgid "Open sidebar"
msgstr "Seitenleiste öffnen"

#: src/components/forms/QuestionForm/index.tsx:45
msgid "Option {i}"
msgstr "Option {i}"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:253
msgid "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"
msgstr "Optionale zusätzliche Informationen, die auf allen Rechnungen erscheinen (z. B. Zahlungsbedingungen, Gebühren für verspätete Zahlungen, Rückgaberichtlinien)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:200
msgid "Optional prefix for invoice numbers (e.g., INV-)"
msgstr "Optionales Präfix für Rechnungsnummern (z. B. INV-)"

#: src/components/forms/QuestionForm/index.tsx:28
msgid "Options"
msgstr "Optionen"

#: src/components/modals/CreateEventModal/index.tsx:118
msgid "or"
msgstr "oder"

#: src/components/common/AttendeeTable/index.tsx:110
msgid "Order"
msgstr "Befehl"

#: src/components/common/OrdersTable/index.tsx:212
#~ msgid "Order #"
#~ msgstr "Befehl #"

#: src/components/forms/WebhookForm/index.tsx:76
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:63
msgid "Order Cancelled"
msgstr "Bestellung storniert"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:62
msgid "Order Completed"
msgstr "Bestellung abgeschlossen"

#: src/components/forms/WebhookForm/index.tsx:52
msgid "Order Created"
msgstr "Bestellung erstellt"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:122
msgid "Order Date"
msgstr "Auftragsdatum"

#: src/components/modals/ManageAttendeeModal/index.tsx:166
#: src/components/modals/ManageOrderModal/index.tsx:86
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:266
msgid "Order Details"
msgstr "Bestelldetails"

#: src/components/modals/ViewOrderModal/index.tsx:30
#~ msgid "Order Details {0}"
#~ msgstr "Bestelldetails {0}"

#: src/components/modals/CancelOrderModal/index.tsx:29
msgid "Order has been canceled and the order owner has been notified."
msgstr "Die Bestellung wurde storniert und der Bestellinhaber wurde benachrichtigt."

#: src/components/common/OrdersTable/index.tsx:79
msgid "Order marked as paid"
msgstr "Bestellung als bezahlt markiert"

#: src/components/forms/WebhookForm/index.tsx:64
msgid "Order Marked as Paid"
msgstr "Bestellung als bezahlt markiert"

#: src/components/modals/ManageOrderModal/index.tsx:92
msgid "Order Notes"
msgstr "Bestellnotizen"

#: src/components/common/MessageList/index.tsx:23
msgid "Order owner"
msgstr "Bestelleigentümer"

#: src/components/modals/SendMessageModal/index.tsx:191
msgid "Order owners with a specific product"
msgstr "Bestelleigentümer mit einem bestimmten Produkt"

#: src/components/common/MessageList/index.tsx:19
msgid "Order owners with products"
msgstr "Bestelleigentümer mit Produkten"

#: src/components/common/QuestionsTable/index.tsx:313
#: src/components/common/QuestionsTable/index.tsx:355
msgid "Order questions"
msgstr "Fragen zur Bestellung"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:112
msgid "Order Reference"
msgstr "Bestellnummer"

#: src/components/forms/WebhookForm/index.tsx:70
msgid "Order Refunded"
msgstr "Bestellung erstattet"

#: src/components/routes/event/orders.tsx:45
msgid "Order Status"
msgstr "Bestellstatus"

#: src/components/modals/SendMessageModal/index.tsx:228
msgid "Order statuses"
msgstr "Bestellstatus"

#: src/components/layouts/Checkout/CheckoutSidebar/index.tsx:28
#: src/components/modals/ManageOrderModal/index.tsx:105
msgid "Order Summary"
msgstr "Bestellübersicht"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:90
msgid "Order timeout"
msgstr "Bestell-Timeout"

#: src/components/forms/WebhookForm/index.tsx:58
msgid "Order Updated"
msgstr "Bestellung aktualisiert"

#: src/components/layouts/Event/index.tsx:100
#: src/components/routes/event/orders.tsx:113
msgid "Orders"
msgstr "Aufträge"

#: src/components/common/StatBoxes/index.tsx:51
#: src/components/routes/event/EventDashboard/index.tsx:105
#~ msgid "Orders Created"
#~ msgstr "Erstellte Aufträge"

#: src/components/routes/event/orders.tsx:94
msgid "Orders Exported"
msgstr "Bestellungen exportiert"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:235
msgid "Organization Address"
msgstr "Organisationsadresse"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:226
msgid "Organization Details"
msgstr "Organisationsdetails"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:228
msgid "Organization Name"
msgstr "Organisationsname"

#: src/components/modals/EditUserModal/index.tsx:55
#: src/components/modals/InviteUserModal/index.tsx:47
#: src/components/routes/events/Dashboard/index.tsx:113
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:186
msgid "Organizer"
msgstr "Veranstalter"

#: src/components/modals/CreateEventModal/index.tsx:41
msgid "Organizer is required"
msgstr "Veranstalter ist erforderlich"

#: src/components/forms/OrganizerForm/index.tsx:27
msgid "Organizer Name"
msgstr "Name des Organisators"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
msgid "Organizers can only manage events and products. They cannot manage users, account settings or billing information."
msgstr "Organisatoren können nur Veranstaltungen und Produkte verwalten. Sie können keine Benutzer, Kontoeinstellungen oder Abrechnungsinformationen verwalten."

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
#~ msgid "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."
#~ msgstr "Organisatoren können nur Events und Tickets verwalten. Sie können keine Benutzer, Kontoeinstellungen oder Rechnungsinformationen verwalten."

#: src/components/layouts/Event/index.tsx:87
msgid "Overview"
msgstr "Übersicht"

#: src/components/common/WidgetEditor/index.tsx:223
msgid "Padding"
msgstr "Polsterung"

#: src/components/routes/event/HomepageDesigner/index.tsx:150
msgid "Page background color"
msgstr "Hintergrundfarbe der Seite"

#: src/components/common/ErrorDisplay/index.tsx:13
msgid "Page not found"
msgstr "Seite nicht gefunden"

#: src/components/common/StatBoxes/index.tsx:45
msgid "Page views"
msgstr "Seitenaufrufe"

#: src/components/forms/ProductForm/index.tsx:358
msgid "page."
msgstr "Seite."

#: src/components/modals/CreateAttendeeModal/index.tsx:180
msgid "paid"
msgstr "bezahlt"

#: src/components/forms/ProductForm/index.tsx:145
msgid "Paid Product"
msgstr "Bezahltes Produkt"

#: src/components/forms/TicketForm/index.tsx:127
#~ msgid "Paid Ticket"
#~ msgstr "Bezahltes Ticket"

#: src/components/routes/event/orders.tsx:30
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:54
msgid "Partially Refunded"
msgstr "Teilweise erstattet"

#: src/components/routes/auth/Login/index.tsx:73
#: src/components/routes/auth/Register/index.tsx:106
#: src/components/routes/profile/ManageProfile/index.tsx:109
msgid "Password"
msgstr "Passwort"

#: src/components/routes/auth/AcceptInvitation/index.tsx:30
msgid "Password must be a minimum  of 8 characters"
msgstr "Das Passwort muss mindestens 8 Zeichen lang sein"

#: src/components/routes/auth/Register/index.tsx:36
msgid "Password must be at least 8 characters"
msgstr "Das Passwort muss mindestens 8 Zeichen lang sein"

#: src/components/routes/auth/ResetPassword/index.tsx:46
msgid "Password reset successfully. Please login with your new password."
msgstr "Passwort erfolgreich zurückgesetzt. Bitte melden Sie sich mit Ihrem neuen Passwort an."

#: src/components/routes/auth/AcceptInvitation/index.tsx:31
#: src/components/routes/auth/Register/index.tsx:37
msgid "Passwords are not the same"
msgstr "Passwörter sind nicht gleich"

#: src/components/common/WidgetEditor/index.tsx:269
msgid "Paste this where you want the widget to appear."
msgstr "Fügen Sie dies dort ein, wo das Widget erscheinen soll."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:104
msgid "Paste URL"
msgstr "URL einfügen"

#: src/components/modals/CreateAttendeeModal/index.tsx:119
msgid "Patrick"
msgstr "Patrick"

#: src/components/modals/CreateAttendeeModal/index.tsx:133
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/forms/WebhookForm/index.tsx:25
msgid "Paused"
msgstr "Pausiert"

#: src/components/forms/StripeCheckoutForm/index.tsx:123
msgid "Payment"
msgstr "Zahlung"

#: src/components/routes/event/Settings/index.tsx:64
msgid "Payment & Invoicing"
msgstr "Zahlung & Rechnungsstellung"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:98
msgid "Payment & Invoicing Settings"
msgstr "Einstellungen für Zahlung & Rechnungsstellung"

#: src/components/routes/account/ManageAccount/index.tsx:38
msgid "Payment & Plan"
msgstr "Zahlung & Plan"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:216
msgid "Payment Due Period"
msgstr "Zahlungsfrist"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:41
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:64
msgid "Payment Failed"
msgstr "Bezahlung fehlgeschlagen"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:228
#: src/components/routes/product-widget/Payment/PaymentMethods/Offline/index.tsx:14
msgid "Payment Instructions"
msgstr "Zahlungsanweisungen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:105
msgid "Payment Methods"
msgstr "Zahlungsmethoden"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:138
msgid "Payment Processing"
msgstr "Zahlungsabwicklung"

#: src/components/common/OrderDetails/index.tsx:76
msgid "Payment provider"
msgstr "Zahlungsanbieter"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:42
msgid "Payment Received"
msgstr "Zahlung erhalten"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:240
msgid "Payment Settings"
msgstr "Zahlungseinstellungen"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:135
msgid "Payment Status"
msgstr "Zahlungsstatus"

#: src/components/forms/StripeCheckoutForm/index.tsx:60
msgid "Payment succeeded!"
msgstr "Zahlung erfolgreich abgeschlossen!"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:214
msgid "Payment Terms"
msgstr "Zahlungsbedingungen"

#: src/components/forms/PromoCodeForm/index.tsx:51
#: src/components/forms/TaxAndFeeForm/index.tsx:27
msgid "Percentage"
msgstr "Prozentsatz"

#: src/components/forms/TaxAndFeeForm/index.tsx:71
msgid "Percentage Amount"
msgstr "Prozentualer Betrag"

#: src/components/routes/product-widget/Payment/index.tsx:122
msgid "Place Order"
msgstr "Bestellung aufgeben"

#: src/components/common/WidgetEditor/index.tsx:257
msgid "Place this in the <head> of your website."
msgstr "Platzieren Sie dies im <head> Ihrer Website."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:40
msgid "Platform Fees"
msgstr "Plattformgebühren"

#: src/components/forms/QuestionForm/index.tsx:31
msgid "Please add at least one option"
msgstr "Bitte fügen Sie mindestens eine Option hinzu"

#: src/hooks/useFormErrorResponseHandler.tsx:18
msgid "Please check the provided information is correct"
msgstr "Bitte überprüfen Sie, ob die angegebenen Informationen korrekt sind"

#: src/components/routes/auth/Login/index.tsx:41
msgid "Please check your email and password and try again"
msgstr "Bitte überprüfen Sie Ihre E-Mail und Ihr Passwort und versuchen Sie es erneut"

#: src/components/routes/auth/AcceptInvitation/index.tsx:32
#: src/components/routes/auth/Register/index.tsx:38
msgid "Please check your email is valid"
msgstr "Bitte überprüfen Sie, ob Ihre E-Mail gültig ist"

#: src/components/routes/profile/ManageProfile/index.tsx:152
msgid "Please check your email to confirm your email address"
msgstr "Bitte überprüfen Sie Ihre E-Mail, um Ihre E-Mail-Adresse zu bestätigen"

#: src/components/routes/auth/AcceptInvitation/index.tsx:86
msgid "Please complete the form below to accept your invitation"
msgstr "Bitte füllen Sie das folgende Formular aus, um Ihre Einladung anzunehmen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:306
msgid "Please continue in the new tab"
msgstr "Bitte fahren Sie im neuen Tab fort"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:47
msgid "Please create a product"
msgstr "Bitte erstellen Sie ein Produkt"

#: src/components/modals/CreateCheckInListModal/index.tsx:51
msgid "Please create a ticket"
msgstr "Bitte erstellen Sie ein Ticket"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:45
msgid "Please enter a valid image URL that points to an image."
msgstr "Bitte geben Sie eine gültige Bild-URL ein, die auf ein Bild verweist."

#: src/components/modals/CreateWebhookModal/index.tsx:30
msgid "Please enter a valid URL"
msgstr "Bitte geben Sie eine gültige URL ein"

#: src/components/routes/auth/ResetPassword/index.tsx:55
msgid "Please enter your new password"
msgstr "Bitte geben Sie Ihr neues Passwort ein"

#: src/components/modals/CancelOrderModal/index.tsx:52
msgid "Please Note"
msgstr "Bitte beachten Sie"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:34
msgid "Please provide an image."
msgstr "Bitte ein Bild angeben."

#: src/components/common/TicketsTable/index.tsx:84
#~ msgid "Please remove filters and set sorting to \"Homepage order\" to enable sorting"
#~ msgstr "Bitte entfernen Sie die Filter und stellen Sie die Sortierung auf \"Startseite-Reihenfolge\", um die Sortierung zu aktivieren"

#: src/components/layouts/Checkout/index.tsx:159
msgid "Please return to the event page to start over."
msgstr "Bitte kehre zur Veranstaltungsseite zurück, um neu zu beginnen."

#: src/components/modals/SendMessageModal/index.tsx:195
msgid "Please select"
msgstr "Bitte auswählen"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:53
msgid "Please select an image."
msgstr "Bitte ein Bild auswählen."

#: src/components/routes/product-widget/SelectProducts/index.tsx:237
msgid "Please select at least one product"
msgstr "Bitte wählen Sie mindestens ein Produkt aus"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:227
#~ msgid "Please select at least one ticket"
#~ msgstr "Bitte wählen Sie mindestens ein Ticket aus"

#: src/components/routes/event/attendees.tsx:49
#: src/components/routes/event/orders.tsx:100
msgid "Please try again."
msgstr "Bitte versuchen Sie es erneut."

#: src/components/routes/profile/ManageProfile/index.tsx:142
msgid "Please verify your email address to access all features"
msgstr "Bitte bestätigen Sie Ihre E-Mail-Adresse, um auf alle Funktionen zugreifen zu können"

#: src/components/routes/event/attendees.tsx:40
msgid "Please wait while we prepare your attendees for export..."
msgstr "Bitte warten Sie, während wir Ihre Teilnehmer für den Export vorbereiten..."

#: src/components/common/OrdersTable/index.tsx:112
#: src/components/layouts/Checkout/index.tsx:50
msgid "Please wait while we prepare your invoice..."
msgstr "Bitte warten Sie, während wir Ihre Rechnung vorbereiten..."

#: src/components/routes/event/orders.tsx:91
msgid "Please wait while we prepare your orders for export..."
msgstr "Bitte warten Sie, während wir Ihre Bestellungen für den Export vorbereiten..."

#: src/components/common/LanguageSwitcher/index.tsx:26
msgid "Portuguese"
msgstr "Portugiesisch"

#: src/locales.ts:47
#~ msgid "Portuguese (Brazil)"
#~ msgstr "Portugiesisch (Brasilien)"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:74
msgid "Post Checkout message"
msgstr "Checkout-Nachricht veröffentlichen"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Powered by"
msgstr "Angetrieben von"

#: src/components/forms/StripeCheckoutForm/index.tsx:137
#~ msgid "Powered by Stripe"
#~ msgstr "Unterstützt durch Stripe"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:67
msgid "Pre Checkout message"
msgstr "Nachricht vor dem Checkout"

#: src/components/common/QuestionsTable/index.tsx:347
msgid "Preview"
msgstr "Vorschau"

#: src/components/layouts/Event/index.tsx:205
#: src/components/layouts/Event/index.tsx:209
msgid "Preview Event page"
msgstr "Vorschau der Veranstaltungsseite"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:236
#: src/components/forms/ProductForm/index.tsx:73
#: src/components/forms/ProductForm/index.tsx:285
msgid "Price"
msgstr "Preis"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:78
msgid "Price display mode"
msgstr "Preisanzeigemodus"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:88
msgid "Price not set"
msgstr "Preis nicht festgelegt"

#: src/components/forms/ProductForm/index.tsx:322
msgid "Price tiers"
msgstr "Preisstufen"

#: src/components/forms/ProductForm/index.tsx:228
msgid "Price Type"
msgstr "Preistyp"

#: src/components/common/WidgetEditor/index.tsx:182
msgid "Primary Color"
msgstr "Primärfarbe"

#: src/components/routes/event/HomepageDesigner/index.tsx:156
msgid "Primary Colour"
msgstr "Grundfarbe"

#: src/components/common/WidgetEditor/index.tsx:190
#: src/components/routes/event/HomepageDesigner/index.tsx:158
msgid "Primary Text Color"
msgstr "Primäre Textfarbe"

#: src/components/common/AttendeeTicket/index.tsx:81
msgid "Print"
msgstr "Drucken"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:288
msgid "Print All Tickets"
msgstr "Alle Tickets ausdrucken"

#: src/components/layouts/Checkout/index.tsx:114
msgid "Print Tickets"
msgstr "Tickets drucken"

#: src/components/common/AttendeeTable/index.tsx:190
#~ msgid "product"
#~ msgstr "Produkt"

#: src/components/common/AttendeeDetails/index.tsx:37
#: src/components/modals/ManageAttendeeModal/index.tsx:119
msgid "Product"
msgstr "Produkt"

#: src/components/forms/ProductForm/index.tsx:266
msgid "Product Category"
msgstr "Produktkategorie"

#: src/components/modals/EditProductCategoryModal/index.tsx:53
msgid "Product category updated successfully."
msgstr "Produktkategorie erfolgreich aktualisiert."

#: src/components/forms/WebhookForm/index.tsx:34
msgid "Product Created"
msgstr "Produkt erstellt"

#: src/components/forms/WebhookForm/index.tsx:46
msgid "Product Deleted"
msgstr "Produkt gelöscht"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:55
msgid "Product deleted successfully"
msgstr "Produkt erfolgreich gelöscht"

#: src/components/common/AttendeeTable/index.tsx:50
#~ msgid "Product email has been resent to attendee"
#~ msgstr "Produkt-E-Mail wurde an den Teilnehmer erneut gesendet"

#: src/components/forms/ProductForm/index.tsx:236
msgid "Product Price Type"
msgstr "Produktpreistyp"

#: src/components/common/QuestionsTable/index.tsx:328
msgid "Product questions"
msgstr "Produktfragen"

#: src/components/routes/event/EventDashboard/index.tsx:217
#: src/components/routes/event/Reports/index.tsx:17
msgid "Product Sales"
msgstr "Produktverkäufe"

#: src/components/routes/event/Reports/index.tsx:18
msgid "Product sales, revenue, and tax breakdown"
msgstr "Produktverkäufe, Einnahmen und Steueraufschlüsselung"

#: src/components/common/ProductSelector/index.tsx:63
msgid "Product Tier"
msgstr "Produktebene"

#: src/components/forms/ProductForm/index.tsx:214
#: src/components/forms/ProductForm/index.tsx:221
msgid "Product Type"
msgstr "Produkttyp"

#: src/components/forms/WebhookForm/index.tsx:40
msgid "Product Updated"
msgstr "Produkt aktualisiert"

#: src/components/common/WidgetEditor/index.tsx:313
msgid "Product Widget Preview"
msgstr "Produkt-Widget-Vorschau"

#: src/components/common/PromoCodeTable/index.tsx:148
msgid "Product(s)"
msgstr "Produkt(e)"

#: src/components/common/PromoCodeTable/index.tsx:72
msgid "Products"
msgstr "Produkte"

#: src/components/common/EventCard/index.tsx:126
msgid "products sold"
msgstr "verkaufte Produkte"

#: src/components/common/StatBoxes/index.tsx:21
msgid "Products sold"
msgstr "Verkaufte Produkte"

#: src/components/routes/event/EventDashboard/index.tsx:238
msgid "Products Sold"
msgstr "Verkaufte Produkte"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:178
msgid "Products sorted successfully"
msgstr "Produkte erfolgreich sortiert"

#: src/components/layouts/AuthLayout/index.tsx:43
msgid "Products, merchandise, and flexible pricing options"
msgstr "Produkte, Waren und flexible Preisoptionen"

#: src/components/routes/profile/ManageProfile/index.tsx:106
msgid "Profile"
msgstr "Profil"

#: src/components/routes/profile/ManageProfile/index.tsx:59
msgid "Profile updated successfully"
msgstr "Profil erfolgreich aktualisiert"

#: src/components/routes/product-widget/SelectProducts/index.tsx:178
msgid "Promo {promo_code} code applied"
msgstr "Aktionscode {promo_code} angewendet"

#: src/components/common/OrderDetails/index.tsx:86
msgid "Promo code"
msgstr "Gutscheincode"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:20
msgid "Promo Code"
msgstr "Aktionscode"

#: src/components/forms/ProductForm/index.tsx:409
msgid "Promo Code page"
msgstr "Aktionscode-Seite"

#: src/components/routes/event/Reports/index.tsx:30
msgid "Promo code usage and discount breakdown"
msgstr "Nutzung von Rabattcodes und Rabattaufschlüsselung"

#: src/components/layouts/Event/index.tsx:106
#: src/components/routes/event/promo-codes.tsx:31
msgid "Promo Codes"
msgstr "Promo-Codes"

#: src/components/common/PromoCodeTable/index.tsx:51
msgid "Promo codes can be used to offer discounts, presale access, or provide special access to your event."
msgstr "Mit Promo-Codes können Sie Rabatte oder Vorverkaufszugang anbieten oder Sonderzugang zu Ihrer Veranstaltung gewähren."

#: src/components/routes/event/Reports/index.tsx:29
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:95
msgid "Promo Codes Report"
msgstr "Bericht zu Aktionscodes"

#: src/components/forms/QuestionForm/index.tsx:189
msgid ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."
msgstr "Geben Sie zusätzlichen Kontext oder Anweisungen für diese Frage an. Verwenden Sie dieses Feld, um Bedingungen, Richtlinien oder wichtige Informationen hinzuzufügen, die die Teilnehmer vor dem Beantworten wissen müssen."

#: src/components/routes/event/EventDashboard/index.tsx:159
msgid "Publish Event"
msgstr "Veranstaltung veröffentlichen"

#: src/components/common/GlobalMenu/index.tsx:25
#~ msgid "Purchase License"
#~ msgstr "Lizenz kaufen"

#: src/components/modals/ShareModal/index.tsx:126
msgid "QR Code"
msgstr "QR-Code"

#: src/components/layouts/AuthLayout/index.tsx:64
msgid "QR code scanning with instant feedback and secure sharing for staff access"
msgstr "QR-Code-Scanning mit sofortigem Feedback und sicherem Teilen für den Mitarbeiterzugang"

#: src/components/forms/ProductForm/index.tsx:85
#: src/components/forms/ProductForm/index.tsx:302
msgid "Quantity Available"
msgstr "Verfügbare Menge"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
msgid "Quantity Sold"
msgstr "Verkaufte Menge"

#: src/components/common/QuestionsTable/index.tsx:170
msgid "Question deleted"
msgstr "Frage gelöscht"

#: src/components/forms/QuestionForm/index.tsx:188
msgid "Question Description"
msgstr "Fragebeschreibung"

#: src/components/forms/QuestionForm/index.tsx:178
msgid "Question Title"
msgstr "Fragentitel"

#: src/components/common/QuestionsTable/index.tsx:275
#: src/components/layouts/Event/index.tsx:102
msgid "Questions"
msgstr "Fragen"

#: src/components/modals/ManageAttendeeModal/index.tsx:184
#: src/components/modals/ManageOrderModal/index.tsx:111
msgid "Questions & Answers"
msgstr "Fragen & Antworten"

#: src/components/common/QuestionsTable/index.tsx:145
msgid "Questions sorted successfully"
msgstr "Fragen erfolgreich sortiert"

#: src/components/forms/QuestionForm/index.tsx:118
msgid "Radio Option"
msgstr "Radio-Option"

#: src/components/common/MessageList/index.tsx:59
msgid "Read less"
msgstr "Lese weniger"

#: src/components/modals/SendMessageModal/index.tsx:36
msgid "Recipient"
msgstr "Empfänger"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:110
#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:195
msgid "Redirecting to Stripe..."
msgstr "Weiterleitung zu Stripe..."

#: src/components/common/OrdersTable/index.tsx:204
#: src/components/common/OrdersTable/index.tsx:274
msgid "Reference"
msgstr "Referenz"

#: src/components/modals/RefundOrderModal/index.tsx:104
msgid "Refund amount ({0})"
msgstr "Rückerstattungsbetrag ({0})"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:52
msgid "Refund Failed"
msgstr "Rückerstattung fehlgeschlagen"

#: src/components/common/OrdersTable/index.tsx:172
msgid "Refund order"
msgstr "Rückerstattungsauftrag"

#: src/components/modals/RefundOrderModal/index.tsx:151
msgid "Refund Order"
msgstr "Rückerstattungsauftrag"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:51
msgid "Refund Pending"
msgstr "Rückerstattung ausstehend"

#: src/components/routes/event/orders.tsx:51
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:128
msgid "Refund Status"
msgstr "Rückerstattungsstatus"

#: src/components/common/OrderSummary/index.tsx:80
#: src/components/common/StatBoxes/index.tsx:33
#: src/components/routes/event/orders.tsx:29
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:53
msgid "Refunded"
msgstr "Rückerstattung"

#: src/components/routes/auth/Register/index.tsx:129
msgid "Register"
msgstr "Registrieren"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:82
msgid "Remaining Uses"
msgstr "Verbleibende Verwendungen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:504
msgid "remove"
msgstr "entfernen"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:140
#: src/components/routes/product-widget/SelectProducts/index.tsx:505
msgid "Remove"
msgstr "Entfernen"

#: src/components/layouts/Event/index.tsx:92
#: src/components/routes/event/Reports/index.tsx:39
msgid "Reports"
msgstr "Berichte"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:169
msgid "Require Billing Address"
msgstr "Rechnungsadresse erforderlich"

#: src/components/routes/event/GettingStarted/index.tsx:197
msgid "Resend confirmation email"
msgstr "Bestätigungsmail erneut senden"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resend email confirmation"
msgstr "E-Mail-Bestätigung erneut senden"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Resend invitation"
msgstr "Einladung erneut versenden"

#: src/components/common/OrdersTable/index.tsx:179
msgid "Resend order email"
msgstr "Bestell-E-Mail erneut senden"

#: src/components/common/AttendeeTable/index.tsx:179
#~ msgid "Resend product email"
#~ msgstr "Produkt-E-Mail erneut senden"

#: src/components/common/AttendeeTable/index.tsx:171
msgid "Resend ticket email"
msgstr "Ticket-E-Mail erneut senden"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resending..."
msgstr "Erneut senden..."

#: src/components/common/FilterModal/index.tsx:248
msgid "Reset"
msgstr "Zurücksetzen"

#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Reset password"
msgstr "Passwort zurücksetzen"

#: src/components/routes/auth/ResetPassword/index.tsx:54
msgid "Reset Password"
msgstr "Passwort zurücksetzen"

#: src/components/routes/auth/ForgotPassword/index.tsx:50
msgid "Reset your password"
msgstr "Setzen Sie Ihr Passwort zurück"

#: src/components/common/EventCard/index.tsx:167
msgid "Restore event"
msgstr "Veranstaltung wiederherstellen"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:50
#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:63
msgid "Return to event page"
msgstr "Zurück zur Veranstaltungsseite"

#: src/components/layouts/Checkout/index.tsx:165
msgid "Return to Event Page"
msgstr "Zur Veranstaltungsseite zurückkehren"

#: src/components/routes/event/EventDashboard/index.tsx:249
msgid "Revenue"
msgstr "Einnahmen"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Revoke invitation"
msgstr "Einladung widerrufen"

#: src/components/modals/EditUserModal/index.tsx:93
#: src/components/modals/InviteUserModal/index.tsx:64
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Role"
msgstr "Rolle"

#: src/components/forms/ProductForm/index.tsx:96
#: src/components/forms/ProductForm/index.tsx:391
msgid "Sale End Date"
msgstr "Verkaufsende"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:75
msgid "Sale ended"
msgstr "Der Verkauf ist beendet"

#: src/components/forms/ProductForm/index.tsx:91
#: src/components/forms/ProductForm/index.tsx:389
msgid "Sale Start Date"
msgstr "Verkaufsstartdatum"

#: src/components/common/ProductPriceAvailability/index.tsx:13
#: src/components/common/ProductPriceAvailability/index.tsx:35
msgid "Sales ended"
msgstr "Verkauf beendet"

#: src/components/common/ProductPriceAvailability/index.tsx:19
#: src/components/common/ProductPriceAvailability/index.tsx:40
msgid "Sales start"
msgstr "Verkaufsstart"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:135
msgid "San Francisco"
msgstr "San Francisco"

#: src/components/common/QuestionAndAnswerList/index.tsx:142
#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:80
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:114
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:96
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:166
#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:94
#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:265
#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:85
msgid "Save"
msgstr "Speichern"

#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/event/HomepageDesigner/index.tsx:166
msgid "Save Changes"
msgstr "Änderungen speichern"

#: src/components/modals/EditOrganizerModal/index.tsx:70
msgid "Save Organizer"
msgstr "Organizer speichern"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:90
msgid "Save Settings"
msgstr "Einstellungen speichern"

#: src/components/layouts/CheckIn/index.tsx:398
#: src/components/layouts/CheckIn/index.tsx:400
msgid "Scan QR Code"
msgstr "QR-Code scannen"

#: src/components/modals/ShareModal/index.tsx:229
msgid "Scan this QR code to access the event page or share it with others"
msgstr "Scannen Sie diesen QR-Code, um auf die Veranstaltungsseite zuzugreifen oder teilen Sie ihn mit anderen"

#: src/components/layouts/CheckIn/index.tsx:288
#~ msgid "Seach by name, order #, attendee # or email..."
#~ msgstr "Suche nach Namen, Bestellnummer, Teilnehmernummer oder E-Mail ..."

#: src/components/routes/event/attendees.tsx:64
msgid "Search by attendee name, email or order #..."
msgstr "Suche nach Teilnehmername, E-Mail oder Bestellnummer ..."

#: src/components/routes/events/Dashboard/index.tsx:69
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:66
msgid "Search by event name..."
msgstr "Suche nach Veranstaltungsnamen..."

#: src/components/routes/event/orders.tsx:126
msgid "Search by name, email, or order #..."
msgstr "Suchen Sie nach Namen, E-Mail oder Bestellnummer ..."

#: src/components/layouts/CheckIn/index.tsx:394
msgid "Search by name, order #, attendee # or email..."
msgstr "Suchen nach Name, Bestellnummer, Teilnehmernummer oder E-Mail..."

#: src/components/routes/event/promo-codes.tsx:34
msgid "Search by name..."
msgstr "Suche mit Name..."

#: src/components/routes/event/products.tsx:43
#~ msgid "Search by product name..."
#~ msgstr ""

#: src/components/routes/event/messages.tsx:34
msgid "Search by subject or content..."
msgstr "Suche nach Thema oder Inhalt..."

#: src/components/routes/event/tickets.tsx:43
#~ msgid "Search by ticket name..."
#~ msgstr "Suche nach Ticketnamen..."

#: src/components/routes/event/CapacityAssignments/index.tsx:37
msgid "Search capacity assignments..."
msgstr "Kapazitätszuweisungen suchen..."

#: src/components/routes/event/CheckInLists/index.tsx:37
msgid "Search check-in lists..."
msgstr "Einchecklisten durchsuchen..."

#: src/components/routes/event/products.tsx:52
msgid "Search products"
msgstr "Produkte suchen"

#: src/components/common/SearchBar/index.tsx:36
msgid "Search..."
msgstr "Suchen..."

#: src/components/routes/event/HomepageDesigner/index.tsx:160
msgid "Secondary color"
msgstr "Sekundäre Farbe"

#: src/components/common/WidgetEditor/index.tsx:198
msgid "Secondary Color"
msgstr "Sekundäre Farbe"

#: src/components/routes/event/HomepageDesigner/index.tsx:162
msgid "Secondary text color"
msgstr "Sekundäre Textfarbe"

#: src/components/common/WidgetEditor/index.tsx:206
msgid "Secondary Text Color"
msgstr "Sekundäre Textfarbe"

#: src/components/common/FilterModal/index.tsx:150
msgid "Select {0}"
msgstr "{0} auswählen"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:214
msgid "Select Camera"
msgstr "Kamera auswählen"

#: src/components/forms/ProductForm/index.tsx:269
msgid "Select category..."
msgstr "Kategorie auswählen..."

#: src/components/forms/WebhookForm/index.tsx:124
msgid "Select event types"
msgstr "Ereignistypen auswählen"

#: src/components/modals/CreateEventModal/index.tsx:110
msgid "Select organizer"
msgstr "Veranstalter auswählen"

#: src/components/modals/ManageAttendeeModal/index.tsx:118
msgid "Select Product"
msgstr "Produkt auswählen"

#: src/components/common/ProductSelector/index.tsx:65
msgid "Select Product Tier"
msgstr "Produktebene auswählen"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:49
#: src/components/modals/SendMessageModal/index.tsx:219
msgid "Select products"
msgstr "Produkte auswählen"

#: src/components/modals/EditUserModal/index.tsx:104
msgid "Select status"
msgstr "Status auswählen"

#: src/components/modals/CreateAttendeeModal/index.tsx:150
msgid "Select Ticket"
msgstr "Ticket auswählen"

#: src/components/modals/CreateAttendeeModal/index.tsx:163
#: src/components/modals/EditAttendeeModal/index.tsx:117
#~ msgid "Select Ticket Tier"
#~ msgstr "Ticketstufe auswählen"

#: src/components/forms/CheckInListForm/index.tsx:26
#: src/components/modals/SendMessageModal/index.tsx:207
msgid "Select tickets"
msgstr "Tickets auswählen"

#: src/components/common/ReportTable/index.tsx:261
msgid "Select time period"
msgstr "Zeitraum auswählen"

#: src/components/forms/WebhookForm/index.tsx:123
msgid "Select which events will trigger this webhook"
msgstr "Wählen Sie aus, welche Ereignisse diesen Webhook auslösen"

#: src/components/forms/ProductForm/index.tsx:352
msgid "Select..."
msgstr "Wählen..."

#: src/components/layouts/AuthLayout/index.tsx:68
msgid "Sell Anything"
msgstr "Sell Anything"

#: src/components/layouts/AuthLayout/index.tsx:69
msgid "Sell merchandise alongside tickets with integrated tax and promo code support"
msgstr "Verkaufen Sie Waren zusammen mit Tickets mit integrierter Steuer- und Rabattcode-Unterstützung"

#: src/components/layouts/AuthLayout/index.tsx:42
msgid "Sell More Than Tickets"
msgstr "Verkaufen Sie mehr als nur Tickets"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send"
msgstr "Schicken"

#: src/components/modals/SendMessageModal/index.tsx:259
msgid "Send a copy to <0>{0}</0>"
msgstr "Senden Sie eine Kopie an <0>{0}</0>"

#: src/components/modals/SendMessageModal/index.tsx:139
msgid "Send a message"
msgstr "Eine Nachricht schicken"

#: src/components/modals/SendMessageModal/index.tsx:269
msgid "Send as a test. This will send the message to your email address instead of the recipients."
msgstr "Als Test senden. Dadurch wird die Nachricht an Ihre E-Mail-Adresse und nicht an die Empfänger gesendet."

#: src/components/routes/event/messages.tsx:41
msgid "Send Message"
msgstr "Nachricht Senden"

#: src/components/modals/CreateAttendeeModal/index.tsx:191
#~ msgid "Send order confirmation and product email"
#~ msgstr "Bestellbestätigung und Produkt-E-Mail senden"

#: src/components/modals/CreateAttendeeModal/index.tsx:192
msgid "Send order confirmation and ticket email"
msgstr "Bestellbestätigung und Ticket-E-Mail senden"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send Test"
msgstr "Test senden"

#: src/components/routes/event/Settings/index.tsx:46
msgid "SEO"
msgstr "SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:70
msgid "SEO Description"
msgstr "SEO-Beschreibung"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:76
msgid "SEO Keywords"
msgstr "SEO-Schlüsselwörter"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:56
msgid "SEO Settings"
msgstr "SEO-Einstellungen"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:64
msgid "SEO Title"
msgstr "SEO-Titel"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "Service Fee"
msgstr "Servicegebühr"

#: src/components/forms/ProductForm/index.tsx:159
msgid "Set a minimum price and let users pay more if they choose"
msgstr "Legen Sie einen Mindestpreis fest und lassen Sie die Nutzer mehr zahlen, wenn sie wollen."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:207
msgid "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."
msgstr "Legen Sie die Startnummer für die Rechnungsnummerierung fest. Dies kann nicht geändert werden, sobald Rechnungen generiert wurden."

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Set up your event"
msgstr "Richten Sie Ihre Veranstaltung ein"

#: src/components/routes/event/EventDashboard/index.tsx:190
#~ msgid "Set up your payment processing to receive funds from ticket sales."
#~ msgstr "Richten Sie Ihre Zahlungsabwicklung ein, um Gelder aus Ticketverkäufen zu erhalten."

#: src/components/routes/event/GettingStarted/index.tsx:183
msgid "Set your event live"
msgstr "Schalten Sie Ihr Event live"

#: src/components/layouts/Event/index.tsx:98
#: src/components/routes/event/Settings/index.tsx:103
msgid "Settings"
msgstr "Einstellungen"

#: src/components/layouts/AuthLayout/index.tsx:26
msgid "Setup in Minutes"
msgstr "Einrichtung in Minuten"

#: src/components/common/ShareIcon/index.tsx:28
#: src/components/modals/ShareModal/index.tsx:123
#: src/components/modals/ShareModal/index.tsx:141
msgid "Share"
msgstr "Teilen"

#: src/components/layouts/Event/index.tsx:251
#: src/components/modals/ShareModal/index.tsx:116
msgid "Share Event"
msgstr "Veranstaltung teilen"

#: src/components/modals/ShareModal/index.tsx:169
msgid "Share to Facebook"
msgstr "Auf Facebook teilen"

#: src/components/modals/ShareModal/index.tsx:163
msgid "Share to LinkedIn"
msgstr "Auf LinkedIn teilen"

#: src/components/modals/ShareModal/index.tsx:193
msgid "Share to Pinterest"
msgstr "Auf Pinterest teilen"

#: src/components/modals/ShareModal/index.tsx:187
msgid "Share to Reddit"
msgstr "Auf Reddit teilen"

#: src/components/modals/ShareModal/index.tsx:148
msgid "Share to Social"
msgstr "Auf sozialen Medien teilen"

#: src/components/modals/ShareModal/index.tsx:181
msgid "Share to Telegram"
msgstr "Auf Telegram teilen"

#: src/components/modals/ShareModal/index.tsx:175
msgid "Share to WhatsApp"
msgstr "Auf WhatsApp teilen"

#: src/components/modals/ShareModal/index.tsx:157
msgid "Share to X"
msgstr "Auf X teilen"

#: src/components/modals/ShareModal/index.tsx:199
msgid "Share via Email"
msgstr "Per E-Mail teilen"

#: src/components/forms/ProductForm/index.tsx:344
msgid "Show"
msgstr "Zeigen"

#: src/components/forms/ProductForm/index.tsx:403
msgid "Show available product quantity"
msgstr "Verfügbare Produktmenge anzeigen"

#: src/components/forms/TicketForm/index.tsx:348
#~ msgid "Show available ticket quantity"
#~ msgstr "Verfügbare Ticketanzahl anzeigen"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Show hidden questions"
msgstr "Versteckte Fragen anzeigen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:459
msgid "Show more"
msgstr "Zeig mehr"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:60
msgid "Show tax and fees separately"
msgstr "Steuern und Gebühren separat ausweisen"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:76
#~ msgid "Shown to the customer after they checkout, on the order summary page"
#~ msgstr "Wird dem Kunden nach dem Bezahlvorgang auf der Bestellübersichtsseite angezeigt"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
msgid "Shown to the customer after they checkout, on the order summary page."
msgstr "Wird dem Kunden nach dem Checkout auf der Bestellübersichtsseite angezeigt."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:69
msgid "Shown to the customer before they checkout"
msgstr "Wird dem Kunden vor dem Bezahlvorgang angezeigt"

#: src/components/forms/QuestionForm/index.tsx:132
msgid "Shows common address fields, including country"
msgstr "Zeigt allgemeine Adressfelder an, einschließlich Land"

#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:151
msgid "Simpson"
msgstr "Simpson"

#: src/components/forms/QuestionForm/index.tsx:100
msgid "Single line text box"
msgstr "Einzeiliges Textfeld"

#: src/components/routes/welcome/index.tsx:137
msgid "Skip this step"
msgstr "Überspringe diesen Schritt"

#: src/components/layouts/AuthLayout/index.tsx:78
msgid "Smart Check-in"
msgstr "Intelligentes Einchecken"

#: src/components/layouts/AuthLayout/index.tsx:53
#~ msgid "Smart Dashboard"
#~ msgstr "Intelligentes Dashboard"

#: src/components/routes/auth/Register/index.tsx:90
msgid "Smith"
msgstr "Schmied"

#: src/components/common/ProductPriceAvailability/index.tsx:9
#: src/components/common/ProductPriceAvailability/index.tsx:32
msgid "Sold out"
msgstr "Ausverkauft"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:67
msgid "Sold Out"
msgstr "Ausverkauft"

#: src/components/common/ErrorDisplay/index.tsx:14
#: src/components/routes/auth/AcceptInvitation/index.tsx:47
msgid "Something went wrong"
msgstr "Etwas ist schief gelaufen"

#: src/components/common/TaxAndFeeList/index.tsx:39
msgid "Something went wrong while deleting the Tax or Fee"
msgstr "Beim Löschen der Steuer oder Gebühr ist ein Fehler aufgetreten"

#: src/components/routes/auth/ForgotPassword/index.tsx:31
msgid "Something went wrong, please try again, or contact support if the problem persists"
msgstr "Etwas ist schiefgelaufen. Bitte versuchen Sie es erneut oder kontaktieren Sie den Support, falls das Problem weiterhin besteht."

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Something went wrong! Please try again"
msgstr "Etwas ist schief gelaufen. Bitte versuche es erneut"

#: src/components/forms/StripeCheckoutForm/index.tsx:69
msgid "Something went wrong."
msgstr "Etwas ist schief gelaufen."

#: src/components/routes/profile/ManageProfile/index.tsx:80
#: src/components/routes/profile/ManageProfile/index.tsx:94
msgid "Something went wrong. Please try again."
msgstr "Etwas ist schief gelaufen. Bitte versuche es erneut."

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:61
msgid "Sorry, something has gone wrong. Please restart the checkout process."
msgstr "Es ist leider ein Fehler aufgetreten. Bitte starten Sie den Bezahlvorgang erneut."

#: src/components/routes/product-widget/CollectInformation/index.tsx:259
msgid "Sorry, something went wrong loading this page."
msgstr "Entschuldigen Sie, beim Laden dieser Seite ist ein Fehler aufgetreten."

#: src/components/routes/product-widget/CollectInformation/index.tsx:247
msgid "Sorry, this order no longer exists."
msgstr "Entschuldigung, diese Bestellung existiert nicht mehr."

#: src/components/routes/product-widget/SelectProducts/index.tsx:246
msgid "Sorry, this promo code is not recognized"
msgstr "Dieser Aktionscode wird leider nicht erkannt"

#: src/components/layouts/Checkout/index.tsx:26
#~ msgid "Sorry, your order has expired. Please start a new order."
#~ msgstr "Leider ist Ihre Bestellung abgelaufen. Bitte starten Sie eine neue Bestellung."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Sorting is disabled while filters and sorting are applied"
#~ msgstr "Die Sortierung ist deaktiviert, während Filter und Sortierung angewendet werden"

#: src/components/common/LanguageSwitcher/index.tsx:18
msgid "Spanish"
msgstr "Spanisch"

#: src/components/forms/ProductForm/index.tsx:147
msgid "Standard product with a fixed price"
msgstr "Standardprodukt mit festem Preis"

#: src/components/forms/TicketForm/index.tsx:129
#~ msgid "Standard ticket with a fixed price"
#~ msgstr "Standardticket zum Festpreis"

#: src/components/modals/CreateEventModal/index.tsx:144
#: src/components/modals/DuplicateEventModal/index.tsx:93
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:87
#: src/components/routes/welcome/index.tsx:90
msgid "Start Date"
msgstr "Startdatum"

#: src/components/common/CheckoutQuestion/index.tsx:165
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:139
#: src/components/routes/product-widget/CollectInformation/index.tsx:339
#: src/components/routes/product-widget/CollectInformation/index.tsx:340
msgid "State or Region"
msgstr "Staat oder Region"

#: src/components/common/AttendeeTable/index.tsx:112
#: src/components/common/OrderDetails/index.tsx:51
#: src/components/common/OrdersTable/index.tsx:209
#: src/components/common/ProductsTable/SortableProduct/index.tsx:222
#: src/components/common/WebhookTable/index.tsx:238
#: src/components/forms/CapaciyAssigmentForm/index.tsx:56
#: src/components/forms/WebhookForm/index.tsx:133
#: src/components/modals/EditUserModal/index.tsx:103
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:88
msgid "Status"
msgstr "Status"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:85
msgid "Stripe"
msgstr "Stripe"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:48
msgid "Stripe payments are not enabled for this event."
msgstr "Stripe-Zahlungen sind für diese Veranstaltung nicht aktiviert."

#: src/components/modals/SendMessageModal/index.tsx:245
msgid "Subject"
msgstr "Thema"

#: src/components/common/OrderSummary/index.tsx:41
msgid "Subtotal"
msgstr "Zwischensumme"

#: src/components/common/OrdersTable/index.tsx:115
#: src/components/layouts/Checkout/index.tsx:53
msgid "Success"
msgstr "Erfolg"

#: src/components/modals/EditUserModal/index.tsx:40
#: src/components/modals/InviteUserModal/index.tsx:32
msgid "Success! {0} will receive an email shortly."
msgstr "Erfolgreich! {0} erhält in Kürze eine E-Mail."

#: src/components/common/AttendeeTable/index.tsx:90
msgid "Successfully {0} attendee"
msgstr "Erfolgreich {0} Teilnehmer"

#: src/components/common/AttendeeCheckInTable/index.tsx:47
#: src/components/common/AttendeeCheckInTable/index.tsx:71
#~ msgid "Successfully checked <0>{0} {1}</0> {2}"
#~ msgstr "Erfolgreich geprüft <0>{0} {1}</0> {2}"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:23
msgid "Successfully confirmed email address"
msgstr "E-Mail-Adresse erfolgreich bestätigt"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:31
msgid "Successfully confirmed email change"
msgstr "E-Mail-Änderung erfolgreich bestätigt"

#: src/components/modals/CreateAttendeeModal/index.tsx:82
msgid "Successfully created attendee"
msgstr "Teilnehmer erfolgreich erstellt"

#: src/components/modals/CreateProductModal/index.tsx:54
msgid "Successfully Created Product"
msgstr "Produkt erfolgreich erstellt"

#: src/components/modals/CreatePromoCodeModal/index.tsx:39
msgid "Successfully Created Promo Code"
msgstr "Promo-Code erfolgreich erstellt"

#: src/components/modals/CreateQuestionModal/index.tsx:45
#: src/components/modals/EditQuestionModal/index.tsx:66
msgid "Successfully Created Question"
msgstr "Frage erfolgreich erstellt"

#: src/components/modals/CreateTicketModal/index.tsx:50
#~ msgid "Successfully Created Ticket"
#~ msgstr "Ticket erfolgreich erstellt"

#: src/components/modals/DuplicateProductModal/index.tsx:95
msgid "Successfully Duplicated Product"
msgstr "Produkt erfolgreich dupliziert"

#: src/components/modals/ManageAttendeeModal/index.tsx:96
msgid "Successfully updated attendee"
msgstr "Teilnehmer erfolgreich aktualisiert"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:47
msgid "Successfully updated Capacity Assignment"
msgstr "Kapazitätszuweisung erfolgreich aktualisiert"

#: src/components/modals/EditCheckInListModal/index.tsx:49
msgid "Successfully updated Check-In List"
msgstr "Eincheckliste erfolgreich aktualisiert"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:43
msgid "Successfully Updated Email Settings"
msgstr "E-Mail-Einstellungen erfolgreich aktualisiert"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:54
msgid "Successfully Updated Event"
msgstr "Ereignis erfolgreich aktualisiert"

#: src/components/routes/event/HomepageDesigner/index.tsx:84
msgid "Successfully Updated Homepage Design"
msgstr "Erfolgreich aktualisiertes Homepage-Design"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:50
msgid "Successfully Updated Homepage Settings"
msgstr "Homepage-Einstellungen erfolgreich aktualisiert"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:73
msgid "Successfully Updated Location"
msgstr "Standort erfolgreich aktualisiert"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:43
msgid "Successfully Updated Misc Settings"
msgstr "Verschiedene Einstellungen erfolgreich aktualisiert"

#: src/components/modals/ManageOrderModal/index.tsx:74
msgid "Successfully updated order"
msgstr "Bestellung erfolgreich aktualisiert"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:74
msgid "Successfully Updated Payment & Invoicing Settings"
msgstr "Einstellungen für Zahlung & Rechnungsstellung erfolgreich aktualisiert"

#: src/components/modals/EditProductModal/index.tsx:89
msgid "Successfully updated product"
msgstr "Produkt erfolgreich aktualisiert"

#: src/components/modals/EditPromoCodeModal/index.tsx:52
msgid "Successfully Updated Promo Code"
msgstr "Promo-Code erfolgreich aktualisiert"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:45
msgid "Successfully Updated Seo Settings"
msgstr "SEO-Einstellungen erfolgreich aktualisiert"

#: src/components/modals/EditTicketModal/index.tsx:85
#~ msgid "Successfully updated ticket"
#~ msgstr "Ticket erfolgreich aktualisiert"

#: src/components/modals/EditWebhookModal/index.tsx:44
msgid "Successfully updated Webhook"
msgstr "Webhook erfolgreich aktualisiert"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:128
msgid "Suite 100"
msgstr "Suite 100"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
msgid "Support Email"
msgstr "Support-E-Mail"

#: src/components/layouts/AuthLayout/index.tsx:59
msgid "Support for tiered, donation-based, and product sales with customizable pricing and capacity"
msgstr "Unterstützung für gestaffelte, spendenbasierte und Produktverkäufe mit anpassbaren Preisen und Kapazitäten"

#: src/components/forms/ProductForm/index.tsx:254
msgid "T-shirt"
msgstr "T-Shirt"

#: src/components/forms/TaxAndFeeForm/index.tsx:12
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Tax"
msgstr "Steuer"

#: src/components/routes/account/ManageAccount/index.tsx:27
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:16
msgid "Tax & Fees"
msgstr "Steuern & Gebühren"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:243
msgid "Tax Details"
msgstr "Steuerdetails"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:244
msgid "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"
msgstr "Steuerinformationen, die unten auf allen Rechnungen erscheinen sollen (z. B. USt-Nummer, Steuerregistrierung)"

#: src/components/common/TaxAndFeeList/index.tsx:36
msgid "Tax or Fee deleted successfully"
msgstr "Steuer oder Gebühr erfolgreich gelöscht"

#: src/components/forms/ProductForm/index.tsx:363
msgid "Taxes"
msgstr "Steuern"

#: src/components/forms/ProductForm/index.tsx:351
#: src/components/forms/ProductForm/index.tsx:358
msgid "Taxes and Fees"
msgstr "Steuern und Gebühren"

#: src/components/routes/product-widget/SelectProducts/index.tsx:138
#: src/components/routes/product-widget/SelectProducts/index.tsx:186
msgid "That promo code is invalid"
msgstr "Dieser Aktionscode ist ungültig"

#: src/components/layouts/CheckIn/index.tsx:316
msgid "The check-in list you are looking for does not exist."
msgstr "Die gesuchte Eincheckliste existiert nicht."

#: src/components/forms/OrganizerForm/index.tsx:47
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:75
msgid "The default currency for your events."
msgstr "Die Standardwährung für Ihre Ereignisse."

#: src/components/forms/OrganizerForm/index.tsx:56
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:84
msgid "The default timezone for your events."
msgstr "Die Standardzeitzone für Ihre Ereignisse."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:16
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:43
msgid "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."
msgstr "Die gesuchte Veranstaltung ist derzeit nicht verfügbar. Sie wurde möglicherweise entfernt, ist abgelaufen oder die URL ist falsch."

#: src/components/modals/CreateAttendeeModal/index.tsx:146
msgid "The language the attendee will receive emails in."
msgstr "Die Sprache, in der der Teilnehmer die E-Mails erhalten soll."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:22
msgid "The link you clicked is invalid."
msgstr "Der Link, auf den Sie geklickt haben, ist ungültig."

#: src/components/routes/product-widget/SelectProducts/index.tsx:444
msgid "The maximum number of products for {0}is {1}"
msgstr "Die maximale Anzahl an Produkten für {0} ist {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:351
#~ msgid "The maximum numbers number of tickets for {0}is {1}"
#~ msgstr "Die maximale Anzahl von Tickets für {0} ist {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:318
#~ msgid "The maximum numbers number of tickets for Generals is {0}"
#~ msgstr "Die maximale Anzahl an Tickets für Generäle beträgt {0}"

#: src/components/common/ErrorDisplay/index.tsx:17
msgid "The page you are looking for does not exist"
msgstr "Die gesuchte Seite existiert nicht"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:56
msgid "The price displayed to the customer will include taxes and fees."
msgstr "Der dem Kunden angezeigte Preis versteht sich inklusive Steuern und Gebühren."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:62
msgid "The price displayed to the customer will not include taxes and fees. They will be shown separately"
msgstr "Der dem Kunden angezeigte Preis enthält keine Steuern und Gebühren. Diese werden separat ausgewiesen"

#: src/components/common/WidgetEditor/index.tsx:169
msgid "The styling settings you choose apply only to copied HTML and won't be stored."
msgstr "Die von Ihnen gewählten Stileinstellungen gelten nur für kopiertes HTML und werden nicht gespeichert."

#: src/components/forms/ProductForm/index.tsx:355
msgid "The taxes and fees to apply to this product. You can create new taxes and fees on the"
msgstr "Die Steuern und Gebühren, die auf dieses Produkt angewendet werden. Sie können neue Steuern und Gebühren erstellen auf der"

#: src/components/forms/TicketForm/index.tsx:300
#~ msgid "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "Die Steuern und Gebühren, die auf dieses Ticket angewendet werden sollen. Sie können neue Steuern und Gebühren auf der"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:63
msgid "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"
msgstr "Der Titel der Veranstaltung, der in Suchmaschinenergebnissen und beim Teilen in sozialen Medien angezeigt wird. Standardmäßig wird der Veranstaltungstitel verwendet"

#: src/components/routes/product-widget/SelectProducts/index.tsx:272
msgid "There are no products available for this event"
msgstr "Für diese Veranstaltung sind keine Produkte verfügbar"

#: src/components/routes/product-widget/SelectProducts/index.tsx:375
msgid "There are no products available in this category"
msgstr "In dieser Kategorie sind keine Produkte verfügbar"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:260
#~ msgid "There are no tickets available for this event"
#~ msgstr "Für diese Veranstaltung sind keine Tickets verfügbar"

#: src/components/modals/RefundOrderModal/index.tsx:137
msgid "There is a refund pending. Please wait for it to complete before requesting another refund."
msgstr "Eine Rückerstattung steht aus. Bitte warten Sie, bis der Vorgang abgeschlossen ist, bevor Sie eine weitere Rückerstattung anfordern."

#: src/components/common/OrdersTable/index.tsx:80
msgid "There was an error marking the order as paid"
msgstr "Beim Markieren der Bestellung als bezahlt ist ein Fehler aufgetreten"

#: src/hooks/useFormErrorResponseHandler.tsx:28
msgid "There was an error processing your request. Please try again."
msgstr "Bei der Bearbeitung Ihrer Anfrage ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut."

#: src/components/common/OrdersTable/index.tsx:95
msgid "There was an error sending your message"
msgstr "Beim Senden Ihrer Nachricht ist ein Fehler aufgetreten"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:105
msgid "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."
msgstr "Diese Details werden nur angezeigt, wenn die Bestellung erfolgreich abgeschlossen wurde. Bestellungen, die auf Zahlung warten, zeigen diese Nachricht nicht an."

#: src/components/layouts/CheckIn/index.tsx:201
msgid "This attendee has an unpaid order."
msgstr "Dieser Teilnehmer hat eine unbezahlte Bestellung."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:61
msgid "This category doesn't have any products yet."
msgstr "Diese Kategorie hat noch keine Produkte."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:112
msgid "This category is hidden from public view"
msgstr "Diese Kategorie ist vor der öffentlichen Ansicht verborgen"

#: src/components/common/CheckInListList/index.tsx:75
msgid "This check-in list has expired"
msgstr "Diese Eincheckliste ist abgelaufen"

#: src/components/layouts/CheckIn/index.tsx:331
msgid "This check-in list has expired and is no longer available for check-ins."
msgstr "Diese Eincheckliste ist abgelaufen und steht nicht mehr für Eincheckungen zur Verfügung."

#: src/components/common/CheckInListList/index.tsx:82
msgid "This check-in list is active"
msgstr "Diese Eincheckliste ist aktiv"

#: src/components/common/CheckInListList/index.tsx:79
msgid "This check-in list is not active yet"
msgstr "Diese Eincheckliste ist noch nicht aktiv"

#: src/components/layouts/CheckIn/index.tsx:348
msgid "This check-in list is not yet active and is not available for check-ins."
msgstr "Diese Eincheckliste ist noch nicht aktiv und steht nicht für Eincheckungen zur Verfügung."

#: src/components/forms/CheckInListForm/index.tsx:37
msgid "This description will be shown to the check-in staff"
msgstr "Diese Beschreibung wird dem Eincheckpersonal angezeigt"

#: src/components/modals/SendMessageModal/index.tsx:285
msgid "This email is not promotional and is directly related to the event."
msgstr "Diese E-Mail hat keinen Werbezweck und steht in direktem Zusammenhang mit der Veranstaltung."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:8
#~ msgid "This event is not available at the moment. Please check back later."
#~ msgstr "Dieses Event ist momentan nicht verfügbar. Bitte versuchen Sie es später noch einmal."

#: src/components/layouts/EventHomepage/index.tsx:49
#~ msgid "This event is not available."
#~ msgstr "Dieses Event ist nicht verfügbar."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
msgid "This information will be shown on the payment page, order summary page, and order confirmation email."
msgstr "Diese Informationen werden auf der Zahlungsseite, der Bestellübersichtsseite und in der Bestellbestätigungs-E-Mail angezeigt."

#: src/components/forms/ProductForm/index.tsx:138
msgid "This is a general product, like a t-shirt or a mug. No ticket will be issued"
msgstr "Dies ist ein allgemeines Produkt, wie ein T-Shirt oder eine Tasse. Es wird kein Ticket ausgestellt"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:91
msgid "This is an online event"
msgstr "Dies ist eine Online-Veranstaltung"

#: src/components/forms/CheckInListForm/index.tsx:57
msgid "This list will no longer be available for check-ins after this date"
msgstr "Diese Liste wird nach diesem Datum nicht mehr für Eincheckungen verfügbar sein"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:68
msgid "This message will be included in the footer of all emails sent from this event"
msgstr "Diese Nachricht wird in die Fußzeile aller E-Mails aufgenommen, die von dieser Veranstaltung gesendet werden."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
msgid "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"
msgstr "Diese Nachricht wird nur angezeigt, wenn die Bestellung erfolgreich abgeschlossen wurde. Bestellungen, die auf Zahlung warten, zeigen diese Nachricht nicht an."

#: src/components/forms/StripeCheckoutForm/index.tsx:93
msgid "This order has already been paid."
msgstr "Diese Bestellung wurde bereits bezahlt."

#: src/components/modals/RefundOrderModal/index.tsx:141
msgid "This order has already been refunded."
msgstr "Diese Bestellung wurde bereits zurückerstattet."

#: src/components/routes/product-widget/CollectInformation/index.tsx:237
msgid "This order has been cancelled"
msgstr "Diese Bestellung wurde storniert"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:209
msgid "This order has been cancelled."
msgstr "Diese Bestellung wurde storniert."

#: src/components/routes/product-widget/CollectInformation/index.tsx:210
msgid "This order has expired. Please start again."
msgstr "Diese Bestellung ist abgelaufen. Bitte erneut beginnen."

#: src/components/routes/product-widget/CollectInformation/index.tsx:221
msgid "This order is awaiting payment"
msgstr "Diese Bestellung wartet auf Zahlung"

#: src/components/routes/product-widget/CollectInformation/index.tsx:229
msgid "This order is complete"
msgstr "Diese Bestellung ist abgeschlossen"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:211
msgid "This order is complete."
msgstr "Diese Bestellung ist abgeschlossen."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:204
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:207
msgid "This order is processing."
msgstr "Diese Bestellung wird bearbeitet."

#: src/components/forms/StripeCheckoutForm/index.tsx:103
msgid "This order page is no longer available."
msgstr "Diese Bestellseite ist nicht mehr verfügbar."

#: src/components/forms/ProductForm/index.tsx:415
msgid "This overrides all visibility settings and will hide the product from all customers."
msgstr "Dies überschreibt alle Sichtbarkeitseinstellungen und verbirgt das Produkt vor allen Kunden."

#: src/components/forms/TicketForm/index.tsx:360
#~ msgid "This overrides all visibility settings and will hide the ticket from all customers."
#~ msgstr "Dadurch werden alle Sichtbarkeitseinstellungen überschrieben und das Ticket wird vor allen Kunden verborgen."

#: src/components/common/ProductsTable/SortableProduct/index.tsx:59
msgid "This product cannot be deleted because it is associated with an order. You can hide it instead."
msgstr "Dieses Produkt kann nicht gelöscht werden, da es mit einer Bestellung verknüpft ist. Sie können es stattdessen ausblenden."

#: src/components/forms/ProductForm/index.tsx:132
msgid "This product is a ticket. Buyers will be issued a ticket upon purchase"
msgstr "Dieses Produkt ist ein Ticket. Käufer erhalten nach dem Kauf ein Ticket"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:215
msgid "This product is hidden from public view"
msgstr "Dieses Produkt ist vor der öffentlichen Ansicht verborgen"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:216
msgid "This product is hidden unless targeted by a Promo Code"
msgstr "Dieses Produkt ist verborgen, es sei denn, es wird von einem Promo-Code anvisiert"

#: src/components/common/QuestionsTable/index.tsx:90
msgid "This question is only visible to the event organizer"
msgstr "Diese Frage ist nur für den Veranstalter sichtbar"

#: src/components/routes/auth/ResetPassword/index.tsx:29
msgid "This reset password link is invalid or expired."
msgstr "Dieser Link zum Zurücksetzen des Passworts ist ungültig oder abgelaufen."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:56
#~ msgid ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."
#~ msgstr ""
#~ "Dieses Ticket kann nicht gelöscht werden, da es\n"
#~ "mit einer Bestellung verknüpft ist. Sie können es stattdessen ausblenden."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:131
#~ msgid "This ticket is hidden from public view"
#~ msgstr "Dieses Ticket ist nicht öffentlich sichtbar."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:132
#~ msgid "This ticket is hidden unless targeted by a Promo Code"
#~ msgstr "Dieses Ticket ist ausgeblendet, sofern es nicht durch einen Promo-Code angesprochen wird."

#: src/components/modals/EditUserModal/index.tsx:65
msgid "This user is not active, as they have not accepted their invitation."
msgstr "Dieser Benutzer ist nicht aktiv, da er die Einladung nicht angenommen hat."

#: src/components/common/AttendeeTable/index.tsx:182
msgid "ticket"
msgstr "Fahrkarte"

#: src/components/common/AttendeeTable/index.tsx:111
#: src/components/forms/ProductForm/index.tsx:130
#: src/components/modals/CreateAttendeeModal/index.tsx:151
msgid "Ticket"
msgstr "Fahrkarte"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:52
#~ msgid "Ticket deleted successfully"
#~ msgstr "Ticket erfolgreich gelöscht"

#: src/components/common/AttendeeTable/index.tsx:49
msgid "Ticket email has been resent to attendee"
msgstr "Die Ticket-E-Mail wurde erneut an den Teilnehmer gesendet."

#: src/components/common/MessageList/index.tsx:22
msgid "Ticket holders"
msgstr "Ticketinhaber"

#: src/components/routes/event/products.tsx:86
msgid "Ticket or Product"
msgstr "Ticket oder Produkt"

#: src/components/routes/event/EventDashboard/index.tsx:53
#~ msgid "Ticket Sales"
#~ msgstr "Ticketverkauf"

#: src/components/modals/CreateAttendeeModal/index.tsx:161
#: src/components/modals/EditAttendeeModal/index.tsx:115
#~ msgid "Ticket Tier"
#~ msgstr "Ticketstufe"

#: src/components/forms/TicketForm/index.tsx:189
#: src/components/forms/TicketForm/index.tsx:196
#~ msgid "Ticket Type"
#~ msgstr "Art des Tickets"

#: src/components/common/WidgetEditor/index.tsx:311
#~ msgid "Ticket Widget Preview"
#~ msgstr "Ticket-Widget-Vorschau"

#: src/components/common/PromoCodeTable/index.tsx:147
#~ msgid "Ticket(s)"
#~ msgstr "Eintrittskarte(n)"

#: src/components/common/PromoCodeTable/index.tsx:71
#: src/components/layouts/Event/index.tsx:40
#: src/components/layouts/EventHomepage/index.tsx:80
#: src/components/routes/event/tickets.tsx:39
#~ msgid "Tickets"
#~ msgstr "Tickets"

#: src/components/layouts/Event/index.tsx:101
#: src/components/routes/event/products.tsx:46
msgid "Tickets & Products"
msgstr "Tickets & Produkte"

#: src/components/routes/product-widget/PrintOrder/index.tsx:43
msgid "Tickets for"
msgstr "Tickets für"

#: src/components/common/EventCard/index.tsx:126
#~ msgid "tickets sold"
#~ msgstr "verkaufte Tickets"

#: src/components/common/StatBoxes/index.tsx:21
#~ msgid "Tickets sold"
#~ msgstr "Verkaufte Tickets"

#: src/components/routes/event/EventDashboard/index.tsx:73
#~ msgid "Tickets Sold"
#~ msgstr "Verkaufte Tickets"

#: src/components/common/TicketsTable/index.tsx:44
#~ msgid "Tickets sorted successfully"
#~ msgstr "Tickets erfolgreich sortiert"

#: src/components/forms/ProductForm/index.tsx:67
msgid "Tier {0}"
msgstr "Stufe {0}"

#: src/components/forms/ProductForm/index.tsx:163
msgid "Tiered Product"
msgstr "Gestuftes Produkt"

#: src/components/forms/ProductForm/index.tsx:240
#~ msgid ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""

#: src/components/forms/ProductForm/index.tsx:243
msgid "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."
msgstr "Gestufte Produkte ermöglichen es Ihnen, mehrere Preisoptionen für dasselbe Produkt anzubieten. Dies ist ideal für Frühbucherprodukte oder um unterschiedliche Preisoptionen für verschiedene Personengruppen anzubieten.\" # de"

#: src/components/forms/TicketForm/index.tsx:145
#~ msgid "Tiered Ticket"
#~ msgstr "Stufenticket"

#: src/components/forms/TicketForm/index.tsx:203
#~ msgid ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""
#~ "Mit gestaffelten Tickets können Sie mehrere Preisoptionen für dasselbe Ticket anbieten.\n"
#~ "Das ist ideal für Frühbuchertickets oder das Anbieten unterschiedlicher Preisoptionen\n"
#~ "für unterschiedliche Personengruppen."

#: src/components/layouts/Checkout/index.tsx:90
msgid "Time left:"
msgstr "Verbleibende Zeit:"

#: src/components/common/PromoCodeTable/index.tsx:71
msgid "Times used"
msgstr "Nutzungshäufigkeit"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:25
msgid "Times Used"
msgstr "Anzahl der Verwendungen"

#: src/components/forms/OrganizerForm/index.tsx:54
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:82
#: src/components/routes/auth/AcceptInvitation/index.tsx:105
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:109
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:181
#: src/components/routes/profile/ManageProfile/index.tsx:161
msgid "Timezone"
msgstr "Zeitzone"

#: src/components/forms/PromoCodeForm/index.tsx:36
msgid "TIP"
msgstr "TIPP"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:206
msgid "Title"
msgstr "Titel"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:182
msgid "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."
msgstr "Um Kreditkartenzahlungen zu empfangen, musst du dein Stripe-Konto verbinden. Stripe ist unser Zahlungsabwicklungspartner, der sichere Transaktionen und pünktliche Auszahlungen gewährleistet."

#: src/components/layouts/Event/index.tsx:108
msgid "Tools"
msgstr "Werkzeuge"

#: src/components/common/OrderSummary/index.tsx:92
msgid "Total"
msgstr "Gesamt"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:52
msgid "Total Before Discounts"
msgstr "Gesamt vor Rabatten"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:58
msgid "Total Discount Amount"
msgstr "Gesamtrabattbetrag"

#: src/components/routes/event/EventDashboard/index.tsx:273
msgid "Total Fees"
msgstr "Gesamtkosten"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:46
msgid "Total Gross Sales"
msgstr "Gesamtumsatz brutto"

#: src/components/common/OrderDetails/index.tsx:59
#: src/components/modals/RefundOrderModal/index.tsx:74
msgid "Total order amount"
msgstr "Gesamtbestellwert"

#: src/components/common/OrderDetails/index.tsx:67
#: src/components/modals/RefundOrderModal/index.tsx:82
msgid "Total refunded"
msgstr "Gesamtbetrag zurückerstattet"

#: src/components/routes/event/EventDashboard/index.tsx:276
msgid "Total Refunded"
msgstr "Insgesamt erstattet"

#: src/components/modals/RefundOrderModal/index.tsx:90
msgid "Total remaining"
msgstr "Verbleibender Gesamtbetrag"

#: src/components/routes/event/EventDashboard/index.tsx:275
msgid "Total Tax"
msgstr "Gesamtsteuer"

#: src/components/layouts/AuthLayout/index.tsx:54
msgid "Track revenue, page views, and sales with detailed analytics and exportable reports"
msgstr "Verfolgen Sie Einnahmen, Seitenaufrufe und Verkäufe mit detaillierten Analysen und exportierbaren Berichten"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
msgid "Transaction Fee:"
msgstr "Transaktionsgebühr:"

#: src/components/forms/TaxAndFeeForm/index.tsx:44
msgid "Type"
msgstr "Typ"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "Unable to {0} attendee"
#~ msgstr "Teilnehmer {0} konnte nicht ausgewählt werden"

#: src/components/layouts/CheckIn/index.tsx:93
msgid "Unable to check in attendee"
msgstr "Teilnehmer konnte nicht eingecheckt werden"

#: src/components/layouts/CheckIn/index.tsx:114
msgid "Unable to check out attendee"
msgstr "Teilnehmer konnte nicht ausgecheckt werden"

#: src/components/modals/CreateProductModal/index.tsx:63
msgid "Unable to create product. Please check the your details"
msgstr "Produkt konnte nicht erstellt werden. Bitte überprüfen Sie Ihre Angaben"

#: src/components/routes/product-widget/SelectProducts/index.tsx:123
msgid "Unable to create product. Please check your details"
msgstr "Produkt konnte nicht erstellt werden. Bitte überprüfen Sie Ihre Angaben"

#: src/components/modals/CreateQuestionModal/index.tsx:60
msgid "Unable to create question. Please check the your details"
msgstr "Frage konnte nicht erstellt werden. Bitte überprüfen Sie Ihre Angaben"

#: src/components/modals/CreateTicketModal/index.tsx:65
#: src/components/routes/ticket-widget/SelectTickets/index.tsx:117
#~ msgid "Unable to create ticket. Please check the your details"
#~ msgstr "Ticket konnte nicht erstellt werden. Bitte überprüfen Sie Ihre Angaben"

#: src/components/modals/DuplicateProductModal/index.tsx:103
msgid "Unable to duplicate product. Please check the your details"
msgstr "Produkt konnte nicht dupliziert werden. Bitte überprüfen Sie Ihre Angaben"

#: src/components/layouts/CheckIn/index.tsx:145
msgid "Unable to fetch attendee"
msgstr "Teilnehmer konnte nicht abgerufen werden"

#: src/components/modals/EditQuestionModal/index.tsx:84
msgid "Unable to update question. Please check the your details"
msgstr "Frage kann nicht aktualisiert werden. Bitte überprüfen Sie Ihre Angaben"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:30
msgid "Unique Customers"
msgstr "Einzigartige Kunden"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:153
msgid "United States"
msgstr "Vereinigte Staaten"

#: src/components/common/QuestionAndAnswerList/index.tsx:284
msgid "Unknown Attendee"
msgstr "Unbekannter Teilnehmer"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:43
#: src/components/forms/ProductForm/index.tsx:83
#: src/components/forms/ProductForm/index.tsx:299
#: src/components/forms/PromoCodeForm/index.tsx:82
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:78
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:84
msgid "Unlimited"
msgstr "Unbegrenzt"

#: src/components/routes/product-widget/SelectProducts/index.tsx:407
msgid "Unlimited available"
msgstr "Unbegrenzt verfügbar"

#: src/components/common/PromoCodeTable/index.tsx:126
msgid "Unlimited usages allowed"
msgstr "Unbegrenzte Nutzung erlaubt"

#: src/components/layouts/CheckIn/index.tsx:200
msgid "Unpaid Order"
msgstr "Unbezahlte Bestellung"

#: src/components/routes/event/EventDashboard/index.tsx:170
msgid "Unpublish Event"
msgstr "Veranstaltung nicht mehr veröffentlichen"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:20
#: src/components/common/EventStatusBadge/index.tsx:16
msgid "Upcoming"
msgstr "Bevorstehende"

#: src/components/routes/events/Dashboard/index.tsx:55
msgid "Upcoming Events"
msgstr "Bevorstehende Veranstaltungen"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Update {0}"
msgstr "Aktualisierung {0}"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:66
msgid "Update event name, description and dates"
msgstr "Aktualisieren Sie den Namen, die Beschreibung und die Daten der Veranstaltung"

#: src/components/routes/profile/ManageProfile/index.tsx:177
msgid "Update profile"
msgstr "Profil aktualisieren"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Upload Cover"
msgstr "Cover hochladen"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:105
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:152
msgid "Upload Image"
msgstr "Bild hochladen"

#: src/components/common/WebhookTable/index.tsx:236
#: src/components/modals/ShareModal/index.tsx:129
msgid "URL"
msgstr "URL"

#: src/components/common/PromoCodeTable/index.tsx:175
msgid "URL copied to clipboard"
msgstr "URL in die Zwischenablage kopiert"

#: src/components/modals/CreateWebhookModal/index.tsx:25
msgid "URL is required"
msgstr "URL ist erforderlich"

#: src/components/common/WidgetEditor/index.tsx:298
msgid "Usage Example"
msgstr "Anwendungsbeispiel"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:76
msgid "Usage Limit"
msgstr "Verwendungslimit"

#: src/components/routes/event/HomepageDesigner/index.tsx:139
msgid "Use a blurred version of the cover image as the background"
msgstr "Verwenden Sie eine unscharfe Version des Titelbilds als Hintergrund"

#: src/components/routes/event/HomepageDesigner/index.tsx:137
msgid "Use cover image"
msgstr "Titelbild verwenden"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User"
msgstr "Benutzer"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User Management"
msgstr "Benutzerverwaltung"

#: src/components/routes/account/ManageAccount/index.tsx:32
msgid "Users"
msgstr "Benutzer"

#: src/components/modals/EditUserModal/index.tsx:81
msgid "Users can change their email in <0>Profile Settings</0>"
msgstr "Benutzer können ihre E-Mail in den <0>Profileinstellungen</0> ändern."

#: src/components/forms/OrganizerForm/index.tsx:55
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:83
#: src/components/routes/auth/AcceptInvitation/index.tsx:106
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:110
#: src/components/routes/profile/ManageProfile/index.tsx:162
msgid "UTC"
msgstr "koordinierte Weltzeit"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "VAT"
msgstr "Umsatzsteuer"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:116
msgid "Venue Name"
msgstr "Veranstaltungsort Namen"

#: src/components/common/LanguageSwitcher/index.tsx:34
msgid "Vietnamese"
msgstr "Vietnamesisch"

#: src/components/modals/ManageAttendeeModal/index.tsx:220
#: src/components/modals/ManageOrderModal/index.tsx:200
msgid "View"
msgstr "Ansehen"

#: src/components/routes/event/Reports/index.tsx:38
msgid "View and download reports for your event. Please note, only completed orders are included in these reports."
msgstr "Sehen Sie sich Berichte zu Ihrer Veranstaltung an und laden Sie sie herunter. Bitte beachten Sie, dass nur abgeschlossene Bestellungen in diesen Berichten enthalten sind."

#: src/components/common/AttendeeList/index.tsx:84
msgid "View Answers"
msgstr "Antworten anzeigen"

#: src/components/common/AttendeeTable/index.tsx:164
#~ msgid "View attendee"
#~ msgstr "Teilnehmer anzeigen"

#: src/components/common/AttendeeList/index.tsx:103
msgid "View Attendee Details"
msgstr "Teilnehmerdetails anzeigen"

#: src/components/layouts/Checkout/index.tsx:55
#~ msgid "View event homepage"
#~ msgstr "Zur Event-Homepage"

#: src/components/common/EventCard/index.tsx:145
msgid "View event page"
msgstr "Zur Veranstaltungsseite"

#: src/components/common/MessageList/index.tsx:59
msgid "View full message"
msgstr "Vollständige Nachricht anzeigen"

#: src/components/common/WebhookTable/index.tsx:113
msgid "View logs"
msgstr "Protokolle anzeigen"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View map"
msgstr "Ansichts Karte"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View on Google Maps"
msgstr "Auf Google Maps anzeigen"

#: src/components/common/OrdersTable/index.tsx:111
#~ msgid "View order"
#~ msgstr "Bestellung anzeigen"

#: src/components/forms/StripeCheckoutForm/index.tsx:94
#: src/components/forms/StripeCheckoutForm/index.tsx:104
#: src/components/routes/product-widget/CollectInformation/index.tsx:231
msgid "View order details"
msgstr "Bestell Details ansehen"

#: src/components/forms/CheckInListForm/index.tsx:21
msgid "VIP check-in list"
msgstr "VIP-Eincheckliste"

#: src/components/forms/ProductForm/index.tsx:254
#~ msgid "VIP Product"
#~ msgstr "VIP-Produkt"

#: src/components/forms/ProductForm/index.tsx:254
msgid "VIP Ticket"
msgstr "VIP-Ticket"

#: src/components/forms/ProductForm/index.tsx:394
msgid "Visibility"
msgstr "Sichtweite"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:77
msgid "We could not process your payment. Please try again or contact support."
msgstr "Wir konnten Ihre Zahlung nicht verarbeiten. Bitte versuchen Sie es erneut oder wenden Sie sich an den Support."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:53
msgid "We couldn't delete the category. Please try again."
msgstr "Die Kategorie konnte nicht gelöscht werden. Bitte versuchen Sie es erneut."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:24
msgid "We couldn't find any tickets matching {0}"
msgstr "Wir konnten keine Tickets finden, die mit {0} übereinstimmen"

#: src/components/common/ErrorLoadingMessage/index.tsx:13
msgid "We couldn't load the data. Please try again."
msgstr "Wir konnten die Daten nicht laden. Bitte versuchen Sie es erneut."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:94
msgid "We couldn't reorder the categories. Please try again."
msgstr "Wir konnten die Kategorien nicht neu ordnen. Bitte versuchen Sie es erneut."

#: src/components/routes/event/HomepageDesigner/index.tsx:118
msgid "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"
msgstr "Wir empfehlen Abmessungen von 2160 x 1080 Pixel und eine maximale Dateigröße von 5 MB."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:42
#~ msgid "We use Stripe to process payments. Connect your Stripe account to start receiving payments."
#~ msgstr "Wir verwenden Stripe zur Zahlungsabwicklung. Verbinden Sie Ihr Stripe-Konto, um Zahlungen zu empfangen."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:84
msgid "We were unable to confirm your payment. Please try again or contact support."
msgstr "Wir konnten Ihre Zahlung nicht bestätigen. Bitte versuchen Sie es erneut oder wenden Sie sich an den Support."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:79
msgid "We're processing your order. Please wait..."
msgstr "Wir bearbeiten Ihre Bestellung. Bitte warten..."

#: src/components/modals/CreateWebhookModal/index.tsx:46
msgid "Webhook created successfully"
msgstr "Webhook erfolgreich erstellt"

#: src/components/common/WebhookTable/index.tsx:53
msgid "Webhook deleted successfully"
msgstr "Webhook erfolgreich gelöscht"

#: src/components/modals/WebhookLogsModal/index.tsx:151
msgid "Webhook Logs"
msgstr "Webhook-Protokolle"

#: src/components/forms/WebhookForm/index.tsx:117
msgid "Webhook URL"
msgstr "Webhook-URL"

#: src/components/forms/WebhookForm/index.tsx:27
msgid "Webhook will not send notifications"
msgstr "Webhook sendet keine Benachrichtigungen"

#: src/components/forms/WebhookForm/index.tsx:21
msgid "Webhook will send notifications"
msgstr "Webhook sendet Benachrichtigungen"

#: src/components/layouts/Event/index.tsx:111
#: src/components/routes/event/Webhooks/index.tsx:30
msgid "Webhooks"
msgstr "Webhooks"

#: src/components/routes/auth/AcceptInvitation/index.tsx:76
msgid "Welcome aboard! Please login to continue."
msgstr "Willkommen an Bord! Bitte melden Sie sich an, um fortzufahren."

#: src/components/routes/auth/Login/index.tsx:55
msgid "Welcome back 👋"
msgstr "Willkommen zurück 👋"

#: src/components/routes/event/EventDashboard/index.tsx:95
msgid "Welcome back{0} 👋"
msgstr "Willkommen zurück{0} 👋"

#: src/components/routes/auth/Register/index.tsx:67
msgid "Welcome to Hi.Events 👋"
msgstr "Willkommen bei Hi.Events 👋"

#: src/components/routes/welcome/index.tsx:125
msgid "Welcome to Hi.Events, {0} 👋"
msgstr "Willkommen bei Hi.Events, {0} 👋"

#: src/components/forms/ProductForm/index.tsx:242
msgid "What are Tiered Products?"
msgstr "Was sind gestufte Produkte?"

#: src/components/forms/TicketForm/index.tsx:202
#~ msgid "What are Tiered Tickets?"
#~ msgstr "Was sind Stufentickets?"

#: src/components/forms/CheckInListForm/index.tsx:50
msgid "What date should this check-in list become active?"
msgstr "An welchem Datum soll diese Eincheckliste aktiv werden?"

#: src/components/modals/CreateProductCategoryModal/index.tsx:53
msgid "What is a Category?"
msgstr "Was ist eine Kategorie?"

#: src/components/routes/event/Webhooks/index.tsx:31
#~ msgid "What is a webhook?"
#~ msgstr "Was ist ein Webhook?"

#: src/components/forms/QuestionForm/index.tsx:159
msgid "What products does this code apply to?"
msgstr "Für welche Produkte gilt dieser Code?"

#: src/components/forms/PromoCodeForm/index.tsx:68
msgid "What products does this code apply to? (Applies to all by default)"
msgstr "Für welche Produkte gilt dieser Code? (Standardmäßig gilt er für alle)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:48
msgid "What products should this capacity apply to?"
msgstr "Für welche Produkte soll diese Kapazität gelten?"

#: src/components/forms/PromoCodeForm/index.tsx:68
#~ msgid "What tickets does this code apply to? (Applies to all by default)"
#~ msgstr "Für welche Tickets gilt dieser Code? (Gilt standardmäßig für alle)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:47
#: src/components/forms/QuestionForm/index.tsx:159
#~ msgid "What tickets should this question be apply to?"
#~ msgstr "Auf welche Tickets soll sich diese Frage beziehen?"

#: src/components/forms/QuestionForm/index.tsx:179
msgid "What time will you be arriving?"
msgstr "Um wie viel Uhr werden Sie ankommen?"

#: src/components/forms/QuestionForm/index.tsx:170
msgid "What type of question is this?"
msgstr "Um welche Art von Frage handelt es sich?"

#: src/components/forms/WebhookForm/index.tsx:108
msgid "When a check-in is deleted"
msgstr "Wenn ein Check-in gelöscht wird"

#: src/components/forms/WebhookForm/index.tsx:84
msgid "When a new attendee is created"
msgstr "Wenn ein neuer Teilnehmer erstellt wird"

#: src/components/forms/WebhookForm/index.tsx:54
msgid "When a new order is created"
msgstr "Wenn eine neue Bestellung erstellt wird"

#: src/components/forms/WebhookForm/index.tsx:36
msgid "When a new product is created"
msgstr "Wenn ein neues Produkt erstellt wird"

#: src/components/forms/WebhookForm/index.tsx:48
msgid "When a product is deleted"
msgstr "Wenn ein Produkt gelöscht wird"

#: src/components/forms/WebhookForm/index.tsx:42
msgid "When a product is updated"
msgstr "Wenn ein Produkt aktualisiert wird"

#: src/components/forms/WebhookForm/index.tsx:96
msgid "When an attendee is cancelled"
msgstr "Wenn ein Teilnehmer storniert wird"

#: src/components/forms/WebhookForm/index.tsx:102
msgid "When an attendee is checked in"
msgstr "Wenn ein Teilnehmer eingecheckt wird"

#: src/components/forms/WebhookForm/index.tsx:90
msgid "When an attendee is updated"
msgstr "Wenn ein Teilnehmer aktualisiert wird"

#: src/components/forms/WebhookForm/index.tsx:78
msgid "When an order is cancelled"
msgstr "Wenn eine Bestellung storniert wird"

#: src/components/forms/WebhookForm/index.tsx:66
msgid "When an order is marked as paid"
msgstr "Wenn eine Bestellung als bezahlt markiert wird"

#: src/components/forms/WebhookForm/index.tsx:72
msgid "When an order is refunded"
msgstr "Wenn eine Bestellung erstattet wird"

#: src/components/forms/WebhookForm/index.tsx:60
msgid "When an order is updated"
msgstr "Wenn eine Bestellung aktualisiert wird"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:182
msgid "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."
msgstr "Wenn aktiviert, werden Rechnungen für Ticketbestellungen erstellt. Rechnungen werden zusammen mit der Bestellbestätigungs-E-Mail gesendet. Teilnehmer können ihre Rechnungen auch von der Bestellbestätigungsseite herunterladen."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:141
msgid "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."
msgstr "Wenn Offline-Zahlungen aktiviert sind, können Benutzer ihre Bestellungen abschließen und ihre Tickets erhalten. Ihre Tickets werden klar anzeigen, dass die Bestellung nicht bezahlt ist, und das Check-in-Tool wird das Check-in-Personal benachrichtigen, wenn eine Bestellung eine Zahlung erfordert."

#: src/components/forms/CheckInListForm/index.tsx:59
msgid "When should this check-in list expire?"
msgstr "Wann soll diese Eincheckliste ablaufen?"

#: src/components/forms/CheckInListForm/index.tsx:25
msgid "Which tickets should be associated with this check-in list?"
msgstr "Welche Tickets sollen mit dieser Eincheckliste verknüpft werden?"

#: src/components/modals/CreateEventModal/index.tsx:107
msgid "Who is organizing this event?"
msgstr "Wer organisiert diese Veranstaltung?"

#: src/components/modals/SendMessageModal/index.tsx:194
msgid "Who is this message to?"
msgstr "An wen ist diese Nachricht gerichtet?"

#: src/components/forms/QuestionForm/index.tsx:151
msgid "Who should be asked this question?"
msgstr "Wem sollte diese Frage gestellt werden?"

#: src/components/layouts/Event/index.tsx:110
msgid "Widget Embed"
msgstr "Widget einbetten"

#: src/components/common/WidgetEditor/index.tsx:162
msgid "Widget Settings"
msgstr "Widget-Einstellungen"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Working"
msgstr "Arbeiten"

#: src/components/modals/CreateProductCategoryModal/index.tsx:60
#: src/components/modals/CreateProductModal/index.tsx:88
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
#: src/components/modals/CreateQuestionModal/index.tsx:74
#: src/components/modals/DuplicateEventModal/index.tsx:142
#: src/components/modals/DuplicateProductModal/index.tsx:113
#: src/components/modals/EditProductCategoryModal/index.tsx:70
#: src/components/modals/EditProductModal/index.tsx:108
#: src/components/modals/EditPromoCodeModal/index.tsx:84
#: src/components/modals/EditQuestionModal/index.tsx:101
#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/Register/index.tsx:129
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Working..."
msgstr "Arbeiten..."

#: src/components/common/ReportTable/index.tsx:51
msgid "Year to date"
msgstr "Seit Jahresbeginn"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "Yes"
msgstr "Ja"

#: src/components/forms/ProductForm/index.tsx:376
msgid "Yes, remove them"
msgstr "Ja, entfernen"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:77
msgid "You already scanned this ticket"
msgstr "Sie haben dieses Ticket bereits gescannt"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:53
msgid "You are changing your email to <0>{0}</0>."
msgstr "Sie ändern Ihre E-Mail zu <0>{0}</0>."

#: src/components/layouts/CheckIn/index.tsx:88
#: src/components/layouts/CheckIn/index.tsx:110
msgid "You are offline"
msgstr "Sie sind offline"

#: src/components/forms/ProductForm/index.tsx:407
msgid "You can create a promo code which targets this product on the"
msgstr "Sie können einen Promo-Code erstellen, der sich auf dieses Produkt richtet auf der"

#: src/components/forms/TicketForm/index.tsx:352
#~ msgid "You can create a promo code which targets this ticket on the"
#~ msgstr "Sie können einen Promo-Code erstellen, der auf dieses Ticket abzielt, auf der"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:31
#~ msgid "You can now start receiving payments through Stripe."
#~ msgstr "Sie können jetzt Zahlungen über Stripe empfangen."

#: src/components/forms/ProductForm/index.tsx:208
msgid "You cannot change the product type as there are attendees associated with this product."
msgstr "Sie können den Produkttyp nicht ändern, da Teilnehmer mit diesem Produkt verknüpft sind."

#: src/components/forms/TicketForm/index.tsx:184
#~ msgid "You cannot change the ticket type as there are attendees associated with this ticket."
#~ msgstr "Sie können den Tickettyp nicht ändern, da diesem Ticket Teilnehmer zugeordnet sind."

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "You cannot check in attendees with unpaid orders."
#~ msgstr "Sie können Teilnehmer mit unbezahlten Bestellungen nicht einchecken."

#: src/components/layouts/CheckIn/index.tsx:129
#: src/components/layouts/CheckIn/index.tsx:164
msgid "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."
msgstr "Sie können Teilnehmer mit unbezahlten Bestellungen nicht einchecken. Diese Einstellung kann in den Veranstaltungsdetails geändert werden."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:40
msgid "You cannot delete the last category."
msgstr "Sie können die letzte Kategorie nicht löschen."

#: src/components/forms/ProductForm/index.tsx:57
msgid "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."
msgstr "Sie können diese Preiskategorie nicht löschen, da für diese Kategorie bereits Produkte verkauft wurden. Sie können sie stattdessen ausblenden."

#: src/components/forms/TicketForm/index.tsx:54
#~ msgid "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."
#~ msgstr "Sie können diese Preisstufe nicht löschen, da für diese Stufe bereits Tickets verkauft wurden. Sie können sie stattdessen ausblenden."

#: src/components/modals/EditUserModal/index.tsx:88
msgid "You cannot edit the role or status of the account owner."
msgstr "Sie können die Rolle oder den Status des Kontoinhabers nicht bearbeiten."

#: src/components/modals/RefundOrderModal/index.tsx:132
msgid "You cannot refund a manually created order."
msgstr "Sie können eine manuell erstellte Bestellung nicht zurückerstatten."

#: src/components/common/QuestionsTable/index.tsx:252
msgid "You created a hidden question but disabled the option to show hidden questions. It has been enabled."
msgstr "Sie haben eine versteckte Frage erstellt, aber die Option zum Anzeigen versteckter Fragen deaktiviert. Sie wurde aktiviert."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:77
#~ msgid "You do not have permission to access this page"
#~ msgstr "Sie haben keine Berechtigung, auf diese Seite zuzugreifen"

#: src/components/modals/ChooseAccountModal/index.tsx:14
msgid "You have access to multiple accounts. Please choose one to continue."
msgstr "Sie haben Zugriff auf mehrere Konten. Bitte wählen Sie eines aus, um fortzufahren."

#: src/components/routes/auth/AcceptInvitation/index.tsx:57
msgid "You have already accepted this invitation. Please login to continue."
msgstr "Sie haben diese Einladung bereits angenommen. Bitte melden Sie sich an, um fortzufahren."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:29
#~ msgid "You have connected your Stripe account"
#~ msgstr "Sie haben Ihr Stripe-Konto verbunden"

#: src/components/common/QuestionsTable/index.tsx:338
msgid "You have no attendee questions."
msgstr "Sie haben keine Teilnehmerfragen."

#: src/components/common/QuestionsTable/index.tsx:323
msgid "You have no order questions."
msgstr "Sie haben keine Bestellfragen."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:41
msgid "You have no pending email change."
msgstr "Sie haben keine ausstehende E-Mail-Änderung."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:39
#~ msgid "You have not completed your Stripe Connect setup"
#~ msgstr "Sie haben die Einrichtung von Stripe Connect noch nicht abgeschlossen"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:38
#~ msgid "You have not connected your Stripe account"
#~ msgstr "Sie haben Ihr Stripe-Konto nicht verbunden"

#: src/components/layouts/Checkout/index.tsx:156
msgid "You have run out of time to complete your order."
msgstr "Die Zeit für die Bestellung ist abgelaufen."

#: src/components/forms/ProductForm/index.tsx:374
#~ msgid "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"
#~ msgstr "Sie haben Steuern und Gebühren zu einem kostenlosen Produkt hinzugefügt. Möchten Sie sie entfernen oder verbergen?"

#: src/components/forms/ProductForm/index.tsx:374
msgid "You have taxes and fees added to a Free Product. Would you like to remove them?"
msgstr "Sie haben Steuern und Gebühren zu einem kostenlosen Produkt hinzugefügt. Möchten Sie diese entfernen?"

#: src/components/forms/TicketForm/index.tsx:319
#~ msgid "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"
#~ msgstr "Sie haben Steuern und Gebühren zu einem Freiticket hinzugefügt. Möchten Sie diese entfernen oder unkenntlich machen?"

#: src/components/common/MessageList/index.tsx:74
msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."
msgstr "Sie haben noch keine Nachrichten gesendet. Sie können Nachrichten an alle Teilnehmer oder an bestimmte Produkthalter senden."

#: src/components/common/MessageList/index.tsx:64
#~ msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."
#~ msgstr "Sie haben noch keine Nachrichten gesendet. Sie können Nachrichten an alle Teilnehmer oder an bestimmte Ticketinhaber senden."

#: src/components/modals/SendMessageModal/index.tsx:108
msgid "You must acknowledge that this email is not promotional"
msgstr "Sie müssen bestätigen, dass diese E-Mail keinen Werbezweck hat"

#: src/components/routes/auth/AcceptInvitation/index.tsx:33
msgid "You must agree to the terms and conditions"
msgstr "Sie müssen den Allgemeinen Geschäftsbedingungen zustimmen"

#: src/components/routes/event/GettingStarted/index.tsx:193
msgid "You must confirm your email address before your event can go live."
msgstr "Sie müssen Ihre E-Mail-Adresse bestätigen, bevor Ihre Veranstaltung live gehen kann."

#: src/components/modals/CreateAttendeeModal/index.tsx:98
msgid "You must create a ticket before you can manually add an attendee."
msgstr "Sie müssen ein Ticket erstellen, bevor Sie einen Teilnehmer manuell hinzufügen können."

#: src/components/forms/ProductForm/index.tsx:60
msgid "You must have at least one price tier"
msgstr "Sie müssen mindestens eine Preisstufe haben"

#: src/components/modals/SendMessageModal/index.tsx:136
#~ msgid "You need to verify your account before you can send messages."
#~ msgstr "Sie müssen Ihr Konto verifizieren, bevor Sie Nachrichten senden können."

#: src/components/modals/SendMessageModal/index.tsx:151
msgid "You need to verify your account email before you can send messages."
msgstr "Sie müssen Ihre Konto-E-Mail-Adresse verifizieren, bevor Sie Nachrichten senden können."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:143
msgid "You will have to mark an order as paid manually. This can be done on the manage order page."
msgstr "Sie müssen eine Bestellung manuell als bezahlt markieren. Dies kann auf der Bestellverwaltungsseite erfolgen."

#: src/components/modals/CreateCheckInListModal/index.tsx:55
msgid "You'll need a ticket before you can create a check-in list."
msgstr "Sie benötigen ein Ticket, bevor Sie eine Eincheckliste erstellen können."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
msgid "You'll need at a product before you can create a capacity assignment."
msgstr "Sie benötigen ein Produkt, bevor Sie eine Kapazitätszuweisung erstellen können."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
#~ msgid "You'll need at a ticket before you can create a capacity assignment."
#~ msgstr "Sie benötigen ein Ticket, bevor Sie eine Kapazitätszuweisung erstellen können."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:43
msgid "You'll need at least one product to get started. Free, paid or let the user decide what to pay."
msgstr "Sie benötigen mindestens ein Produkt, um loszulegen. Kostenlos, bezahlt oder lassen Sie den Benutzer entscheiden, was er zahlen möchte."

#: src/components/common/TicketsTable/index.tsx:69
#~ msgid "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."
#~ msgstr "Sie benötigen mindestens ein Ticket, um loslegen zu können. Kostenlos, kostenpflichtig oder der Benutzer kann selbst entscheiden, was er bezahlen möchte."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:93
msgid "You're going to {0}! 🎉"
msgstr "Du gehst zu {0}! 🎉"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:64
msgid "Your account name is used on event pages and in emails."
msgstr "Ihr Kontoname wird auf Veranstaltungsseiten und in E-Mails verwendet."

#: src/components/routes/event/attendees.tsx:44
msgid "Your attendees have been exported successfully."
msgstr "Deine Teilnehmer wurden erfolgreich exportiert."

#: src/components/common/AttendeeTable/index.tsx:61
msgid "Your attendees will appear here once they have registered for your event. You can also manually add attendees."
msgstr "Ihre Teilnehmer werden hier angezeigt, sobald sie sich für Ihre Veranstaltung registriert haben. Sie können Teilnehmer auch manuell hinzufügen."

#: src/components/common/WidgetEditor/index.tsx:329
msgid "Your awesome website 🎉"
msgstr "Eure tolle Website 🎉"

#: src/components/routes/product-widget/CollectInformation/index.tsx:276
msgid "Your Details"
msgstr "Deine Details"

#: src/components/routes/auth/ForgotPassword/index.tsx:52
msgid "Your Email"
msgstr "Ihre E-Mail"

#: src/components/routes/profile/ManageProfile/index.tsx:118
msgid "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"
msgstr "Ihre E-Mail-Anfrage zur Änderung auf <0>{0}</0> steht noch aus. Bitte überprüfen Sie Ihre E-Mail, um sie zu bestätigen"

#: src/components/routes/event/EventDashboard/index.tsx:150
msgid "Your event must be live before you can sell tickets to attendees."
msgstr "Ihr Event muss live sein, bevor Sie Tickets an Teilnehmer verkaufen können."

#: src/components/routes/event/EventDashboard/index.tsx:164
#~ msgid "Your event must be live before you can sell tickets."
#~ msgstr "Ihre Veranstaltung muss live sein, bevor Sie Tickets verkaufen können."

#: src/components/common/OrdersTable/index.tsx:88
msgid "Your message has been sent"
msgstr "Ihre Nachricht wurde gesendet"

#: src/components/layouts/Checkout/index.tsx:84
msgid "Your Order"
msgstr "Deine Bestellung"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:94
msgid "Your order has been cancelled"
msgstr "Deine Bestellung wurde storniert"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:96
msgid "Your order is awaiting payment 🏦"
msgstr "Ihre Bestellung wartet auf Zahlung 🏦"

#: src/components/routes/event/orders.tsx:95
msgid "Your orders have been exported successfully."
msgstr "Deine Bestellungen wurden erfolgreich exportiert."

#: src/components/common/OrdersTable/index.tsx:66
msgid "Your orders will appear here once they start rolling in."
msgstr "Sobald Ihre Bestellungen eintreffen, werden sie hier angezeigt."

#: src/components/routes/auth/Login/index.tsx:74
#: src/components/routes/auth/Register/index.tsx:107
msgid "Your password"
msgstr "Ihr Passwort"

#: src/components/forms/StripeCheckoutForm/index.tsx:63
msgid "Your payment is processing."
msgstr "Ihre Zahlung wird verarbeitet."

#: src/components/forms/StripeCheckoutForm/index.tsx:66
msgid "Your payment was not successful, please try again."
msgstr "Ihre Zahlung war nicht erfolgreich, bitte versuchen Sie es erneut."

#: src/components/forms/StripeCheckoutForm/index.tsx:126
msgid "Your payment was unsuccessful. Please try again."
msgstr "Ihre Zahlung war nicht erfolgreich. Bitte versuchen Sie es erneut."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#~ msgid "Your product for"
#~ msgstr "Ihr Produkt für"

#: src/components/modals/RefundOrderModal/index.tsx:58
msgid "Your refund is processing."
msgstr "Ihre Rückerstattung wird bearbeitet."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:153
msgid "Your Stripe account is connected and ready to process payments."
msgstr "Ihr Stripe-Konto ist verbunden und bereit zur Zahlungsabwicklung."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#: src/components/routes/product-widget/PrintProduct/index.tsx:42
msgid "Your ticket for"
msgstr "Ihr Ticket für"

#: src/components/routes/product-widget/CollectInformation/index.tsx:348
msgid "ZIP / Postal Code"
msgstr "Postleitzahl"

#: src/components/common/CheckoutQuestion/index.tsx:170
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:146
msgid "Zip or Postal Code"
msgstr "Postleitzahl"

#: src/components/routes/product-widget/CollectInformation/index.tsx:349
msgid "ZIP or Postal Code"
msgstr "Postleitzahl"
