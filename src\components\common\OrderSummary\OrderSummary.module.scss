.summary {
  .items {

    .separator {
      height: 1px;
      margin: 7px 0;
      width: 100%;
      border-bottom: 1px solid #ddd;
    }

    .itemRow {
      display: flex;
      align-content: center;
      justify-content: space-between;
      padding: 10px 0;

      .itemName {
        text-align: left;

        .total {
          font-size: 1.1em;
        }
      }

      .itemValue {
        text-align: right;
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }
}