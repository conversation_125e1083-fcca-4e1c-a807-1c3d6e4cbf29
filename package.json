{"name": "hievents-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev:csr": "vite --port 5678 --host 0.0.0.0", "dev": "npm run dev:csr", "build:csr": "vite build", "build-strict:csr": "tsc && npm run build:csr", "preview:csr": "vite preview", "dev:ssr": "cross-env NODE_ENV=development node server.js --port 5678", "build:ssr:client": "vite build --ssrManifest --outDir dist/client", "build:ssr:server": "vite build --ssr src/entry.server.tsx --outDir dist/server", "start": "cross-env NODE_ENV=production node server.js", "build": "npm run messages:extract && npm run messages:compile && npm run build:ssr:client && npm run build:ssr:server", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "messages:extract": "lingui extract", "messages:compile": "lingui compile"}, "dependencies": {"@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@lingui/macro": "^4.7.0", "@lingui/react": "^4.7.0", "@mantine/carousel": "^8.0.1", "@mantine/charts": "^8.0.1", "@mantine/core": "^8.0.1", "@mantine/dates": "^8.0.1", "@mantine/dropzone": "^8.0.1", "@mantine/form": "^8.0.1", "@mantine/hooks": "^8.0.1", "@mantine/modals": "^8.0.1", "@mantine/notifications": "^8.0.1", "@mantine/nprogress": "^8.0.1", "@mantine/tiptap": "^8.0.1", "@react-pdf/renderer": "^3.3.4", "@react-router/node": "^7.1.5", "@remix-run/node": "^2.8.1", "@stripe/react-stripe-js": "^2.1.1", "@stripe/stripe-js": "^1.54.1", "@tabler/icons-react": "^2.44.0", "@tanstack/react-query": "5.76.1", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-text-align": "^2.1.13", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.1.13", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "axios": "^1.4.0", "classnames": "^2.3.2", "collect.js": "^4.36.1", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "dayjs": "^1.11.8", "dotenv": "^16.4.7", "express": "^4.19.2", "qr-scanner": "^1.4.2", "query-string": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.4", "react-qr-code": "^2.0.12", "react-router": "^7.1.5", "react-router-dom": "^7.1.5", "recharts": "2", "sirv": "^2.0.4"}, "devDependencies": {"@lingui/vite-plugin": "^4.14.1", "@swc/core": "1.11.24", "@types/express": "^4.17.21", "@types/lodash": "^4.17.0", "@types/node": "^20.12.2", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.9.0", "babel-plugin-macros": "^3.1.0", "eslint": "^8.38.0", "eslint-plugin-lingui": "^0.2.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.24", "postcss-preset-mantine": "^1.6.0", "postcss-simple-vars": "^7.0.1", "sass": "^1.89.0", "typescript": "^5.0.2", "vite": "^5.4.19", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-copy": "^0.1.6"}}