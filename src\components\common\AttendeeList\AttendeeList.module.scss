.container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.attendeeList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.attendee {
  display: flex;
  flex-direction: column;
  padding: 0.875rem;
  background: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-2);
  border-radius: var(--mantine-radius-sm);
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--mantine-color-gray-3);
    transform: translateY(-1px);
  }
}

.attendeeInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.details {
  flex: 1;
  min-width: 0;
}

.name {
  line-height: 1.2;
}

.product {
  margin-top: 0.25rem;
  color: var(--mantine-color-gray-6);
}

.actionButton {
  margin-left: auto;

  &:hover {
    background-color: var(--mantine-color-blue-0);
  }
}

.avatar {
  background: var(--tk-color-gray);
  font-weight: 500;
}

/* Added styles for answers display */
.answersContainer {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  padding-left: .4rem;
  border-top: 1px solid var(--mantine-color-gray-2);
}

.questionAnswer {
  margin-bottom: 0.75rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.questionTitle {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  align-items: flex-start;
}

.answer {
  color: var(--mantine-color-gray-7);
  padding-left: 1.5rem;
  font-size: var(--mantine-font-size-sm);
}
