#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-17 13:53-0800\n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"X-Generator: Poedit 3.6\n"

#: src/components/layouts/Event/index.tsx:189
msgid "- Click to Publish"
msgstr "- Klik om te publiceren"

#: src/components/layouts/Event/index.tsx:191
msgid "- Click to Unpublish"
msgstr "- Klik om te verwijderen"

#~ msgid "..."
#~ msgstr "..."

#: src/components/common/NoResultsSplash/index.tsx:14
msgid "'There\\'s nothing to show yet'"
msgstr "'Er is nog niets te laten zien'"

#: src/components/modals/SendMessageModal/index.tsx:159
#~ msgid ""
#~ "\"\"Due to the high risk of spam, we require manual verification before you can send messages.\n"
#~ "\"\"Please contact us to request access."
#~ msgstr ""
#~ "Vanwege het hoge risico op spam is handmatige verificatie vereist voordat je berichten kunt verzenden.\n"
#~ "Neem contact met ons op om toegang aan te vragen."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
#~ msgid ""
#~ "\"\"If you have an account with us, you will receive an email with instructions on how to reset your\n"
#~ "\"\"password."
#~ msgstr ""
#~ "Als je een account bij ons hebt, ontvang je een e-mail met instructies om je\n"
#~ "wachtwoord opnieuw in te stellen."

#~ msgid "{0, plural, one {# hidden question} other {# hidden questions}}"
#~ msgstr "{0, meervoud, een {# verborgen vraag} andere {# verborgen vragen}}"

#: src/components/common/ReportTable/index.tsx:303
#: src/locales.ts:37
msgid "{0}"
msgstr "{0}"

#~ msgid "{0} {1}"
#~ msgstr "{0} {1}"

#: src/components/layouts/CheckIn/index.tsx:82
msgid "{0} <0>checked in</0> successfully"
msgstr "{0} <0>ingecheckt</0> succesvol"

#: src/components/layouts/CheckIn/index.tsx:106
msgid "{0} <0>checked out</0> successfully"
msgstr "{0} <0>uitgevinkt</0> succesvol"

#: src/components/routes/event/Webhooks/index.tsx:24
msgid "{0} Active Webhooks"
msgstr "{0} Actieve webhooks"

#: src/components/routes/product-widget/SelectProducts/index.tsx:412
msgid "{0} available"
msgstr "{0} beschikbaar"

#~ msgid "{0} checked in"
#~ msgstr "{0} ingecheckt"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
msgid "{0} created successfully"
msgstr "{0} succesvol aangemaakt"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
msgid "{0} updated successfully"
msgstr "{0} succesvol bijgewerkt"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:57
msgid "{0}'s Events"
msgstr "{0}'s Evenementen"

#~ msgid "{0}{1}"
#~ msgstr "{0}{1}"

#: src/components/layouts/CheckIn/index.tsx:449
msgid "{0}/{1} checked in"
msgstr "{0}/{1} ingecheckt"

#: src/components/common/Countdown/index.tsx:68
msgid "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{days} dagen, {hours} uren, {minutes} minuten en {seconds} seconden"

#: src/components/common/WebhookTable/index.tsx:79
msgid "{eventCount} events"
msgstr "{eventCount} evenementen"

#: src/components/common/Countdown/index.tsx:70
msgid "{hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{hours} uren, {minutes} minuten en {seconds} seconden"

#~ msgid "{message}"
#~ msgstr "{boodschap}"

#: src/components/common/Countdown/index.tsx:72
msgid "{minutes} minutes and {seconds} seconds"
msgstr "{minutes} minuten en {seconds} seconden"

#: src/components/routes/welcome/index.tsx:59
msgid "{organizerName}'s first event"
msgstr "Eerste evenement van {organizerName}"

#~ msgid "{title}"
#~ msgstr "{title}"

#: src/components/common/CapacityAssignmentList/index.tsx:47
msgid "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"
msgstr "<0>Met capaciteitstoewijzingen kun je de capaciteit van tickets of een heel evenement beheren. Ideaal voor meerdaagse evenementen, workshops en meer, waar het beheersen van bezoekersaantallen cruciaal is.</0><1>Je kunt bijvoorbeeld een capaciteitstoewijzing koppelen aan een <2>Dag één</2> en <3>Alle dagen</3> ticket. Zodra de capaciteit is bereikt, zijn beide tickets automatisch niet meer beschikbaar voor verkoop.</1>"

#: src/components/common/CheckInListList/index.tsx:49
msgid "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"
msgstr "<0>Inchecklijsten helpen bij het beheren van de toegang van bezoekers voor je evenement. Je kunt meerdere tickets aan een inchecklijst koppelen en ervoor zorgen dat alleen personen met geldige tickets naar binnen kunnen.</0>"

#: src/components/common/WidgetEditor/index.tsx:324
msgid "<0>https://</0>your-website.com"
msgstr "<0>https://</0>uw-website.nl"

#: src/components/forms/ProductForm/index.tsx:287
msgid "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"
msgstr "<0>Voer de prijs in exclusief belastingen en toeslagen.</0><1>Belastingen en toeslagen kunnen hieronder worden toegevoegd.</1>"

#: src/components/forms/ProductForm/index.tsx:304
msgid "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"
msgstr "<0>Het aantal beschikbare producten voor dit product</0><1>Deze waarde kan worden overschreven als er <2>Capaciteitsbeperkingen</2> zijn gekoppeld aan dit product.</1>"

#~ msgid "<0>The number of tickets available for this ticket</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this ticket.</1>"
#~ msgstr "<0>Het aantal beschikbare tickets voor dit ticket</0><1>Deze waarde kan worden overschreven als er <2>Capaciteitslimieten</2> zijn gekoppeld aan dit ticket.</1>"

#: src/components/common/WebhookTable/index.tsx:205
msgid "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"
msgstr "<0>Webhooks stellen externe services direct op de hoogte wanneer er iets gebeurt, zoals het toevoegen van een nieuwe deelnemer aan je CRM of mailinglijst na registratie, en zorgen zo voor naadloze automatisering.</0><1>Gebruik diensten van derden zoals <2>Zapier</2>, <3>IFTTT</3> of <4>Make</4> om aangepaste workflows te maken en taken te automatiseren.</1>"

#: src/components/routes/event/GettingStarted/index.tsx:134
msgid "⚡️ Set up your event"
msgstr "⚡️ Uw evenement opzetten"

#: src/components/routes/event/GettingStarted/index.tsx:190
msgid "✉️ Confirm your email address"
msgstr "✉️ Bevestig je e-mailadres"

#~ msgid "🎉 Congratulation on creating an event!"
#~ msgstr "🎉 Gefeliciteerd met het maken van een evenement!"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "🎉 Congratulations on creating an event!"
#~ msgstr "🎉 Gefeliciteerd met het maken van een evenement!"

#: src/components/routes/event/GettingStarted/index.tsx:67
#~ msgid "🎟️ Add products"
#~ msgstr "🎟️ Producten toevoegen"

#: src/components/routes/event/GettingStarted/index.tsx:119
msgid "🎟️ Add tickets"
msgstr "🎟️ Tickets toevoegen"

#: src/components/routes/event/GettingStarted/index.tsx:162
msgid "🎨 Customize your event page"
msgstr "🎨 Je evenementpagina aanpassen"

#: src/components/routes/event/GettingStarted/index.tsx:147
msgid "💳 Connect with Stripe"
msgstr "💳 Maak verbinding met Stripe"

#~ msgid "📢 Promote your event"
#~ msgstr "📢 Promoot uw evenement"

#: src/components/routes/event/GettingStarted/index.tsx:176
msgid "🚀 Set your event live"
msgstr "🚀 Je evenement live zetten"

#: src/components/common/Countdown/index.tsx:34
msgid "0 minutes and 0 seconds"
msgstr "0 minuten en 0 seconden"

#~ msgid "0.50 for $0.50"
#~ msgstr "0,50 voor $0,50"

#: src/components/routes/event/Webhooks/index.tsx:23
msgid "1 Active Webhook"
msgstr "1 Actieve webhook"

#~ msgid "1.75 for 1.75%"
#~ msgstr "1,75 voor 1,75%"

#: src/components/modals/RefundOrderModal/index.tsx:105
msgid "10.00"
msgstr "10.00"

#~ msgid "10001"
#~ msgstr "10001"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:123
msgid "123 Main Street"
msgstr "Hoofdstraat 123"

#: src/components/common/WidgetEditor/index.tsx:226
msgid "20"
msgstr "20"

#: src/components/routes/welcome/index.tsx:91
msgid "2024-01-01 10:00"
msgstr "2024-01-01 10:00"

#: src/components/routes/welcome/index.tsx:97
msgid "2024-01-01 18:00"
msgstr "2024-01-01 18:00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:147
msgid "94103"
msgstr "94103"

#: src/components/forms/QuestionForm/index.tsx:138
msgid "A date input. Perfect for asking for a date of birth etc."
msgstr "Een datuminvoer. Perfect voor het vragen naar een geboortedatum enz."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
msgid "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."
msgstr "Een standaard {type} wordt automatisch toegepast op alle nieuwe producten. Je kunt dit opheffen per product."

#~ msgid "A default {type} is automaticaly applied to all new tickets. You can override this on a per ticket basis."
#~ msgstr "Een standaard {type} wordt automatisch toegepast op alle nieuwe tickets. Je kunt dit per ticket veranderen."

#: src/components/forms/QuestionForm/index.tsx:126
msgid "A Dropdown input allows only one selection"
msgstr "Een Dropdown-ingang laat slechts één selectie toe"

#: src/components/forms/TaxAndFeeForm/index.tsx:20
msgid "A fee, like a booking fee or a service fee"
msgstr "Een vergoeding, zoals reserveringskosten of servicekosten"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
msgid "A fixed amount per product. E.g, $0.50 per product"
msgstr "Een vast bedrag per product. Bijv. $0,50 per product"

#~ msgid "A fixed amount per ticket. E.g, $0.50 per ticket"
#~ msgstr "Een vast bedrag per ticket. Bijv. $0,50 per ticket"

#: src/components/forms/QuestionForm/index.tsx:108
msgid "A multi line text input"
msgstr "Een meerregelige tekstinvoer"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
msgid "A percentage of the product price. E.g., 3.5% of the product price"
msgstr "Een percentage van de productprijs. Bijvoorbeeld 3,5% van de productprijs"

#~ msgid "A percentage of the ticket price. E.g., 3.5% of the ticket price"
#~ msgstr "Een percentage van de ticketprijs. Bijvoorbeeld 3,5% van de ticketprijs."

#: src/components/forms/PromoCodeForm/index.tsx:37
msgid "A promo code with no discount can be used to reveal hidden products."
msgstr "Een promotiecode zonder korting kan worden gebruikt om verborgen producten te onthullen."

#~ msgid "A promo code with no discount can be used to reveal hidden tickets."
#~ msgstr "Een promotiecode zonder korting kan worden gebruikt om verborgen tickets te onthullen."

#~ msgid "A Radio option allows has multiple options but only one can be selected."
#~ msgstr "Een Radio-optie biedt meerdere opties, maar er kan er maar één worden geselecteerd."

#~ msgid "A Radio Option allows only one selection"
#~ msgstr "Een radio-optie staat slechts één selectie toe"

#: src/components/forms/QuestionForm/index.tsx:120
msgid "A Radio option has multiple options but only one can be selected."
msgstr "Een Radio-optie heeft meerdere opties, maar er kan er maar één worden geselecteerd."

#~ msgid "A short description of Awesome Events Ltd."
#~ msgstr "Een korte beschrijving van Awesome Events Ltd."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:69
msgid "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"
msgstr "Een korte beschrijving van het evenement die zal worden weergegeven in zoekmachineresultaten en bij het delen op sociale media. Standaard wordt de beschrijving van het evenement gebruikt"

#: src/components/forms/QuestionForm/index.tsx:102
msgid "A single line text input"
msgstr "Een enkele regel tekstinvoer"

#~ msgid "A single question per attendee. E.g, What is your preferred meal?"
#~ msgstr "Eén vraag per deelnemer. Bijvoorbeeld: Wat is uw favoriete maaltijd?"

#~ msgid "A single question per order. E.g, What is your company name?"
#~ msgstr "Eén vraag per opdracht. Bijv. Wat is uw bedrijfsnaam?"

#: src/components/forms/QuestionForm/index.tsx:87
msgid "A single question per order. E.g, What is your shipping address?"
msgstr "Eén vraag per bestelling. Bijv. Wat is uw verzendadres?"

#: src/components/forms/QuestionForm/index.tsx:93
msgid "A single question per product. E.g, What is your t-shirt size?"
msgstr "Eén vraag per product. Bijv. Wat is je t-shirtmaat?"

#: src/components/forms/TaxAndFeeForm/index.tsx:14
msgid "A standard tax, like VAT or GST"
msgstr "Een standaardbelasting, zoals BTW of GST"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:76
msgid "About"
msgstr "Over"

#~ msgid "About hi.events"
#~ msgstr "Over hi.events"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:211
msgid "About Stripe Connect"
msgstr "Over Stripe Connect"

#~ msgid "About the event"
#~ msgstr "Over het evenement"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:91
msgid "Accept bank transfers, checks, or other offline payment methods"
msgstr "Accepteer bankoverschrijvingen, cheques of andere offline betalingsmethoden"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:86
msgid "Accept credit card payments with Stripe"
msgstr "Accepteer creditcardbetalingen met Stripe"

#: src/components/routes/auth/AcceptInvitation/index.tsx:126
msgid "Accept Invitation"
msgstr "Uitnodiging accepteren"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:125
msgid "Access Denied"
msgstr "Toegang geweigerd"

#~ msgid "Access to the VIP area..."
#~ msgstr "Toegang tot de VIP-ruimte..."

#: src/components/routes/account/ManageAccount/index.tsx:24
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:53
msgid "Account"
msgstr "Account"

#~ msgid "Account Email"
#~ msgstr "Account e-mail"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:62
msgid "Account Name"
msgstr "Naam rekening"

#: src/components/common/GlobalMenu/index.tsx:32
#: src/components/routes/account/ManageAccount/index.tsx:19
msgid "Account Settings"
msgstr "Accountinstellingen"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:42
msgid "Account updated successfully"
msgstr "Account succesvol bijgewerkt"

#: src/components/common/AttendeeTable/index.tsx:158
#: src/components/common/ProductsTable/SortableProduct/index.tsx:267
#: src/components/common/QuestionsTable/index.tsx:108
msgid "Actions"
msgstr "Acties"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "Activate"
msgstr "Activeer"

#: src/components/forms/CheckInListForm/index.tsx:47
msgid "Activation date"
msgstr "Activeringsdatum"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:19
#: src/components/modals/EditUserModal/index.tsx:110
msgid "Active"
msgstr "Actief"

#~ msgid "Add"
#~ msgstr "Voeg  toe"

#~ msgid "Add a cover image, description, and more to make your event stand out."
#~ msgstr "Voeg een omslagafbeelding, beschrijving en meer toe om je evenement op te laten vallen."

#: src/components/forms/CheckInListForm/index.tsx:39
msgid "Add a description for this check-in list"
msgstr "Voeg een beschrijving toe voor deze check-in lijst"

#: src/components/modals/ManageAttendeeModal/index.tsx:131
msgid "Add any notes about the attendee. These will not be visible to the attendee."
msgstr "Voeg notities over de genodigde toe. Deze zijn niet zichtbaar voor de deelnemer."

#: src/components/modals/ManageAttendeeModal/index.tsx:133
msgid "Add any notes about the attendee..."
msgstr "Notities over de deelnemer toevoegen..."

#: src/components/modals/ManageOrderModal/index.tsx:165
msgid "Add any notes about the order. These will not be visible to the customer."
msgstr "Voeg eventuele notities over de bestelling toe. Deze zijn niet zichtbaar voor de klant."

#: src/components/modals/ManageOrderModal/index.tsx:169
msgid "Add any notes about the order..."
msgstr "Opmerkingen over de bestelling toevoegen..."

#: src/components/forms/QuestionForm/index.tsx:202
msgid "Add description"
msgstr "Beschrijving toevoegen"

#: src/components/routes/event/GettingStarted/index.tsx:85
#~ msgid "Add event details and and manage event settings."
#~ msgstr "Voeg details van evenementen toe en beheer evenementinstellingen."

#: src/components/routes/event/GettingStarted/index.tsx:137
msgid "Add event details and manage event settings."
msgstr "Voeg evenementgegevens toe en beheer de instellingen."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:153
msgid "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"
msgstr "Instructies voor offline betalingen toevoegen (bijv. details voor bankoverschrijving, waar cheques naartoe moeten, betalingstermijnen)"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add More products"
#~ msgstr "Meer producten toevoegen"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add More tickets"
msgstr "Meer tickets toevoegen"

#: src/components/common/HeadingCard/index.tsx:27
msgid "Add New"
msgstr "Nieuw toevoegen"

#: src/components/forms/QuestionForm/index.tsx:68
msgid "Add Option"
msgstr "Optie toevoegen"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:68
msgid "Add Product"
msgstr "Product toevoegen"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:51
msgid "Add Product to Category"
msgstr "Product toevoegen aan categorie"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add products"
#~ msgstr "Producten toevoegen"

#: src/components/common/QuestionsTable/index.tsx:281
msgid "Add question"
msgstr "Vraag toevoegen"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:18
msgid "Add Tax or Fee"
msgstr "Belasting of toeslag toevoegen"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add tickets"
msgstr "Tickets toevoegen"

#: src/components/forms/ProductForm/index.tsx:337
msgid "Add tier"
msgstr "Niveau toevoegen"

#: src/components/common/AddEventToCalendarButton/index.tsx:112
#: src/components/common/AddEventToCalendarButton/index.tsx:120
msgid "Add to Calendar"
msgstr "Toevoegen aan kalender"

#: src/components/common/WebhookTable/index.tsx:223
#: src/components/routes/event/Webhooks/index.tsx:35
msgid "Add Webhook"
msgstr "Webhook toevoegen"

#~ msgid "Additional Details"
#~ msgstr "Extra details"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:219
msgid "Additional Information"
msgstr "Aanvullende informatie"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/forms/ProductForm/index.tsx:348
msgid "Additional Options"
msgstr "Extra opties"

#: src/components/common/OrderDetails/index.tsx:96
#: src/components/forms/QuestionForm/index.tsx:130
msgid "Address"
msgstr "Adres"

#: src/components/common/CheckoutQuestion/index.tsx:156
msgid "Address line 1"
msgstr "Adresregel 1"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:122
#: src/components/routes/product-widget/CollectInformation/index.tsx:319
#: src/components/routes/product-widget/CollectInformation/index.tsx:320
msgid "Address Line 1"
msgstr "Adresregel 1"

#: src/components/common/CheckoutQuestion/index.tsx:159
msgid "Address line 2"
msgstr "Adresregel 2"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:127
#: src/components/routes/product-widget/CollectInformation/index.tsx:324
#: src/components/routes/product-widget/CollectInformation/index.tsx:325
msgid "Address Line 2"
msgstr "Adresregel 2"

#: src/components/modals/EditUserModal/index.tsx:49
#: src/components/modals/InviteUserModal/index.tsx:41
msgid "Admin"
msgstr "Admin"

#: src/components/modals/EditUserModal/index.tsx:51
#: src/components/modals/InviteUserModal/index.tsx:43
msgid "Admin users have full access to events and account settings."
msgstr "Admin-gebruikers hebben volledige toegang tot evenementen en accountinstellingen."

#~ msgid "Affiliates"
#~ msgstr "Filialen"

#: src/components/common/MessageList/index.tsx:21
msgid "All attendees"
msgstr "Alle deelnemers"

#: src/components/modals/SendMessageModal/index.tsx:187
msgid "All attendees of this event"
msgstr "Alle deelnemers aan dit evenement"

#~ msgid "All Events"
#~ msgstr "Alle evenementen"

#: src/components/common/PromoCodeTable/index.tsx:131
msgid "All Products"
msgstr "Alle producten"

#~ msgid "All Tickets"
#~ msgstr "Alle tickets"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:157
msgid "Allow attendees associated with unpaid orders to check in"
msgstr "Deelnemers met onbetaalde bestellingen toestaan om in te checken"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:82
msgid "Allow search engine indexing"
msgstr "Indexering door zoekmachines toestaan"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:81
msgid "Allow search engines to index this event"
msgstr "Laat zoekmachines dit evenement indexeren"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:78
msgid "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."
msgstr "We zijn er bijna! We wachten op de verwerking van je betaling. Dit duurt maar een paar seconden."

#~ msgid "Already have an account?"
#~ msgstr "Heb je al een account?"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:77
msgid "Amazing, Event, Keywords..."
msgstr "Verbazingwekkend, evenement, trefwoorden..."

#: src/components/common/OrdersTable/index.tsx:207
#: src/components/forms/TaxAndFeeForm/index.tsx:71
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:36
msgid "Amount"
msgstr "Bedrag"

#~ msgid "amount in {0}"
#~ msgstr "bedrag in {0}"

#: src/components/modals/CreateAttendeeModal/index.tsx:165
msgid "Amount paid ({0})"
msgstr "Betaald bedrag ({0})"

#~ msgid "Amount paid ${0}"
#~ msgstr "Betaald bedrag ${0}"

#: src/mutations/useExportAnswers.ts:45
msgid "An error occurred while checking export status."
msgstr "Er is een fout opgetreden tijdens het controleren van de exportstatus."

#: src/components/common/ErrorDisplay/index.tsx:18
msgid "An error occurred while loading the page"
msgstr "Er is een fout opgetreden tijdens het laden van de pagina"

#: src/components/common/QuestionsTable/index.tsx:148
msgid "An error occurred while sorting the questions. Please try again or refresh the page"
msgstr "Er is een fout opgetreden tijdens het sorteren van de vragen. Probeer het opnieuw of vernieuw de pagina"

#~ msgid "An error occurred while sorting the tickets. Please try again or refresh the page"
#~ msgstr "Er is een fout opgetreden tijdens het sorteren van de tickets. Probeer het opnieuw of vernieuw de pagina"

#~ msgid "An event is the actual event you are hosting"
#~ msgstr "Een evenement is het evenement dat je organiseert"

#~ msgid "An event is the actual event you are hosting. You can add more details later."
#~ msgstr "Een evenement is het eigenlijke evenement dat je organiseert. Je kunt later meer details toevoegen."

#: src/components/routes/welcome/index.tsx:75
msgid "An event is the gathering or occasion you’re organizing. You can add more details later."
msgstr "Een evenement is de bijeenkomst of gelegenheid die je organiseert. Je kunt later meer details toevoegen."

#: src/components/routes/welcome/index.tsx:23
msgid "An organizer is the company or person who is hosting the event"
msgstr "Een organisator is het bedrijf of de persoon die het evenement organiseert"

#: src/components/forms/StripeCheckoutForm/index.tsx:40
msgid "An unexpected error occurred."
msgstr "Er is een onverwachte fout opgetreden."

#: src/hooks/useFormErrorResponseHandler.tsx:48
msgid "An unexpected error occurred. Please try again."
msgstr "Er is een onverwachte fout opgetreden. Probeer het opnieuw."

#: src/components/common/QuestionAndAnswerList/index.tsx:97
msgid "Answer updated successfully."
msgstr "Antwoord succesvol bijgewerkt."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
msgid "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
msgstr "Vragen van producthouders worden naar dit e-mailadres gestuurd. Dit e-mailadres wordt ook gebruikt als"

#~ msgid "Any queries from ticket holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
#~ msgstr "Vragen van tickethouders worden naar dit e-mailadres gestuurd. Dit e-mailadres wordt ook gebruikt als"

#: src/components/common/WidgetEditor/index.tsx:213
msgid "Appearance"
msgstr "Uiterlijk"

#: src/components/routes/product-widget/SelectProducts/index.tsx:500
msgid "applied"
msgstr "toegepast"

#: src/components/common/CapacityAssignmentList/index.tsx:97
msgid "Applies to {0} products"
msgstr "Geldt voor {0} producten"

#~ msgid "Applies to {0} tickets"
#~ msgstr "Geldt voor {0} tickets"

#: src/components/common/CapacityAssignmentList/index.tsx:98
msgid "Applies to 1 product"
msgstr "Geldt voor 1 product"

#~ msgid "Applies to 1 ticket"
#~ msgstr "Geldt voor 1 ticket"

#: src/components/common/FilterModal/index.tsx:253
msgid "Apply"
msgstr "Toepassen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:528
msgid "Apply Promo Code"
msgstr "Kortingscode toepassen"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
msgid "Apply this {type} to all new products"
msgstr "Pas dit {type} toe op alle nieuwe producten"

#~ msgid "Apply this {type} to all new tickets"
#~ msgstr "Pas dit {type} toe op alle nieuwe tickets"

#: src/components/common/EventCard/index.tsx:167
msgid "Archive event"
msgstr "Archief evenement"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:32
msgid "Archived"
msgstr "Gearchiveerd"

#: src/components/routes/events/Dashboard/index.tsx:59
msgid "Archived Events"
msgstr "Gearchiveerde evenementen"

#: src/components/common/AttendeeTable/index.tsx:76
msgid "Are you sure you want to activate this attendee?"
msgstr "Weet je zeker dat je deze deelnemer wilt activeren?"

#: src/components/common/EventCard/index.tsx:58
msgid "Are you sure you want to archive this event?"
msgstr "Weet je zeker dat je dit evenement wilt archiveren?"

#~ msgid "Are you sure you want to cancel this attendee? This will void their product"
#~ msgstr "Weet je zeker dat je deze deelnemer wilt annuleren? Hierdoor vervalt hun product"

#: src/components/common/AttendeeTable/index.tsx:77
msgid "Are you sure you want to cancel this attendee? This will void their ticket"
msgstr "Weet je zeker dat je deze deelnemer wilt annuleren? Hiermee vervalt hun ticket"

#: src/components/common/PromoCodeTable/index.tsx:37
msgid "Are you sure you want to delete this promo code?"
msgstr "Weet je zeker dat je deze promotiecode wilt verwijderen?"

#: src/components/common/QuestionsTable/index.tsx:164
msgid "Are you sure you want to delete this question?"
msgstr "Weet je zeker dat je deze vraag wilt verwijderen?"

#: src/components/common/WebhookTable/index.tsx:122
msgid "Are you sure you want to delete this webhook?"
msgstr "Weet je zeker dat je deze webhook wilt verwijderen?"

#: src/components/layouts/Event/index.tsx:68
#: src/components/routes/event/EventDashboard/index.tsx:64
msgid "Are you sure you want to make this event draft? This will make the event invisible to the public"
msgstr "Weet je zeker dat je dit evenement concept wilt maken? Dit maakt het evenement onzichtbaar voor het publiek"

#: src/components/layouts/Event/index.tsx:69
#: src/components/routes/event/EventDashboard/index.tsx:65
msgid "Are you sure you want to make this event public? This will make the event visible to the public"
msgstr "Weet je zeker dat je dit evenement openbaar wilt maken? Hierdoor wordt het evenement zichtbaar voor het publiek"

#: src/components/common/EventCard/index.tsx:59
msgid "Are you sure you want to restore this event? It will be restored as a draft event."
msgstr "Weet je zeker dat je dit evenements wilt herstellen? Het zal worden hersteld als een conceptevenement."

#: src/components/common/CapacityAssignmentList/index.tsx:163
msgid "Are you sure you would like to delete this Capacity Assignment?"
msgstr "Weet je zeker dat je deze Capaciteitstoewijzing wilt verwijderen?"

#: src/components/common/CheckInListList/index.tsx:183
msgid "Are you sure you would like to delete this Check-In List?"
msgstr "Weet je zeker dat je deze Check-In lijst wilt verwijderen?"

#~ msgid "Ask once per attendee"
#~ msgstr "Vraag één keer per deelnemer"

#: src/components/forms/QuestionForm/index.tsx:85
msgid "Ask once per order"
msgstr "Vraag één keer per bestelling"

#: src/components/forms/QuestionForm/index.tsx:91
msgid "Ask once per product"
msgstr "Vraag één keer per product"

#: src/components/modals/CreateWebhookModal/index.tsx:33
msgid "At least one event type must be selected"
msgstr "Er moet minstens één evenement type worden geselecteerd"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Attendee"
msgstr "Deelnemer"

#: src/components/forms/WebhookForm/index.tsx:94
msgid "Attendee Cancelled"
msgstr "Deelnemer geannuleerd"

#: src/components/forms/WebhookForm/index.tsx:82
msgid "Attendee Created"
msgstr "Deelnemer Gemaakt"

#: src/components/modals/ManageAttendeeModal/index.tsx:147
msgid "Attendee Details"
msgstr "Details deelnemers"

#: src/components/layouts/AuthLayout/index.tsx:73
msgid "Attendee Management"
msgstr "Beheer van deelnemers"

#: src/components/layouts/CheckIn/index.tsx:150
msgid "Attendee not found"
msgstr "Deelnemer niet gevonden"

#: src/components/modals/ManageAttendeeModal/index.tsx:153
msgid "Attendee Notes"
msgstr "Opmerkingen voor deelnemers"

#: src/components/common/QuestionsTable/index.tsx:369
msgid "Attendee questions"
msgstr "Vragen van deelnemers"

#~ msgid "Attendee questions are asked once per attendee. By default, people are asked for their first name, last name, and email address."
#~ msgstr "Vragen aan deelnemers worden één keer per deelnemer gesteld. Standaard worden mensen gevraagd naar hun voornaam, achternaam en e-mailadres."

#~ msgid "Attendee questions are asked once per attendee. By default, we ask for the attendee's first name, last name, and email address."
#~ msgstr "Vragen over deelnemers worden één keer per deelnemer gesteld. Standaard vragen we naar de voornaam, achternaam en het e-mailadres van de deelnemer."

#: src/components/modals/ManageAttendeeModal/index.tsx:172
msgid "Attendee Ticket"
msgstr "Bezoekerskaartje"

#: src/components/forms/WebhookForm/index.tsx:88
msgid "Attendee Updated"
msgstr "Deelnemer bijgewerkt"

#: src/components/common/OrdersTable/index.tsx:206
#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
#: src/components/common/StatBoxes/index.tsx:27
#: src/components/layouts/Event/index.tsx:99
#: src/components/modals/ManageOrderModal/index.tsx:126
#: src/components/routes/event/attendees.tsx:59
msgid "Attendees"
msgstr "Aanwezigen"

#: src/components/routes/event/attendees.tsx:43
msgid "Attendees Exported"
msgstr "Geëxporteerde bezoekers"

#: src/components/routes/event/EventDashboard/index.tsx:239
msgid "Attendees Registered"
msgstr "Geregistreerde deelnemers"

#~ msgid "Attendees with a specific product"
#~ msgstr "Deelnemers met een specifiek product"

#: src/components/modals/SendMessageModal/index.tsx:183
msgid "Attendees with a specific ticket"
msgstr "Deelnemers met een specifiek ticket"

#: src/components/common/WidgetEditor/index.tsx:233
msgid "Auto Resize"
msgstr "Automatisch formaat wijzigen"

#: src/components/layouts/AuthLayout/index.tsx:88
#~ msgid "Auto Workflow"
#~ msgstr "Automatische workflow"

#: src/components/layouts/AuthLayout/index.tsx:79
msgid "Automated entry management with multiple check-in lists and real-time validation"
msgstr "Geautomatiseerd invoerbeheer met meerdere inchecklijsten en real-time validatie"

#: src/components/common/WidgetEditor/index.tsx:236
msgid "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."
msgstr "Pas de hoogte van de widget automatisch aan op basis van de inhoud. Als deze optie is uitgeschakeld, vult de widget de hoogte van de container."

#~ msgid "Availability"
#~ msgstr "Beschikbaarheid"

#~ msgid "Avg Discount/Order"
#~ msgstr "Gem Korting/Bestel"

#~ msgid "Avg Order Value"
#~ msgstr "Gemiddelde bestelwaarde"

#: src/components/common/OrderStatusBadge/index.tsx:15
#: src/components/modals/SendMessageModal/index.tsx:231
msgid "Awaiting offline payment"
msgstr "In afwachting van offline betaling"

#: src/components/routes/event/orders.tsx:25
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:43
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:66
msgid "Awaiting Offline Payment"
msgstr "In afwachting van offline betaling"

#: src/components/layouts/CheckIn/index.tsx:263
msgid "Awaiting payment"
msgstr "In afwachting van betaling"

#: src/components/common/AttendeeTicket/index.tsx:67
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:40
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:65
msgid "Awaiting Payment"
msgstr "In afwachting van betaling"

#: src/components/routes/welcome/index.tsx:84
msgid "Awesome Event"
msgstr "Geweldig evenement"

#~ msgid "Awesome Events Ltd."
#~ msgstr "Awesome Events Ltd."

#: src/components/forms/OrganizerForm/index.tsx:28
msgid "Awesome Organizer Ltd."
msgstr "Awesome Organizer Ltd."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:42
msgid "Back to all events"
msgstr "Terug naar alle evenementen"

#~ msgid "Back to event homepage"
#~ msgstr "Terug naar de homepage van het evenement"

#: src/components/layouts/Checkout/index.tsx:73
#: src/components/routes/product-widget/CollectInformation/index.tsx:249
#: src/components/routes/product-widget/CollectInformation/index.tsx:261
msgid "Back to event page"
msgstr "Terug naar de evenementpagina"

#: src/components/routes/auth/ForgotPassword/index.tsx:59
#: src/components/routes/auth/ResetPassword/index.tsx:66
msgid "Back to login"
msgstr "Terug naar inloggen"

#~ msgid "Background color"
#~ msgstr "Achtergrondkleur"

#: src/components/common/WidgetEditor/index.tsx:175
msgid "Background Color"
msgstr "Achtergrondkleur"

#: src/components/routes/event/HomepageDesigner/index.tsx:144
msgid "Background Type"
msgstr "Achtergrond Type"

#~ msgid "Basic Details"
#~ msgstr "Basisgegevens"

#~ msgid "Because you are the account owner, you cannot change your role or status."
#~ msgstr "Omdat je de accounteigenaar bent, kun je je rol of status niet wijzigen."

#: src/components/modals/SendMessageModal/index.tsx:278
msgid "Before you send!"
msgstr "Voordat je verzendt!"

#~ msgid "Before you're event can go live, there's a few thing to do."
#~ msgstr "Voordat je evenement live kan gaan, moet je een paar dingen doen."

#: src/components/routes/event/GettingStarted/index.tsx:60
#~ msgid "Before your event can go live, there are a few things you need to do."
#~ msgstr "Voordat je evenement live kan gaan, moet je een paar dingen doen."

#: src/components/routes/event/GettingStarted/index.tsx:92
msgid "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."
msgstr "Voordat je evenement live kan gaan, moet je een paar dingen doen. Voltooi alle onderstaande stappen om te beginnen."

#~ msgid "Begin selling products in minutes"
#~ msgstr "Begin binnen enkele minuten met het verkopen van producten"

#~ msgid "Begin selling tickets in minutes"
#~ msgstr "Begin binnen enkele minuten met het verkopen van tickets"

#~ msgid "Billing"
#~ msgstr "Facturering"

#: src/components/routes/product-widget/CollectInformation/index.tsx:313
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:142
msgid "Billing Address"
msgstr "Factuuradres"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:167
msgid "Billing Settings"
msgstr "Factureringsinstellingen"

#: src/components/layouts/AuthLayout/index.tsx:83
#~ msgid "Brand Control"
#~ msgstr "Merkcontrole"

#: src/components/common/LanguageSwitcher/index.tsx:28
msgid "Brazilian Portuguese"
msgstr "Braziliaans Portugees"

#~ msgid "Button color"
#~ msgstr "Kleur knop"

#: src/components/routes/auth/Register/index.tsx:133
msgid "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."
msgstr "Door je te registreren ga je akkoord met onze <0>Servicevoorwaarden</0> en <1>Privacybeleid</1>."

#: src/components/forms/TaxAndFeeForm/index.tsx:52
msgid "Calculation Type"
msgstr "Type berekening"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:140
msgid "California"
msgstr "Californië"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:175
msgid "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."
msgstr "Cameratoestemming is geweigerd. <0>Vraag toestemming</0> opnieuw, of als dit niet werkt, moet je <1>deze pagina</1> toegang geven tot je camera in je browserinstellingen."

#~ msgid "Can't load events"
#~ msgstr "Kan evenementen niet laden"

#: src/components/common/AttendeeTable/index.tsx:182
#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/QuestionAndAnswerList/index.tsx:149
#: src/components/layouts/CheckIn/index.tsx:225
#: src/utilites/confirmationDialog.tsx:11
msgid "Cancel"
msgstr "Annuleren"

#: src/components/routes/profile/ManageProfile/index.tsx:126
msgid "Cancel email change"
msgstr "E-mailwijziging annuleren"

#: src/components/common/OrdersTable/index.tsx:190
#: src/components/modals/RefundOrderModal/index.tsx:112
msgid "Cancel order"
msgstr "Bestelling annuleren"

#: src/components/modals/CancelOrderModal/index.tsx:59
msgid "Cancel Order"
msgstr "Bestelling annuleren"

#: src/components/modals/CancelOrderModal/index.tsx:44
msgid "Cancel Order {0}"
msgstr "Annuleer order {0}"

#~ msgid "Canceled"
#~ msgstr "Geannuleerd"

#: src/components/modals/CancelOrderModal/index.tsx:54
msgid "Canceling will cancel all products associated with this order, and release the products back into the available pool."
msgstr "Door te annuleren worden alle producten geannuleerd die aan deze bestelling zijn gekoppeld en worden de producten teruggezet in de beschikbare pool."

#~ msgid "Canceling will cancel all tickets associated with this order, and release the tickets back into the available pool."
#~ msgstr "Door te annuleren worden alle tickets geannuleerd die aan deze bestelling zijn gekoppeld en worden de tickets teruggegeven aan de beschikbare pool."

#: src/components/common/AttendeeTicket/index.tsx:61
#: src/components/common/OrderStatusBadge/index.tsx:12
#: src/components/routes/event/orders.tsx:24
msgid "Cancelled"
msgstr "Geannuleerd"

#: src/components/layouts/CheckIn/index.tsx:173
msgid "Cannot Check In"
msgstr "Kan niet inchecken"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:42
#: src/components/layouts/Event/index.tsx:103
msgid "Capacity"
msgstr "Capaciteit"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:36
msgid "Capacity Assignment created successfully"
msgstr "Capaciteitstoewijzing succesvol aangemaakt"

#: src/components/common/CapacityAssignmentList/index.tsx:31
msgid "Capacity Assignment deleted successfully"
msgstr "Capaciteitstoewijzing succesvol verwijderd"

#: src/components/routes/event/CapacityAssignments/index.tsx:32
msgid "Capacity Management"
msgstr "Capaciteitsmanagement"

#: src/components/modals/CreateProductCategoryModal/index.tsx:56
msgid "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."
msgstr "Met categorieën kun je producten groeperen. Je kunt bijvoorbeeld een categorie hebben voor."

#: src/components/forms/ProductForm/index.tsx:267
msgid "Categories help you organize your products. This title will be displayed on the public event page."
msgstr "Categorieën helpen je om je producten te organiseren. Deze titel wordt weergegeven op de openbare evenementpagina."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:91
msgid "Categories reordered successfully."
msgstr "Categorieën opnieuw gerangschikt."

#: src/components/routes/event/products.tsx:96
msgid "Category"
msgstr "Categorie"

#: src/components/modals/CreateProductCategoryModal/index.tsx:37
msgid "Category Created Successfully"
msgstr "Categorie succesvol aangemaakt"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Change Cover"
msgstr "Verander Cover"

#: src/components/routes/profile/ManageProfile/index.tsx:201
msgid "Change password"
msgstr "Wachtwoord wijzigen"

#~ msgid "check in"
#~ msgstr "inchecken"

#: src/components/layouts/CheckIn/index.tsx:180
msgid "Check In"
msgstr "Inchecken"

#: src/components/layouts/CheckIn/index.tsx:193
msgid "Check in {0} {1}"
msgstr "Inchecken {0} {1}"

#: src/components/layouts/CheckIn/index.tsx:218
msgid "Check in and mark order as paid"
msgstr "Check in en markeer bestelling als betaald"

#: src/components/layouts/CheckIn/index.tsx:209
msgid "Check in only"
msgstr "Alleen inchecken"

#~ msgid "check out"
#~ msgstr "uitchecken"

#: src/components/layouts/CheckIn/index.tsx:177
msgid "Check Out"
msgstr "Uitchecken"

#: src/components/layouts/Checkout/index.tsx:106
msgid "Check out this event!"
msgstr "Bekijk dit evenement!"

#: src/components/common/EventCard/index.tsx:156
msgid "Check-in"
msgstr "Check-in"

#~ msgid "Check-In"
#~ msgstr "Check-In"

#: src/components/forms/WebhookForm/index.tsx:100
msgid "Check-in Created"
msgstr "Gecreëerd inchecken"

#: src/components/forms/WebhookForm/index.tsx:106
msgid "Check-in Deleted"
msgstr "Check-in verwijderd"

#: src/components/modals/CreateCheckInListModal/index.tsx:40
msgid "Check-In List created successfully"
msgstr "Check-in lijst succesvol aangemaakt"

#: src/components/common/CheckInListList/index.tsx:33
msgid "Check-In List deleted successfully"
msgstr "Check-in lijst succesvol verwijderd"

#: src/components/layouts/CheckIn/index.tsx:326
msgid "Check-in list has expired"
msgstr "Check-in lijst is verlopen"

#: src/components/layouts/CheckIn/index.tsx:343
msgid "Check-in list is not active"
msgstr "Check-in lijst is niet actief"

#: src/components/layouts/CheckIn/index.tsx:311
msgid "Check-in list not found"
msgstr "Check-in lijst niet gevonden"

#: src/components/layouts/Event/index.tsx:104
#: src/components/routes/event/CheckInLists/index.tsx:32
msgid "Check-In Lists"
msgstr "Inchecklijsten"

#: src/components/common/CheckInListList/index.tsx:162
msgid "Check-In URL copied to clipboard"
msgstr "Check-In URL gekopieerd naar klembord"

#: src/components/forms/QuestionForm/index.tsx:114
msgid "Checkbox options allow multiple selections"
msgstr "Selectievakjes maken meerdere selecties mogelijk"

#: src/components/forms/QuestionForm/index.tsx:112
msgid "Checkboxes"
msgstr "Selectievakjes"

#: src/components/common/AttendeeDetails/index.tsx:29
msgid "Checked In"
msgstr "Ingecheckt"

#~ msgid "Checked in successfully"
#~ msgstr "Succesvol ingecheckt"

#: src/components/layouts/Checkout/index.tsx:83
#: src/components/routes/event/Settings/index.tsx:40
msgid "Checkout"
msgstr "Kassa"

#~ msgid "Checkout Messaging"
#~ msgstr "Afrekenberichten"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:61
msgid "Checkout Settings"
msgstr "Afrekeninstellingen"

#~ msgid "Chinese"
#~ msgstr "Chinees"

#: src/components/common/LanguageSwitcher/index.tsx:30
msgid "Chinese (Simplified)"
msgstr "Chinees (Vereenvoudigd)"

#: src/components/common/LanguageSwitcher/index.tsx:32
msgid "Chinese (Traditional)"
msgstr "Chinees (Traditioneel)"

#: src/components/routes/event/HomepageDesigner/index.tsx:133
msgid "Choose a color for your background"
msgstr "Kies een kleur voor je achtergrond"

#: src/components/modals/ChooseAccountModal/index.tsx:13
msgid "Choose an account"
msgstr "Kies een account"

#~ msgid "Choose what notifications you want to receive"
#~ msgstr "Kies welke meldingen je wilt ontvangen"

#: src/components/common/CheckoutQuestion/index.tsx:162
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:134
#: src/components/routes/product-widget/CollectInformation/index.tsx:333
#: src/components/routes/product-widget/CollectInformation/index.tsx:334
msgid "City"
msgstr "Stad"

#: src/components/common/SearchBar/index.tsx:75
msgid "Clear Search Text"
msgstr "Zoektekst wissen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:288
#~ msgid "click here"
#~ msgstr "klik hier"

#: src/components/common/PromoCodeTable/index.tsx:98
msgid "Click to copy"
msgstr "Klik om te kopiëren"

#: src/components/routes/product-widget/SelectProducts/index.tsx:533
msgid "close"
msgstr "sluiten"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:186
#: src/components/modals/RefundOrderModal/index.tsx:125
#: src/components/routes/product-widget/SelectProducts/index.tsx:534
msgid "Close"
msgstr "Sluit"

#: src/components/layouts/Event/index.tsx:281
msgid "Close sidebar"
msgstr "Zijbalk sluiten"

#: src/components/common/PromoCodeTable/index.tsx:69
#: src/components/forms/PromoCodeForm/index.tsx:34
msgid "Code"
msgstr "Code"

#: src/components/modals/CreatePromoCodeModal/index.tsx:26
#: src/components/modals/EditPromoCodeModal/index.tsx:38
msgid "Code must be between 3 and 50 characters long"
msgstr "De code moet tussen 3 en 50 tekens lang zijn"

#: src/components/forms/ProductForm/index.tsx:401
msgid "Collapse this product when the event page is initially loaded"
msgstr "Dit product samenvouwen wanneer de evenementpagina voor het eerst wordt geladen"

#~ msgid "Collapse this ticket when the event page is initially loaded"
#~ msgstr "Dit ticket samenvouwen wanneer de evenementpagina voor het eerst wordt geladen"

#: src/components/routes/event/HomepageDesigner/index.tsx:131
msgid "Color"
msgstr "Kleur"

#: src/components/common/WidgetEditor/index.tsx:21
msgid "Color must be a valid hex color code. Example: #ffffff"
msgstr "De kleur moet een geldige hex-kleurcode zijn. Voorbeeld: #ffffff"

#: src/components/common/WidgetEditor/index.tsx:166
#: src/components/routes/event/HomepageDesigner/index.tsx:124
msgid "Colors"
msgstr "Kleuren"

#: src/components/layouts/Event/index.tsx:158
msgid "Coming Soon"
msgstr "Binnenkort beschikbaar"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
msgid "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"
msgstr "Door komma's gescheiden trefwoorden die het evenement beschrijven. Deze worden door zoekmachines gebruikt om het evenement te categoriseren en indexeren"

#: src/components/routes/product-widget/CollectInformation/index.tsx:453
msgid "Complete Order"
msgstr "Volledige bestelling"

#: src/components/routes/product-widget/CollectInformation/index.tsx:223
msgid "Complete payment"
msgstr "Volledige betaling"

#: src/components/routes/product-widget/Payment/index.tsx:131
msgid "Complete Payment"
msgstr "Volledige betaling"

#: src/components/layouts/AuthLayout/index.tsx:68
#~ msgid "Complete Store"
#~ msgstr "Volledige winkel"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:202
#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Complete Stripe Setup"
msgstr "Volledige Stripe-instelling"

#: src/components/routes/event/EventDashboard/index.tsx:127
msgid "Complete these steps to start selling tickets for your event."
msgstr "Voer deze stappen uit om tickets voor je evenement te gaan verkopen."

#: src/components/modals/SendMessageModal/index.tsx:230
#: src/components/routes/event/GettingStarted/index.tsx:70
#: src/components/routes/event/orders.tsx:23
msgid "Completed"
msgstr "Voltooid"

#: src/components/common/StatBoxes/index.tsx:51
msgid "Completed orders"
msgstr "Afgeronde bestellingen"

#: src/components/routes/event/EventDashboard/index.tsx:237
msgid "Completed Orders"
msgstr "Afgeronde bestellingen"

#: src/components/common/WidgetEditor/index.tsx:287
msgid "Component Code"
msgstr "Onderdeel Code"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:35
msgid "Configured Discount"
msgstr "Geconfigureerde korting"

#: src/utilites/confirmationDialog.tsx:11
msgid "Confirm"
msgstr "Bevestig"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:51
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:56
msgid "Confirm Email Change"
msgstr "Bevestig e-mailwijziging"

#: src/components/routes/profile/ManageProfile/index.tsx:199
msgid "Confirm New Password"
msgstr "Nieuw wachtwoord bevestigen"

#: src/components/routes/auth/Register/index.tsx:115
msgid "Confirm password"
msgstr "Wachtwoord bevestigen"

#: src/components/routes/auth/AcceptInvitation/index.tsx:112
#: src/components/routes/auth/Register/index.tsx:114
#: src/components/routes/auth/ResetPassword/index.tsx:60
msgid "Confirm Password"
msgstr "Wachtwoord bevestigen"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:36
msgid "Confirming email address..."
msgstr "E-mailadres bevestigen..."

#~ msgid "Congratulation on creating an event!"
#~ msgstr "Gefeliciteerd met het maken van een evenement!"

#: src/components/routes/event/GettingStarted/index.tsx:88
msgid "Congratulations on creating an event!"
msgstr "Gefeliciteerd met het aanmaken van een evenement!"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:173
msgid "Connect Documentation"
msgstr "Documentatie aansluiten"

#: src/components/routes/event/EventDashboard/index.tsx:192
msgid "Connect payment processing"
msgstr "Betalingsverwerking aansluiten"

#~ msgid "Connect Stripe"
#~ msgstr "Streep aansluiten"

#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Connect to Stripe"
msgstr "Verbinding maken met Stripe"

#: src/components/layouts/AuthLayout/index.tsx:89
msgid "Connect with CRM and automate tasks using webhooks and integrations"
msgstr "Verbinding maken met CRM en taken automatiseren met webhooks en integraties"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:201
#: src/components/routes/event/GettingStarted/index.tsx:154
msgid "Connect with Stripe"
msgstr "Maak verbinding met Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:150
msgid "Connect your Stripe account to start receiving payments."
msgstr "Maak verbinding met je Stripe-account om betalingen te ontvangen."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:148
msgid "Connected to Stripe"
msgstr "Verbonden met Stripe"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:98
msgid "Connection Details"
msgstr "Details verbinding"

#~ msgid "Contact email"
#~ msgstr "Contact e-mail"

#: src/components/modals/SendMessageModal/index.tsx:167
msgid "Contact Support"
msgstr "Contact Ondersteuning"

#: src/components/modals/SendMessageModal/index.tsx:158
msgid "Contact us to enable messaging"
msgstr "Neem contact met ons op om berichtgeving in te schakelen"

#: src/components/routes/event/HomepageDesigner/index.tsx:155
msgid "Content background color"
msgstr "Achtergrondkleur inhoud"

#: src/components/common/WidgetEditor/index.tsx:31
#: src/components/common/WidgetEditor/index.tsx:217
#: src/components/layouts/Checkout/CheckoutFooter/index.tsx:38
#: src/components/routes/product-widget/CollectInformation/index.tsx:444
#: src/components/routes/product-widget/SelectProducts/index.tsx:486
msgid "Continue"
msgstr "Ga verder"

#: src/components/routes/event/HomepageDesigner/index.tsx:164
msgid "Continue button text"
msgstr "Doorgaan knop tekst"

#: src/components/common/WidgetEditor/index.tsx:216
msgid "Continue Button Text"
msgstr "Doorgaan Knoptekst"

#: src/components/modals/CreateEventModal/index.tsx:153
msgid "Continue Event Setup"
msgstr "Doorgaan met evenementinstellingen"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Continue set up"
msgstr "Doorgaan met instellen"

#~ msgid "Continue Stripe Connect Setup"
#~ msgstr "Doorgaan met Stripe Connect Setup"

#: src/components/routes/product-widget/SelectProducts/index.tsx:337
msgid "Continue to Checkout"
msgstr "Verder naar afrekenen"

#~ msgid "Continue To Payment"
#~ msgstr "Doorgaan naar betaling"

#: src/components/common/AttendeeTicket/index.tsx:91
#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:243
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copied"
msgstr "Gekopieerd"

#: src/components/common/PromoCodeTable/index.tsx:106
msgid "copied to clipboard"
msgstr "gekopieerd naar klembord"

#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copy"
msgstr "Kopie"

#: src/components/common/CheckInListList/index.tsx:156
msgid "Copy Check-In URL"
msgstr "Check-in URL kopiëren"

#: src/components/routes/product-widget/CollectInformation/index.tsx:306
msgid "Copy details to all attendees"
msgstr "Kopieer de details naar alle aanwezigen"

#: src/components/common/AttendeeTicket/index.tsx:91
msgid "Copy Link"
msgstr "Link kopiëren"

#: src/components/common/PromoCodeTable/index.tsx:177
#: src/components/modals/ShareModal/index.tsx:243
msgid "Copy URL"
msgstr "URL kopiëren"

#: src/components/common/CheckoutQuestion/index.tsx:174
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:152
#: src/components/routes/product-widget/CollectInformation/index.tsx:354
msgid "Country"
msgstr "Land"

#: src/components/routes/event/HomepageDesigner/index.tsx:117
msgid "Cover"
msgstr "Omslag"

#~ msgid "Cover Image"
#~ msgstr "Afbeelding omslag"

#: src/components/routes/event/attendees.tsx:71
#: src/components/routes/event/products.tsx:74
msgid "Create"
msgstr "Maak"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
msgid "Create {0}"
msgstr "Maak {0}"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:59
msgid "Create a Product"
msgstr "Een product maken"

#: src/components/common/PromoCodeTable/index.tsx:57
msgid "Create a Promo Code"
msgstr "Maak een Promo Code"

#: src/components/modals/CreateCheckInListModal/index.tsx:63
msgid "Create a Ticket"
msgstr "Een ticket maken"

#: src/components/routes/auth/Register/index.tsx:69
msgid "Create an account or <0>{0}</0> to get started"
msgstr "Maak een account aan of <0>{0}</0> om te beginnen"

#: src/components/modals/CreateEventModal/index.tsx:120
msgid "create an organizer"
msgstr "een organisator maken"

#: src/components/layouts/AuthLayout/index.tsx:27
msgid "Create and customize your event page instantly"
msgstr "Maak en pas je evenementpagina direct aan"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Create Attendee"
msgstr "Aanwezige maken"

#: src/components/common/CapacityAssignmentList/index.tsx:63
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:68
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:79
#: src/components/routes/event/CapacityAssignments/index.tsx:46
msgid "Create Capacity Assignment"
msgstr "Capaciteitstoewijzing maken"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:128
#: src/components/common/ProductsTable/SortableCategory/index.tsx:129
msgid "Create category"
msgstr "Categorie maken"

#: src/components/modals/CreateProductCategoryModal/index.tsx:45
#: src/components/modals/CreateProductCategoryModal/index.tsx:60
msgid "Create Category"
msgstr "Categorie maken"

#: src/components/common/CheckInListList/index.tsx:61
#: src/components/modals/CreateCheckInListModal/index.tsx:72
#: src/components/modals/CreateCheckInListModal/index.tsx:82
#: src/components/routes/event/CheckInLists/index.tsx:46
msgid "Create Check-In List"
msgstr "Check-in lijst maken"

#: src/components/common/NoEventsBlankSlate/index.tsx:27
#: src/components/modals/CreateEventModal/index.tsx:85
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:81
#: src/components/routes/welcome/index.tsx:108
msgid "Create Event"
msgstr "Evenement creëren"

#: src/components/routes/events/Dashboard/index.tsx:91
msgid "Create new"
msgstr "Nieuw maken"

#: src/components/forms/OrganizerForm/index.tsx:111
#: src/components/modals/CreateEventModal/index.tsx:93
#: src/components/modals/CreateOrganizerModal/index.tsx:16
msgid "Create Organizer"
msgstr "Organisator maken"

#: src/components/modals/CreateProductModal/index.tsx:80
#: src/components/modals/CreateProductModal/index.tsx:88
msgid "Create Product"
msgstr "Product maken"

#: src/components/routes/event/GettingStarted/index.tsx:70
#~ msgid "Create products for your event, set prices, and manage available quantity."
#~ msgstr "Maak producten voor je evenement, stel prijzen in en beheer de beschikbare hoeveelheid."

#: src/components/modals/CreatePromoCodeModal/index.tsx:51
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
msgid "Create Promo Code"
msgstr "Promocode maken"

#~ msgid "Create promo codes"
#~ msgstr "Promocodes maken"

#~ msgid "Create promo codes to offer discounts to your attendees."
#~ msgstr "Maak promotiecodes om kortingen aan te bieden aan je deelnemers."

#: src/components/modals/CreateQuestionModal/index.tsx:69
#: src/components/modals/CreateQuestionModal/index.tsx:74
msgid "Create Question"
msgstr "Vraag maken"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:41
msgid "Create Tax or Fee"
msgstr "Creëer belasting of heffing"

#~ msgid "Create Ticket"
#~ msgstr "Ticket maken"

#: src/components/routes/event/GettingStarted/index.tsx:122
msgid "Create tickets for your event, set prices, and manage available quantity."
msgstr "Maak tickets voor je evenement, stel prijzen in en beheer de beschikbare hoeveelheid."

#: src/components/modals/CreateWebhookModal/index.tsx:57
#: src/components/modals/CreateWebhookModal/index.tsx:67
msgid "Create Webhook"
msgstr "Webhook maken"

#: src/components/common/OrdersTable/index.tsx:208
#: src/components/common/OrdersTable/index.tsx:281
msgid "Created"
msgstr "Gemaakt"

#: src/components/forms/OrganizerForm/index.tsx:45
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:73
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:100
msgid "Currency"
msgstr "Valuta"

#: src/components/routes/profile/ManageProfile/index.tsx:191
msgid "Current Password"
msgstr "Huidig wachtwoord"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:159
msgid "Custom Maps URL"
msgstr "Aangepaste kaarten URL"

#: src/components/common/ReportTable/index.tsx:53
msgid "Custom Range"
msgstr "Aangepast bereik"

#: src/components/common/OrdersTable/index.tsx:205
msgid "Customer"
msgstr "Klant"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:55
msgid "Customize the email and notification settings for this event"
msgstr "De e-mail- en meldingsinstellingen voor dit evenement aanpassen"

#~ msgid "Customize the email settings for this event"
#~ msgstr "De e-mailinstellingen voor dit evenement aanpassen"

#~ msgid "Customize the event homepage and checkout experience"
#~ msgstr "De homepage en kassa-ervaring van het evenement aanpassen"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:62
msgid "Customize the event homepage and checkout messaging"
msgstr "De homepage van het evenement en de berichten bij de kassa aanpassen"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:70
msgid "Customize the miscellaneous settings for this event"
msgstr "De diverse instellingen voor dit evenement aanpassen"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:57
msgid "Customize the SEO settings for this event"
msgstr "De SEO-instellingen voor dit evenement aanpassen"

#: src/components/routes/event/GettingStarted/index.tsx:169
msgid "Customize your event page"
msgstr "Je evenementpagina aanpassen"

#: src/components/layouts/AuthLayout/index.tsx:84
msgid "Customize your event page and widget design to match your brand perfectly"
msgstr "Pas het ontwerp van je evenementpagina en widget aan zodat ze perfect bij je merk passen"

#: src/components/routes/event/GettingStarted/index.tsx:165
msgid "Customize your event page to match your brand and style."
msgstr "Pas je evenementpagina aan aan je merk en stijl."

#: src/components/routes/event/Reports/index.tsx:23
msgid "Daily Sales Report"
msgstr "Dagelijks verkooprapport"

#: src/components/routes/event/Reports/index.tsx:24
msgid "Daily sales, tax, and fee breakdown"
msgstr "Dagelijkse uitsplitsing naar verkoop, belasting en kosten"

#: src/components/common/CapacityAssignmentList/index.tsx:156
#: src/components/common/CheckInListList/index.tsx:176
#: src/components/common/OrdersTable/index.tsx:186
#: src/components/common/ProductsTable/SortableProduct/index.tsx:287
#: src/components/common/PromoCodeTable/index.tsx:181
#: src/components/common/QuestionsTable/index.tsx:115
#: src/components/common/WebhookTable/index.tsx:116
msgid "Danger zone"
msgstr "Gevarenzone"

#: src/components/common/AttendeeTable/index.tsx:179
msgid "Danger Zone"
msgstr "Gevarenzone"

#: src/components/layouts/Event/index.tsx:89
msgid "Dashboard"
msgstr "Dashboard"

#: src/components/common/OrderDetails/index.tsx:39
#: src/components/forms/QuestionForm/index.tsx:136
msgid "Date"
msgstr "Datum"

#~ msgid "Date & Time"
#~ msgstr "Datum en tijd"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:38
msgid "Day one capacity"
msgstr "Capaciteit op dag één"

#~ msgid "Day one check-in list"
#~ msgstr "Check-in lijst voor dag één"

#~ msgid "Deactivate user"
#~ msgstr "Gebruiker deactiveren"

#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/TaxAndFeeList/index.tsx:73
msgid "Delete"
msgstr "Verwijder"

#: src/components/common/CapacityAssignmentList/index.tsx:159
msgid "Delete Capacity"
msgstr "Capaciteit verwijderen"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:146
#: src/components/common/ProductsTable/SortableCategory/index.tsx:147
msgid "Delete category"
msgstr "Categorie verwijderen"

#: src/components/common/CheckInListList/index.tsx:179
msgid "Delete Check-In List"
msgstr "Check-in lijst verwijderen"

#: src/components/common/PromoCodeTable/index.tsx:185
msgid "Delete code"
msgstr "Code verwijderen"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:113
msgid "Delete Cover"
msgstr "Omslag verwijderen"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:98
msgid "Delete Image"
msgstr "Afbeelding verwijderen"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:292
msgid "Delete product"
msgstr "Product verwijderen"

#: src/components/common/QuestionsTable/index.tsx:120
msgid "Delete question"
msgstr "Vraag verwijderen"

#~ msgid "Delete ticket"
#~ msgstr "Ticket verwijderen"

#~ msgid "Delete user"
#~ msgstr "Gebruiker verwijderen"

#: src/components/common/WebhookTable/index.tsx:127
msgid "Delete webhook"
msgstr "Webhook verwijderen"

#: src/components/forms/ProductForm/index.tsx:258
#: src/components/forms/TaxAndFeeForm/index.tsx:81
#: src/components/modals/CreateEventModal/index.tsx:135
#: src/components/modals/DuplicateEventModal/index.tsx:84
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:78
msgid "Description"
msgstr "Beschrijving"

#: src/components/forms/CheckInListForm/index.tsx:36
msgid "Description for check-in staff"
msgstr "Beschrijving voor incheckpersoneel"

#~ msgid "Description should be less than 50,000 characters"
#~ msgstr "De beschrijving moet minder dan 50.000 tekens bevatten"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Details"
msgstr "Details"

#~ msgid "Disable code"
#~ msgstr "Code uitschakelen"

#~ msgid "Disable this capacity track capacity without stopping product sales"
#~ msgstr "Schakel deze capaciteitstracker uit zonder de productverkoop te stoppen"

#~ msgid "Disable this capacity track capacity without stopping ticket sales"
#~ msgstr "Schakel deze capaciteit bijhouden uit zonder de kaartverkoop te stoppen"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
msgid "Disabling this capacity will track sales but not stop them when the limit is reached"
msgstr "Als je deze capaciteit uitschakelt, worden de verkopen bijgehouden, maar niet gestopt als de limiet is bereikt"

#: src/components/common/PromoCodeTable/index.tsx:70
msgid "Discount"
msgstr "Korting"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount %"
msgstr "Korting %"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount in {0}"
msgstr "Korting in {0}"

#: src/components/forms/PromoCodeForm/index.tsx:43
msgid "Discount Type"
msgstr "Korting Type"

#: src/components/routes/product-widget/SelectProducts/index.tsx:297
#~ msgid "Dismiss"
#~ msgstr "Afwijzen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:354
msgid "Dismiss this message"
msgstr "Dit bericht negeren"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:190
msgid "Document Label"
msgstr "Documentlabel"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:222
msgid "Documentation"
msgstr "Documentatie"

#~ msgid "Does not exist"
#~ msgstr "Bestaat niet"

#: src/components/routes/auth/Login/index.tsx:57
msgid "Don't have an account?   <0>Sign up</0>"
msgstr "Heb je geen account? <0>Aanmelden</0>"

#~ msgid "Don't have an account?   <0>Sign Up</0>"
#~ msgstr "Heb je nog geen account? <0>Aanmelden</0>"

#: src/components/forms/ProductForm/index.tsx:157
msgid "Donation / Pay what you'd like product"
msgstr "Donatie / Betaal wat je wilt product"

#~ msgid "Donation / Pay what you'd like ticket"
#~ msgstr "Donatie / Betaal wat je wilt ticket"

#~ msgid "Donation amount"
#~ msgstr "Donatiebedrag"

#~ msgid "Donation Ticket"
#~ msgstr "Donatie Ticket"

#: src/components/common/AddEventToCalendarButton/index.tsx:137
msgid "Download .ics"
msgstr "Download .ics"

#: src/components/common/DownloadCsvButton/index.tsx:48
msgid "Download CSV"
msgstr "CSV downloaden"

#: src/components/common/OrdersTable/index.tsx:162
msgid "Download invoice"
msgstr "Factuur downloaden"

#: src/components/layouts/Checkout/index.tsx:126
msgid "Download Invoice"
msgstr "Factuur downloaden"

#: src/components/modals/ShareModal/index.tsx:224
msgid "Download QR Code"
msgstr "QR-code downloaden"

#~ msgid "Download Tickets PDF"
#~ msgstr "Download Tickets PDF"

#: src/components/common/OrdersTable/index.tsx:111
#: src/components/layouts/Checkout/index.tsx:49
msgid "Downloading Invoice"
msgstr "Factuur downloaden"

#: src/components/layouts/Event/index.tsx:188
msgid "Draft"
msgstr "Concept"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:83
msgid "Drag and drop or click"
msgstr "Slepen en neerzetten of klikken"

#~ msgid "Drag to sort"
#~ msgstr "Slepen om te sorteren"

#~ msgid "Drop an image, or click here to replace the Cover Image"
#~ msgstr "Drop een afbeelding of klik hier om de coverafbeelding te vervangen"

#~ msgid "Drop an image, or click here to upload the Cover Image"
#~ msgstr "Drop een afbeelding of klik hier om de Cover Image te uploaden"

#: src/components/forms/QuestionForm/index.tsx:124
msgid "Dropdown selection"
msgstr "Dropdown selectie"

#: src/components/modals/SendMessageModal/index.tsx:159
msgid ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."
msgstr ""
"Vanwege het hoge risico op spam is handmatige verificatie vereist voordat je berichten kunt verzenden.\n"
"Neem contact met ons op om toegang aan te vragen."

#: src/components/modals/DuplicateEventModal/index.tsx:124
msgid "Duplicate Capacity Assignments"
msgstr "Dupliceer capaciteitstoewijzingen"

#: src/components/modals/DuplicateEventModal/index.tsx:128
msgid "Duplicate Check-In Lists"
msgstr "Dupliceer inchecklijsten"

#: src/components/common/EventCard/index.tsx:162
msgid "Duplicate event"
msgstr "Dupliceer evenement"

#: src/components/modals/DuplicateEventModal/index.tsx:69
#: src/components/modals/DuplicateEventModal/index.tsx:142
msgid "Duplicate Event"
msgstr "Dupliceer Evenement"

#: src/components/modals/DuplicateEventModal/index.tsx:132
msgid "Duplicate Event Cover Image"
msgstr "Dupliceer evenement omslag"

#: src/components/modals/DuplicateEventModal/index.tsx:103
msgid "Duplicate Options"
msgstr "Dupliceer opties"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:285
#: src/components/modals/DuplicateProductModal/index.tsx:109
#: src/components/modals/DuplicateProductModal/index.tsx:113
msgid "Duplicate Product"
msgstr "Dupliceer product"

#: src/components/modals/DuplicateEventModal/index.tsx:108
msgid "Duplicate Products"
msgstr "Dupliceer producten"

#: src/components/modals/DuplicateEventModal/index.tsx:120
msgid "Duplicate Promo Codes"
msgstr "Dupliceer Promo Codes"

#: src/components/modals/DuplicateEventModal/index.tsx:112
msgid "Duplicate Questions"
msgstr "Dupliceer vragen"

#: src/components/modals/DuplicateEventModal/index.tsx:116
msgid "Duplicate Settings"
msgstr "Dupliceer instellingen"

#~ msgid "Duplicate Tickets"
#~ msgstr "Dubbele tickets"

#: src/components/modals/DuplicateEventModal/index.tsx:137
msgid "Duplicate Webhooks"
msgstr "Dupliceer Webhooks"

#: src/components/common/LanguageSwitcher/index.tsx:24
msgid "Dutch"
msgstr "Nederlands"

#: src/components/forms/ProductForm/index.tsx:78
msgid "Early bird"
msgstr "Vroege vogel"

#: src/components/common/TaxAndFeeList/index.tsx:65
#: src/components/modals/ManageAttendeeModal/index.tsx:221
#: src/components/modals/ManageOrderModal/index.tsx:203
msgid "Edit"
msgstr "Bewerk"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
msgid "Edit {0}"
msgstr "Bewerk {0}"

#: src/components/common/QuestionAndAnswerList/index.tsx:159
msgid "Edit Answer"
msgstr "Antwoord bewerken"

#~ msgid "Edit attendee"
#~ msgstr "Bewerk deelnemer"

#~ msgid "Edit Attendee"
#~ msgstr "Bewerk deelnemer"

#: src/components/common/CapacityAssignmentList/index.tsx:146
msgid "Edit Capacity"
msgstr "Bewerk capaciteit"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:66
#: src/components/modals/EditCapacityAssignmentModal/index.tsx:74
msgid "Edit Capacity Assignment"
msgstr "Capaciteitstoewijzing bewerken"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:137
#: src/components/common/ProductsTable/SortableCategory/index.tsx:138
msgid "Edit category"
msgstr "Categorie bewerken"

#: src/components/common/CheckInListList/index.tsx:148
#: src/components/modals/EditCheckInListModal/index.tsx:69
#: src/components/modals/EditCheckInListModal/index.tsx:91
msgid "Edit Check-In List"
msgstr "Check-in lijst bewerken"

#: src/components/common/PromoCodeTable/index.tsx:170
msgid "Edit Code"
msgstr "Code bewerken"

#: src/components/modals/EditOrganizerModal/index.tsx:63
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:51
msgid "Edit Organizer"
msgstr "Organisator bewerken"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:280
#: src/components/modals/EditProductModal/index.tsx:100
#: src/components/modals/EditProductModal/index.tsx:108
msgid "Edit Product"
msgstr "Bewerk product"

#: src/components/modals/EditProductCategoryModal/index.tsx:62
#: src/components/modals/EditProductCategoryModal/index.tsx:70
msgid "Edit Product Category"
msgstr "Bewerk productcategorie"

#: src/components/modals/EditPromoCodeModal/index.tsx:79
#: src/components/modals/EditPromoCodeModal/index.tsx:84
msgid "Edit Promo Code"
msgstr "Kortingscode bewerken"

#: src/components/common/QuestionsTable/index.tsx:112
msgid "Edit question"
msgstr "Vraag bewerken"

#: src/components/modals/EditQuestionModal/index.tsx:95
#: src/components/modals/EditQuestionModal/index.tsx:101
msgid "Edit Question"
msgstr "Bewerk Vraag"

#~ msgid "Edit Ticket"
#~ msgstr "Bewerk ticket"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Edit user"
msgstr "Gebruiker bewerken"

#: src/components/modals/EditUserModal/index.tsx:62
#: src/components/modals/EditUserModal/index.tsx:120
msgid "Edit User"
msgstr "Gebruiker bewerken"

#: src/components/common/WebhookTable/index.tsx:104
msgid "Edit webhook"
msgstr "Webhook bewerken"

#: src/components/modals/EditWebhookModal/index.tsx:66
#: src/components/modals/EditWebhookModal/index.tsx:87
msgid "Edit Webhook"
msgstr "Webhook bewerken"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 2.50 for $2.50"
msgstr "bijv. 2,50 voor $2,50"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 23.5 for 23.5%"
msgstr "bijv. 23,5 voor 23,5%"

#: src/components/common/AttendeeDetails/index.tsx:21
#: src/components/common/AttendeeTable/index.tsx:109
#: src/components/common/OrderDetails/index.tsx:31
#: src/components/forms/OrganizerForm/index.tsx:32
#: src/components/modals/EditUserModal/index.tsx:80
#: src/components/modals/InviteUserModal/index.tsx:61
#: src/components/routes/auth/AcceptInvitation/index.tsx:98
#: src/components/routes/auth/Login/index.tsx:68
#: src/components/routes/auth/Register/index.tsx:97
#: src/components/routes/event/Settings/index.tsx:52
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:117
#: src/components/routes/profile/ManageProfile/index.tsx:138
msgid "Email"
msgstr "E-mail"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
msgid "Email & Notification Settings"
msgstr "Instellingen voor e-mail en meldingen"

#: src/components/modals/CreateAttendeeModal/index.tsx:132
#: src/components/modals/ManageAttendeeModal/index.tsx:114
#: src/components/modals/ManageOrderModal/index.tsx:157
msgid "Email address"
msgstr "E-mailadres"

#: src/components/common/QuestionsTable/index.tsx:220
#: src/components/common/QuestionsTable/index.tsx:221
#: src/components/routes/product-widget/CollectInformation/index.tsx:298
#: src/components/routes/product-widget/CollectInformation/index.tsx:299
#: src/components/routes/product-widget/CollectInformation/index.tsx:408
#: src/components/routes/product-widget/CollectInformation/index.tsx:409
msgid "Email Address"
msgstr "E-mailadres"

#: src/components/routes/profile/ManageProfile/index.tsx:77
msgid "Email change cancelled successfully"
msgstr "E-mailwijziging succesvol geannuleerd"

#: src/components/routes/profile/ManageProfile/index.tsx:116
msgid "Email change pending"
msgstr "E-mailwijziging in behandeling"

#~ msgid "Email Configuration"
#~ msgstr "E-mail configuratie"

#: src/components/routes/profile/ManageProfile/index.tsx:151
msgid "Email confirmation resent"
msgstr "E-mailbevestiging opnieuw verzonden"

#: src/components/routes/profile/ManageProfile/index.tsx:90
msgid "Email confirmation resent successfully"
msgstr "Bericht voettekst e-mail"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:66
msgid "Email footer message"
msgstr "Bevestigingsmail succesvol opnieuw verstuurd"

#: src/components/routes/profile/ManageProfile/index.tsx:141
msgid "Email not verified"
msgstr "E-mail niet geverifieerd"

#~ msgid "Email Settings"
#~ msgstr "E-mailinstellingen"

#: src/components/common/WidgetEditor/index.tsx:272
msgid "Embed Code"
msgstr "Code insluiten"

#: src/components/common/WidgetEditor/index.tsx:260
msgid "Embed Script"
msgstr "Script insluiten"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:181
msgid "Enable Invoicing"
msgstr "Facturering inschakelen"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:21
msgid "Enable this capacity to stop product sales when the limit is reached"
msgstr "Schakel deze capaciteit in om de verkoop van producten te stoppen als de limiet is bereikt"

#~ msgid "Enable this capacity to stop ticket sales when the limit is reached"
#~ msgstr "Schakel deze capaciteit in om de ticketverkoop te stoppen als de limiet is bereikt"

#: src/components/forms/WebhookForm/index.tsx:19
msgid "Enabled"
msgstr "Ingeschakeld"

#: src/components/modals/CreateEventModal/index.tsx:149
#: src/components/modals/DuplicateEventModal/index.tsx:98
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:92
#: src/components/routes/welcome/index.tsx:96
msgid "End Date"
msgstr "Einddatum"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:26
#: src/components/common/EventStatusBadge/index.tsx:14
msgid "Ended"
msgstr "Beëindigd"

#: src/components/routes/events/Dashboard/index.tsx:57
msgid "Ended Events"
msgstr "Beëindigde evenementen"

#: src/components/common/LanguageSwitcher/index.tsx:16
#: src/components/common/LanguageSwitcher/index.tsx:50
#: src/components/modals/CreateAttendeeModal/index.tsx:145
#: src/components/routes/profile/ManageProfile/index.tsx:173
msgid "English"
msgstr "Engels"

#: src/components/modals/CreateAttendeeModal/index.tsx:170
msgid "Enter an amount excluding taxes and fees."
msgstr "Voer een bedrag in exclusief belastingen en toeslagen."

#: src/components/common/OrdersTable/index.tsx:119
#: src/components/layouts/Checkout/index.tsx:57
msgid "Error"
msgstr "Fout"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:27
msgid "Error confirming email address"
msgstr "Fout bij bevestigen e-mailadres"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:35
msgid "Error confirming email change"
msgstr "Fout bij het bevestigen van een e-mailwijziging"

#: src/components/modals/WebhookLogsModal/index.tsx:166
msgid "Error loading logs"
msgstr "Fout bij het laden van logboeken"

#: src/components/forms/OrganizerForm/index.tsx:46
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:74
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:101
msgid "EUR"
msgstr "EUR"

#: src/components/routes/events/Dashboard/index.tsx:103
msgid "Event"
msgstr "Evenement"

#~ msgid "Event created successfully"
#~ msgstr "Gebeurtenis succesvol aangemaakt"

#: src/components/modals/CreateEventModal/index.tsx:76
#~ msgid "Event created successfully 🎉"
#~ msgstr "Evenement succesvol aangemaakt 🎉"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:161
msgid "Event Date"
msgstr "Evenement Datum"

#: src/components/routes/account/ManageAccount/sections/EventDefaultsSettings/index.tsx:5
msgid "Event Defaults"
msgstr "Evenement Standaarden"

#: src/components/routes/event/Settings/index.tsx:28
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:276
msgid "Event Details"
msgstr "Evenement Details"

#: src/components/modals/DuplicateEventModal/index.tsx:58
msgid "Event duplicated successfully"
msgstr "Evenement succesvol gedupliceerd"

#: src/components/layouts/Checkout/index.tsx:79
msgid "Event Homepage"
msgstr "Homepage evenement"

#: src/components/layouts/Event/index.tsx:166
#~ msgid "Event is not visible to the public"
#~ msgstr "Evenement is niet zichtbaar voor het publiek"

#: src/components/layouts/Event/index.tsx:165
#~ msgid "Event is visible to the public"
#~ msgstr "Evenement is zichtbaar voor het publiek"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:85
msgid "Event location & venue details"
msgstr "Locatie en details evenement"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:12
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
msgid "Event Not Available"
msgstr "Evenement niet beschikbaar"

#: src/components/layouts/Event/index.tsx:187
#~ msgid "Event page"
#~ msgstr "Evenement pagina"

#: src/components/layouts/Event/index.tsx:212
msgid "Event Page"
msgstr "Evenementpagina"

#~ msgid "Event Settings"
#~ msgstr "Evenement Instellingen"

#: src/components/common/EventCard/index.tsx:70
#: src/components/layouts/Event/index.tsx:80
#: src/components/routes/event/EventDashboard/index.tsx:76
#: src/components/routes/event/GettingStarted/index.tsx:63
msgid "Event status update failed. Please try again later"
msgstr "Update status evenement mislukt. Probeer het later opnieuw"

#: src/components/common/EventCard/index.tsx:67
#: src/components/layouts/Event/index.tsx:77
#: src/components/routes/event/EventDashboard/index.tsx:73
#: src/components/routes/event/GettingStarted/index.tsx:60
msgid "Event status updated"
msgstr "Evenementstatus bijgewerkt"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Event Ticketing by"
msgstr "Evenementtickets door"

#: src/components/common/WebhookTable/index.tsx:237
#: src/components/forms/WebhookForm/index.tsx:122
msgid "Event Types"
msgstr "Soorten evenementen"

#: src/components/modals/ShareModal/index.tsx:237
msgid "Event URL"
msgstr "URL evenement"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
#~ msgid "Event wdwdeNot Available"
#~ msgstr ""

#: src/components/layouts/Event/index.tsx:226
msgid "Events"
msgstr "Evenementen"

#: src/components/forms/CheckInListForm/index.tsx:56
msgid "Expiration date"
msgstr "Vervaldatum"

#: src/components/common/PromoCodeTable/index.tsx:73
msgid "Expires"
msgstr "Verloopt op"

#: src/components/forms/PromoCodeForm/index.tsx:79
msgid "Expiry Date"
msgstr "Vervaldatum"

#: src/components/routes/event/attendees.tsx:80
#: src/components/routes/event/orders.tsx:140
msgid "Export"
msgstr "Exporteer"

#: src/components/common/QuestionsTable/index.tsx:267
msgid "Export answers"
msgstr "Antwoorden exporteren"

#: src/mutations/useExportAnswers.ts:39
msgid "Export failed. Please try again."
msgstr "Exporteren mislukt. Probeer het opnieuw."

#: src/mutations/useExportAnswers.ts:17
msgid "Export started. Preparing file..."
msgstr "Export gestart. Bestand wordt voorbereid..."

#: src/components/routes/event/attendees.tsx:39
msgid "Exporting Attendees"
msgstr "Deelnemers exporteren"

#: src/mutations/useExportAnswers.ts:33
msgid "Exporting complete. Downloading file..."
msgstr "Exporteren voltooid. Bestand wordt gedownload..."

#: src/components/routes/event/orders.tsx:90
msgid "Exporting Orders"
msgstr "Orders exporteren"

#: src/components/common/AttendeeTable/index.tsx:97
msgid "Failed to cancel attendee"
msgstr "Deelnemer niet geannuleerd"

#: src/components/modals/CancelOrderModal/index.tsx:33
msgid "Failed to cancel order"
msgstr "Bestelling niet geannuleerd"

#: src/components/common/QuestionsTable/index.tsx:175
msgid "Failed to delete message. Please try again."
msgstr "Bericht niet verwijderd. Probeer het opnieuw."

#: src/components/common/OrdersTable/index.tsx:120
#: src/components/layouts/Checkout/index.tsx:58
msgid "Failed to download invoice. Please try again."
msgstr "Downloaden van factuur mislukt. Probeer het opnieuw."

#: src/components/routes/event/attendees.tsx:48
msgid "Failed to export attendees"
msgstr "Geen deelnemers geëxporteerd"

#~ msgid "Failed to export attendees. Please try again."
#~ msgstr "Het exporteren van deelnemers is mislukt. Probeer het opnieuw."

#: src/components/routes/event/orders.tsx:99
msgid "Failed to export orders"
msgstr "Geen orders kunnen exporteren"

#~ msgid "Failed to export orders. Please try again."
#~ msgstr "Het is niet gelukt om orders te exporteren. Probeer het opnieuw."

#: src/components/modals/EditCheckInListModal/index.tsx:79
msgid "Failed to load Check-In List"
msgstr "Inchecklijst niet geladen"

#: src/components/modals/EditWebhookModal/index.tsx:75
msgid "Failed to load Webhook"
msgstr "Webhook niet geladen"

#~ msgid "Failed to resend product email"
#~ msgstr "Product e-mail niet opnieuw verzonden"

#: src/components/common/AttendeeTable/index.tsx:50
msgid "Failed to resend ticket email"
msgstr "Niet gelukt om ticket e-mail opnieuw te versturen"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:179
msgid "Failed to sort products"
msgstr "Sorteren van producten mislukt"

#: src/mutations/useExportAnswers.ts:15
msgid "Failed to start export job"
msgstr "Exporttaak niet gestart"

#: src/components/common/QuestionAndAnswerList/index.tsx:102
msgid "Failed to update answer."
msgstr "Antwoord niet bijgewerkt."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:64
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:69
msgid "Failed to upload image."
msgstr "Afbeelding uploaden mislukt."

#: src/components/forms/TaxAndFeeForm/index.tsx:18
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Fee"
msgstr "Tarief"

#~ msgid "Feedback"
#~ msgstr "Feedback"

#: src/components/forms/ProductForm/index.tsx:366
msgid "Fees"
msgstr "Tarieven"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:80
msgid "Fees are subject to change. You will be notified of any changes to your fee structure."
msgstr "Tarieven kunnen worden gewijzigd. Je wordt op de hoogte gebracht van wijzigingen in je tarievenstructuur."

#: src/components/routes/event/orders.tsx:121
msgid "Filter Orders"
msgstr "Bestellingen filteren"

#: src/components/common/FilterModal/index.tsx:75
#: src/components/common/FilterModal/index.tsx:223
msgid "Filters"
msgstr "Filters"

#: src/components/common/FilterModal/index.tsx:223
msgid "Filters ({activeFilterCount})"
msgstr "Filters ({activeFilterCount})"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:206
msgid "First Invoice Number"
msgstr "Eerste factuurnummer"

#: src/components/common/QuestionsTable/index.tsx:208
#: src/components/modals/CreateAttendeeModal/index.tsx:118
#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:144
#: src/components/routes/product-widget/CollectInformation/index.tsx:284
#: src/components/routes/product-widget/CollectInformation/index.tsx:395
msgid "First name"
msgstr "Voornaam"

#: src/components/common/QuestionsTable/index.tsx:207
#: src/components/modals/EditUserModal/index.tsx:71
#: src/components/modals/InviteUserModal/index.tsx:57
#: src/components/routes/auth/AcceptInvitation/index.tsx:92
#: src/components/routes/auth/Register/index.tsx:83
#: src/components/routes/product-widget/CollectInformation/index.tsx:283
#: src/components/routes/product-widget/CollectInformation/index.tsx:394
#: src/components/routes/profile/ManageProfile/index.tsx:135
msgid "First Name"
msgstr "Voornaam"

#: src/components/routes/auth/AcceptInvitation/index.tsx:29
msgid "First name must be between 1 and 50 characters"
msgstr "De voornaam moet tussen 1 en 50 tekens zijn"

#: src/components/common/QuestionsTable/index.tsx:349
msgid "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."
msgstr "Voornaam, Achternaam en E-mailadres zijn standaardvragen en worden altijd opgenomen in het afrekenproces."

#~ msgid "First Name, Last Name, and Email Address are default questions that are always included in the checkout process."
#~ msgstr "Voornaam, Achternaam en E-mailadres zijn standaardvragen die altijd worden opgenomen in het afrekenproces."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
msgid "First Used"
msgstr "Voor het eerst gebruikt"

#: src/components/forms/TaxAndFeeForm/index.tsx:33
msgid "Fixed"
msgstr "Vast"

#: src/components/forms/PromoCodeForm/index.tsx:55
msgid "Fixed amount"
msgstr "Vast bedrag"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:65
msgid "Fixed Fee:"
msgstr "Vast tarief:"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:123
msgid "Flash is not available on this device"
msgstr "Flash is niet beschikbaar op dit apparaat"

#: src/components/layouts/AuthLayout/index.tsx:58
msgid "Flexible Ticketing"
msgstr "Flexibele ticketing"

#: src/components/routes/auth/Login/index.tsx:80
msgid "Forgot password?"
msgstr "Wachtwoord vergeten?"

#: src/components/common/AttendeeTicket/index.tsx:40
#: src/components/common/Currency/index.tsx:90
#: src/components/common/OrderSummary/index.tsx:46
#: src/components/common/OrderSummary/index.tsx:99
#: src/components/common/ProductsTable/SortableProduct/index.tsx:93
#: src/components/common/ProductsTable/SortableProduct/index.tsx:103
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:53
msgid "Free"
msgstr "Gratis"

#: src/components/forms/ProductForm/index.tsx:151
msgid "Free Product"
msgstr "Gratis product"

#: src/components/forms/ProductForm/index.tsx:153
msgid "Free product, no payment information required"
msgstr "Gratis product, geen betalingsgegevens nodig"

#~ msgid "Free Ticket"
#~ msgstr "Gratis ticket"

#~ msgid "Free ticket, no payment information required"
#~ msgstr "Gratis ticket, geen betalingsgegevens nodig"

#: src/components/common/LanguageSwitcher/index.tsx:20
msgid "French"
msgstr "Frans"

#~ msgid "From"
#~ msgstr "Van"

#: src/components/layouts/AuthLayout/index.tsx:88
msgid "Fully Integrated"
msgstr "Volledig geïntegreerd"

#: src/components/forms/ProductForm/index.tsx:136
msgid "General"
msgstr "Algemeen"

#: src/components/common/LanguageSwitcher/index.tsx:14
msgid "German"
msgstr "Duits"

#: src/components/layouts/AuthLayout/index.tsx:35
msgid "Get started for free, no subscription fees"
msgstr "Gratis aan de slag, geen abonnementskosten"

#~ msgid "Get Tickets"
#~ msgstr "Koop tickets"

#: src/components/routes/event/EventDashboard/index.tsx:125
msgid "Get your event ready"
msgstr "Bereid je evenement voor"

#: src/components/layouts/Event/index.tsx:88
msgid "Getting Started"
msgstr "Aan de slag"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:23
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:42
msgid "Go back to profile"
msgstr "Ga terug naar profiel"

#~ msgid "Go to Dashboard"
#~ msgstr "Ga naar het dashboard"

#: src/components/routes/product-widget/CollectInformation/index.tsx:239
msgid "Go to event homepage"
msgstr "Ga naar de homepage van het evenement"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:53
msgid "Go to Hi.Events"
msgstr "Ga naar Hi.Events"

#: src/components/common/ErrorDisplay/index.tsx:64
msgid "Go to home page"
msgstr "Ga naar de startpagina"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:162
msgid "Go to Stripe Dashboard"
msgstr "Ga naar het Stripe Dashboard"

#: src/components/common/AddEventToCalendarButton/index.tsx:128
msgid "Google Calendar"
msgstr "Google Agenda"

#: src/components/common/EventCard/index.tsx:133
msgid "gross sales"
msgstr "brutoverkoop"

#: src/components/common/StatBoxes/index.tsx:39
msgid "Gross sales"
msgstr "Brutoverkoop"

#: src/components/routes/event/EventDashboard/index.tsx:274
msgid "Gross Sales"
msgstr "Brutoverkoop"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:281
msgid "Guests"
msgstr "Gasten"

#: src/components/routes/product-widget/SelectProducts/index.tsx:495
msgid "Have a promo code?"
msgstr "Heb je een promotiecode?"

#: src/components/forms/OrganizerForm/index.tsx:33
msgid "<EMAIL>"
msgstr "<EMAIL>"

#~ msgid "Help & Support"
#~ msgstr "Hulp en ondersteuning"

#: src/components/common/WidgetEditor/index.tsx:295
msgid "Here is an example of how you can use the component in your application."
msgstr "Hier is een voorbeeld van hoe je de component in je toepassing kunt gebruiken."

#: src/components/common/WidgetEditor/index.tsx:284
msgid "Here is the React component you can use to embed the widget in your application."
msgstr "Hier is het React-component dat je kunt gebruiken om de widget in te sluiten in je applicatie."

#: src/components/routes/event/EventDashboard/index.tsx:101
msgid "Hi {0} 👋"
msgstr "Hi {0} 👋"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:43
msgid "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."
msgstr "Hi.Events brengt platformkosten in rekening om onze diensten te onderhouden en te verbeteren. Deze kosten worden automatisch van elke transactie afgetrokken."

#: src/components/modals/CreateEventModal/index.tsx:130
#: src/components/modals/DuplicateEventModal/index.tsx:79
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:73
msgid "Hi.Events Conference {0}"
msgstr "Hi.Events Conferentie {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:117
msgid "Hi.Events Conference Center"
msgstr "Hi.Events Conferentiecentrum"

#: src/components/layouts/AuthLayout/index.tsx:131
msgid "hi.events logo"
msgstr "hi.events logo"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:79
msgid "Hidden from public view"
msgstr "Verborgen voor het publiek"

#: src/components/common/QuestionsTable/index.tsx:290
msgid "hidden question"
msgstr "verborgen vraag"

#: src/components/common/QuestionsTable/index.tsx:291
msgid "hidden questions"
msgstr "verborgen vragen"

#: src/components/forms/QuestionForm/index.tsx:218
msgid "Hidden questions are only visible to the event organizer and not to the customer."
msgstr "Verborgen vragen zijn alleen zichtbaar voor de organisator van het evenement en niet voor de klant."

#~ msgid "Hidden will not be shown to customers."
#~ msgstr "Verborgen informatie wordt niet aan klanten getoond."

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/routes/product-widget/SelectProducts/index.tsx:460
msgid "Hide"
msgstr "Verberg"

#: src/components/common/AttendeeList/index.tsx:84
msgid "Hide Answers"
msgstr "Antwoorden verbergen"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:89
msgid "Hide getting started page"
msgstr "Startpagina verbergen"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Hide hidden questions"
msgstr "Verborgen vragen verbergen"

#: src/components/forms/ProductForm/index.tsx:399
msgid "Hide product after sale end date"
msgstr "Verberg product na einddatum verkoop"

#: src/components/forms/ProductForm/index.tsx:397
msgid "Hide product before sale start date"
msgstr "Verberg product voor start verkoopdatum"

#: src/components/forms/ProductForm/index.tsx:412
msgid "Hide product unless user has applicable promo code"
msgstr "Verberg product tenzij gebruiker toepasselijke promotiecode heeft"

#: src/components/forms/ProductForm/index.tsx:405
msgid "Hide product when sold out"
msgstr "Verberg product als het uitverkocht is"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:90
msgid "Hide the getting started page from the sidebar"
msgstr "Verberg de pagina Aan de slag uit de zijbalk"

#: src/components/forms/ProductForm/index.tsx:417
msgid "Hide this product from customers"
msgstr "Verberg dit product voor klanten"

#: src/components/forms/QuestionForm/index.tsx:219
msgid "Hide this question"
msgstr "Verberg deze vraag"

#~ msgid "Hide this ticket from customers"
#~ msgstr "Verberg dit ticket voor klanten"

#: src/components/forms/ProductForm/index.tsx:103
msgid "Hide this tier from users"
msgstr "Verberg dit niveau voor gebruikers"

#~ msgid "Hide ticket after sale end date"
#~ msgstr "Verberg ticket na einddatum verkoop"

#~ msgid "Hide ticket before sale start date"
#~ msgstr "Verberg ticket vóór de startdatum van de verkoop"

#~ msgid "Hide ticket unless user has applicable promo code"
#~ msgstr "Verberg ticket tenzij gebruiker toepasselijke promotiecode heeft"

#~ msgid "Hide ticket when sold out"
#~ msgstr "Verberg ticket wanneer uitverkocht"

#: src/components/forms/ProductForm/index.tsx:101
msgid "Hiding a product will prevent users from seeing it on the event page."
msgstr "Door een product te verbergen, kunnen gebruikers het niet zien op de evenementpagina."

#~ msgid "Hiding a ticket will prevent users from seeing it on the event page."
#~ msgstr "Door een ticket te verbergen, kunnen gebruikers het niet zien op de evenementpagina."

#~ msgid "Homepage & Checkout"
#~ msgstr "Homepage & afrekenen"

#~ msgid "Homepage & Checkout Settings"
#~ msgstr "Instellingen Homepage & Afrekenen"

#: src/components/routes/event/HomepageDesigner/index.tsx:115
msgid "Homepage Design"
msgstr "Homepage-ontwerp"

#: src/components/layouts/Event/index.tsx:109
msgid "Homepage Designer"
msgstr "Homepage Ontwerper"

#: src/components/routes/event/HomepageDesigner/index.tsx:174
msgid "Homepage Preview"
msgstr "Voorbeschouwing"

#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:145
msgid "Homer"
msgstr "Homer"

#~ msgid "How many minutes the customer has to complete their order"
#~ msgstr "Hoeveel minuten de klant heeft om zijn bestelling af te ronden"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
msgid "How many minutes the customer has to complete their order. We recommend at least 15 minutes"
msgstr "Hoeveel minuten de klant heeft om zijn bestelling af te ronden. We raden minimaal 15 minuten aan"

#: src/components/forms/PromoCodeForm/index.tsx:84
msgid "How many times can this code be used?"
msgstr "Hoe vaak kan deze code worden gebruikt?"

#: src/components/common/Editor/index.tsx:75
msgid "HTML character limit exceeded: {htmlLength}/{maxLength}"
msgstr "HTML karakterlimiet overschreden: {htmlLength}/{maxLength}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:160
msgid "https://example-maps-service.com/..."
msgstr "https://voorbeeld-maps-service.com/..."

#: src/components/forms/WebhookForm/index.tsx:118
msgid "https://webhook-domain.com/webhook"
msgstr "https://webhook-domain.com/webhook"

#: src/components/routes/auth/AcceptInvitation/index.tsx:118
msgid "I agree to the <0>terms and conditions</0>"
msgstr "Ik ga akkoord met de <0>voorwaarden</0>"

#~ msgid "I agree to the terms and conditions"
#~ msgstr "Ik ga akkoord met de algemene voorwaarden"

#: src/components/routes/product-widget/Payment/index.tsx:106
msgid "I would like to pay using an offline method"
msgstr "Ik wil graag betalen via een offline methode"

#: src/components/routes/product-widget/Payment/index.tsx:107
msgid "I would like to pay using an online method (credit card etc.)"
msgstr "Ik wil graag online betalen (creditcard etc.)"

#: src/components/routes/product-widget/SelectProducts/index.tsx:315
msgid "If a new tab did not open automatically, please click the button below to continue to checkout."
msgstr "Als er geen nieuw tabblad automatisch is geopend, klik dan op de knop hieronder om door te gaan naar afrekenen."

#~ msgid "If a new tab did not open, please  <0>{0}.</0>"
#~ msgstr "Als er geen nieuw tabblad is geopend, <0>{0}.</0>"

#: src/components/routes/product-widget/SelectProducts/index.tsx:284
#~ msgid "If a new tab did not open, please  <0><1>{0}</1>.</0>"
#~ msgstr "Als er geen nieuw tabblad is geopend, <0><1>{0}</1>.</0>"

#~ msgid "If blank, the address will be used to generate a Google map link"
#~ msgstr "Als dit leeg is, wordt het adres gebruikt om een Google-kaartlink te genereren."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:158
msgid "If blank, the address will be used to generate a Google Mapa link"
msgstr "Als dit veld leeg is, wordt het adres gebruikt om een Google Mapa-link te genereren"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:158
msgid "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."
msgstr "Als dit is ingeschakeld, kunnen incheckmedewerkers aanwezigen markeren als ingecheckt of de bestelling als betaald markeren en de aanwezigen inchecken. Als deze optie is uitgeschakeld, kunnen bezoekers van onbetaalde bestellingen niet worden ingecheckt."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:76
msgid "If enabled, the organizer will receive an email notification when a new order is placed"
msgstr "Als deze optie is ingeschakeld, ontvangt de organisator een e-mailbericht wanneer er een nieuwe bestelling is geplaatst"

#: src/components/routes/profile/ManageProfile/index.tsx:122
msgid "If you did not request this change, please immediately change your password."
msgstr "Als je deze wijziging niet hebt aangevraagd, verander dan onmiddellijk je wachtwoord."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
msgid ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."
msgstr "Als je een account bij ons hebt, ontvang je een e-mail met instructies om je wachtwoord opnieuw in te stellen."

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:108
msgid "Image deleted successfully"
msgstr "Afbeelding succesvol verwijderd"

#~ msgid "Image dimensions must be at least 600px by 300px"
#~ msgstr "Afbeeldingsafmetingen moeten minimaal 600px bij 300px zijn"

#~ msgid "Image dimensions must be at least 900px by 450px"
#~ msgstr "Afbeeldingsafmetingen moeten minimaal 900px bij 450px zijn"

#~ msgid "Image dimensions must be between 3000px by 2000px. With a max height of 2000px and max width of 3000px"
#~ msgstr "Afbeeldingsafmetingen moeten tussen 3000px en 2000px zijn. Met een maximale hoogte van 2000px en een maximale breedte van 3000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
msgid "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"
msgstr "Afbeeldingsafmetingen moeten tussen 4000px bij 4000px zijn. Met een maximale hoogte van 4000px en een maximale breedte van 4000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:49
msgid "Image must be less than 5MB"
msgstr "Afbeelding moet minder dan 5MB zijn"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:63
msgid "Image uploaded successfully"
msgstr "Afbeelding succesvol geüpload"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:111
msgid "Image URL"
msgstr "Afbeelding URL"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
msgid "Image width must be at least 900px and height at least 50px"
msgstr "De afbeeldingsbreedte moet minimaal 900px zijn en de hoogte minimaal 50px"

#: src/components/layouts/AuthLayout/index.tsx:53
msgid "In-depth Analytics"
msgstr "Diepgaande analyses"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:25
#: src/components/modals/EditUserModal/index.tsx:111
msgid "Inactive"
msgstr "Inactief"

#: src/components/modals/EditUserModal/index.tsx:107
msgid "Inactive users cannot log in."
msgstr "Inactieve gebruikers kunnen niet inloggen."

#~ msgid "Include connection details for your online event"
#~ msgstr "Verbindingsgegevens voor je online evenement opnemen"

#~ msgid "Include connection details for your online event. These details will be after successful registration."
#~ msgstr "Vermeld de verbindingsgegevens voor je online evenement. Deze gegevens krijg je na een succesvolle registratie."

#~ msgid "Include connection details for your online event. These details will be shown after successful registration."
#~ msgstr "Voeg verbindingsgegevens voor je online evenement toe. Deze gegevens worden getoond na een succesvolle registratie."

#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee product page"
#~ msgstr "Neem verbindingsgegevens op voor je online evenement. Deze gegevens worden weergegeven op de overzichtspagina van de bestelling en de productpagina voor deelnemers"

#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page"
#~ msgstr "Neem verbindingsgegevens op voor je online evenement. Deze gegevens worden weergegeven op de overzichtspagina van de bestelling en de ticketpagina voor deelnemers"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:102
msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."
msgstr "Vermeld verbindingsgegevens voor je online evenement. Deze gegevens worden weergegeven op de overzichtspagina van de bestelling en de ticketpagina voor deelnemers."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:54
msgid "Include tax and fees in the price"
msgstr "Belastingen en toeslagen in de prijs opnemen"

#: src/components/common/CheckInListList/index.tsx:103
msgid "Includes {0} products"
msgstr "Inclusief {0} producten"

#~ msgid "Includes {0} tickets"
#~ msgstr "Inclusief {0} tickets"

#: src/components/common/CheckInListList/index.tsx:104
msgid "Includes 1 product"
msgstr "Omvat 1 product"

#~ msgid "Includes 1 ticket"
#~ msgstr "Inclusief 1 ticket"

#: src/components/common/MessageList/index.tsx:20
msgid "Individual attendees"
msgstr "Individuele deelnemers"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:100
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:121
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:137
msgid "Insert Image"
msgstr "Afbeelding invoegen"

#~ msgid "Integrations"
#~ msgstr "Integraties"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation resent!"
msgstr "Uitnodiging verzonden!"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation revoked!"
msgstr "Uitnodiging ingetrokken!"

#: src/components/modals/InviteUserModal/index.tsx:54
#: src/components/modals/InviteUserModal/index.tsx:74
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invite User"
msgstr "Gebruiker uitnodigen"

#: src/components/common/OrdersTable/index.tsx:116
#: src/components/layouts/Checkout/index.tsx:54
msgid "Invoice downloaded successfully"
msgstr "Factuur succesvol gedownload"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:252
msgid "Invoice Notes"
msgstr "Factuurnotities"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:197
msgid "Invoice Numbering"
msgstr "Factuurnummering"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:177
msgid "Invoice Settings"
msgstr "Factuur Instellingen"

#: src/components/modals/RefundOrderModal/index.tsx:115
msgid "Issue refund"
msgstr "Terugbetaling"

#: src/components/common/LanguageSwitcher/index.tsx:22
msgid "Italian"
msgstr "Italiaans"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Item"
msgstr "Item"

#: src/components/routes/auth/Register/index.tsx:84
msgid "John"
msgstr "Jan"

#: src/components/modals/CreateAttendeeModal/index.tsx:126
msgid "Johnson"
msgstr "Jansen"

#~ msgid "KittenTech Conference {0}"
#~ msgstr "KittenTech Conferentie {0}"

#~ msgid "KittenTech Conference Center"
#~ msgstr "KittenTech conferentiecentrum"

#: src/components/forms/ProductForm/index.tsx:77
msgid "Label"
msgstr "Label"

#: src/components/common/AttendeeDetails/index.tsx:45
#: src/components/modals/CreateAttendeeModal/index.tsx:144
#: src/components/routes/profile/ManageProfile/index.tsx:172
msgid "Language"
msgstr "Taal"

#: src/components/common/ReportTable/index.tsx:52
msgid "Last 12 months"
msgstr "Laatste 12 maanden"

#: src/components/common/ReportTable/index.tsx:47
msgid "Last 14 days"
msgstr "Laatste 14 dagen"

#: src/components/common/ReportTable/index.tsx:44
msgid "Last 24 hours"
msgstr "Laatste 24 uur"

#: src/components/common/ReportTable/index.tsx:48
msgid "Last 30 days"
msgstr "Laatste 30 dagen"

#: src/components/common/ReportTable/index.tsx:45
msgid "Last 48 hours"
msgstr "Laatste 48 uur"

#: src/components/common/ReportTable/index.tsx:50
msgid "Last 6 months"
msgstr "Laatste 6 maanden"

#: src/components/common/ReportTable/index.tsx:46
msgid "Last 7 days"
msgstr "Laatste 7 dagen"

#: src/components/common/ReportTable/index.tsx:49
msgid "Last 90 days"
msgstr "Laatste 90 dagen"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Last login"
msgstr "Laatste login"

#: src/components/modals/CreateAttendeeModal/index.tsx:125
#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:150
msgid "Last name"
msgstr "Achternaam"

#: src/components/common/QuestionsTable/index.tsx:212
#: src/components/common/QuestionsTable/index.tsx:213
#: src/components/modals/EditUserModal/index.tsx:72
#: src/components/modals/InviteUserModal/index.tsx:58
#: src/components/routes/auth/AcceptInvitation/index.tsx:94
#: src/components/routes/auth/Register/index.tsx:89
#: src/components/routes/product-widget/CollectInformation/index.tsx:289
#: src/components/routes/product-widget/CollectInformation/index.tsx:290
#: src/components/routes/product-widget/CollectInformation/index.tsx:400
#: src/components/routes/product-widget/CollectInformation/index.tsx:401
#: src/components/routes/profile/ManageProfile/index.tsx:137
msgid "Last Name"
msgstr "Achternaam"

#: src/components/common/WebhookTable/index.tsx:239
msgid "Last Response"
msgstr "Laatste reactie"

#: src/components/common/WebhookTable/index.tsx:240
msgid "Last Triggered"
msgstr "Laatst geactiveerd"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
msgid "Last Used"
msgstr "Laatst gebruikt"

#~ msgid "Latest Orders"
#~ msgstr "Laatste bestellingen"

#~ msgid "Learn more about Stripe"
#~ msgstr "Meer informatie over Stripe"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:191
msgid "Leave blank to use the default word \"Invoice\""
msgstr "Laat leeg om het standaardwoord te gebruiken"

#~ msgid "Let's get started by creating your first event"
#~ msgstr "Laten we beginnen met het maken van je eerste evenement"

#: src/components/routes/welcome/index.tsx:20
msgid "Let's get started by creating your first organizer"
msgstr "Laten we beginnen met het maken van je eerste organisator"

#~ msgid "Link color"
#~ msgstr "Link kleur"

#: src/components/routes/event/EventDashboard/index.tsx:194
msgid "Link your Stripe account to receive funds from ticket sales."
msgstr "Koppel je Stripe-account om geld te ontvangen van ticketverkoop."

#: src/components/layouts/Event/index.tsx:190
msgid "Live"
msgstr "Live"

#: src/components/routes/event/Webhooks/index.tsx:21
msgid "Loading Webhooks"
msgstr "Webhooks laden"

#: src/components/common/ReportTable/index.tsx:210
msgid "Loading..."
msgstr "Aan het laden..."

#: src/components/routes/event/Settings/index.tsx:34
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:84
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:167
msgid "Location"
msgstr "Locatie"

#: src/components/routes/auth/Login/index.tsx:84
#: src/components/routes/auth/Register/index.tsx:71
msgid "Log in"
msgstr "Log in"

#: src/components/routes/auth/Login/index.tsx:84
msgid "Logging in"
msgstr "Inloggen"

#~ msgid "Login"
#~ msgstr "Inloggen"

#: src/components/common/GlobalMenu/index.tsx:47
msgid "Logout"
msgstr "Afmelden"

#: src/components/common/WidgetEditor/index.tsx:331
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."

#~ msgid "Lorem ipsum..."
#~ msgstr "Lorem ipsum..."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:170
msgid "Make billing address mandatory during checkout"
msgstr "Maak factuuradres verplicht tijdens het afrekenen"

#~ msgid "Make Event Live"
#~ msgstr "Evenement live maken"

#: src/components/forms/QuestionForm/index.tsx:212
msgid "Make this question mandatory"
msgstr "Maak deze vraag verplicht"

#: src/components/routes/event/EventDashboard/index.tsx:148
msgid "Make your event live"
msgstr "Maak je evenement live"

#: src/components/common/CapacityAssignmentList/index.tsx:143
#: src/components/common/CheckInListList/index.tsx:145
#: src/components/common/EventCard/index.tsx:180
#: src/components/common/OrdersTable/index.tsx:139
#: src/components/common/OrdersTable/index.tsx:154
#: src/components/common/ProductsTable/SortableProduct/index.tsx:256
#: src/components/common/PromoCodeTable/index.tsx:167
#: src/components/layouts/Event/index.tsx:97
msgid "Manage"
msgstr "Beheer"

#: src/components/common/AttendeeTable/index.tsx:161
msgid "Manage attendee"
msgstr "Deelnemer beheren"

#~ msgid "Manage billing information and view invoices"
#~ msgstr "Factureringsgegevens beheren en facturen bekijken"

#~ msgid "Manage default settings for new events"
#~ msgstr "Standaardinstellingen voor nieuwe gebeurtenissen beheren"

#: src/components/common/EventCard/index.tsx:150
msgid "Manage event"
msgstr "Evenement beheren"

#~ msgid "Manage general account settings"
#~ msgstr "Algemene accountinstellingen beheren"

#: src/components/common/OrdersTable/index.tsx:156
msgid "Manage order"
msgstr "Bestelling beheren"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:99
msgid "Manage payment and invoicing settings for this event."
msgstr "Beheer de betalings- en factureringsinstellingen voor dit evenement."

#~ msgid "Manage products"
#~ msgstr "Producten beheren"

#: src/components/routes/profile/ManageProfile/index.tsx:101
msgid "Manage Profile"
msgstr "Profiel beheren"

#~ msgid "Manage taxes and fees which can be applied to tickets"
#~ msgstr "Beheer belastingen en toeslagen die kunnen worden toegepast op tickets"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
msgid "Manage taxes and fees which can be applied to your products"
msgstr "Belastingen en toeslagen beheren die kunnen worden toegepast op je producten"

#~ msgid "Manage taxes and fees which can be applied to your tickets"
#~ msgstr "Beheer belastingen en toeslagen die kunnen worden toegepast op je tickets"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
msgid "Manage tickets"
msgstr "Tickets beheren"

#~ msgid "Manage users and their permissions"
#~ msgstr "Gebruikers en hun rechten beheren"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:54
msgid "Manage your account details and default settings"
msgstr "Beheer je accountgegevens en standaardinstellingen"

#~ msgid "Manage your billing and payment details"
#~ msgstr "Je factuur- en betalingsgegevens beheren"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:241
msgid "Manage your payment processing and view platform fees"
msgstr "Beheer uw betalingsverwerking en bekijk de platformkosten"

#~ msgid "Manage your Stripe payment details"
#~ msgstr "Beheer je Stripe betalingsgegevens"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Manage your users and their permissions"
msgstr "Beheer je gebruikers en hun rechten"

#: src/components/forms/QuestionForm/index.tsx:211
msgid "Mandatory questions must be answered before the customer can checkout."
msgstr "Verplichte vragen moeten worden beantwoord voordat de klant kan afrekenen."

#: src/components/common/AttendeeTable/index.tsx:67
msgid "Manually add an Attendee"
msgstr "Handmatig een genodigde toevoegen"

#: src/components/modals/CreateAttendeeModal/index.tsx:97
#: src/components/modals/CreateAttendeeModal/index.tsx:113
msgid "Manually Add Attendee"
msgstr "Deelnemer handmatig toevoegen"

#~ msgid "Manually adding an attendee will adjust ticket quantity."
#~ msgstr "Als je handmatig een deelnemer toevoegt, wordt het aantal tickets aangepast."

#: src/components/common/OrdersTable/index.tsx:167
msgid "Mark as paid"
msgstr "Markeer als betaald"

#: src/components/layouts/AuthLayout/index.tsx:83
msgid "Match Your Brand"
msgstr "Pas aan je merk aan"

#: src/components/forms/ProductForm/index.tsx:383
msgid "Maximum Per Order"
msgstr "Maximum per bestelling"

#: src/components/common/AttendeeTable/index.tsx:166
msgid "Message attendee"
msgstr "Bericht deelnemer"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:273
msgid "Message Attendees"
msgstr "Bericht Deelnemers"

#~ msgid "Message attendees with specific products"
#~ msgstr "Deelnemers berichten sturen met specifieke producten"

#: src/components/modals/SendMessageModal/index.tsx:206
msgid "Message attendees with specific tickets"
msgstr "Deelnemers berichten sturen met specifieke tickets"

#: src/components/layouts/AuthLayout/index.tsx:74
msgid "Message attendees, manage orders, and handle refunds all in one place"
msgstr "Deelnemers berichten sturen, bestellingen beheren en terugbetalingen afhandelen, alles op één plek"

#: src/components/common/OrdersTable/index.tsx:158
msgid "Message buyer"
msgstr "Bericht koper"

#: src/components/modals/SendMessageModal/index.tsx:250
msgid "Message Content"
msgstr "Inhoud bericht"

#: src/components/modals/SendMessageModal/index.tsx:71
msgid "Message individual attendees"
msgstr "Bericht individuele deelnemers"

#: src/components/modals/SendMessageModal/index.tsx:218
msgid "Message order owners with specific products"
msgstr "Bestelbezitters berichten sturen met specifieke producten"

#: src/components/modals/SendMessageModal/index.tsx:118
msgid "Message Sent"
msgstr "Bericht verzonden"

#: src/components/layouts/Event/index.tsx:105
#: src/components/routes/event/messages.tsx:31
msgid "Messages"
msgstr "Berichten"

#: src/components/forms/ProductForm/index.tsx:381
msgid "Minimum Per Order"
msgstr "Minimum per bestelling"

#: src/components/forms/ProductForm/index.tsx:285
msgid "Minimum Price"
msgstr "Minimale prijs"

#: src/components/routes/event/Settings/index.tsx:58
msgid "Miscellaneous"
msgstr "Diverse"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:69
msgid "Miscellaneous Settings"
msgstr "Diverse instellingen"

#: src/components/layouts/AuthLayout/index.tsx:63
msgid "Mobile Check-in"
msgstr "Mobiel inchecken"

#: src/components/forms/QuestionForm/index.tsx:106
msgid "Multi line text box"
msgstr "Meerregelig tekstvak"

#~ msgid "Multiple price options, users can choose which to pay"
#~ msgstr "Meerdere prijsopties, gebruikers kunnen kiezen welke ze willen betalen"

#: src/components/forms/ProductForm/index.tsx:165
msgid "Multiple price options. Perfect for early bird products etc."
msgstr "Meerdere prijsopties. Perfect voor early bird-producten enz."

#~ msgid "Multiple price options. Perfect for early bird tickets etc."
#~ msgstr "Meerdere prijsopties. Perfect voor early bird tickets etc."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:71
msgid "My amazing event description..."
msgstr "Mijn verbazingwekkende evenementbeschrijving..."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:65
msgid "My amazing event title..."
msgstr "Mijn verbazingwekkende evenementtitel..."

#: src/components/common/GlobalMenu/index.tsx:27
msgid "My Profile"
msgstr "Mijn profiel"

#: src/components/common/QuestionAndAnswerList/index.tsx:178
msgid "N/A"
msgstr "N.V.T."

#: src/components/common/WidgetEditor/index.tsx:354
msgid "Nam placerat elementum..."
msgstr "Nam placerat elementum..."

#: src/components/common/AttendeeDetails/index.tsx:13
#: src/components/common/AttendeeTable/index.tsx:108
#: src/components/common/OrderDetails/index.tsx:23
#: src/components/forms/CapaciyAssigmentForm/index.tsx:37
#: src/components/forms/CheckInListForm/index.tsx:20
#: src/components/forms/ProductForm/index.tsx:253
#: src/components/forms/TaxAndFeeForm/index.tsx:61
#: src/components/modals/CreateEventModal/index.tsx:129
#: src/components/modals/DuplicateEventModal/index.tsx:78
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:63
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:72
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:107
#: src/components/routes/welcome/index.tsx:83
msgid "Name"
msgstr "Naam"

#: src/components/modals/CreateEventModal/index.tsx:38
msgid "Name should be less than 150 characters"
msgstr "De naam moet minder dan 150 tekens bevatten"

#: src/components/common/QuestionAndAnswerList/index.tsx:181
#: src/components/common/QuestionAndAnswerList/index.tsx:289
msgid "Navigate to Attendee"
msgstr "Navigeer naar deelnemer"

#: src/components/common/PromoCodeTable/index.tsx:158
#: src/components/common/WebhookTable/index.tsx:266
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Never"
msgstr "Nooit"

#: src/components/routes/auth/AcceptInvitation/index.tsx:111
#: src/components/routes/auth/ResetPassword/index.tsx:59
#: src/components/routes/profile/ManageProfile/index.tsx:195
msgid "New Password"
msgstr "Nieuw wachtwoord"

#~ msgid "New York"
#~ msgstr "New York"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "No"
msgstr "Geen"

#~ msgid "No {0} available."
#~ msgstr "Geen {0} beschikbaar."

#: src/components/routes/event/Webhooks/index.tsx:22
msgid "No Active Webhooks"
msgstr "Geen actieve webhooks"

#: src/components/common/NoEventsBlankSlate/index.tsx:21
msgid "No archived events to show."
msgstr "Geen gearchiveerde evenementen om weer te geven."

#: src/components/common/AttendeeList/index.tsx:23
msgid "No attendees found for this order."
msgstr "Geen aanwezigen gevonden voor deze bestelling."

#: src/components/modals/ManageOrderModal/index.tsx:132
msgid "No attendees have been added to this order."
msgstr "Er zijn geen deelnemers aan deze bestelling toegevoegd."

#: src/components/common/AttendeeTable/index.tsx:56
msgid "No Attendees to show"
msgstr "Geen aanwezigen"

#: src/components/forms/CheckInListForm/index.tsx:48
msgid "No attendees will be able to check in before this date using this list"
msgstr "Bezoekers kunnen niet voor deze datum inchecken met deze lijst"

#: src/components/common/CapacityAssignmentList/index.tsx:42
msgid "No Capacity Assignments"
msgstr "Geen capaciteitstoewijzingen"

#: src/components/common/CheckInListList/index.tsx:44
msgid "No Check-In Lists"
msgstr "Geen inchecklijsten"

#: src/components/layouts/AuthLayout/index.tsx:34
msgid "No Credit Card Required"
msgstr "Geen creditcard nodig"

#: src/components/common/ReportTable/index.tsx:218
msgid "No data available"
msgstr "Geen gegevens beschikbaar"

#: src/components/common/ReportTable/index.tsx:214
msgid "No data to show. Please select a date range"
msgstr "Geen gegevens om weer te geven. Selecteer een datumbereik"

#: src/components/forms/PromoCodeForm/index.tsx:47
msgid "No Discount"
msgstr "Geen Korting"

#: src/components/common/NoEventsBlankSlate/index.tsx:20
msgid "No ended events to show."
msgstr "Geen beëindigde evenementen om te laten zien."

#~ msgid "No events for this organizer"
#~ msgstr "Geen evenementen voor deze organisator"

#: src/components/common/NoEventsBlankSlate/index.tsx:14
msgid "No events to show"
msgstr "Geen evenementen om weer te geven"

#: src/components/common/FilterModal/index.tsx:230
msgid "No filters available"
msgstr "Geen filters beschikbaar"

#: src/components/modals/WebhookLogsModal/index.tsx:177
msgid "No logs found"
msgstr "Geen logboeken gevonden"

#: src/components/common/MessageList/index.tsx:69
msgid "No messages to show"
msgstr "Geen berichten om te tonen"

#: src/components/common/OrdersTable/index.tsx:63
msgid "No orders to show"
msgstr "Geen orders om te laten zien"

#: src/components/routes/product-widget/Payment/index.tsx:76
msgid "No payment methods are currently available. Please contact the event organizer for assistance."
msgstr "Er zijn momenteel geen betalingsmethoden beschikbaar. Neem contact op met de organisator van het evenement voor hulp."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:39
msgid "No Payment Required"
msgstr "Geen betaling vereist"

#: src/components/modals/ManageAttendeeModal/index.tsx:177
msgid "No product associated with this attendee."
msgstr "Geen product gekoppeld aan deze deelnemer."

#: src/components/common/ProductSelector/index.tsx:33
msgid "No products available for selection"
msgstr "Geen producten beschikbaar voor selectie"

#: src/components/modals/CreateProductCategoryModal/index.tsx:22
msgid "No products available in this category."
msgstr "Geen producten beschikbaar in deze categorie."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:39
msgid "No Products Yet"
msgstr "Nog geen producten"

#: src/components/common/PromoCodeTable/index.tsx:46
msgid "No Promo Codes to show"
msgstr "Geen promotiecodes om te tonen"

#: src/components/modals/ManageAttendeeModal/index.tsx:193
msgid "No questions answered by this attendee."
msgstr "Geen vragen beantwoord door deze deelnemer."

#~ msgid "No questions have been answered by this attendee."
#~ msgstr "Er zijn geen vragen beantwoord door deze deelnemer."

#: src/components/modals/ManageOrderModal/index.tsx:119
msgid "No questions have been asked for this order."
msgstr "Er zijn geen vragen gesteld voor deze bestelling."

#: src/components/common/WebhookTable/index.tsx:174
msgid "No response"
msgstr "Geen reactie"

#: src/components/common/WebhookTable/index.tsx:141
msgid "No responses yet"
msgstr "Nog geen reacties"

#: src/components/common/NoResultsSplash/index.tsx:24
msgid "No results"
msgstr "Geen resultaten"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:20
msgid "No Search Results"
msgstr "Geen zoekresultaten"

#: src/components/common/NoResultsSplash/index.tsx:29
msgid "No search results."
msgstr "Geen zoekresultaten."

#~ msgid "No Taxes or Fees have been added yet."
#~ msgstr "Er zijn nog geen belastingen of heffingen toegevoegd."

#: src/components/common/TaxAndFeeList/index.tsx:101
msgid "No Taxes or Fees have been added."
msgstr "Er zijn geen belastingen of toeslagen toegevoegd."

#~ msgid "No tickets available"
#~ msgstr "Geen tickets beschikbaar"

#~ msgid "No tickets to show"
#~ msgstr "Geen tickets om te laten zien"

#: src/components/modals/WebhookLogsModal/index.tsx:180
msgid "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."
msgstr "Er zijn nog geen webhook-events opgenomen voor dit eindpunt. Evenementen zullen hier verschijnen zodra ze worden geactiveerd."

#: src/components/common/WebhookTable/index.tsx:201
msgid "No Webhooks"
msgstr "Geen webhooks"

#: src/components/common/PromoCodeTable/index.tsx:82
msgid "None"
msgstr "Geen"

#: src/components/common/ProductPriceAvailability/index.tsx:27
#: src/components/common/ProductPriceAvailability/index.tsx:48
msgid "Not available"
msgstr "Niet beschikbaar"

#~ msgid "Not Checked In"
#~ msgstr "Niet ingecheckt"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "Not On Sale"
msgstr "Niet in de verkoop"

#: src/components/modals/ManageAttendeeModal/index.tsx:130
#: src/components/modals/ManageOrderModal/index.tsx:164
msgid "Notes"
msgstr "Opmerkingen"

#: src/components/common/ReportTable/index.tsx:240
msgid "Nothing to show yet"
msgstr "Nog niets om te laten zien"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:72
msgid "Notification Settings"
msgstr "Instellingen meldingen"

#: src/components/modals/RefundOrderModal/index.tsx:108
msgid "Notify buyer of refund"
msgstr "Koper op de hoogte stellen van terugbetaling"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:75
msgid "Notify organizer of new orders"
msgstr "Organisator op de hoogte stellen van nieuwe bestellingen"

#: src/components/routes/welcome/index.tsx:72
msgid "Now let's create your first event"
msgstr "Laten we nu je eerste evenement maken"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:217
msgid "Number of days allowed for payment (leave blank to omit payment terms from invoices)"
msgstr "Aantal dagen toegestaan voor betaling (leeg laten om betalingstermijnen weg te laten van facturen)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:199
msgid "Number Prefix"
msgstr "Nummer Voorvoegsel"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:145
msgid "Offline orders are not reflected in event statistics until the order is marked as paid."
msgstr "Offline bestellingen worden niet weergegeven in evenementstatistieken totdat de bestelling als betaald is gemarkeerd."

#: src/components/routes/product-widget/Payment/index.tsx:66
msgid "Offline payment failed. Please try again or contact the event organizer."
msgstr "Offline betaling mislukt. Probeer het opnieuw of neem contact op met de organisator van het evenement."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:151
msgid "Offline Payment Instructions"
msgstr "Offline betalingsinstructies"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:90
msgid "Offline Payments"
msgstr "Offline betalingen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:139
msgid "Offline Payments Information"
msgstr "Informatie over offline betalingen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:134
msgid "Offline Payments Settings"
msgstr "Instellingen voor offline betalingen"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:71
msgid "On sale"
msgstr "Te koop"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "On Sale"
msgstr "In de uitverkoop"

#: src/components/common/NoEventsBlankSlate/index.tsx:19
msgid "Once you create an event, you'll see it here."
msgstr "Zodra je een evenement hebt gemaakt, zie je het hier."

#: src/components/common/ReportTable/index.tsx:245
msgid "Once you start collecting data, you'll see it here."
msgstr "Zodra je gegevens begint te verzamelen, zie je ze hier."

#: src/components/routes/event/GettingStarted/index.tsx:179
msgid "Once you're ready, set your event live and start selling products."
msgstr "Zodra je klaar bent, kun je je evenement live zetten en beginnen met het verkopen van producten."

#~ msgid "Once you're ready, set your event live and start selling tickets."
#~ msgstr "Zodra je klaar bent, kun je je evenement live zetten en tickets gaan verkopen."

#: src/components/common/EventStatusBadge/index.tsx:18
msgid "Ongoing"
msgstr "Doorlopend"

#: src/components/common/EventCard/index.tsx:118
msgid "Online event"
msgstr "Online evenement"

#: src/components/common/OnlineEventDetails/index.tsx:9
msgid "Online Event Details"
msgstr "Details online evenement"

#: src/components/modals/SendMessageModal/index.tsx:279
msgid ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."
msgstr "Stuur alleen belangrijke e-mails die direct verband houden met dit evenement via dit formulier. Elk misbruik, inclusief het versturen van promotionele e-mails, leidt tot een onmiddellijke blokkering van je account."

#: src/components/modals/SendMessageModal/index.tsx:226
msgid "Only send to orders with these statuses"
msgstr "Alleen verzenden naar orders met deze statussen"

#: src/components/common/CheckInListList/index.tsx:167
msgid "Open Check-In Page"
msgstr "Check-in pagina openen"

#: src/components/layouts/Event/index.tsx:288
msgid "Open sidebar"
msgstr "Zijbalk openen"

#: src/components/forms/QuestionForm/index.tsx:45
msgid "Option {i}"
msgstr "Optie {i}"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:253
msgid "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"
msgstr "Optionele aanvullende informatie die op alle facturen moet worden vermeld (bijv. betalingsvoorwaarden, kosten voor te late betaling, retourbeleid)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:200
msgid "Optional prefix for invoice numbers (e.g., INV-)"
msgstr "Optioneel voorvoegsel voor factuurnummers (bijv. INV-)"

#: src/components/forms/QuestionForm/index.tsx:28
msgid "Options"
msgstr "Opties"

#: src/components/modals/CreateEventModal/index.tsx:118
msgid "or"
msgstr "of"

#: src/components/common/AttendeeTable/index.tsx:110
msgid "Order"
msgstr "Bestel"

#~ msgid "Order #"
#~ msgstr "Bestel #"

#: src/components/forms/WebhookForm/index.tsx:76
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:63
msgid "Order Cancelled"
msgstr "Bestelling geannuleerd"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:62
msgid "Order Completed"
msgstr "Bestelling voltooid"

#: src/components/forms/WebhookForm/index.tsx:52
msgid "Order Created"
msgstr "Bestelling aangemaakt"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:122
msgid "Order Date"
msgstr "Bestel Datum"

#: src/components/modals/ManageAttendeeModal/index.tsx:166
#: src/components/modals/ManageOrderModal/index.tsx:86
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:266
msgid "Order Details"
msgstr "Bestel Details"

#~ msgid "Order Details {0}"
#~ msgstr "Bestelgegevens {0}"

#: src/components/modals/CancelOrderModal/index.tsx:29
msgid "Order has been canceled and the order owner has been notified."
msgstr "Bestelling is geannuleerd en de eigenaar van de bestelling is op de hoogte gesteld."

#: src/components/common/OrdersTable/index.tsx:79
msgid "Order marked as paid"
msgstr "Bestelling gemarkeerd als betaald"

#: src/components/forms/WebhookForm/index.tsx:64
msgid "Order Marked as Paid"
msgstr "Bestelling gemarkeerd als betaald"

#: src/components/modals/ManageOrderModal/index.tsx:92
msgid "Order Notes"
msgstr "Bestelnotities"

#: src/components/common/MessageList/index.tsx:23
msgid "Order owner"
msgstr "Eigenaar bestelling"

#: src/components/modals/SendMessageModal/index.tsx:191
msgid "Order owners with a specific product"
msgstr "Bestel eigenaars met een specifiek product"

#: src/components/common/MessageList/index.tsx:19
msgid "Order owners with products"
msgstr "Besteleigenaars met producten"

#: src/components/common/QuestionsTable/index.tsx:313
#: src/components/common/QuestionsTable/index.tsx:355
msgid "Order questions"
msgstr "Vragen bestelling"

#~ msgid "Order questions are asked once per order. By default, people are asked for their first name, last name, and email address."
#~ msgstr "Vragen over bestellingen worden één keer per bestelling gesteld. Standaard worden mensen gevraagd naar hun voornaam, achternaam en e-mailadres."

#~ msgid "Order questions are asked once per order. By default, we ask for the first name, last name, and email address."
#~ msgstr "Vragen over bestellingen worden één keer per bestelling gesteld. Standaard vragen we naar de voornaam, achternaam en het e-mailadres."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:112
msgid "Order Reference"
msgstr "Bestelreferentie"

#: src/components/forms/WebhookForm/index.tsx:70
msgid "Order Refunded"
msgstr "Bestelling terugbetaald"

#: src/components/routes/event/orders.tsx:45
msgid "Order Status"
msgstr "Bestelstatus"

#: src/components/modals/SendMessageModal/index.tsx:228
msgid "Order statuses"
msgstr "Bestelstatussen"

#: src/components/layouts/Checkout/CheckoutSidebar/index.tsx:28
#: src/components/modals/ManageOrderModal/index.tsx:105
msgid "Order Summary"
msgstr "Overzicht bestelling"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:90
msgid "Order timeout"
msgstr "Time-out bestelling"

#: src/components/forms/WebhookForm/index.tsx:58
msgid "Order Updated"
msgstr "Bijgewerkte bestelling"

#: src/components/layouts/Event/index.tsx:100
#: src/components/routes/event/orders.tsx:113
msgid "Orders"
msgstr "Bestellingen"

#~ msgid "Orders Created"
#~ msgstr "Orders aangemaakt"

#: src/components/routes/event/orders.tsx:94
msgid "Orders Exported"
msgstr "Geëxporteerde bestellingen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:235
msgid "Organization Address"
msgstr "Adres organisatie"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:226
msgid "Organization Details"
msgstr "Organisatie details"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:228
msgid "Organization Name"
msgstr "Naam organisatie"

#: src/components/modals/EditUserModal/index.tsx:55
#: src/components/modals/InviteUserModal/index.tsx:47
#: src/components/routes/events/Dashboard/index.tsx:113
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:186
msgid "Organizer"
msgstr "Organisator"

#~ msgid "Organizer Details"
#~ msgstr "Details organisator"

#: src/components/modals/CreateEventModal/index.tsx:41
msgid "Organizer is required"
msgstr "Organisator is vereist"

#: src/components/forms/OrganizerForm/index.tsx:27
msgid "Organizer Name"
msgstr "Naam organisator"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
msgid "Organizers can only manage events and products. They cannot manage users, account settings or billing information."
msgstr "Organisatoren kunnen alleen evenementen en producten beheren. Ze kunnen geen gebruikers, accountinstellingen of factureringsgegevens beheren."

#~ msgid "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."
#~ msgstr "Organisatoren kunnen alleen evenementen en tickets beheren. Ze kunnen geen gebruikers, accountinstellingen of factureringsgegevens beheren."

#: src/components/layouts/Event/index.tsx:87
msgid "Overview"
msgstr "Overzicht"

#: src/components/common/WidgetEditor/index.tsx:223
msgid "Padding"
msgstr "Opvulling"

#: src/components/routes/event/HomepageDesigner/index.tsx:150
msgid "Page background color"
msgstr "Achtergrondkleur pagina"

#: src/components/common/ErrorDisplay/index.tsx:13
msgid "Page not found"
msgstr "Pagina niet gevonden"

#: src/components/common/StatBoxes/index.tsx:45
msgid "Page views"
msgstr "Bekeken pagina's"

#: src/components/forms/ProductForm/index.tsx:358
msgid "page."
msgstr "pagina."

#: src/components/modals/CreateAttendeeModal/index.tsx:180
msgid "paid"
msgstr "betaald"

#: src/components/forms/ProductForm/index.tsx:145
msgid "Paid Product"
msgstr "Betaald product"

#~ msgid "Paid Ticket"
#~ msgstr "Betaald ticket"

#: src/components/routes/event/orders.tsx:30
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:54
msgid "Partially Refunded"
msgstr "Gedeeltelijk Terugbetaald"

#: src/components/routes/auth/Login/index.tsx:73
#: src/components/routes/auth/Register/index.tsx:106
#: src/components/routes/profile/ManageProfile/index.tsx:109
msgid "Password"
msgstr "Wachtwoord"

#: src/components/routes/auth/AcceptInvitation/index.tsx:30
msgid "Password must be a minimum  of 8 characters"
msgstr "Wachtwoord moet minimaal 8 tekens bevatten"

#: src/components/routes/auth/Register/index.tsx:36
msgid "Password must be at least 8 characters"
msgstr "Wachtwoord moet minstens 8 tekens bevatten"

#: src/components/routes/auth/ResetPassword/index.tsx:46
msgid "Password reset successfully. Please login with your new password."
msgstr "Wachtwoord opnieuw ingesteld. Log in met je nieuwe wachtwoord."

#: src/components/routes/auth/AcceptInvitation/index.tsx:31
#: src/components/routes/auth/Register/index.tsx:37
msgid "Passwords are not the same"
msgstr "Wachtwoorden zijn niet hetzelfde"

#: src/components/common/WidgetEditor/index.tsx:269
msgid "Paste this where you want the widget to appear."
msgstr "Plak dit waar je wilt dat de widget verschijnt."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:104
msgid "Paste URL"
msgstr "Plak URL"

#: src/components/modals/CreateAttendeeModal/index.tsx:119
msgid "Patrick"
msgstr "Patrick"

#: src/components/modals/CreateAttendeeModal/index.tsx:133
msgid "<EMAIL>"
msgstr "<EMAIL>"

#~ msgid "Pause Ticket"
#~ msgstr "Pauze Ticket"

#: src/components/forms/WebhookForm/index.tsx:25
msgid "Paused"
msgstr "Gepauzeerd"

#: src/components/forms/StripeCheckoutForm/index.tsx:123
msgid "Payment"
msgstr "Betaling"

#: src/components/routes/event/Settings/index.tsx:64
msgid "Payment & Invoicing"
msgstr "Betaling & facturering"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:98
msgid "Payment & Invoicing Settings"
msgstr "Instellingen voor betaling en facturering"

#: src/components/routes/account/ManageAccount/index.tsx:38
msgid "Payment & Plan"
msgstr "Betaling & Plan"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:216
msgid "Payment Due Period"
msgstr "Betalingstermijn"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:41
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:64
msgid "Payment Failed"
msgstr "Betaling mislukt"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:228
#: src/components/routes/product-widget/Payment/PaymentMethods/Offline/index.tsx:14
msgid "Payment Instructions"
msgstr "Betalingsinstructies"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:105
msgid "Payment Methods"
msgstr "Betaalmethoden"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:138
msgid "Payment Processing"
msgstr "Verwerking van betalingen"

#: src/components/common/OrderDetails/index.tsx:76
msgid "Payment provider"
msgstr "Betalingsprovider"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:42
msgid "Payment Received"
msgstr "Ontvangen betaling"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:240
msgid "Payment Settings"
msgstr "Betalingsinstellingen"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:135
msgid "Payment Status"
msgstr "Betalingsstatus"

#: src/components/forms/StripeCheckoutForm/index.tsx:60
msgid "Payment succeeded!"
msgstr "Betaling gelukt!"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:214
msgid "Payment Terms"
msgstr "Betalingsvoorwaarden"

#~ msgid "PDF"
#~ msgstr "PDF"

#~ msgid "percent"
#~ msgstr "procent"

#: src/components/forms/PromoCodeForm/index.tsx:51
#: src/components/forms/TaxAndFeeForm/index.tsx:27
msgid "Percentage"
msgstr "Percentage"

#: src/components/forms/TaxAndFeeForm/index.tsx:71
msgid "Percentage Amount"
msgstr "Percentage Bedrag"

#: src/components/routes/product-widget/Payment/index.tsx:122
msgid "Place Order"
msgstr "Bestelling plaatsen"

#: src/components/common/WidgetEditor/index.tsx:257
msgid "Place this in the <head> of your website."
msgstr "Plaats dit in de <head> van je website."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:40
msgid "Platform Fees"
msgstr "Platformkosten"

#: src/components/forms/QuestionForm/index.tsx:31
msgid "Please add at least one option"
msgstr "Voeg ten minste één optie toe"

#: src/hooks/useFormErrorResponseHandler.tsx:18
msgid "Please check the provided information is correct"
msgstr "Controleer of de verstrekte informatie correct is"

#: src/components/routes/auth/Login/index.tsx:41
msgid "Please check your email and password and try again"
msgstr "Controleer je e-mail en wachtwoord en probeer het opnieuw"

#: src/components/routes/auth/AcceptInvitation/index.tsx:32
#: src/components/routes/auth/Register/index.tsx:38
msgid "Please check your email is valid"
msgstr "Controleer of je e-mailadres geldig is"

#: src/components/routes/profile/ManageProfile/index.tsx:152
msgid "Please check your email to confirm your email address"
msgstr "Controleer je e-mail om je e-mailadres te bevestigen"

#: src/components/routes/auth/AcceptInvitation/index.tsx:86
msgid "Please complete the form below to accept your invitation"
msgstr "Vul het onderstaande formulier in om uw uitnodiging te accepteren"

#: src/components/routes/product-widget/SelectProducts/index.tsx:306
msgid "Please continue in the new tab"
msgstr "Ga verder in het nieuwe tabblad"

#~ msgid "Please continue your order in the new tab"
#~ msgstr "Ga verder met uw bestelling in het nieuwe tabblad"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:47
msgid "Please create a product"
msgstr "Maak een product"

#: src/components/modals/CreateCheckInListModal/index.tsx:51
msgid "Please create a ticket"
msgstr "Maak een ticket aan"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:45
msgid "Please enter a valid image URL that points to an image."
msgstr "Voer een geldige URL in die naar een afbeelding verwijst."

#~ msgid "Please enter a valid promo code"
#~ msgstr "Voer een geldige promotiecode in"

#: src/components/modals/CreateWebhookModal/index.tsx:30
msgid "Please enter a valid URL"
msgstr "Voer een geldige URL in"

#: src/components/routes/auth/ResetPassword/index.tsx:55
msgid "Please enter your new password"
msgstr "Voer uw nieuwe wachtwoord in"

#: src/components/modals/CancelOrderModal/index.tsx:52
msgid "Please Note"
msgstr "Let op"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:34
msgid "Please provide an image."
msgstr "Geef een afbeelding op."

#~ msgid "Please remove filters and set sorting to \"Homepage order\" to enable sorting"
#~ msgstr "Verwijder filters en stel sorteren in op"

#: src/components/layouts/Checkout/index.tsx:159
msgid "Please return to the event page to start over."
msgstr "Ga terug naar de evenementpagina om opnieuw te beginnen."

#: src/components/modals/SendMessageModal/index.tsx:195
msgid "Please select"
msgstr "Selecteer"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:53
msgid "Please select an image."
msgstr "Selecteer een afbeelding."

#: src/components/routes/product-widget/SelectProducts/index.tsx:237
msgid "Please select at least one product"
msgstr "Selecteer ten minste één product"

#~ msgid "Please select at least one ticket"
#~ msgstr "Selecteer ten minste één ticket"

#: src/components/routes/event/attendees.tsx:49
#: src/components/routes/event/orders.tsx:100
msgid "Please try again."
msgstr "Probeer het opnieuw."

#: src/components/routes/profile/ManageProfile/index.tsx:142
msgid "Please verify your email address to access all features"
msgstr "Controleer uw e-mailadres om toegang te krijgen tot alle functies"

#: src/components/routes/event/attendees.tsx:40
msgid "Please wait while we prepare your attendees for export..."
msgstr "Wacht even terwijl we je deelnemers voorbereiden voor export..."

#: src/components/common/OrdersTable/index.tsx:112
#: src/components/layouts/Checkout/index.tsx:50
msgid "Please wait while we prepare your invoice..."
msgstr "Wacht even terwijl we uw factuur opstellen..."

#: src/components/routes/event/orders.tsx:91
msgid "Please wait while we prepare your orders for export..."
msgstr "Even geduld alstublieft terwijl we uw bestellingen klaarmaken voor export..."

#: src/components/common/LanguageSwitcher/index.tsx:26
msgid "Portuguese"
msgstr "Portugees"

#~ msgid "Portuguese (Brazil)"
#~ msgstr "Portugees (Brazilië)"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:74
msgid "Post Checkout message"
msgstr "Post Checkout-bericht"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Powered by"
msgstr "Aangedreven door"

#~ msgid "Powered By"
#~ msgstr "Aangedreven door"

#~ msgid "Powered by <0>Hi.Events</0> 👋"
#~ msgstr "Aangedreven door <0>Hi.Events</0> 👋"

#~ msgid "Powered By Hi.Events"
#~ msgstr "Aangedreven door Hi.Events"

#~ msgid "Powered by Stripe"
#~ msgstr "Aangedreven door Stripe"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:67
msgid "Pre Checkout message"
msgstr "Bericht voor het afrekenen"

#: src/components/common/QuestionsTable/index.tsx:347
msgid "Preview"
msgstr "Voorbeeld"

#: src/components/layouts/Event/index.tsx:205
#: src/components/layouts/Event/index.tsx:209
msgid "Preview Event page"
msgstr "Voorbeeld van de evenementpagina"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:236
#: src/components/forms/ProductForm/index.tsx:73
#: src/components/forms/ProductForm/index.tsx:285
msgid "Price"
msgstr "Prijs"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:78
msgid "Price display mode"
msgstr "Modus prijsweergave"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:88
msgid "Price not set"
msgstr "Prijs niet ingesteld"

#: src/components/forms/ProductForm/index.tsx:322
msgid "Price tiers"
msgstr "Prijsniveaus"

#~ msgid "Price Tiers"
#~ msgstr "Prijsniveaus"

#: src/components/forms/ProductForm/index.tsx:228
msgid "Price Type"
msgstr "Prijs Type"

#: src/components/common/WidgetEditor/index.tsx:182
msgid "Primary Color"
msgstr "Primaire kleur"

#: src/components/routes/event/HomepageDesigner/index.tsx:156
msgid "Primary Colour"
msgstr "Primaire kleur"

#: src/components/common/WidgetEditor/index.tsx:190
#: src/components/routes/event/HomepageDesigner/index.tsx:158
msgid "Primary Text Color"
msgstr "Primaire tekstkleur"

#: src/components/common/AttendeeTicket/index.tsx:81
msgid "Print"
msgstr "Afdrukken"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:288
msgid "Print All Tickets"
msgstr "Alle tickets afdrukken"

#: src/components/layouts/Checkout/index.tsx:114
msgid "Print Tickets"
msgstr "Tickets afdrukken"

#~ msgid "product"
#~ msgstr "product"

#: src/components/common/AttendeeDetails/index.tsx:37
#: src/components/modals/ManageAttendeeModal/index.tsx:119
msgid "Product"
msgstr "Product"

#: src/components/forms/ProductForm/index.tsx:266
msgid "Product Category"
msgstr "Productcategorie"

#: src/components/modals/EditProductCategoryModal/index.tsx:53
msgid "Product category updated successfully."
msgstr "Productcategorie succesvol bijgewerkt."

#: src/components/forms/WebhookForm/index.tsx:34
msgid "Product Created"
msgstr "Gemaakt product"

#: src/components/forms/WebhookForm/index.tsx:46
msgid "Product Deleted"
msgstr "Product verwijderd"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:55
msgid "Product deleted successfully"
msgstr "Product succesvol verwijderd"

#~ msgid "Product email has been resent to attendee"
#~ msgstr "Product e-mail is opnieuw verzonden naar de deelnemer"

#: src/components/forms/ProductForm/index.tsx:236
msgid "Product Price Type"
msgstr "Product Prijs Type"

#: src/components/common/QuestionsTable/index.tsx:328
msgid "Product questions"
msgstr "Product vragen"

#: src/components/routes/event/EventDashboard/index.tsx:217
#: src/components/routes/event/Reports/index.tsx:17
msgid "Product Sales"
msgstr "Productverkoop"

#: src/components/routes/event/Reports/index.tsx:18
msgid "Product sales, revenue, and tax breakdown"
msgstr "Uitsplitsing productverkoop, inkomsten en belastingen"

#: src/components/common/ProductSelector/index.tsx:63
msgid "Product Tier"
msgstr "Productniveau"

#: src/components/forms/ProductForm/index.tsx:214
#: src/components/forms/ProductForm/index.tsx:221
msgid "Product Type"
msgstr "Soort product"

#: src/components/forms/WebhookForm/index.tsx:40
msgid "Product Updated"
msgstr "Bijgewerkt product"

#: src/components/common/WidgetEditor/index.tsx:313
msgid "Product Widget Preview"
msgstr "Voorbeeld van productwidget"

#: src/components/common/PromoCodeTable/index.tsx:148
msgid "Product(s)"
msgstr "Product(en)"

#: src/components/common/PromoCodeTable/index.tsx:72
msgid "Products"
msgstr "Producten"

#: src/components/common/EventCard/index.tsx:126
msgid "products sold"
msgstr "verkochte producten"

#: src/components/common/StatBoxes/index.tsx:21
msgid "Products sold"
msgstr "Verkochte producten"

#: src/components/routes/event/EventDashboard/index.tsx:238
msgid "Products Sold"
msgstr "Verkochte producten"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:178
msgid "Products sorted successfully"
msgstr "Producten succesvol gesorteerd"

#: src/components/layouts/AuthLayout/index.tsx:43
msgid "Products, merchandise, and flexible pricing options"
msgstr "Producten, koopwaar en flexibele prijsopties"

#: src/components/routes/profile/ManageProfile/index.tsx:106
msgid "Profile"
msgstr "Profiel"

#: src/components/routes/profile/ManageProfile/index.tsx:59
msgid "Profile updated successfully"
msgstr "Profiel succesvol bijgewerkt"

#: src/components/routes/product-widget/SelectProducts/index.tsx:178
msgid "Promo {promo_code} code applied"
msgstr "Promo {promo_code} code toegepast"

#~ msgid "Promo {promoCode} code applied"
#~ msgstr "Promo {promoCode} code toegepast"

#: src/components/common/OrderDetails/index.tsx:86
msgid "Promo code"
msgstr "Kortingscode"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:20
msgid "Promo Code"
msgstr "Kortingscode"

#: src/components/forms/ProductForm/index.tsx:409
msgid "Promo Code page"
msgstr "Promo Code pagina"

#: src/components/routes/event/Reports/index.tsx:30
msgid "Promo code usage and discount breakdown"
msgstr "Gebruik van promocodes en uitsplitsing van kortingen"

#: src/components/layouts/Event/index.tsx:106
#: src/components/routes/event/promo-codes.tsx:31
msgid "Promo Codes"
msgstr "Promo codes"

#: src/components/common/PromoCodeTable/index.tsx:51
msgid "Promo codes can be used to offer discounts, presale access, or provide special access to your event."
msgstr "Promocodes kunnen worden gebruikt voor kortingen, toegang tot de voorverkoop of speciale toegang tot je evenement."

#: src/components/routes/event/Reports/index.tsx:29
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:95
msgid "Promo Codes Report"
msgstr "Promocodes Rapport"

#: src/components/forms/QuestionForm/index.tsx:189
msgid ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."
msgstr "Geef aanvullende context of instructies voor deze vraag. Gebruik dit veld om algemene voorwaarden, richtlijnen of andere belangrijke informatie toe te voegen die deelnemers moeten weten voordat ze antwoorden."

#: src/components/routes/event/EventDashboard/index.tsx:159
msgid "Publish Event"
msgstr "Evenement publiceren"

#~ msgid "Purchase License"
#~ msgstr "Licentie kopen"

#: src/components/modals/ShareModal/index.tsx:126
msgid "QR Code"
msgstr "QR-code"

#: src/components/layouts/AuthLayout/index.tsx:64
msgid "QR code scanning with instant feedback and secure sharing for staff access"
msgstr "QR-code scannen met directe feedback en veilig delen voor personeel"

#: src/components/forms/ProductForm/index.tsx:85
#: src/components/forms/ProductForm/index.tsx:302
msgid "Quantity Available"
msgstr "Beschikbare hoeveelheid"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
msgid "Quantity Sold"
msgstr "Verkochte hoeveelheid"

#: src/components/common/QuestionsTable/index.tsx:170
msgid "Question deleted"
msgstr "Vraag verwijderd"

#: src/components/forms/QuestionForm/index.tsx:188
msgid "Question Description"
msgstr "Beschrijving van de vraag"

#: src/components/forms/QuestionForm/index.tsx:178
msgid "Question Title"
msgstr "Titel van de vraag"

#: src/components/common/QuestionsTable/index.tsx:275
#: src/components/layouts/Event/index.tsx:102
msgid "Questions"
msgstr "Vragen"

#: src/components/modals/ManageAttendeeModal/index.tsx:184
#: src/components/modals/ManageOrderModal/index.tsx:111
msgid "Questions & Answers"
msgstr "Vragen en antwoorden"

#: src/components/common/QuestionsTable/index.tsx:145
msgid "Questions sorted successfully"
msgstr "Vragen succesvol gesorteerd"

#: src/components/forms/QuestionForm/index.tsx:118
msgid "Radio Option"
msgstr "Radio-optie"

#: src/components/common/MessageList/index.tsx:59
msgid "Read less"
msgstr "Minder lezen"

#: src/components/modals/SendMessageModal/index.tsx:36
msgid "Recipient"
msgstr "Ontvanger"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:110
#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:195
msgid "Redirecting to Stripe..."
msgstr "Doorverwijzen naar Stripe..."

#: src/components/common/OrdersTable/index.tsx:204
#: src/components/common/OrdersTable/index.tsx:274
msgid "Reference"
msgstr "Referentie"

#: src/components/modals/RefundOrderModal/index.tsx:104
msgid "Refund amount ({0})"
msgstr "Restitutiebedrag ({0})"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:52
msgid "Refund Failed"
msgstr "Terugbetaling mislukt"

#: src/components/common/OrdersTable/index.tsx:172
msgid "Refund order"
msgstr "Bestelling terugbetalen"

#: src/components/modals/RefundOrderModal/index.tsx:151
msgid "Refund Order"
msgstr "Terugbetaling"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:51
msgid "Refund Pending"
msgstr "Restitutie in behandeling"

#: src/components/routes/event/orders.tsx:51
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:128
msgid "Refund Status"
msgstr "Restitutie Status"

#: src/components/common/OrderSummary/index.tsx:80
#: src/components/common/StatBoxes/index.tsx:33
#: src/components/routes/event/orders.tsx:29
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:53
msgid "Refunded"
msgstr "Terugbetaald"

#: src/components/routes/auth/Register/index.tsx:129
msgid "Register"
msgstr "Registreer"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:82
msgid "Remaining Uses"
msgstr "Overblijvend gebruik"

#: src/components/routes/product-widget/SelectProducts/index.tsx:504
msgid "remove"
msgstr "verwijderen"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:140
#: src/components/routes/product-widget/SelectProducts/index.tsx:505
msgid "Remove"
msgstr "Verwijder"

#~ msgid "Reply to email"
#~ msgstr "E-mail beantwoorden"

#: src/components/layouts/Event/index.tsx:92
#: src/components/routes/event/Reports/index.tsx:39
msgid "Reports"
msgstr "Rapporten"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:169
msgid "Require Billing Address"
msgstr "Factuuradres vereisen"

#: src/components/routes/event/GettingStarted/index.tsx:197
msgid "Resend confirmation email"
msgstr "Bevestigingsmail opnieuw verzenden"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resend email confirmation"
msgstr "E-mailbevestiging opnieuw verzenden"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Resend invitation"
msgstr "Uitnodiging opnieuw versturen"

#: src/components/common/OrdersTable/index.tsx:179
msgid "Resend order email"
msgstr "E-mail met bestelling opnieuw verzenden"

#~ msgid "Resend product email"
#~ msgstr "Producte-mail opnieuw verzenden"

#: src/components/common/AttendeeTable/index.tsx:171
msgid "Resend ticket email"
msgstr "Ticket opnieuw verzenden"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resending..."
msgstr "Opnieuw verzenden..."

#: src/components/common/FilterModal/index.tsx:248
msgid "Reset"
msgstr "Reset"

#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Reset password"
msgstr "Wachtwoord opnieuw instellen"

#: src/components/routes/auth/ResetPassword/index.tsx:54
msgid "Reset Password"
msgstr "Wachtwoord opnieuw instellen"

#: src/components/routes/auth/ForgotPassword/index.tsx:50
msgid "Reset your password"
msgstr "Wachtwoord opnieuw instellen"

#: src/components/common/EventCard/index.tsx:167
msgid "Restore event"
msgstr "Evenement herstellen"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:50
#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:63
msgid "Return to event page"
msgstr "Terug naar de evenementpagina"

#: src/components/layouts/Checkout/index.tsx:165
msgid "Return to Event Page"
msgstr "Terug naar evenementpagina"

#: src/components/routes/event/EventDashboard/index.tsx:249
msgid "Revenue"
msgstr "Inkomsten"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Revoke invitation"
msgstr "Uitnodiging intrekken"

#: src/components/modals/EditUserModal/index.tsx:93
#: src/components/modals/InviteUserModal/index.tsx:64
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Role"
msgstr "Rol"

#: src/components/forms/ProductForm/index.tsx:96
#: src/components/forms/ProductForm/index.tsx:391
msgid "Sale End Date"
msgstr "Einddatum verkoop"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:75
msgid "Sale ended"
msgstr "Verkoop beëindigd"

#: src/components/forms/ProductForm/index.tsx:91
#: src/components/forms/ProductForm/index.tsx:389
msgid "Sale Start Date"
msgstr "Startdatum verkoop"

#~ msgid "Sale starts"
#~ msgstr "Verkoop begint"

#: src/components/common/ProductPriceAvailability/index.tsx:13
#: src/components/common/ProductPriceAvailability/index.tsx:35
msgid "Sales ended"
msgstr "Verkoop beëindigd"

#: src/components/common/ProductPriceAvailability/index.tsx:19
#: src/components/common/ProductPriceAvailability/index.tsx:40
msgid "Sales start"
msgstr "Start verkoop"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:135
msgid "San Francisco"
msgstr "San Francisco"

#: src/components/common/QuestionAndAnswerList/index.tsx:142
#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:80
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:114
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:96
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:166
#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:94
#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:265
#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:85
msgid "Save"
msgstr "Opslaan"

#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/event/HomepageDesigner/index.tsx:166
msgid "Save Changes"
msgstr "Wijzigingen opslaan"

#: src/components/modals/EditOrganizerModal/index.tsx:70
msgid "Save Organizer"
msgstr "Organisator opslaan"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:90
msgid "Save Settings"
msgstr "Instellingen opslaan"

#: src/components/layouts/CheckIn/index.tsx:398
#: src/components/layouts/CheckIn/index.tsx:400
msgid "Scan QR Code"
msgstr "QR-code scannen"

#: src/components/modals/ShareModal/index.tsx:229
msgid "Scan this QR code to access the event page or share it with others"
msgstr "Scan deze QR-code om de evenementpagina te openen of te delen met anderen"

#~ msgid "Seach by name, order #, attendee # or email..."
#~ msgstr "Zoek op naam, bestelnummer, aanwezigheidsnummer of e-mail..."

#: src/components/routes/event/attendees.tsx:64
msgid "Search by attendee name, email or order #..."
msgstr "Zoek op naam van een deelnemer, e-mail of bestelnummer..."

#~ msgid "Search by event name or description..."
#~ msgstr "Zoeken op evenementnaam of beschrijving..."

#: src/components/routes/events/Dashboard/index.tsx:69
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:66
msgid "Search by event name..."
msgstr "Zoeken op evenementnaam..."

#~ msgid "Search by name or description..."
#~ msgstr "Zoeken op naam of beschrijving..."

#: src/components/routes/event/orders.tsx:126
msgid "Search by name, email, or order #..."
msgstr "Zoeken op naam, e-mail of bestelnummer..."

#: src/components/layouts/CheckIn/index.tsx:394
msgid "Search by name, order #, attendee # or email..."
msgstr "Zoek op naam, ordernummer, aanwezigheidsnummer of e-mail..."

#: src/components/routes/event/promo-codes.tsx:34
msgid "Search by name..."
msgstr "Zoeken op naam..."

#~ msgid "Search by order #, name or email..."
#~ msgstr "Zoeken op bestelnummer, naam of e-mail..."

#~ msgid "Search by product name..."
#~ msgstr "Zoeken op productnaam..."

#~ msgid "Search by subject or body..."
#~ msgstr "Zoeken op onderwerp of lichaam..."

#: src/components/routes/event/messages.tsx:34
msgid "Search by subject or content..."
msgstr "Zoeken op onderwerp of inhoud..."

#~ msgid "Search by ticket name..."
#~ msgstr "Zoeken op ticketnaam..."

#: src/components/routes/event/CapacityAssignments/index.tsx:37
msgid "Search capacity assignments..."
msgstr "Zoek capaciteitstoewijzingen..."

#: src/components/routes/event/CheckInLists/index.tsx:37
msgid "Search check-in lists..."
msgstr "Check-in lijsten doorzoeken..."

#: src/components/routes/event/products.tsx:52
msgid "Search products"
msgstr "Producten zoeken"

#: src/components/common/SearchBar/index.tsx:36
msgid "Search..."
msgstr "Zoeken..."

#: src/components/routes/event/HomepageDesigner/index.tsx:160
msgid "Secondary color"
msgstr "Secundaire kleur"

#: src/components/common/WidgetEditor/index.tsx:198
msgid "Secondary Color"
msgstr "Secundaire kleur"

#: src/components/routes/event/HomepageDesigner/index.tsx:162
msgid "Secondary text color"
msgstr "Secundaire tekstkleur"

#: src/components/common/WidgetEditor/index.tsx:206
msgid "Secondary Text Color"
msgstr "Secundaire tekstkleur"

#: src/components/common/FilterModal/index.tsx:150
msgid "Select {0}"
msgstr "Kies {0}"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:214
msgid "Select Camera"
msgstr "Camera selecteren"

#: src/components/forms/ProductForm/index.tsx:269
msgid "Select category..."
msgstr "Selecteer categorie..."

#: src/components/forms/WebhookForm/index.tsx:124
msgid "Select event types"
msgstr "Soorten evenementen selecteren"

#: src/components/modals/CreateEventModal/index.tsx:110
msgid "Select organizer"
msgstr "Organisator selecteren"

#: src/components/modals/ManageAttendeeModal/index.tsx:118
msgid "Select Product"
msgstr "Kies product"

#: src/components/common/ProductSelector/index.tsx:65
msgid "Select Product Tier"
msgstr "Selecteer productcategorie"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:49
#: src/components/modals/SendMessageModal/index.tsx:219
msgid "Select products"
msgstr "Selecteer producten"

#: src/components/modals/EditUserModal/index.tsx:104
msgid "Select status"
msgstr "Selecteer status"

#: src/components/modals/CreateAttendeeModal/index.tsx:150
msgid "Select Ticket"
msgstr "Selecteer ticket"

#~ msgid "Select Ticket Tier"
#~ msgstr "Selecteer ticketniveau"

#: src/components/forms/CheckInListForm/index.tsx:26
#: src/components/modals/SendMessageModal/index.tsx:207
msgid "Select tickets"
msgstr "Kies tickets"

#~ msgid "Select Tickets"
#~ msgstr "Kies tickets"

#: src/components/common/ReportTable/index.tsx:261
msgid "Select time period"
msgstr "Selecteer tijdsperiode"

#: src/components/forms/WebhookForm/index.tsx:123
msgid "Select which events will trigger this webhook"
msgstr "Selecteer welke evenementen deze webhook activeren"

#: src/components/forms/ProductForm/index.tsx:352
msgid "Select..."
msgstr "Selecteer..."

#: src/components/layouts/AuthLayout/index.tsx:68
msgid "Sell Anything"
msgstr "Verkoop alles"

#: src/components/layouts/AuthLayout/index.tsx:69
msgid "Sell merchandise alongside tickets with integrated tax and promo code support"
msgstr "Verkoop merchandise naast tickets met geïntegreerde ondersteuning voor btw en promotiecodes"

#: src/components/layouts/AuthLayout/index.tsx:42
msgid "Sell More Than Tickets"
msgstr "Meer verkopen dan alleen kaartjes"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send"
msgstr "Stuur"

#: src/components/modals/SendMessageModal/index.tsx:259
msgid "Send a copy to <0>{0}</0>"
msgstr "Stuur een kopie naar <0>{0}</0>"

#: src/components/modals/SendMessageModal/index.tsx:139
msgid "Send a message"
msgstr "Stuur een bericht"

#: src/components/modals/SendMessageModal/index.tsx:269
msgid "Send as a test. This will send the message to your email address instead of the recipients."
msgstr "Verzenden als test. Hierdoor wordt het bericht naar jouw e-mailadres gestuurd in plaats van naar de ontvangers."

#~ msgid "Send confirmation email"
#~ msgstr "Bevestigingse-mail verzenden"

#: src/components/routes/event/messages.tsx:41
msgid "Send Message"
msgstr "Verstuur bericht"

#~ msgid "Send order confirmation and product email"
#~ msgstr "Bestellingsbevestiging en producte-mail verzenden"

#: src/components/modals/CreateAttendeeModal/index.tsx:192
msgid "Send order confirmation and ticket email"
msgstr "Verzend orderbevestiging en ticket e-mail"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send Test"
msgstr "Test verzenden"

#: src/components/routes/event/Settings/index.tsx:46
msgid "SEO"
msgstr "SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:70
msgid "SEO Description"
msgstr "SEO Beschrijving"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:76
msgid "SEO Keywords"
msgstr "SEO Trefwoorden"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:56
msgid "SEO Settings"
msgstr "SEO-instellingen"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:64
msgid "SEO Title"
msgstr "SEO titel"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "Service Fee"
msgstr "Servicevergoeding"

#~ msgid "Set a minimum price and let users donate more"
#~ msgstr "Stel een minimumprijs in en laat gebruikers meer doneren"

#: src/components/forms/ProductForm/index.tsx:159
msgid "Set a minimum price and let users pay more if they choose"
msgstr "Stel een minimumprijs in en laat gebruikers meer betalen als ze dat willen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:207
msgid "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."
msgstr "Stel het startnummer voor factuurnummering in. Dit kan niet worden gewijzigd als de facturen eenmaal zijn gegenereerd."

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Set up your event"
msgstr "Uw evenement opzetten"

#~ msgid "Set up your payment processing to receive funds from ticket sales."
#~ msgstr "Stel je betalingsverwerking in om geld te ontvangen van ticketverkoop."

#: src/components/routes/event/GettingStarted/index.tsx:183
msgid "Set your event live"
msgstr "Je evenement live zetten"

#: src/components/layouts/Event/index.tsx:98
#: src/components/routes/event/Settings/index.tsx:103
msgid "Settings"
msgstr "Instellingen"

#: src/components/layouts/AuthLayout/index.tsx:26
msgid "Setup in Minutes"
msgstr "Set-up in enkele minuten"

#: src/components/common/ShareIcon/index.tsx:28
#: src/components/modals/ShareModal/index.tsx:123
#: src/components/modals/ShareModal/index.tsx:141
msgid "Share"
msgstr "Deel"

#: src/components/layouts/Event/index.tsx:251
#: src/components/modals/ShareModal/index.tsx:116
msgid "Share Event"
msgstr "Evenement delen"

#: src/components/modals/ShareModal/index.tsx:169
msgid "Share to Facebook"
msgstr "Delen naar Facebook"

#: src/components/modals/ShareModal/index.tsx:163
msgid "Share to LinkedIn"
msgstr "Delen naar LinkedIn"

#: src/components/modals/ShareModal/index.tsx:193
msgid "Share to Pinterest"
msgstr "Delen naar Pinterest"

#: src/components/modals/ShareModal/index.tsx:187
msgid "Share to Reddit"
msgstr "Delen naar Reddit"

#: src/components/modals/ShareModal/index.tsx:148
msgid "Share to Social"
msgstr "Delen naar sociale media"

#: src/components/modals/ShareModal/index.tsx:181
msgid "Share to Telegram"
msgstr "Delen naar Telegram"

#: src/components/modals/ShareModal/index.tsx:175
msgid "Share to WhatsApp"
msgstr "Delen naar WhatsApp"

#: src/components/modals/ShareModal/index.tsx:157
msgid "Share to X"
msgstr "Delen naar X"

#: src/components/modals/ShareModal/index.tsx:199
msgid "Share via Email"
msgstr "Delen via e-mail"

#~ msgid "Should this {type} be applied to all new tickets?"
#~ msgstr "Moet dit {type} worden toegepast op alle nieuwe tickets?"

#: src/components/forms/ProductForm/index.tsx:344
msgid "Show"
msgstr "Toon"

#: src/components/forms/ProductForm/index.tsx:403
msgid "Show available product quantity"
msgstr "Beschikbare producthoeveelheid tonen"

#~ msgid "Show available ticket quantity"
#~ msgstr "Toon beschikbaar ticketaantal"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Show hidden questions"
msgstr "Verborgen vragen tonen"

#: src/components/routes/product-widget/SelectProducts/index.tsx:459
msgid "Show more"
msgstr "Meer tonen"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:60
msgid "Show tax and fees separately"
msgstr "Belastingen en toeslagen apart weergeven"

#~ msgid "Shown to the customer after they checkout, on the order summary page"
#~ msgstr "Weergegeven aan de klant na het afrekenen, op de overzichtspagina van de bestelling"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
msgid "Shown to the customer after they checkout, on the order summary page."
msgstr "Wordt aan de klant getoond nadat hij heeft afgerekend, op de overzichtspagina van de bestelling."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:69
msgid "Shown to the customer before they checkout"
msgstr "Aan de klant getoond voordat hij afrekent"

#: src/components/forms/QuestionForm/index.tsx:132
msgid "Shows common address fields, including country"
msgstr "Toont algemene adresvelden, inclusief land"

#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:151
msgid "Simpson"
msgstr "Simpson"

#: src/components/forms/QuestionForm/index.tsx:100
msgid "Single line text box"
msgstr "Enkelregelig tekstvak"

#: src/components/routes/welcome/index.tsx:137
msgid "Skip this step"
msgstr "Deze stap overslaan"

#: src/components/layouts/AuthLayout/index.tsx:78
msgid "Smart Check-in"
msgstr "Slim inchecken"

#: src/components/layouts/AuthLayout/index.tsx:53
#~ msgid "Smart Dashboard"
#~ msgstr "Slim Dashboard"

#: src/components/routes/auth/Register/index.tsx:90
msgid "Smith"
msgstr "Jansen"

#: src/components/common/ProductPriceAvailability/index.tsx:9
#: src/components/common/ProductPriceAvailability/index.tsx:32
msgid "Sold out"
msgstr "Uitverkocht"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:67
msgid "Sold Out"
msgstr "Uitverkocht"

#: src/components/common/ErrorDisplay/index.tsx:14
#: src/components/routes/auth/AcceptInvitation/index.tsx:47
msgid "Something went wrong"
msgstr "Er ging iets mis"

#: src/components/common/TaxAndFeeList/index.tsx:39
msgid "Something went wrong while deleting the Tax or Fee"
msgstr "Er is iets misgegaan bij het verwijderen van de Belasting of Belastinggeld"

#: src/components/routes/auth/ForgotPassword/index.tsx:31
msgid "Something went wrong, please try again, or contact support if the problem persists"
msgstr "Er is iets misgegaan. Probeer het opnieuw of neem contact op met support als het probleem zich blijft voordoen"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Something went wrong! Please try again"
msgstr "Er ging iets mis! Probeer het opnieuw"

#: src/components/forms/StripeCheckoutForm/index.tsx:69
msgid "Something went wrong."
msgstr "Er ging iets mis."

#: src/components/routes/profile/ManageProfile/index.tsx:80
#: src/components/routes/profile/ManageProfile/index.tsx:94
msgid "Something went wrong. Please try again."
msgstr "Er is iets misgegaan. Probeer het opnieuw."

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:61
msgid "Sorry, something has gone wrong. Please restart the checkout process."
msgstr "Sorry, er is iets misgegaan. Start het betalingsproces opnieuw."

#: src/components/routes/product-widget/CollectInformation/index.tsx:259
msgid "Sorry, something went wrong loading this page."
msgstr "Sorry, er ging iets mis bij het laden van deze pagina."

#: src/components/routes/product-widget/CollectInformation/index.tsx:247
msgid "Sorry, this order no longer exists."
msgstr "Sorry, deze bestelling bestaat niet meer."

#~ msgid "Sorry, this promo code is invalid'"
#~ msgstr "Sorry, deze promotiecode is ongeldig'."

#: src/components/routes/product-widget/SelectProducts/index.tsx:246
msgid "Sorry, this promo code is not recognized"
msgstr "Sorry, deze promotiecode wordt niet herkend"

#~ msgid "Sorry, your order has expired. Please start a new order."
#~ msgstr "Sorry, je bestelling is verlopen. Begin een nieuwe bestelling."

#~ msgid "Sorting is disabled while filters and sorting are applied"
#~ msgstr "Sorteren is uitgeschakeld terwijl filters en sorteren worden toegepast"

#: src/components/common/LanguageSwitcher/index.tsx:18
msgid "Spanish"
msgstr "Spaans"

#: src/components/forms/ProductForm/index.tsx:147
msgid "Standard product with a fixed price"
msgstr "Standaardproduct met een vaste prijs"

#~ msgid "Standard ticket with a fixed price"
#~ msgstr "Standaard ticket met een vaste prijs"

#: src/components/modals/CreateEventModal/index.tsx:144
#: src/components/modals/DuplicateEventModal/index.tsx:93
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:87
#: src/components/routes/welcome/index.tsx:90
msgid "Start Date"
msgstr "Startdatum"

#~ msgid "State"
#~ msgstr "Staat"

#: src/components/common/CheckoutQuestion/index.tsx:165
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:139
#: src/components/routes/product-widget/CollectInformation/index.tsx:339
#: src/components/routes/product-widget/CollectInformation/index.tsx:340
msgid "State or Region"
msgstr "Staat of regio"

#: src/components/common/AttendeeTable/index.tsx:112
#: src/components/common/OrderDetails/index.tsx:51
#: src/components/common/OrdersTable/index.tsx:209
#: src/components/common/ProductsTable/SortableProduct/index.tsx:222
#: src/components/common/WebhookTable/index.tsx:238
#: src/components/forms/CapaciyAssigmentForm/index.tsx:56
#: src/components/forms/WebhookForm/index.tsx:133
#: src/components/modals/EditUserModal/index.tsx:103
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:88
msgid "Status"
msgstr "Status"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:85
msgid "Stripe"
msgstr "Stripe"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:48
msgid "Stripe payments are not enabled for this event."
msgstr "Stripe-betalingen zijn niet ingeschakeld voor dit evenement."

#: src/components/modals/SendMessageModal/index.tsx:245
msgid "Subject"
msgstr "Onderwerp"

#: src/components/common/OrderSummary/index.tsx:41
msgid "Subtotal"
msgstr "Subtotaal"

#: src/components/common/OrdersTable/index.tsx:115
#: src/components/layouts/Checkout/index.tsx:53
msgid "Success"
msgstr "Succes"

#: src/components/modals/EditUserModal/index.tsx:40
#: src/components/modals/InviteUserModal/index.tsx:32
msgid "Success! {0} will receive an email shortly."
msgstr "Succes! {0} ontvangt binnenkort een e-mail."

#: src/components/common/AttendeeTable/index.tsx:90
msgid "Successfully {0} attendee"
msgstr "Succesvol {0} deelnemer"

#~ msgid "Successfully ${0} attendee"
#~ msgstr "Succesvol ${0} deelnemer"

#~ msgid "Successfully checked <0>{0} {1}</0> {2}"
#~ msgstr "Met succes gecontroleerd <0>{0} {1}</0> {2}"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:23
msgid "Successfully confirmed email address"
msgstr "E-mailadres succesvol bevestigd"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:31
msgid "Successfully confirmed email change"
msgstr "E-mailwijziging succesvol bevestigd"

#: src/components/modals/CreateAttendeeModal/index.tsx:82
msgid "Successfully created attendee"
msgstr "Succesvol aangemaakte deelnemer"

#: src/components/modals/CreateProductModal/index.tsx:54
msgid "Successfully Created Product"
msgstr "Succesvol gecreëerd product"

#: src/components/modals/CreatePromoCodeModal/index.tsx:39
msgid "Successfully Created Promo Code"
msgstr "Succesvol aangemaakte promotiecode"

#: src/components/modals/CreateQuestionModal/index.tsx:45
#: src/components/modals/EditQuestionModal/index.tsx:66
msgid "Successfully Created Question"
msgstr "Succesvol aangemaakte vraag"

#~ msgid "Successfully Created Ticket"
#~ msgstr "Succesvol aangemaakt ticket"

#~ msgid "Successfully deleted ticket"
#~ msgstr "Ticket succesvol verwijderd"

#: src/components/modals/DuplicateProductModal/index.tsx:95
msgid "Successfully Duplicated Product"
msgstr "Succesvol gedupliceerd product"

#: src/components/modals/ManageAttendeeModal/index.tsx:96
msgid "Successfully updated attendee"
msgstr "Deelnemer succesvol bijgewerkt"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:47
msgid "Successfully updated Capacity Assignment"
msgstr "Capaciteitstoewijzing succesvol bijgewerkt"

#: src/components/modals/EditCheckInListModal/index.tsx:49
msgid "Successfully updated Check-In List"
msgstr "Check-in lijst succesvol bijgewerkt"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:43
msgid "Successfully Updated Email Settings"
msgstr "E-mailinstellingen met succes bijgewerkt"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:54
msgid "Successfully Updated Event"
msgstr "Evenement succesvol bijgewerkt"

#: src/components/routes/event/HomepageDesigner/index.tsx:84
msgid "Successfully Updated Homepage Design"
msgstr "Succesvol vernieuwd homepage-ontwerp"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:50
msgid "Successfully Updated Homepage Settings"
msgstr "Homepage-instellingen met succes bijgewerkt"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:73
msgid "Successfully Updated Location"
msgstr "Locatie succesvol bijgewerkt"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:43
msgid "Successfully Updated Misc Settings"
msgstr "Misc-instellingen met succes bijgewerkt"

#: src/components/modals/ManageOrderModal/index.tsx:74
msgid "Successfully updated order"
msgstr "Bestelling succesvol bijgewerkt"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:74
msgid "Successfully Updated Payment & Invoicing Settings"
msgstr "Instellingen voor betalen en factureren succesvol bijgewerkt"

#: src/components/modals/EditProductModal/index.tsx:89
msgid "Successfully updated product"
msgstr "Product succesvol bijgewerkt"

#: src/components/modals/EditPromoCodeModal/index.tsx:52
msgid "Successfully Updated Promo Code"
msgstr "Succesvol bijgewerkte promotiecode"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:45
msgid "Successfully Updated Seo Settings"
msgstr "Succesvol bijgewerkte Seo-instellingen"

#~ msgid "Successfully updated ticket"
#~ msgstr "Succesvol bijgewerkt ticket"

#: src/components/modals/EditWebhookModal/index.tsx:44
msgid "Successfully updated Webhook"
msgstr "Webhook succesvol bijgewerkt"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:128
msgid "Suite 100"
msgstr "Suite 100"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
msgid "Support Email"
msgstr "Ondersteuning per e-mail"

#: src/components/layouts/AuthLayout/index.tsx:59
msgid "Support for tiered, donation-based, and product sales with customizable pricing and capacity"
msgstr "Ondersteuning voor gelaagde, op donaties gebaseerde en productverkoop met aanpasbare prijzen en capaciteit"

#: src/components/forms/ProductForm/index.tsx:254
msgid "T-shirt"
msgstr "T-shirt"

#: src/components/forms/TaxAndFeeForm/index.tsx:12
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Tax"
msgstr "Belasting"

#: src/components/routes/account/ManageAccount/index.tsx:27
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:16
msgid "Tax & Fees"
msgstr "Belastingen en heffingen"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:243
msgid "Tax Details"
msgstr "Belastingdetails"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:244
msgid "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"
msgstr "Belastinginformatie die onderaan alle facturen moet staan (bijv. btw-nummer, belastingregistratie)"

#: src/components/common/TaxAndFeeList/index.tsx:36
msgid "Tax or Fee deleted successfully"
msgstr "Belasting of vergoeding succesvol verwijderd"

#: src/components/forms/ProductForm/index.tsx:363
msgid "Taxes"
msgstr "Belastingen"

#~ msgid "Taxes & Fees"
#~ msgstr "Belastingen en heffingen"

#: src/components/forms/ProductForm/index.tsx:351
#: src/components/forms/ProductForm/index.tsx:358
msgid "Taxes and Fees"
msgstr "Belastingen en heffingen"

#~ msgid "Taxes and Fees can be associated with tickets and will be added to the ticket price."
#~ msgstr "Belastingen en toeslagen kunnen worden gekoppeld aan tickets en worden toegevoegd aan de ticketprijs."

#~ msgid "Text Colour"
#~ msgstr "Tekstkleur"

#: src/components/routes/product-widget/SelectProducts/index.tsx:138
#: src/components/routes/product-widget/SelectProducts/index.tsx:186
msgid "That promo code is invalid"
msgstr "Die promotiecode is ongeldig"

#~ msgid "The background color for the event homepage"
#~ msgstr "De achtergrondkleur voor de homepage van het evenement"

#~ msgid "The background color for the ticket widget on the event homepage"
#~ msgstr "De achtergrondkleur voor de ticketwidget op de homepage van het evenement"

#~ msgid "The basic details of your event"
#~ msgstr "De basisgegevens van je evenement"

#: src/components/layouts/CheckIn/index.tsx:316
msgid "The check-in list you are looking for does not exist."
msgstr "De check-in lijst die je zoekt bestaat niet."

#~ msgid "The color of the buttons on the event homepage"
#~ msgstr "De kleur van de knoppen op de homepage van het evenement"

#~ msgid "The color of the links on the event homepage"
#~ msgstr "De kleur van de links op de homepage van het evenement"

#: src/components/forms/OrganizerForm/index.tsx:47
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:75
msgid "The default currency for your events."
msgstr "De standaardvaluta voor je evenementen."

#: src/components/forms/OrganizerForm/index.tsx:56
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:84
msgid "The default timezone for your events."
msgstr "De standaard tijdzone voor je evenementen."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:16
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:43
msgid "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."
msgstr "Het evenement dat je zoekt is momenteel niet beschikbaar. Mogelijk is het verwijderd, verlopen of is de URL onjuist."

#: src/components/modals/CreateAttendeeModal/index.tsx:146
msgid "The language the attendee will receive emails in."
msgstr "De taal waarin de deelnemer e-mails ontvangt."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:22
msgid "The link you clicked is invalid."
msgstr "De link waarop je hebt geklikt is ongeldig."

#~ msgid "The location of your event"
#~ msgstr "De locatie van uw evenement"

#: src/components/routes/product-widget/SelectProducts/index.tsx:444
msgid "The maximum number of products for {0}is {1}"
msgstr "Het maximum aantal producten voor {0}is {1}"

#~ msgid "The maximum numbers number of tickets for {0}is {1}"
#~ msgstr "Het maximumaantal tickets voor {0} is {1}"

#~ msgid "The maximum numbers number of tickets for Generals is {0}"
#~ msgstr "Het maximumaantal tickets voor Generaals is {0}"

#~ msgid "The organizer details of your event"
#~ msgstr "De organisatorgegevens van je evenement"

#: src/components/common/ErrorDisplay/index.tsx:17
msgid "The page you are looking for does not exist"
msgstr "De pagina die u zoekt bestaat niet"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:56
msgid "The price displayed to the customer will include taxes and fees."
msgstr "De prijs die aan de klant wordt getoond is inclusief belastingen en toeslagen."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:62
msgid "The price displayed to the customer will not include taxes and fees. They will be shown separately"
msgstr "De prijs die aan de klant wordt getoond is exclusief belastingen en toeslagen. Deze worden apart weergegeven"

#: src/components/common/WidgetEditor/index.tsx:169
msgid "The styling settings you choose apply only to copied HTML and won't be stored."
msgstr "De stylinginstellingen die je kiest, zijn alleen van toepassing op gekopieerde HTML en worden niet opgeslagen."

#~ msgid "The tax and fee to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "De belasting en toeslag die van toepassing zijn op dit ticket. Je kunt nieuwe belastingen en toeslagen aanmaken op de"

#: src/components/forms/ProductForm/index.tsx:355
msgid "The taxes and fees to apply to this product. You can create new taxes and fees on the"
msgstr "De belastingen en toeslagen die van toepassing zijn op dit product. U kunt nieuwe belastingen en kosten aanmaken op de"

#~ msgid "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "De belastingen en toeslagen die van toepassing zijn op dit ticket. Je kunt nieuwe belastingen en toeslagen aanmaken op de"

#~ msgid "The text color for the event homepage"
#~ msgstr "De tekstkleur voor de homepage van het evenement"

#~ msgid "The text color for the ticket widget on the event homepage"
#~ msgstr "De tekstkleur voor de ticketwidget op de homepage van het evenement"

#~ msgid "The text to display in the 'Continue' button. Defaults to 'Continue'"
#~ msgstr "De tekst die moet worden weergegeven in de knop 'Doorgaan'. Standaard ingesteld op 'Doorgaan'."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:63
msgid "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"
msgstr "De titel van het evenement die zal worden weergegeven in zoekmachineresultaten en bij het delen op sociale media. Standaard wordt de titel van het evenement gebruikt"

#~ msgid "The user must login to change their email."
#~ msgstr "De gebruiker moet inloggen om zijn e-mailadres te wijzigen."

#: src/components/routes/product-widget/SelectProducts/index.tsx:272
msgid "There are no products available for this event"
msgstr "Er zijn geen producten beschikbaar voor dit evenement"

#: src/components/routes/product-widget/SelectProducts/index.tsx:375
msgid "There are no products available in this category"
msgstr "Er zijn geen producten beschikbaar in deze categorie"

#~ msgid "There are no tickets available for this event"
#~ msgstr "Er zijn geen tickets beschikbaar voor dit evenement"

#: src/components/modals/RefundOrderModal/index.tsx:137
msgid "There is a refund pending. Please wait for it to complete before requesting another refund."
msgstr "Er is een restitutie in behandeling. Wacht tot deze is voltooid voordat je een nieuwe restitutie aanvraagt."

#~ msgid "There was an error loading this content. Please refresh the page and try again."
#~ msgstr "Er is een fout opgetreden bij het laden van deze inhoud. Vernieuw de pagina en probeer het opnieuw."

#: src/components/common/OrdersTable/index.tsx:80
msgid "There was an error marking the order as paid"
msgstr "Er is een fout opgetreden bij het markeren van de bestelling als betaald"

#: src/hooks/useFormErrorResponseHandler.tsx:28
msgid "There was an error processing your request. Please try again."
msgstr "Er is een fout opgetreden bij het verwerken van uw verzoek. Probeer het opnieuw."

#: src/components/common/OrdersTable/index.tsx:95
msgid "There was an error sending your message"
msgstr "Er is een fout opgetreden bij het verzenden van uw bericht"

#~ msgid "These allow multiple selections"
#~ msgstr "Deze maken meerdere selecties mogelijk"

#~ msgid "These colors are not saved in our system."
#~ msgstr "Deze kleuren worden niet opgeslagen in ons systeem."

#~ msgid "These colors are not saved in our system. They are only used to generate the widget."
#~ msgstr "Deze kleuren worden niet opgeslagen in ons systeem. Ze worden alleen gebruikt om de widget te genereren."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:105
msgid "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."
msgstr "Deze gegevens worden alleen weergegeven als de bestelling succesvol is afgerond. Bestellingen die op betaling wachten, krijgen dit bericht niet te zien."

#: src/components/layouts/CheckIn/index.tsx:201
msgid "This attendee has an unpaid order."
msgstr "Deze deelnemer heeft een onbetaalde bestelling."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:61
msgid "This category doesn't have any products yet."
msgstr "Deze categorie heeft nog geen producten."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:112
msgid "This category is hidden from public view"
msgstr "Deze categorie is niet zichtbaar voor het publiek"

#: src/components/common/CheckInListList/index.tsx:75
msgid "This check-in list has expired"
msgstr "Deze check-in lijst is verlopen"

#: src/components/layouts/CheckIn/index.tsx:331
msgid "This check-in list has expired and is no longer available for check-ins."
msgstr "Deze check-ins lijst is verlopen en niet langer beschikbaar voor check-ins."

#: src/components/common/CheckInListList/index.tsx:82
msgid "This check-in list is active"
msgstr "Deze check-in lijst is actief"

#: src/components/common/CheckInListList/index.tsx:79
msgid "This check-in list is not active yet"
msgstr "Deze check-in lijst is nog niet actief"

#: src/components/layouts/CheckIn/index.tsx:348
msgid "This check-in list is not yet active and is not available for check-ins."
msgstr "Deze check-ins lijst is nog niet actief en is niet beschikbaar voor check-ins."

#: src/components/forms/CheckInListForm/index.tsx:37
msgid "This description will be shown to the check-in staff"
msgstr "Deze beschrijving wordt getoond aan het incheckpersoneel"

#: src/components/modals/SendMessageModal/index.tsx:285
msgid "This email is not promotional and is directly related to the event."
msgstr "Deze e-mail is niet promotioneel en is direct gerelateerd aan het evenement."

#~ msgid "This event is not available at the moment. Please check back later."
#~ msgstr "Dit evenement is momenteel niet beschikbaar. Kom later nog eens terug."

#~ msgid "This event is not available."
#~ msgstr "Dit evenement is niet beschikbaar."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
msgid "This information will be shown on the payment page, order summary page, and order confirmation email."
msgstr "Deze informatie wordt weergegeven op de betaalpagina, de pagina met het overzicht van de bestelling en de e-mail ter bevestiging van de bestelling."

#: src/components/forms/ProductForm/index.tsx:138
msgid "This is a general product, like a t-shirt or a mug. No ticket will be issued"
msgstr "Dit is een algemeen product, zoals een t-shirt of een mok. Er wordt geen ticket uitgegeven"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:91
msgid "This is an online event"
msgstr "Dit is een online evenement"

#~ msgid "This is the email address that will be used as the reply-to address for all emails sent from this event"
#~ msgstr "Dit is het e-mailadres dat wordt gebruikt als antwoordadres voor alle e-mails die vanuit dit evenement worden verzonden."

#: src/components/forms/CheckInListForm/index.tsx:57
msgid "This list will no longer be available for check-ins after this date"
msgstr "Deze lijst is na deze datum niet meer beschikbaar voor check-ins"

#~ msgid "This message is how below the"
#~ msgstr "Dit bericht staat onder de"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:68
msgid "This message will be included in the footer of all emails sent from this event"
msgstr "Dit bericht wordt opgenomen in de voettekst van alle e-mails die vanuit dit evenement worden verzonden"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
msgid "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"
msgstr "Dit bericht wordt alleen weergegeven als de bestelling succesvol is afgerond. Bestellingen die op betaling wachten, krijgen dit bericht niet te zien"

#: src/components/forms/StripeCheckoutForm/index.tsx:93
msgid "This order has already been paid."
msgstr "Deze bestelling is al betaald."

#~ msgid "This order has already been paid. <0>View order details</0>"
#~ msgstr "Deze bestelling is al betaald. <0>Bekijk bestelgegevens</0>"

#~ msgid "This order has already been processed."
#~ msgstr "Deze bestelling is al verwerkt."

#: src/components/modals/RefundOrderModal/index.tsx:141
msgid "This order has already been refunded."
msgstr "Deze bestelling is al terugbetaald."

#: src/components/routes/product-widget/CollectInformation/index.tsx:237
msgid "This order has been cancelled"
msgstr "Deze bestelling is geannuleerd"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:209
msgid "This order has been cancelled."
msgstr "Deze bestelling is geannuleerd."

#~ msgid "This order has been completed."
#~ msgstr "Deze bestelling is voltooid."

#: src/components/routes/product-widget/CollectInformation/index.tsx:210
msgid "This order has expired. Please start again."
msgstr "Deze bestelling is verlopen. Begin opnieuw."

#: src/components/routes/product-widget/CollectInformation/index.tsx:221
msgid "This order is awaiting payment"
msgstr "Deze bestelling wacht op betaling"

#~ msgid "This order is awaiting payment."
#~ msgstr "Deze bestelling wacht op betaling."

#: src/components/routes/product-widget/CollectInformation/index.tsx:229
msgid "This order is complete"
msgstr "Deze bestelling is compleet"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:211
msgid "This order is complete."
msgstr "Deze bestelling is compleet."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:204
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:207
msgid "This order is processing."
msgstr "Deze bestelling wordt verwerkt."

#~ msgid "This order is processing. TODO - a nice image and poll the API"
#~ msgstr "Deze bestelling wordt verwerkt. TODO - een mooie afbeelding en peil de API"

#: src/components/forms/StripeCheckoutForm/index.tsx:103
msgid "This order page is no longer available."
msgstr "Deze bestelpagina is niet langer beschikbaar."

#: src/components/forms/ProductForm/index.tsx:415
msgid "This overrides all visibility settings and will hide the product from all customers."
msgstr "Dit overschrijft alle zichtbaarheidsinstellingen en verbergt het product voor alle klanten."

#~ msgid "This overrides all visibility settings and will hide the ticket from all customers."
#~ msgstr "Dit overschrijft alle zichtbaarheidsinstellingen en verbergt het ticket voor alle klanten."

#~ msgid "This page has expired. <0>View order details</0>"
#~ msgstr "Deze pagina is verlopen. <0>Bekijk bestelgegevens</0>"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:59
msgid "This product cannot be deleted because it is associated with an order. You can hide it instead."
msgstr "Dit product kan niet worden verwijderd omdat het gekoppeld is aan een bestelling. In plaats daarvan kunt u het verbergen."

#: src/components/forms/ProductForm/index.tsx:132
msgid "This product is a ticket. Buyers will be issued a ticket upon purchase"
msgstr "Dit product is een ticket. Kopers krijgen bij aankoop een ticket"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:215
msgid "This product is hidden from public view"
msgstr "This ticket kan niet worden verwijderd omdat het nog wordt geassosieerd met een order. Je kunt het ook verbergen"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:216
msgid "This product is hidden unless targeted by a Promo Code"
msgstr "Dit product is niet zichtbaar voor het publiek"

#: src/components/common/QuestionsTable/index.tsx:90
msgid "This question is only visible to the event organizer"
msgstr "Dit product is verborgen, tenzij er een promotiecode voor geldt"

#: src/components/routes/auth/ResetPassword/index.tsx:29
msgid "This reset password link is invalid or expired."
msgstr "Deze vraag is alleen zichtbaar voor de organisator van het evenement."

#~ msgid ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."
#~ msgstr "Deze link om het wachtwoord te resetten is ongeldig of verlopen."

#~ msgid "This ticket is hidden from public view"
#~ msgstr "Dit ticket is niet zichtbaar voor het publiek"

#~ msgid "This ticket is hidden unless targeted by a Promo Code"
#~ msgstr "Dit ticket is verborgen tenzij het is voorzien van een Promo Code"

#: src/components/modals/EditUserModal/index.tsx:65
msgid "This user is not active, as they have not accepted their invitation."
msgstr "Deze gebruiker is niet actief, omdat hij zijn uitnodiging niet heeft geaccepteerd."

#: src/components/common/AttendeeTable/index.tsx:182
msgid "ticket"
msgstr "ticket"

#: src/components/common/AttendeeTable/index.tsx:111
#: src/components/forms/ProductForm/index.tsx:130
#: src/components/modals/CreateAttendeeModal/index.tsx:151
msgid "Ticket"
msgstr "Ticket"

#~ msgid "Ticket deleted successfully"
#~ msgstr "Ticket succesvol verwijderd"

#: src/components/common/AttendeeTable/index.tsx:49
msgid "Ticket email has been resent to attendee"
msgstr "Ticket e-mail is opnieuw verzonden naar de deelnemer"

#: src/components/common/MessageList/index.tsx:22
msgid "Ticket holders"
msgstr "Tickethouders"

#: src/components/routes/event/products.tsx:86
msgid "Ticket or Product"
msgstr "Ticket of product"

#~ msgid "Ticket page message"
#~ msgstr "Bericht ticketpagina"

#~ msgid "Ticket Sales"
#~ msgstr "Kaartverkoop"

#~ msgid "Ticket Tier"
#~ msgstr "Ticketcategorie"

#~ msgid "Ticket Type"
#~ msgstr "Type ticket"

#~ msgid "Ticket widget background color"
#~ msgstr "Achtergrondkleur ticketwidget"

#~ msgid "Ticket Widget Preview"
#~ msgstr "Ticket Widget Voorbeeld"

#~ msgid "Ticket widget text color"
#~ msgstr "Tekstkleur ticketwidget"

#~ msgid "Ticket(s)"
#~ msgstr "Kaartje(s)"

#~ msgid "Tickets"
#~ msgstr "Tickets"

#: src/components/layouts/Event/index.tsx:101
#: src/components/routes/event/products.tsx:46
msgid "Tickets & Products"
msgstr "Tickets en producten"

#: src/components/routes/product-widget/PrintOrder/index.tsx:43
msgid "Tickets for"
msgstr "Tickets voor"

#~ msgid "tickets sold"
#~ msgstr "verkochte tickets"

#~ msgid "Tickets sold"
#~ msgstr "Verkochte tickets"

#~ msgid "Tickets Sold"
#~ msgstr "Verkochte tickets"

#~ msgid "Tickets sorted successfully"
#~ msgstr "Tickets succesvol gesorteerd"

#~ msgid "Tickets to which the promo code applies (Applies to all by default)"
#~ msgstr "Tickets waarop de promotiecode van toepassing is (Standaard van toepassing op alle)"

#: src/components/forms/ProductForm/index.tsx:67
msgid "Tier {0}"
msgstr "Niveau {0}"

#: src/components/forms/ProductForm/index.tsx:163
msgid "Tiered Product"
msgstr "Gelaagd product"

#~ msgid ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr "Met gelaagde producten kun je meerdere prijsopties aanbieden voor hetzelfde product. Dit is perfect voor early bird-producten of om verschillende prijsopties aan te bieden voor verschillende groepen mensen."

#: src/components/forms/ProductForm/index.tsx:243
msgid "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."
msgstr "Met gelaagde producten kun je meerdere prijsopties aanbieden voor hetzelfde product. Dit is perfect voor early bird-producten of om verschillende prijsopties aan te bieden voor verschillende groepen mensen."

#~ msgid "Tiered Ticket"
#~ msgstr "Gespreid ticket"

#~ msgid "Tiered Ticket - Coming Soon"
#~ msgstr "Gespreid ticket - Binnenkort verkrijgbaar"

#~ msgid ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr "Gelaagde tickets stellen je in staat om meerdere prijsopties aan te bieden voor hetzelfde ticket. Dit is perfect voor early bird-tickets of om verschillende prijsopties aan te bieden voor verschillende groepen mensen."

#~ msgid "Tiered tickets allow you to offer multiple price options for the same ticket. This is perfect for early bird tickets or offering different price options for different groups of people."
#~ msgstr "Gelaagde tickets stellen je in staat om meerdere prijsopties aan te bieden voor hetzelfde ticket. Dit is perfect voor early bird-tickets of om verschillende prijsopties aan te bieden voor verschillende groepen mensen."

#: src/components/layouts/Checkout/index.tsx:90
msgid "Time left:"
msgstr "Resterende tijd:"

#: src/components/common/PromoCodeTable/index.tsx:71
msgid "Times used"
msgstr "Gebruikte tijden"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:25
msgid "Times Used"
msgstr "Gebruikte tijden"

#: src/components/forms/OrganizerForm/index.tsx:54
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:82
#: src/components/routes/auth/AcceptInvitation/index.tsx:105
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:109
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:181
#: src/components/routes/profile/ManageProfile/index.tsx:161
msgid "Timezone"
msgstr "Tijdzone"

#: src/components/forms/PromoCodeForm/index.tsx:36
msgid "TIP"
msgstr "TIP"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:206
msgid "Title"
msgstr "Titel"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:182
msgid "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."
msgstr "Om creditcardbetalingen te ontvangen, moet je je Stripe-account koppelen. Stripe is onze partner voor betalingsverwerking die veilige transacties en tijdige uitbetalingen garandeert."

#: src/components/layouts/Event/index.tsx:108
msgid "Tools"
msgstr "Gereedschap"

#: src/components/common/OrderSummary/index.tsx:92
msgid "Total"
msgstr "Totaal"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:52
msgid "Total Before Discounts"
msgstr "Totaal vóór kortingen"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:58
msgid "Total Discount Amount"
msgstr "Totaal kortingsbedrag"

#: src/components/routes/event/EventDashboard/index.tsx:273
msgid "Total Fees"
msgstr "Totaal vergoedingen"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:46
msgid "Total Gross Sales"
msgstr "Totaal Brutoverkoop"

#: src/components/common/OrderDetails/index.tsx:59
#: src/components/modals/RefundOrderModal/index.tsx:74
msgid "Total order amount"
msgstr "Totaal bestelbedrag"

#: src/components/common/OrderDetails/index.tsx:67
#: src/components/modals/RefundOrderModal/index.tsx:82
msgid "Total refunded"
msgstr "Totaal terugbetaald"

#: src/components/routes/event/EventDashboard/index.tsx:276
msgid "Total Refunded"
msgstr "Totaal Terugbetaald"

#: src/components/modals/RefundOrderModal/index.tsx:90
msgid "Total remaining"
msgstr "Totaal overgebleven"

#: src/components/routes/event/EventDashboard/index.tsx:275
msgid "Total Tax"
msgstr "Totale belasting"

#: src/components/layouts/AuthLayout/index.tsx:54
msgid "Track revenue, page views, and sales with detailed analytics and exportable reports"
msgstr "Inkomsten, paginaweergaves en verkopen bijhouden met gedetailleerde analyses en exporteerbare rapporten"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
msgid "Transaction Fee:"
msgstr "Transactiekosten:"

#: src/components/forms/TaxAndFeeForm/index.tsx:44
msgid "Type"
msgstr "Type"

#~ msgid "Unable to {0} attendee"
#~ msgstr "Kan {0} deelnemer niet bereiken"

#: src/components/layouts/CheckIn/index.tsx:93
msgid "Unable to check in attendee"
msgstr "Deelnemer kan niet worden ingecheckt"

#: src/components/layouts/CheckIn/index.tsx:114
msgid "Unable to check out attendee"
msgstr "Deelnemer kan niet worden uitgecheckt"

#: src/components/modals/CreateProductModal/index.tsx:63
msgid "Unable to create product. Please check the your details"
msgstr "Kan geen product maken. Controleer uw gegevens"

#: src/components/routes/product-widget/SelectProducts/index.tsx:123
msgid "Unable to create product. Please check your details"
msgstr "Kan geen product maken. Controleer uw gegevens"

#: src/components/modals/CreateQuestionModal/index.tsx:60
msgid "Unable to create question. Please check the your details"
msgstr "Kan geen vraag maken. Controleer uw gegevens"

#~ msgid "Unable to create ticket. Please check the your details"
#~ msgstr "Kan geen ticket aanmaken. Controleer uw gegevens"

#: src/components/modals/DuplicateProductModal/index.tsx:103
msgid "Unable to duplicate product. Please check the your details"
msgstr "Kan product niet dupliceren. Controleer uw gegevens"

#: src/components/layouts/CheckIn/index.tsx:145
msgid "Unable to fetch attendee"
msgstr "Kan deelnemer niet ophalen"

#: src/components/modals/EditQuestionModal/index.tsx:84
msgid "Unable to update question. Please check the your details"
msgstr "Kan vraag niet bijwerken. Controleer uw gegevens"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:30
msgid "Unique Customers"
msgstr "Unieke klanten"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:153
msgid "United States"
msgstr "Verenigde Staten"

#: src/components/common/QuestionAndAnswerList/index.tsx:284
msgid "Unknown Attendee"
msgstr "Onbekende deelnemer"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:43
#: src/components/forms/ProductForm/index.tsx:83
#: src/components/forms/ProductForm/index.tsx:299
#: src/components/forms/PromoCodeForm/index.tsx:82
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:78
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:84
msgid "Unlimited"
msgstr "Onbeperkt"

#: src/components/routes/product-widget/SelectProducts/index.tsx:407
msgid "Unlimited available"
msgstr "Onbeperkt beschikbaar"

#~ msgid "Unlimited ticket quantity available"
#~ msgstr "Onbeperkt aantal tickets beschikbaar"

#: src/components/common/PromoCodeTable/index.tsx:126
msgid "Unlimited usages allowed"
msgstr "Onbeperkt gebruik toegestaan"

#: src/components/layouts/CheckIn/index.tsx:200
msgid "Unpaid Order"
msgstr "Onbetaalde bestelling"

#: src/components/routes/event/EventDashboard/index.tsx:170
msgid "Unpublish Event"
msgstr "Evenement niet publiceren"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:20
#: src/components/common/EventStatusBadge/index.tsx:16
msgid "Upcoming"
msgstr "Komende"

#: src/components/routes/events/Dashboard/index.tsx:55
msgid "Upcoming Events"
msgstr "Komende evenementen"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Update {0}"
msgstr "Update {0}"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:66
msgid "Update event name, description and dates"
msgstr "Naam, beschrijving en data van evenement bijwerken"

#: src/components/routes/profile/ManageProfile/index.tsx:177
msgid "Update profile"
msgstr "Profiel bijwerken"

#~ msgid "Upload an image to be displayed on the event page"
#~ msgstr "Upload een afbeelding voor weergave op de evenementpagina"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Upload Cover"
msgstr "Upload omslag"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:105
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:152
msgid "Upload Image"
msgstr "Afbeelding uploaden"

#: src/components/common/WebhookTable/index.tsx:236
#: src/components/modals/ShareModal/index.tsx:129
msgid "URL"
msgstr "URL"

#: src/components/common/PromoCodeTable/index.tsx:175
msgid "URL copied to clipboard"
msgstr "URL gekopieerd naar klembord"

#: src/components/modals/CreateWebhookModal/index.tsx:25
msgid "URL is required"
msgstr "URL is vereist"

#: src/components/common/WidgetEditor/index.tsx:298
msgid "Usage Example"
msgstr "Gebruiksvoorbeeld"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:76
msgid "Usage Limit"
msgstr "Gebruikslimiet"

#: src/components/routes/event/HomepageDesigner/index.tsx:139
msgid "Use a blurred version of the cover image as the background"
msgstr "Gebruik een onscherpe versie van de omslagafbeelding als achtergrond"

#: src/components/routes/event/HomepageDesigner/index.tsx:137
msgid "Use cover image"
msgstr "Coverafbeelding gebruiken"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User"
msgstr "Gebruiker"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User Management"
msgstr "Gebruikersbeheer"

#: src/components/routes/account/ManageAccount/index.tsx:32
msgid "Users"
msgstr "Gebruikers"

#: src/components/modals/EditUserModal/index.tsx:81
msgid "Users can change their email in <0>Profile Settings</0>"
msgstr "Gebruikers kunnen hun e-mailadres wijzigen in <0>Profielinstellingen</0>"

#: src/components/forms/OrganizerForm/index.tsx:55
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:83
#: src/components/routes/auth/AcceptInvitation/index.tsx:106
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:110
#: src/components/routes/profile/ManageProfile/index.tsx:162
msgid "UTC"
msgstr "UTC"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "VAT"
msgstr "BTW"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:116
msgid "Venue Name"
msgstr "Naam locatie"

#: src/components/common/LanguageSwitcher/index.tsx:34
msgid "Vietnamese"
msgstr "Vietnamees"

#: src/components/modals/ManageAttendeeModal/index.tsx:220
#: src/components/modals/ManageOrderModal/index.tsx:200
msgid "View"
msgstr "Bekijk"

#: src/components/routes/event/Reports/index.tsx:38
msgid "View and download reports for your event. Please note, only completed orders are included in these reports."
msgstr "Bekijk en download rapporten voor je evenement. Let op: alleen voltooide bestellingen worden opgenomen in deze rapporten."

#: src/components/common/AttendeeList/index.tsx:84
msgid "View Answers"
msgstr "Antwoorden bekijken"

#~ msgid "View attendee"
#~ msgstr "Deelnemer bekijken"

#: src/components/common/AttendeeList/index.tsx:103
msgid "View Attendee Details"
msgstr "Details van deelnemers bekijken"

#~ msgid "View event homepage"
#~ msgstr "Homepage evenement bekijken"

#: src/components/common/EventCard/index.tsx:145
msgid "View event page"
msgstr "Evenementpagina bekijken"

#: src/components/common/MessageList/index.tsx:59
msgid "View full message"
msgstr "Bekijk volledig bericht"

#: src/components/common/WebhookTable/index.tsx:113
msgid "View logs"
msgstr "Logboeken bekijken"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View map"
msgstr "Kaart bekijken"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View on Google Maps"
msgstr "Bekijk op Google Maps"

#~ msgid "View order"
#~ msgstr "Bestelling bekijken"

#: src/components/forms/StripeCheckoutForm/index.tsx:94
#: src/components/forms/StripeCheckoutForm/index.tsx:104
#: src/components/routes/product-widget/CollectInformation/index.tsx:231
msgid "View order details"
msgstr "Bestelgegevens bekijken"

#: src/components/forms/CheckInListForm/index.tsx:21
msgid "VIP check-in list"
msgstr "VIP check-in lijst"

#~ msgid "VIP Product"
#~ msgstr "VIP-product"

#: src/components/forms/ProductForm/index.tsx:254
msgid "VIP Ticket"
msgstr "VIP-ticket"

#: src/components/forms/ProductForm/index.tsx:394
msgid "Visibility"
msgstr "Zichtbaarheid"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:77
msgid "We could not process your payment. Please try again or contact support."
msgstr "We konden je betaling niet verwerken. Probeer het opnieuw of neem contact op met de klantenservice."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:53
msgid "We couldn't delete the category. Please try again."
msgstr "We konden de categorie niet verwijderen. Probeer het opnieuw."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:24
msgid "We couldn't find any tickets matching {0}"
msgstr "We konden geen tickets vinden die overeenkomen met {0}"

#: src/components/common/ErrorLoadingMessage/index.tsx:13
msgid "We couldn't load the data. Please try again."
msgstr "We konden de gegevens niet laden. Probeer het opnieuw."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:94
msgid "We couldn't reorder the categories. Please try again."
msgstr "We konden de categorieën niet opnieuw ordenen. Probeer het opnieuw."

#: src/components/routes/event/HomepageDesigner/index.tsx:118
msgid "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"
msgstr "We raden afmetingen aan van 2160px bij 1080px en een maximale bestandsgrootte van 5MB"

#~ msgid "We use Stripe to process payments. Connect your Stripe account to start receiving payments."
#~ msgstr "We gebruiken Stripe om betalingen te verwerken. Maak verbinding met je Stripe-account om betalingen te ontvangen."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:84
msgid "We were unable to confirm your payment. Please try again or contact support."
msgstr "We konden je betaling niet bevestigen. Probeer het opnieuw of neem contact op met de klantenservice."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:79
msgid "We're processing your order. Please wait..."
msgstr "We zijn je bestelling aan het verwerken. Even geduld alstublieft..."

#: src/components/modals/CreateWebhookModal/index.tsx:46
msgid "Webhook created successfully"
msgstr "Webhook succesvol aangemaakt"

#: src/components/common/WebhookTable/index.tsx:53
msgid "Webhook deleted successfully"
msgstr "Webhook succesvol verwijderd"

#: src/components/modals/WebhookLogsModal/index.tsx:151
msgid "Webhook Logs"
msgstr "Webhook logboeken"

#: src/components/forms/WebhookForm/index.tsx:117
msgid "Webhook URL"
msgstr "Webhook URL"

#: src/components/forms/WebhookForm/index.tsx:27
msgid "Webhook will not send notifications"
msgstr "Webhook verzendt geen meldingen"

#: src/components/forms/WebhookForm/index.tsx:21
msgid "Webhook will send notifications"
msgstr "Webhook stuurt meldingen"

#: src/components/layouts/Event/index.tsx:111
#: src/components/routes/event/Webhooks/index.tsx:30
msgid "Webhooks"
msgstr "Webhooks"

#: src/components/routes/auth/AcceptInvitation/index.tsx:76
msgid "Welcome aboard! Please login to continue."
msgstr "Welkom aan boord! Log in om verder te gaan."

#: src/components/routes/auth/Login/index.tsx:55
msgid "Welcome back 👋"
msgstr "Welkom terug 👋"

#: src/components/routes/event/EventDashboard/index.tsx:95
msgid "Welcome back{0} 👋"
msgstr "Welkom terug{0} 👋"

#: src/components/routes/auth/Register/index.tsx:67
msgid "Welcome to Hi.Events 👋"
msgstr "Welkom bij Hi.Events 👋"

#: src/components/routes/welcome/index.tsx:125
msgid "Welcome to Hi.Events, {0} 👋"
msgstr "Welkom bij Hi.Events, {0} 👋"

#: src/components/forms/ProductForm/index.tsx:242
msgid "What are Tiered Products?"
msgstr "Wat zijn gelaagde producten?"

#~ msgid "What are Tiered Tickets?"
#~ msgstr "Wat zijn gestaffelde tickets?"

#: src/components/forms/CheckInListForm/index.tsx:50
msgid "What date should this check-in list become active?"
msgstr "Op welke datum moet deze check-in lijst actief worden?"

#: src/components/modals/CreateProductCategoryModal/index.tsx:53
msgid "What is a Category?"
msgstr "Wat is een categorie?"

#~ msgid "What is a Tiered Ticketing?"
#~ msgstr "Wat is gelaagde ticketverkoop?"

#~ msgid "What is a webhook?"
#~ msgstr "Wat is een webhook?"

#: src/components/forms/QuestionForm/index.tsx:159
msgid "What products does this code apply to?"
msgstr "Op welke producten is deze code van toepassing?"

#: src/components/forms/PromoCodeForm/index.tsx:68
msgid "What products does this code apply to? (Applies to all by default)"
msgstr "Op welke producten is deze code van toepassing? (Geldt standaard voor alle)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:48
msgid "What products should this capacity apply to?"
msgstr "Op welke producten moet deze capaciteit van toepassing zijn?"

#~ msgid "What tickets does this code apply to? (Applies to all by default)"
#~ msgstr "Op welke tickets is deze code van toepassing? (Geldt standaard voor alle)"

#~ msgid "What tickets should this question be apply to?"
#~ msgstr "Op welke tickets moet deze vraag van toepassing zijn?"

#: src/components/forms/QuestionForm/index.tsx:179
msgid "What time will you be arriving?"
msgstr "Hoe laat kom je aan?"

#: src/components/forms/QuestionForm/index.tsx:170
msgid "What type of question is this?"
msgstr "Wat voor vraag is dit?"

#: src/components/forms/WebhookForm/index.tsx:108
msgid "When a check-in is deleted"
msgstr "Wanneer een check-in wordt verwijderd"

#: src/components/forms/WebhookForm/index.tsx:84
msgid "When a new attendee is created"
msgstr "Wanneer een nieuwe deelnemer wordt aangemaakt"

#: src/components/forms/WebhookForm/index.tsx:54
msgid "When a new order is created"
msgstr "Wanneer een nieuwe order wordt aangemaakt"

#: src/components/forms/WebhookForm/index.tsx:36
msgid "When a new product is created"
msgstr "Wanneer een nieuw product wordt gemaakt"

#: src/components/forms/WebhookForm/index.tsx:48
msgid "When a product is deleted"
msgstr "Wanneer een product wordt verwijderd"

#: src/components/forms/WebhookForm/index.tsx:42
msgid "When a product is updated"
msgstr "Wanneer een product wordt bijgewerkt"

#: src/components/forms/WebhookForm/index.tsx:96
msgid "When an attendee is cancelled"
msgstr "Wanneer een deelnemer wordt geannuleerd"

#: src/components/forms/WebhookForm/index.tsx:102
msgid "When an attendee is checked in"
msgstr "Wanneer een deelnemer is ingecheckt"

#: src/components/forms/WebhookForm/index.tsx:90
msgid "When an attendee is updated"
msgstr "Wanneer een deelnemer wordt bijgewerkt"

#: src/components/forms/WebhookForm/index.tsx:78
msgid "When an order is cancelled"
msgstr "Wanneer een bestelling wordt geannuleerd"

#: src/components/forms/WebhookForm/index.tsx:66
msgid "When an order is marked as paid"
msgstr "Wanneer een bestelling is gemarkeerd als betaald"

#: src/components/forms/WebhookForm/index.tsx:72
msgid "When an order is refunded"
msgstr "Wanneer een bestelling wordt terugbetaald"

#: src/components/forms/WebhookForm/index.tsx:60
msgid "When an order is updated"
msgstr "Wanneer een bestelling wordt bijgewerkt"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:182
msgid "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."
msgstr "Als deze optie is ingeschakeld, worden facturen gegenereerd voor ticketbestellingen. Facturen worden samen met de e-mail ter bevestiging van de bestelling verzonden. Bezoekers kunnen hun facturen ook downloaden van de bestelbevestigingspagina."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:141
msgid "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."
msgstr "Als offline betalingen zijn ingeschakeld, kunnen gebruikers hun bestellingen afronden en hun tickets ontvangen. Hun tickets zullen duidelijk aangeven dat de bestelling niet betaald is en de check-in tool zal het check-in personeel informeren als een bestelling betaald moet worden."

#: src/components/forms/CheckInListForm/index.tsx:59
msgid "When should this check-in list expire?"
msgstr "Wanneer moet deze check-in lijst verlopen?"

#: src/components/forms/CheckInListForm/index.tsx:25
msgid "Which tickets should be associated with this check-in list?"
msgstr "Welke tickets moeten aan deze inchecklijst worden gekoppeld?"

#: src/components/modals/CreateEventModal/index.tsx:107
msgid "Who is organizing this event?"
msgstr "Wie organiseert dit evenement?"

#: src/components/modals/SendMessageModal/index.tsx:194
msgid "Who is this message to?"
msgstr "Aan wie is deze boodschap gericht?"

#: src/components/forms/QuestionForm/index.tsx:151
msgid "Who should be asked this question?"
msgstr "Aan wie moet deze vraag worden gesteld?"

#~ msgid "Who type of question is this?"
#~ msgstr "Wat voor vraag is dit?"

#~ msgid "Whoops! something went wrong. Please try again or contact support if the problem persists."
#~ msgstr "Oeps! Er is iets misgegaan. Probeer het opnieuw of neem contact op met support als het probleem zich blijft voordoen."

#~ msgid "Widget"
#~ msgstr "Widget"

#~ msgid "Widget Configuration"
#~ msgstr "Widget configuratie"

#: src/components/layouts/Event/index.tsx:110
msgid "Widget Embed"
msgstr "Widget insluiten"

#: src/components/common/WidgetEditor/index.tsx:162
msgid "Widget Settings"
msgstr "Widget-instellingen"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Working"
msgstr "Werken"

#: src/components/modals/CreateProductCategoryModal/index.tsx:60
#: src/components/modals/CreateProductModal/index.tsx:88
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
#: src/components/modals/CreateQuestionModal/index.tsx:74
#: src/components/modals/DuplicateEventModal/index.tsx:142
#: src/components/modals/DuplicateProductModal/index.tsx:113
#: src/components/modals/EditProductCategoryModal/index.tsx:70
#: src/components/modals/EditProductModal/index.tsx:108
#: src/components/modals/EditPromoCodeModal/index.tsx:84
#: src/components/modals/EditQuestionModal/index.tsx:101
#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/Register/index.tsx:129
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Working..."
msgstr "Werken..."

#: src/components/common/ReportTable/index.tsx:51
msgid "Year to date"
msgstr "Jaar tot nu toe"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "Yes"
msgstr "Ja"

#: src/components/forms/ProductForm/index.tsx:376
msgid "Yes, remove them"
msgstr "Ja, verwijder ze"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:77
msgid "You already scanned this ticket"
msgstr "Je hebt dit ticket al gescand"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:53
msgid "You are changing your email to <0>{0}</0>."
msgstr "Je wijzigt je e-mailadres in <0>{0}</0>."

#: src/components/layouts/CheckIn/index.tsx:88
#: src/components/layouts/CheckIn/index.tsx:110
msgid "You are offline"
msgstr "Je bent offline"

#~ msgid "You can connecting using this Zoom link..."
#~ msgstr "Je kunt verbinding maken via deze Zoom-link..."

#: src/components/forms/ProductForm/index.tsx:407
msgid "You can create a promo code which targets this product on the"
msgstr "Je kunt een promotiecode maken die gericht is op dit product op de"

#~ msgid "You can create a promo code which targets this ticket on the"
#~ msgstr "Je kunt een promotiecode maken die gericht is op dit ticket op de"

#~ msgid "You can now start receiving payments through Stripe."
#~ msgstr "Je kunt nu betalingen ontvangen via Stripe."

#: src/components/forms/ProductForm/index.tsx:208
msgid "You cannot change the product type as there are attendees associated with this product."
msgstr "Je kunt het producttype niet wijzigen omdat er deelnemers aan dit product zijn gekoppeld."

#~ msgid "You cannot change the ticket type as there are attendees associated with this ticket."
#~ msgstr "Je kunt het type ticket niet wijzigen omdat er deelnemers aan dit ticket zijn gekoppeld."

#~ msgid "You cannot change the ticket type because there are already tickets sold for this ticket."
#~ msgstr "Je kunt het type ticket niet wijzigen omdat er al tickets verkocht zijn voor dit ticket."

#~ msgid "You cannot check in attendees with unpaid orders."
#~ msgstr "Je kunt deelnemers met onbetaalde bestellingen niet inchecken."

#: src/components/layouts/CheckIn/index.tsx:129
#: src/components/layouts/CheckIn/index.tsx:164
msgid "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."
msgstr "Je kunt deelnemers met onbetaalde bestellingen niet inchecken. Je kunt deze instelling wijzigen in de evenementinstellingen."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:40
msgid "You cannot delete the last category."
msgstr "Je kunt de laatste categorie niet verwijderen."

#: src/components/forms/ProductForm/index.tsx:57
msgid "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."
msgstr "Je kunt dit prijsniveau niet verwijderen omdat er al producten voor dit niveau worden verkocht. In plaats daarvan kun je het verbergen."

#~ msgid "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."
#~ msgstr "Je kunt dit prijsniveau niet verwijderen omdat er al tickets voor dit niveau zijn verkocht. In plaats daarvan kun je het verbergen."

#~ msgid "You cannot edit a default question"
#~ msgstr "U kunt een standaardvraag niet bewerken"

#: src/components/modals/EditUserModal/index.tsx:88
msgid "You cannot edit the role or status of the account owner."
msgstr "Je kunt de rol of status van de accounteigenaar niet bewerken."

#: src/components/modals/RefundOrderModal/index.tsx:132
msgid "You cannot refund a manually created order."
msgstr "Je kunt een handmatig aangemaakte bestelling niet terugbetalen."

#: src/components/common/QuestionsTable/index.tsx:252
msgid "You created a hidden question but disabled the option to show hidden questions. It has been enabled."
msgstr "Je hebt een verborgen vraag gemaakt maar de optie om verborgen vragen weer te geven uitgeschakeld. Deze is ingeschakeld."

#~ msgid "You do not have permission to access this page"
#~ msgstr "Je hebt geen toestemming om deze pagina te openen"

#: src/components/modals/ChooseAccountModal/index.tsx:14
msgid "You have access to multiple accounts. Please choose one to continue."
msgstr "Je hebt toegang tot meerdere accounts. Kies er een om verder te gaan."

#: src/components/routes/auth/AcceptInvitation/index.tsx:57
msgid "You have already accepted this invitation. Please login to continue."
msgstr "Je hebt deze uitnodiging al geaccepteerd. Log in om verder te gaan."

#~ msgid "You have connected your Stripe account"
#~ msgstr "Je hebt je Stripe-account gekoppeld"

#: src/components/common/QuestionsTable/index.tsx:338
msgid "You have no attendee questions."
msgstr "U hebt geen vragen van deelnemers."

#~ msgid "You have no attendee questions. Attendee questions are asked once per attendee."
#~ msgstr "Je hebt geen vragen van deelnemers. Vragen van deelnemers worden één keer per deelnemer gesteld."

#: src/components/common/QuestionsTable/index.tsx:323
msgid "You have no order questions."
msgstr "Je hebt geen bestelvragen."

#~ msgid "You have no order questions. Order questions are asked once per order."
#~ msgstr "Je hebt geen bestelvragen. Bestelvragen worden één keer per bestelling gesteld."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:41
msgid "You have no pending email change."
msgstr "Je hebt geen in behandeling zijnde e-mailwijziging."

#~ msgid "You have not completed your Stripe Connect setup"
#~ msgstr "Je hebt de Stripe Connect setup niet voltooid"

#~ msgid "You have not connected your Stripe account"
#~ msgstr "Je hebt je Stripe-account niet gekoppeld"

#: src/components/layouts/Checkout/index.tsx:156
msgid "You have run out of time to complete your order."
msgstr "Je hebt geen tijd meer om je bestelling af te ronden."

#~ msgid "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"
#~ msgstr "Je hebt belastingen en kosten toegevoegd aan een Gratis product. Wilt u deze verwijderen of verbergen?"

#: src/components/forms/ProductForm/index.tsx:374
msgid "You have taxes and fees added to a Free Product. Would you like to remove them?"
msgstr "Je hebt belastingen en kosten toegevoegd aan een Gratis product. Wilt u deze verwijderen?"

#~ msgid "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"
#~ msgstr "Je hebt belastingen en toeslagen toegevoegd aan een Gratis ticket. Wilt u deze verwijderen of verbergen?"

#: src/components/common/MessageList/index.tsx:74
msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."
msgstr "Je hebt nog geen berichten verzonden. Je kunt berichten sturen naar alle deelnemers of naar specifieke producthouders."

#~ msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."
#~ msgstr "Je hebt nog geen berichten verstuurd. Je kunt berichten sturen naar alle aanwezigen of naar specifieke tickethouders."

#: src/components/modals/SendMessageModal/index.tsx:108
msgid "You must acknowledge that this email is not promotional"
msgstr "U moet erkennen dat deze e-mail geen promotie is"

#: src/components/routes/auth/AcceptInvitation/index.tsx:33
msgid "You must agree to the terms and conditions"
msgstr "U moet akkoord gaan met de algemene voorwaarden"

#: src/components/routes/event/GettingStarted/index.tsx:193
msgid "You must confirm your email address before your event can go live."
msgstr "Je moet je e-mailadres bevestigen voordat je evenement live kan gaan."

#: src/components/modals/CreateAttendeeModal/index.tsx:98
msgid "You must create a ticket before you can manually add an attendee."
msgstr "Je moet een ticket aanmaken voordat je handmatig een genodigde kunt toevoegen."

#: src/components/forms/ProductForm/index.tsx:60
msgid "You must have at least one price tier"
msgstr "Je moet minstens één prijsniveau hebben"

#~ msgid "You must have at least one tier"
#~ msgstr "Je moet minstens één niveau hebben"

#~ msgid "You need to verify your account before you can send messages."
#~ msgstr "U moet uw account verifiëren voordat u berichten kunt verzenden."

#: src/components/modals/SendMessageModal/index.tsx:151
msgid "You need to verify your account email before you can send messages."
msgstr "U moet het e-mailadres van uw account verifiëren voordat u berichten kunt verzenden."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:143
msgid "You will have to mark an order as paid manually. This can be done on the manage order page."
msgstr "Je moet een bestelling handmatig als betaald markeren. Dit kun je doen op de pagina Bestelling beheren."

#: src/components/modals/CreateCheckInListModal/index.tsx:55
msgid "You'll need a ticket before you can create a check-in list."
msgstr "Je hebt een ticket nodig voordat je een inchecklijst kunt maken."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
msgid "You'll need at a product before you can create a capacity assignment."
msgstr "U hebt een product nodig voordat u een capaciteitstoewijzing kunt maken."

#~ msgid "You'll need at a ticket before you can create a capacity assignment."
#~ msgstr "Je hebt een ticket nodig voordat je een capaciteitstoewijzing kunt maken."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:43
msgid "You'll need at least one product to get started. Free, paid or let the user decide what to pay."
msgstr "Je hebt minstens één product nodig om te beginnen. Gratis, betaald of laat de gebruiker beslissen wat hij wil betalen."

#~ msgid "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."
#~ msgstr "Je hebt minstens één ticket nodig om te beginnen. Gratis, betaald of laat de gebruiker beslissen wat hij wil betalen."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:93
msgid "You're going to {0}! 🎉"
msgstr "Je gaat naar {0}! 🎉"

#~ msgid "Your account email in outgoing emails."
#~ msgstr "Het e-mailadres van uw account in uitgaande e-mails."

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:64
msgid "Your account name is used on event pages and in emails."
msgstr "Je accountnaam wordt gebruikt op evenementpagina's en in e-mails."

#: src/components/routes/event/attendees.tsx:44
msgid "Your attendees have been exported successfully."
msgstr "Je deelnemers zijn succesvol geëxporteerd."

#: src/components/common/AttendeeTable/index.tsx:61
msgid "Your attendees will appear here once they have registered for your event. You can also manually add attendees."
msgstr "Je bezoekers verschijnen hier zodra ze zich hebben geregistreerd voor je evenement. Je kunt deelnemers ook handmatig toevoegen."

#: src/components/common/WidgetEditor/index.tsx:329
msgid "Your awesome website 🎉"
msgstr "Uw geweldige website 🎉"

#: src/components/routes/product-widget/CollectInformation/index.tsx:276
msgid "Your Details"
msgstr "Uw gegevens"

#: src/components/routes/auth/ForgotPassword/index.tsx:52
msgid "Your Email"
msgstr "Uw e-mail"

#: src/components/routes/profile/ManageProfile/index.tsx:118
msgid "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"
msgstr "Uw verzoek om uw e-mail te wijzigen in <0>{0}</0> is in behandeling. Controleer uw e-mail om te bevestigen"

#: src/components/routes/event/EventDashboard/index.tsx:150
msgid "Your event must be live before you can sell tickets to attendees."
msgstr "Je evenement moet live zijn voordat je tickets kunt verkopen aan deelnemers."

#~ msgid "Your event must be live before you can sell tickets."
#~ msgstr "Je evenement moet live zijn voordat je tickets kunt verkopen."

#: src/components/common/OrdersTable/index.tsx:88
msgid "Your message has been sent"
msgstr "Uw bericht is verzonden"

#: src/components/layouts/Checkout/index.tsx:84
msgid "Your Order"
msgstr "Uw bestelling"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:94
msgid "Your order has been cancelled"
msgstr "Uw bestelling is geannuleerd"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:96
msgid "Your order is awaiting payment 🏦"
msgstr "Uw bestelling wacht op betaling 🏦"

#~ msgid "Your order is in progress"
#~ msgstr "Uw bestelling is in behandeling"

#: src/components/routes/event/orders.tsx:95
msgid "Your orders have been exported successfully."
msgstr "Je bestellingen zijn succesvol geëxporteerd."

#: src/components/common/OrdersTable/index.tsx:66
msgid "Your orders will appear here once they start rolling in."
msgstr "Je bestellingen verschijnen hier zodra ze binnenkomen."

#: src/components/routes/auth/Login/index.tsx:74
#: src/components/routes/auth/Register/index.tsx:107
msgid "Your password"
msgstr "Uw wachtwoord"

#: src/components/forms/StripeCheckoutForm/index.tsx:63
msgid "Your payment is processing."
msgstr "Je betaling wordt verwerkt."

#: src/components/forms/StripeCheckoutForm/index.tsx:66
msgid "Your payment was not successful, please try again."
msgstr "Uw betaling is niet gelukt, probeer het opnieuw."

#: src/components/forms/StripeCheckoutForm/index.tsx:126
msgid "Your payment was unsuccessful. Please try again."
msgstr "Uw betaling is mislukt. Probeer het opnieuw."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#~ msgid "Your product for"
#~ msgstr "Uw product voor"

#: src/components/modals/RefundOrderModal/index.tsx:58
msgid "Your refund is processing."
msgstr "Je terugbetaling wordt verwerkt."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:153
msgid "Your Stripe account is connected and ready to process payments."
msgstr "Je Stripe-account is verbonden en klaar om betalingen te verwerken."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#: src/components/routes/product-widget/PrintProduct/index.tsx:42
msgid "Your ticket for"
msgstr "Uw ticket voor"

#~ msgid "Zip"
#~ msgstr "Zip"

#: src/components/routes/product-widget/CollectInformation/index.tsx:348
msgid "ZIP / Postal Code"
msgstr "Postcode"

#: src/components/common/CheckoutQuestion/index.tsx:170
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:146
msgid "Zip or Postal Code"
msgstr "Postcode"

#: src/components/routes/product-widget/CollectInformation/index.tsx:349
msgid "ZIP or Postal Code"
msgstr "Postcode"

#~ msgid "Zoom link, Google Meet link, etc."
#~ msgstr "Zoom link, Google Meet link, enz."
