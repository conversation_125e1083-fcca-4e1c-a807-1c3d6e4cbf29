.full-height {
  height: 100vh;
}

.hi-product-widget-container {
  background-color: var(--widget-background-color, var(--tk-color-white));
  color: var(--widget-primary-text-color);
  padding: var(--widget-padding, var(--tk-spacing-md));

  a, button {
    color: var(--widget-primary-text-color, var(--tk-primary));
  }

  .hi-product-rows {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;

    .hi-product-row {
      display: flex;
      flex-flow: column;
      border: 3px solid var(--widget-primary-color, var(--tk-secondary));
      border-radius: 5px;

      .hi-product-description-row {
        font-size: .9em;
      }

      .hi-title-row {
        display: flex;
        justify-content: center;
        align-items: center;

        .hi-product-title {
          flex: 1;
          padding: 10px 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          h3 {
            flex: 1;
            margin: 10px 0;

            a {
              font-weight: bold;
            }
          }

          .hi-product-title-metadata {
            margin-left: 5px;
            font-weight: normal;
            display: flex;
          }

          .hi-product-collapse-arrow {
            display: flex;
            color: var(--widget-secondary-color, var(--tk-primary));
            margin-left: 10px;

            svg {
              transition: all 0.1s linear;
            }

            svg.open {
              transform: rotate(90deg);
            }
          }
        }
      }

      .hi-product-content {
        padding: 10px 20px;
      }

      .hi-price-tiers-rows {
        .productPrice {

          .hi-price-tier-amount {
            span:first-child {
              display: block;
              color: #909090;
              font-size: .85em;
            }
          }
        }
      }

      .hi-product-quantity-error {
        color: var(--widget-primary-color, var(--tk-primary));
        background-color: var(--widget-background-color, var(--tk-color-white));
        border: 2px solid var(--widget-primary-color, var(--tk-primary));
        padding: 10px 20px;
        margin: 10px 0;
        border-radius: 5px;
      }

      .hi-price-tier-row {
        margin-bottom: 10px;

        .hi-price-tier {
          .hi-price-tier-label {
            font-weight: bold;
          }

          .hi-price-tier-price {
            .hi-price-tier-price-amount {
              display: flex;
              gap: 5px;

              svg {
                margin-top: 3px;
              }
            }

            .hi-donation-input-wrapper {
              .hi-donation-input {
                color: var(--widget-primary-text-color, var(--tk-primary));
                background-color: var(--widget-background-color, var(--tk-color-white));
                border: 2px solid var(--widget-primary-color, var(--tk-primary));
              }
            }
          }
        }

        .hi-product-quantity-selector {
          .button-input {
            input {
              color: var(--widget-primary-text-color, var(--tk-primary));
            }

            button {
              &:disabled {
                border: none;
                opacity: .5;
              }

              background-color: var(--widget-secondary-color, var(--tk-primary));
              color: var(--widget-secondary-text-color, var(--tk-color-white));
            }
          }

          .select-input {
            input {
              background-color: var(--widget-background-color, var(--tk-color-white));
              border: 2px solid var(--widget-primary-color, var(--tk-primary));
              color: var(--widget-primary-text-color, var(--tk-primary));
            }

            svg {
              color: var(--widget-primary-text-color, var(--tk-primary));
            }
          }
        }
      }
    }
  }

  .hi-promo-code-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 20px;

    .hi-promo-code-input {
      flex: 1;
      margin-right: 20px;
      background: var(--widget-background-color, var(--tk-color-white));
      border: 2px solid var(--widget-primary-color, var(--tk-primary));
      color: var(--widget-primary-text-color, var(--tk-primary));
    }

    .hi-apply-promo-code-button {
      width: 200px;
      background-color: var(--widget-secondary-color, var(--tk-primary));
      color: var(--widget-secondary-text-color, var(--tk-color-white));
      border: none;
    }

    .hi-close-promo-code-input-button {
        background-color: var(--widget-secondary-color, var(--tk-primary));
        color: var(--widget-secondary-text-color, var(--tk-color-white));
        border: none;
    }

    .hi-promo-code-applied {
      display: flex;
      align-items: center;
    }

    .hi-promo-code-applied-remove-icon-button {
      margin-left: 10px;
    }
  }

  .hi-footer-row {
    display: flex;
    margin-top: 20px;
    flex-direction: column;

    .hi-product-page-message {
      margin-bottom: 20px;
      padding: 10px 20px;
      border-radius: 5px;
      background-color: var(--widget-secondary-color, var(--tk-secondary));
    }

    .hi-continue-button {
      background-color: var(--widget-secondary-color, var(--tk-primary));
      color: var(--widget-secondary-text-color, var(--tk-color-white));
    }
  }
}

