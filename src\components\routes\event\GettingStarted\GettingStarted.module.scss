@use "../../../../styles/mixins.scss";

.headerCard {
  background: linear-gradient(
    135deg,
    var(--tk-primary) 0%,
    var(--tk-pink) 100%
  );
  border-radius: var(--tk-radius-xl);
  box-shadow: 0 12px 40px rgba(71, 46, 120, 0.25) !important;
  padding: var(--tk-spacing-xl) * 2;
  margin-bottom: var(--tk-spacing-xl) * 2;
  border: none;
  position: relative;
  overflow: hidden;
  color: white;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%,
      rgba(255, 255, 255, 0.05) 100%
    );
    pointer-events: none;
    z-index: 1;
  }

  &::after {
    content: "";
    position: absolute;
    top: -50%;
    right: -20%;
    width: 300px;
    height: 300px;
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 70%
    );
    border-radius: 50%;
    z-index: 0;
  }

  @include mixins.respond-below(lg) {
    &::after {
      display: none;
    }
  }

  @include mixins.respond-below(sm) {
    padding: var(--tk-spacing-xl);
  }
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 2;

  @include mixins.respond-below(sm) {
    flex-direction: column;
    gap: var(--tk-spacing-xl);
  }
}

.headerTitle {
  flex: 1;
}

.mainTitle {
  font-size: 2.25rem;
  font-weight: 800;
  color: white;
  margin: 0;
  line-height: 1.2;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  @include mixins.respond-below(sm) {
    font-size: 1.875rem;
  }
}

.confettiIcon {
  color: rgba(255, 255, 255, 0.9);
  animation: pulse 2s infinite ease-in-out;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.subtitle {
  margin-top: var(--tk-spacing-lg);
  font-size: 1.125rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  font-weight: 400;
}

.progressBarContainer {
  margin-top: var(--tk-spacing-xl);
  max-width: 600px;

  @include mixins.respond-below(sm) {
    margin-top: var(--tk-spacing-lg);
  }
}

.progressBar {
  height: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: var(--tk-radius-lg);
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);

  [role="progressbar"] {
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.9),
      rgba(255, 255, 255, 0.7)
    );
    box-shadow: 0 0 12px rgba(255, 255, 255, 0.3);
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--tk-radius-lg);
  }
}

.confettiContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
  animation: confettiAnimation 4s ease-in-out forwards;
  background-image: radial-gradient(
      circle at 25% 25%,
      #ff4a4a 2px,
      transparent 0
    ),
    radial-gradient(circle at 75% 75%, #4a4aff 2px, transparent 0),
    radial-gradient(circle at 25% 75%, #4aff4a 3px, transparent 0),
    radial-gradient(circle at 75% 25%, #ffff4a 3px, transparent 0),
    radial-gradient(circle at 50% 50%, #ff4aff 4px, transparent 0);
  background-size: 10% 10%;
}

@keyframes confettiAnimation {
  0% {
    background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  100% {
    background-position: 0% 100%, 0% 100%, 0% 100%, 0% 100%, 0% 100%;
    opacity: 0;
  }
}

.actionItems {
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(calc(50% - var(--tk-spacing-lg)), 1fr)
  );
  gap: var(--tk-spacing-xl);

  @include mixins.respond-below(sm) {
    grid-template-columns: 1fr;
    gap: var(--tk-spacing-lg);
  }

  & > div {
    margin-bottom: 0;
    position: relative;
    padding: var(--tk-spacing-xl);
    background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
    border: 1px solid rgba(71, 46, 120, 0.1);
    border-radius: var(--tk-radius-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(71, 46, 120, 0.08);
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(
        90deg,
        var(--tk-primary) 0%,
        var(--tk-pink) 100%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      box-shadow: 0 12px 40px rgba(71, 46, 120, 0.15);
      transform: translateY(-4px);
      border-color: rgba(71, 46, 120, 0.2);

      &::before {
        opacity: 1;
      }
    }

    h2 {
      margin-top: 0;
      margin-bottom: var(--tk-spacing-lg);
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--tk-primary);
      line-height: 1.3;
    }

    p {
      margin-bottom: var(--tk-spacing-xl);
      color: var(--tk-secondary-text);
      line-height: 1.6;
      font-size: 0.95rem;
      font-weight: 400;
    }

    @include mixins.respond-below(sm) {
      padding: var(--tk-spacing-lg);
    }
  }
}

.completedCard {
  background: linear-gradient(
    135deg,
    rgba(52, 199, 89, 0.05) 0%,
    rgba(52, 199, 89, 0.02) 100%
  ) !important;
  border-color: rgba(52, 199, 89, 0.3) !important;

  &::before {
    background: linear-gradient(90deg, #34c759 0%, #30d158 100%) !important;
    opacity: 1 !important;
  }

  h2 {
    color: #34c759 !important;
  }
}

.completedBadge {
  position: absolute;
  top: var(--tk-spacing-lg);
  right: var(--tk-spacing-lg);
  background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
  color: white;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
  z-index: 10;

  &:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(52, 199, 89, 0.4);
  }

  svg {
    width: 18px;
    height: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
}

// Enhanced animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.actionItems > div {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;

  &:nth-child(1) {
    animation-delay: 0.1s;
  }
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  &:nth-child(3) {
    animation-delay: 0.3s;
  }
  &:nth-child(4) {
    animation-delay: 0.4s;
  }
  &:nth-child(5) {
    animation-delay: 0.5s;
  }
  &:nth-child(6) {
    animation-delay: 0.6s;
  }
}
