.wrapper {
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-repeat: no-repeat;
    background-position: center;
}

.backgroundOrb1 {
    position: absolute;
    top: 20%;
    left: -10%;
    width: 40%;
    height: 40%;
    border-radius: 50%;
    background-color: rgba(147, 51, 234, 0.1);
    filter: blur(100px);
    animation: drift 20s ease-in-out infinite;
    z-index: -1;
}

.backgroundOrb2 {
    position: absolute;
    bottom: 10%;
    right: -10%;
    width: 40%;
    height: 40%;
    border-radius: 50%;
    background-color: rgba(236, 72, 153, 0.08);
    filter: blur(120px);
    animation: drift-slow 25s ease-in-out infinite;
    z-index: -1;
}

@keyframes drift {
    0%, 100% {
        transform: translate(0, 0);
    }
    50% {
        transform: translate(5%, 5%);
    }
}

@keyframes drift-slow {
    0%, 100% {
        transform: translate(0, 0);
    }
    50% {
        transform: translate(-5%, -5%);
    }
}

.root {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--mantine-spacing-xl) 0;
    position: relative;
    z-index: 1;
}

.logo {
    display: flex;
    justify-content: center;
}

.content {
    max-width: 500px;
    text-align: center;
    background-color: var(--mantine-color-body);
    padding: var(--mantine-spacing-xl);
    border-radius: var(--mantine-radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(147, 51, 234, 0.1);
}

.title {
    font-weight: 800;
    margin-bottom: var(--mantine-spacing-md);
    font-size: 32px;
    background: linear-gradient(to right, var(--mantine-color-purple-6), var(--mantine-color-pink-6));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    @media (max-width: 768px) {
        font-size: 28px
    }
}

.description {
    line-height: 1.6;
    margin-bottom: var(--mantine-spacing-lg);
}

.button {
    transition: all 250ms ease;

    &:hover {
        transform: translateY(-3px);
        box-shadow: var(--mantine-shadow-md);
    }
}
