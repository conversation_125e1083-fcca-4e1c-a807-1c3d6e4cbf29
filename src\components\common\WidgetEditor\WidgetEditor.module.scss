.widgetGrid {
  display: grid;
  grid-template-areas: 'form preview';
  grid-template-columns: 320px 1fr;
  margin-top: -15px;
  margin-bottom: -15px;
  margin-right: -15px;

  @media (max-width: 760px) {
    grid-template-areas:
            'form'
            'preview';
    grid-template-columns: auto;
  }
}

.widgetForm {
  padding: 20px;
  grid-area: form;
  margin-top: 20px;

  .formHeader {
    margin-top: 0;
  }
}

.widgetContainer {
  position: absolute;
  bottom: 20px;
  z-index: 4;

  // You may need to write JavaScript to toggle between 'left' and 'right' based on props.campaign.tab_position.toString() === 'BOTTOM_LEFT' condition.
}

.previewPane {
  grid-area: preview;
  padding: 20px;
  background: #f5f5f5;
}

.browserChrome {
  width: 100%;
  background: #fff;
  border-radius: 5px 5px 0 0;
  height: 40px;
  display: flex;
  border-bottom: 1px solid #dbdbdb;
}

.browserActionButtons {
  width: 100px;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 10px;

  div {
    width: 15px;
    height: 15px;
    border-radius: 50px;
    background-color: #eee;
    display: inline-block;
    margin-right: 4px;
  }
}

.browserAddressBar {
  flex: 1;
  align-self: center;

  div {
    background-color: #f8f8f8;
    border-radius: 5px;
    margin-right: 20%;
    font-size: 0.9em;
    padding: 2px 12px;
    color: #909090;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    span {
      color: #6eb96e;
    }
  }
}

.previewHeader {
  img {
    width: 20px;
    height: 20px;
    display: inline;
  }
}

.websitePlaceholder {
  position: absolute;
  user-select: none;
  top: auto;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  z-index: 1;
  padding: 20px;
  overflow: scroll;

  .widgetWrapper {
    padding: 10px;
  }

  // You may need to write JavaScript to change the opacity based on the widgetType prop.

  p.lorem {
    color: #ddd;
    margin-bottom: 20px;
  }
}

.stickyContainer {
  position: sticky;
  top: 20px;
  min-height: 80vh;
  background-color: #ffffff;
  border-radius: var(--tk-radius-lg);
  border: 1px solid #dbdbdb;
  overflow: hidden;
}

.sectionHeader {
  color: #6eb96e;
  letter-spacing: 2px;
  margin: 20px 0;
  font-size: 1.3em;
}
