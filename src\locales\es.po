msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Hi.Events\n"
"Language: es\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/components/layouts/Event/index.tsx:189
msgid "- Click to Publish"
msgstr "- Haz clic para publicar"

#: src/components/layouts/Event/index.tsx:191
msgid "- Click to Unpublish"
msgstr "- Haz clic para despublicar"

#: src/components/common/NoResultsSplash/index.tsx:14
msgid "'There\\'s nothing to show yet'"
msgstr "'No hay nada que mostrar todavía'"

#: src/components/modals/SendMessageModal/index.tsx:159
#~ msgid ""
#~ "\"\"Due to the high risk of spam, we require manual verification before you can send messages.\n"
#~ "\"\"Please contact us to request access."
#~ msgstr ""
#~ "Debido al alto riesgo de spam, requerimos verificación manual antes de que puedas enviar mensajes.\n"
#~ "Por favor, contáctanos para solicitar acceso."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
#~ msgid ""
#~ "\"\"If you have an account with us, you will receive an email with instructions on how to reset your\n"
#~ "\"\"password."
#~ msgstr ""
#~ "Si tienes una cuenta con nosotros, recibirás un correo electrónico con instrucciones sobre cómo restablecer tu\n"
#~ "contraseña."

#: src/components/common/ReportTable/index.tsx:303
#: src/locales.ts:37
msgid "{0}"
msgstr "{0}"

#: src/components/layouts/CheckIn/index.tsx:82
msgid "{0} <0>checked in</0> successfully"
msgstr "{0} <0>registrado</0> con éxito"

#: src/components/layouts/CheckIn/index.tsx:106
msgid "{0} <0>checked out</0> successfully"
msgstr "{0} <0>retirado</0> con éxito"

#: src/components/routes/event/Webhooks/index.tsx:24
msgid "{0} Active Webhooks"
msgstr "{0} webhooks activos"

#: src/components/routes/product-widget/SelectProducts/index.tsx:412
msgid "{0} available"
msgstr "{0} disponible"

#: src/components/common/AttendeeCheckInTable/index.tsx:155
#~ msgid "{0} checked in"
#~ msgstr "{0} registrado"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
msgid "{0} created successfully"
msgstr "{0} creado correctamente"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
msgid "{0} updated successfully"
msgstr "{0} actualizado correctamente"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:57
msgid "{0}'s Events"
msgstr "Eventos de {0}"

#: src/components/layouts/CheckIn/index.tsx:449
msgid "{0}/{1} checked in"
msgstr "{0}/{1} registrado"

#: src/components/common/Countdown/index.tsx:68
msgid "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{days} días, {hours} horas, {minutes} minutos y {seconds} segundos"

#: src/components/common/WebhookTable/index.tsx:79
msgid "{eventCount} events"
msgstr "{eventCount} eventos"

#: src/components/common/Countdown/index.tsx:70
msgid "{hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{hours} horas, {minutes} minutos y {seconds} segundos"

#: src/components/common/Countdown/index.tsx:72
msgid "{minutes} minutes and {seconds} seconds"
msgstr "{minutos} minutos y {segundos} segundos"

#: src/components/routes/welcome/index.tsx:59
msgid "{organizerName}'s first event"
msgstr "El primer evento de {organizerName}"

#: src/components/common/ReportTable/index.tsx:256
#~ msgid "{title}"
#~ msgstr ""

#: src/components/common/CapacityAssignmentList/index.tsx:47
msgid "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"
msgstr "<0>Las asignaciones de capacidad te permiten gestionar la capacidad en entradas o en todo un evento. Ideal para eventos de varios días, talleres y más, donde es crucial controlar la asistencia.</0><1>Por ejemplo, puedes asociar una asignación de capacidad con el ticket de <2>Día Uno</2> y el de <3>Todos los Días</3>. Una vez que se alcance la capacidad, ambos tickets dejarán automáticamente de estar disponibles para la venta.</1>"

#: src/components/common/CheckInListList/index.tsx:49
msgid "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"
msgstr "<0>Las listas de registro ayudan a gestionar la entrada de los asistentes a su evento. Puede asociar múltiples boletos con una lista de registro y asegurarse de que solo aquellos con boletos válidos puedan ingresar.</0>"

#: src/components/common/WidgetEditor/index.tsx:324
msgid "<0>https://</0>your-website.com"
msgstr "<0>https://</0>tu-sitio-web.com"

#: src/components/forms/ProductForm/index.tsx:287
msgid "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"
msgstr "<0>Por favor, introduce el precio sin incluir impuestos y tasas.</0><1>Los impuestos y tasas se pueden agregar a continuación.</1>"

#: src/components/forms/ProductForm/index.tsx:304
msgid "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"
msgstr "<0>El número de productos disponibles para este producto</0><1>Este valor se puede sobrescribir si hay <2>Límites de Capacidad</2> asociados con este producto.</1>"

#: src/components/forms/TicketForm/index.tsx:249
#~ msgid "<0>The number of tickets available for this ticket</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this ticket.</1>"
#~ msgstr "<0>El número de entradas disponibles para este ticket</0><1>Este valor puede ser anulado si hay <2>Límites de Capacidad</2> asociados con este ticket.</1>"

#: src/components/common/WebhookTable/index.tsx:205
msgid "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"
msgstr "<0>Los webhooks notifican instantáneamente a los servicios externos cuando ocurren eventos, como agregar un nuevo asistente a tu CRM o lista de correo al registrarse, asegurando una automatización fluida.</0><1>Usa servicios de terceros como <2>Zapier</2>, <3>IFTTT</3> o <4>Make</4> para crear flujos de trabajo personalizados y automatizar tareas.</1>"

#: src/components/routes/event/GettingStarted/index.tsx:134
msgid "⚡️ Set up your event"
msgstr "⚡️ Configura tu evento"

#: src/components/routes/event/GettingStarted/index.tsx:190
msgid "✉️ Confirm your email address"
msgstr "✉️ Confirma tu dirección de correo electrónico"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "🎉 Congratulations on creating an event!"
#~ msgstr "🎉 ¡Felicitaciones por crear un evento!"

#: src/components/routes/event/GettingStarted/index.tsx:67
#~ msgid "🎟️ Add products"
#~ msgstr "🎟️ Añadir productos"

#: src/components/routes/event/GettingStarted/index.tsx:119
msgid "🎟️ Add tickets"
msgstr "🎟️ Agregar entradas"

#: src/components/routes/event/GettingStarted/index.tsx:162
msgid "🎨 Customize your event page"
msgstr "🎨 Personaliza la página de tu evento"

#: src/components/routes/event/GettingStarted/index.tsx:147
msgid "💳 Connect with Stripe"
msgstr "💳 Conéctate con Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:176
msgid "🚀 Set your event live"
msgstr "🚀 Configura tu evento en vivo"

#: src/components/common/Countdown/index.tsx:34
msgid "0 minutes and 0 seconds"
msgstr "0 minutos y 0 segundos"

#: src/components/routes/event/Webhooks/index.tsx:23
msgid "1 Active Webhook"
msgstr "1 webhook activo"

#: src/components/modals/RefundOrderModal/index.tsx:105
msgid "10.00"
msgstr "10.00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:123
msgid "123 Main Street"
msgstr "123 calle principal"

#: src/components/common/WidgetEditor/index.tsx:226
msgid "20"
msgstr "20"

#: src/components/routes/welcome/index.tsx:91
msgid "2024-01-01 10:00"
msgstr "2024-01-01 10:00"

#: src/components/routes/welcome/index.tsx:97
msgid "2024-01-01 18:00"
msgstr "2024-01-01 18:00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:147
msgid "94103"
msgstr "94103"

#: src/components/forms/QuestionForm/index.tsx:138
msgid "A date input. Perfect for asking for a date of birth etc."
msgstr "Un campo de fecha. Perfecto para pedir una fecha de nacimiento, etc."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
msgid "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."
msgstr "Un {type} predeterminado se aplica automáticamente a todos los nuevos productos. Puede sobrescribir esto por cada producto."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
#~ msgid "A default {type} is automaticaly applied to all new tickets. You can override this on a per ticket basis."
#~ msgstr "Un {tipo} predeterminado se aplica automáticamente a todos los tickets nuevos. Puede anular esto por ticket."

#: src/components/forms/QuestionForm/index.tsx:126
msgid "A Dropdown input allows only one selection"
msgstr "Una entrada desplegable permite solo una selección"

#: src/components/forms/TaxAndFeeForm/index.tsx:20
msgid "A fee, like a booking fee or a service fee"
msgstr "Una tarifa, como una tarifa de reserva o una tarifa de servicio"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
msgid "A fixed amount per product. E.g, $0.50 per product"
msgstr "Un monto fijo por producto. Ej., $0.50 por producto"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
#~ msgid "A fixed amount per ticket. E.g, $0.50 per ticket"
#~ msgstr "Una cantidad fija por billete. Por ejemplo, $0,50 por boleto"

#: src/components/forms/QuestionForm/index.tsx:108
msgid "A multi line text input"
msgstr "Una entrada de texto de varias líneas"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
msgid "A percentage of the product price. E.g., 3.5% of the product price"
msgstr "Un porcentaje del precio del producto. Ej., 3.5% del precio del producto"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
#~ msgid "A percentage of the ticket price. E.g., 3.5% of the ticket price"
#~ msgstr "Un porcentaje del precio del billete. Por ejemplo, el 3,5% del precio del billete."

#: src/components/forms/PromoCodeForm/index.tsx:37
msgid "A promo code with no discount can be used to reveal hidden products."
msgstr "Un código promocional sin descuento puede usarse para revelar productos ocultos."

#: src/components/forms/PromoCodeForm/index.tsx:36
#~ msgid "A promo code with no discount can be used to reveal hidden tickets."
#~ msgstr "Se puede utilizar un código de promoción sin descuento para revelar entradas ocultas."

#: src/components/forms/QuestionForm/index.tsx:120
msgid "A Radio option has multiple options but only one can be selected."
msgstr "Una opción de Radio tiene múltiples opciones pero solo se puede seleccionar una."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:69
msgid "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"
msgstr "Una breve descripción del evento que se mostrará en los resultados del motor de búsqueda y al compartir en las redes sociales. De forma predeterminada, se utilizará la descripción del evento."

#: src/components/forms/QuestionForm/index.tsx:102
msgid "A single line text input"
msgstr "Una entrada de texto de una sola línea"

#: src/components/forms/QuestionForm/index.tsx:92
#~ msgid "A single question per attendee. E.g, What is your preferred meal?"
#~ msgstr "Una única pregunta por asistente. Por ejemplo, ¿cuál es tu comida preferida?"

#: src/components/forms/QuestionForm/index.tsx:86
#~ msgid "A single question per order. E.g, What is your company name?"
#~ msgstr "Una sola pregunta por pedido. Por ejemplo, ¿cuál es el nombre de su empresa?"

#: src/components/forms/QuestionForm/index.tsx:87
msgid "A single question per order. E.g, What is your shipping address?"
msgstr "Una sola pregunta por pedido. Ej., ¿Cuál es su dirección de envío?"

#: src/components/forms/QuestionForm/index.tsx:93
msgid "A single question per product. E.g, What is your t-shirt size?"
msgstr "Una sola pregunta por producto. Ej., ¿Cuál es su talla de camiseta?"

#: src/components/forms/TaxAndFeeForm/index.tsx:14
msgid "A standard tax, like VAT or GST"
msgstr "Un impuesto estándar, como el IVA o el GST"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:76
msgid "About"
msgstr "Acerca de"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:211
msgid "About Stripe Connect"
msgstr "Acerca de Stripe Connect"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:79
#~ msgid "About the event"
#~ msgstr "Sobre el evento"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:91
msgid "Accept bank transfers, checks, or other offline payment methods"
msgstr "Aceptar transferencias bancarias, cheques u otros métodos de pago offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:86
msgid "Accept credit card payments with Stripe"
msgstr "Aceptar pagos con tarjeta de crédito a través de Stripe"

#: src/components/routes/auth/AcceptInvitation/index.tsx:126
msgid "Accept Invitation"
msgstr "Aceptar la invitacion"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:125
msgid "Access Denied"
msgstr "Acceso denegado"

#: src/components/routes/account/ManageAccount/index.tsx:24
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:53
msgid "Account"
msgstr "Cuenta"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:62
msgid "Account Name"
msgstr "Nombre de la cuenta"

#: src/components/common/GlobalMenu/index.tsx:32
#: src/components/routes/account/ManageAccount/index.tsx:19
msgid "Account Settings"
msgstr "Configuraciones de la cuenta"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:42
msgid "Account updated successfully"
msgstr "Cuenta actualizada exitosamente"

#: src/components/common/AttendeeTable/index.tsx:158
#: src/components/common/ProductsTable/SortableProduct/index.tsx:267
#: src/components/common/QuestionsTable/index.tsx:108
msgid "Actions"
msgstr "Comportamiento"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "Activate"
msgstr "Activar"

#: src/components/forms/CheckInListForm/index.tsx:47
msgid "Activation date"
msgstr "Fecha de activación"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:19
#: src/components/modals/EditUserModal/index.tsx:110
msgid "Active"
msgstr "Activo"

#: src/components/routes/event/attendees.tsx:59
#~ msgid "Add"
#~ msgstr "Agregar"

#: src/components/forms/CheckInListForm/index.tsx:39
msgid "Add a description for this check-in list"
msgstr "Agregue una descripción para esta lista de registro"

#: src/components/modals/ManageAttendeeModal/index.tsx:131
msgid "Add any notes about the attendee. These will not be visible to the attendee."
msgstr "Agrega cualquier nota sobre el asistente. Estas no serán visibles para el asistente."

#: src/components/modals/ManageAttendeeModal/index.tsx:133
msgid "Add any notes about the attendee..."
msgstr "Agrega cualquier nota sobre el asistente..."

#: src/components/modals/ManageOrderModal/index.tsx:165
msgid "Add any notes about the order. These will not be visible to the customer."
msgstr "Agregue notas sobre el pedido. Estas no serán visibles para el cliente."

#: src/components/modals/ManageOrderModal/index.tsx:169
msgid "Add any notes about the order..."
msgstr "Agregue notas sobre el pedido..."

#: src/components/forms/QuestionForm/index.tsx:202
msgid "Add description"
msgstr "Añadir descripción"

#: src/components/routes/event/GettingStarted/index.tsx:85
#~ msgid "Add event details and and manage event settings."
#~ msgstr "Agregue detalles del evento y administre la configuración del evento."

#: src/components/routes/event/GettingStarted/index.tsx:137
msgid "Add event details and manage event settings."
msgstr "Agrega detalles del evento y administra la configuración del evento."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:153
msgid "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"
msgstr "Agregue instrucciones para pagos offline (por ejemplo, detalles de transferencia bancaria, dónde enviar cheques, fechas límite de pago)"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add More products"
#~ msgstr "Añadir más productos"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add More tickets"
msgstr "Añadir más entradas"

#: src/components/common/HeadingCard/index.tsx:27
msgid "Add New"
msgstr "Agregar nuevo"

#: src/components/forms/QuestionForm/index.tsx:68
msgid "Add Option"
msgstr "Agregar opción"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:68
msgid "Add Product"
msgstr "Añadir producto"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:51
msgid "Add Product to Category"
msgstr "Añadir producto a la categoría"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add products"
#~ msgstr "Añadir productos"

#: src/components/common/QuestionsTable/index.tsx:281
msgid "Add question"
msgstr "Agregar pregunta"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:18
msgid "Add Tax or Fee"
msgstr "Agregar impuesto o tarifa"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add tickets"
msgstr "Agregar entradas"

#: src/components/forms/ProductForm/index.tsx:337
msgid "Add tier"
msgstr "Agregar nivel"

#: src/components/common/AddEventToCalendarButton/index.tsx:112
#: src/components/common/AddEventToCalendarButton/index.tsx:120
msgid "Add to Calendar"
msgstr "Agregar al calendario"

#: src/components/common/WebhookTable/index.tsx:223
#: src/components/routes/event/Webhooks/index.tsx:35
msgid "Add Webhook"
msgstr "Agregar Webhook"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:219
msgid "Additional Information"
msgstr "Información adicional"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/forms/ProductForm/index.tsx:348
msgid "Additional Options"
msgstr "Opciones adicionales"

#: src/components/common/OrderDetails/index.tsx:96
#: src/components/forms/QuestionForm/index.tsx:130
msgid "Address"
msgstr "DIRECCIÓN"

#: src/components/common/CheckoutQuestion/index.tsx:156
msgid "Address line 1"
msgstr "Dirección Línea 1"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:122
#: src/components/routes/product-widget/CollectInformation/index.tsx:319
#: src/components/routes/product-widget/CollectInformation/index.tsx:320
msgid "Address Line 1"
msgstr "Dirección Línea 1"

#: src/components/common/CheckoutQuestion/index.tsx:159
msgid "Address line 2"
msgstr "Línea de dirección 2"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:127
#: src/components/routes/product-widget/CollectInformation/index.tsx:324
#: src/components/routes/product-widget/CollectInformation/index.tsx:325
msgid "Address Line 2"
msgstr "Línea de dirección 2"

#: src/components/modals/EditUserModal/index.tsx:49
#: src/components/modals/InviteUserModal/index.tsx:41
msgid "Admin"
msgstr "Administración"

#: src/components/modals/EditUserModal/index.tsx:51
#: src/components/modals/InviteUserModal/index.tsx:43
msgid "Admin users have full access to events and account settings."
msgstr "Los usuarios administradores tienen acceso completo a los eventos y la configuración de la cuenta."

#: src/components/layouts/Event/index.tsx:53
#~ msgid "Affiliates"
#~ msgstr "Afiliados"

#: src/components/common/MessageList/index.tsx:21
msgid "All attendees"
msgstr "Todos los asistentes"

#: src/components/modals/SendMessageModal/index.tsx:187
msgid "All attendees of this event"
msgstr "Todos los asistentes a este evento."

#: src/components/routes/events/Dashboard/index.tsx:52
#~ msgid "All Events"
#~ msgstr "Todos los eventos"

#: src/components/common/PromoCodeTable/index.tsx:131
msgid "All Products"
msgstr "Todos los productos"

#: src/components/common/PromoCodeTable/index.tsx:130
#: src/components/forms/PromoCodeForm/index.tsx:67
#~ msgid "All Tickets"
#~ msgstr "Todas las entradas"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:157
msgid "Allow attendees associated with unpaid orders to check in"
msgstr "Permitir que los asistentes asociados con pedidos no pagados se registren"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:82
msgid "Allow search engine indexing"
msgstr "Permitir la indexación en motores de búsqueda"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:81
msgid "Allow search engines to index this event"
msgstr "Permitir que los motores de búsqueda indexen este evento"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:78
msgid "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."
msgstr "¡Casi llegamos! Estamos esperando que se procese su pago. Esto debería tomar sólo unos pocos segundos.."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:77
msgid "Amazing, Event, Keywords..."
msgstr "Increíble, evento, palabras clave..."

#: src/components/common/OrdersTable/index.tsx:207
#: src/components/forms/TaxAndFeeForm/index.tsx:71
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:36
msgid "Amount"
msgstr "Cantidad"

#: src/components/modals/CreateAttendeeModal/index.tsx:165
msgid "Amount paid ({0})"
msgstr "Importe pagado ({0})"

#: src/mutations/useExportAnswers.ts:45
msgid "An error occurred while checking export status."
msgstr "Ocurrió un error al verificar el estado de la exportación."

#: src/components/common/ErrorDisplay/index.tsx:18
msgid "An error occurred while loading the page"
msgstr "Se produjo un error al cargar la página."

#: src/components/common/QuestionsTable/index.tsx:148
msgid "An error occurred while sorting the questions. Please try again or refresh the page"
msgstr "Se produjo un error al ordenar las preguntas. Inténtalo de nuevo o actualiza la página."

#: src/components/common/TicketsTable/index.tsx:47
#~ msgid "An error occurred while sorting the tickets. Please try again or refresh the page"
#~ msgstr "Se produjo un error al clasificar los boletos. Inténtalo de nuevo o actualiza la página."

#: src/components/routes/welcome/index.tsx:75
#~ msgid "An event is the actual event you are hosting. You can add more details later."
#~ msgstr "Un evento es el evento real que estás organizando. Puedes agregar más detalles más adelante."

#: src/components/routes/welcome/index.tsx:75
msgid "An event is the gathering or occasion you’re organizing. You can add more details later."
msgstr "Un evento es la reunión o ocasión que estás organizando. Puedes agregar más detalles más adelante."

#: src/components/routes/welcome/index.tsx:23
msgid "An organizer is the company or person who is hosting the event"
msgstr "Un organizador es la empresa o persona que organiza el evento."

#: src/components/forms/StripeCheckoutForm/index.tsx:40
msgid "An unexpected error occurred."
msgstr "Ocurrió un error inesperado."

#: src/hooks/useFormErrorResponseHandler.tsx:48
msgid "An unexpected error occurred. Please try again."
msgstr "Ocurrió un error inesperado. Inténtalo de nuevo."

#: src/components/common/QuestionAndAnswerList/index.tsx:97
msgid "Answer updated successfully."
msgstr "Respuesta actualizada con éxito."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
msgid "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
msgstr "Cualquier consulta de los titulares de productos se enviará a esta dirección de correo electrónico. Esta también se usará como la dirección de \"respuesta a\" para todos los correos electrónicos enviados desde este evento"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
#~ msgid "Any queries from ticket holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
#~ msgstr "Cualquier consulta de los poseedores de entradas se enviará a esta dirección de correo electrónico. Esta también se utilizará como dirección de \"respuesta\" para todos los correos electrónicos enviados desde este evento."

#: src/components/common/WidgetEditor/index.tsx:213
msgid "Appearance"
msgstr "Apariencia"

#: src/components/routes/product-widget/SelectProducts/index.tsx:500
msgid "applied"
msgstr "aplicado"

#: src/components/common/CapacityAssignmentList/index.tsx:97
msgid "Applies to {0} products"
msgstr "Se aplica a {0} productos"

#: src/components/common/CapacityAssignmentList/index.tsx:97
#~ msgid "Applies to {0} tickets"
#~ msgstr "Se aplica a {0} entradas"

#: src/components/common/CapacityAssignmentList/index.tsx:98
msgid "Applies to 1 product"
msgstr "Se aplica a 1 producto"

#: src/components/common/CapacityAssignmentList/index.tsx:98
#~ msgid "Applies to 1 ticket"
#~ msgstr "Se aplica a 1 entrada"

#: src/components/common/FilterModal/index.tsx:253
msgid "Apply"
msgstr "Aplicar"

#: src/components/routes/product-widget/SelectProducts/index.tsx:528
msgid "Apply Promo Code"
msgstr "Aplicar código promocional"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
msgid "Apply this {type} to all new products"
msgstr "Aplicar este {type} a todos los nuevos productos"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
#~ msgid "Apply this {type} to all new tickets"
#~ msgstr "Aplicar este {tipo} a todos los tickets nuevos"

#: src/components/common/EventCard/index.tsx:167
msgid "Archive event"
msgstr "Archivar evento"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:32
msgid "Archived"
msgstr "Archivado"

#: src/components/routes/events/Dashboard/index.tsx:59
msgid "Archived Events"
msgstr "Eventos archivados"

#: src/components/common/AttendeeTable/index.tsx:76
msgid "Are you sure you want to activate this attendee?"
msgstr "¿Está seguro de que desea activar este asistente?"

#: src/components/common/EventCard/index.tsx:58
msgid "Are you sure you want to archive this event?"
msgstr "¿Está seguro de que desea archivar este evento?"

#: src/components/common/AttendeeTable/index.tsx:78
#~ msgid "Are you sure you want to cancel this attendee? This will void their product"
#~ msgstr "¿Está seguro de que desea cancelar a este asistente? Esto anulará su producto"

#: src/components/common/AttendeeTable/index.tsx:77
msgid "Are you sure you want to cancel this attendee? This will void their ticket"
msgstr "¿Está seguro de que desea cancelar este asistente? Esto anulará su boleto."

#: src/components/common/PromoCodeTable/index.tsx:37
msgid "Are you sure you want to delete this promo code?"
msgstr "¿Estás seguro de que deseas eliminar este código de promoción?"

#: src/components/common/QuestionsTable/index.tsx:164
msgid "Are you sure you want to delete this question?"
msgstr "¿Estás seguro de que deseas eliminar esta pregunta?"

#: src/components/common/WebhookTable/index.tsx:122
msgid "Are you sure you want to delete this webhook?"
msgstr "¿Estás seguro de que quieres eliminar este webhook?"

#: src/components/layouts/Event/index.tsx:68
#: src/components/routes/event/EventDashboard/index.tsx:64
msgid "Are you sure you want to make this event draft? This will make the event invisible to the public"
msgstr "¿Estás seguro de que quieres hacer este borrador de evento? Esto hará que el evento sea invisible para el público."

#: src/components/layouts/Event/index.tsx:69
#: src/components/routes/event/EventDashboard/index.tsx:65
msgid "Are you sure you want to make this event public? This will make the event visible to the public"
msgstr "¿Estás seguro de que quieres hacer público este evento? Esto hará que el evento sea visible para el público."

#: src/components/common/EventCard/index.tsx:59
msgid "Are you sure you want to restore this event? It will be restored as a draft event."
msgstr "¿Está seguro de que desea restaurar este evento? Será restaurado como un evento borrador."

#: src/components/common/CapacityAssignmentList/index.tsx:163
msgid "Are you sure you would like to delete this Capacity Assignment?"
msgstr "¿Estás seguro de que deseas eliminar esta Asignación de Capacidad?"

#: src/components/common/CheckInListList/index.tsx:183
msgid "Are you sure you would like to delete this Check-In List?"
msgstr "¿Está seguro de que desea eliminar esta lista de registro?"

#: src/components/forms/QuestionForm/index.tsx:90
#~ msgid "Ask once per attendee"
#~ msgstr "Preguntar una vez por asistente"

#: src/components/forms/QuestionForm/index.tsx:85
msgid "Ask once per order"
msgstr "Preguntar una vez por pedido"

#: src/components/forms/QuestionForm/index.tsx:91
msgid "Ask once per product"
msgstr "Preguntar una vez por producto"

#: src/components/modals/CreateWebhookModal/index.tsx:33
msgid "At least one event type must be selected"
msgstr "Debe seleccionarse al menos un tipo de evento"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Attendee"
msgstr "Asistente"

#: src/components/forms/WebhookForm/index.tsx:94
msgid "Attendee Cancelled"
msgstr "Asistente cancelado"

#: src/components/forms/WebhookForm/index.tsx:82
msgid "Attendee Created"
msgstr "Asistente creado"

#: src/components/modals/ManageAttendeeModal/index.tsx:147
msgid "Attendee Details"
msgstr "Detalles de los asistentes"

#: src/components/layouts/AuthLayout/index.tsx:73
msgid "Attendee Management"
msgstr "Gestión de asistentes"

#: src/components/layouts/CheckIn/index.tsx:150
msgid "Attendee not found"
msgstr "Asistente no encontrado"

#: src/components/modals/ManageAttendeeModal/index.tsx:153
msgid "Attendee Notes"
msgstr "Notas del asistente"

#: src/components/common/QuestionsTable/index.tsx:369
msgid "Attendee questions"
msgstr "Preguntas de los asistentes"

#: src/components/modals/ManageAttendeeModal/index.tsx:172
msgid "Attendee Ticket"
msgstr "Entrada del asistente"

#: src/components/forms/WebhookForm/index.tsx:88
msgid "Attendee Updated"
msgstr "Asistente actualizado"

#: src/components/common/OrdersTable/index.tsx:206
#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
#: src/components/common/StatBoxes/index.tsx:27
#: src/components/layouts/Event/index.tsx:99
#: src/components/modals/ManageOrderModal/index.tsx:126
#: src/components/routes/event/attendees.tsx:59
msgid "Attendees"
msgstr "Asistentes"

#: src/components/routes/event/attendees.tsx:43
msgid "Attendees Exported"
msgstr "Asistentes exportados"

#: src/components/routes/event/EventDashboard/index.tsx:239
msgid "Attendees Registered"
msgstr "Asistentes registrados"

#: src/components/modals/SendMessageModal/index.tsx:146
#~ msgid "Attendees with a specific product"
#~ msgstr "Asistentes con un producto específico"

#: src/components/modals/SendMessageModal/index.tsx:183
msgid "Attendees with a specific ticket"
msgstr "Asistentes con entrada específica"

#: src/components/common/WidgetEditor/index.tsx:233
msgid "Auto Resize"
msgstr "Cambio de tamaño automático"

#: src/components/layouts/AuthLayout/index.tsx:88
#~ msgid "Auto Workflow"
#~ msgstr "Flujo de trabajo automatizado"

#: src/components/layouts/AuthLayout/index.tsx:79
msgid "Automated entry management with multiple check-in lists and real-time validation"
msgstr "Gestión automatizada de entradas con múltiples listas de registro y validación en tiempo real"

#: src/components/common/WidgetEditor/index.tsx:236
msgid "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."
msgstr "Cambie automáticamente el tamaño de la altura del widget según el contenido. Cuando está deshabilitado, el widget ocupará la altura del contenedor."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
#~ msgid "Avg Discount/Order"
#~ msgstr "Descuento promedio/Pedido"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
#~ msgid "Avg Order Value"
#~ msgstr "Valor promedio del pedido"

#: src/components/common/OrderStatusBadge/index.tsx:15
#: src/components/modals/SendMessageModal/index.tsx:231
msgid "Awaiting offline payment"
msgstr "Esperando pago offline"

#: src/components/routes/event/orders.tsx:25
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:43
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:66
msgid "Awaiting Offline Payment"
msgstr "Esperando pago offline"

#: src/components/layouts/CheckIn/index.tsx:263
msgid "Awaiting payment"
msgstr "Esperando pago"

#: src/components/common/AttendeeTicket/index.tsx:67
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:40
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:65
msgid "Awaiting Payment"
msgstr "En espera de pago"

#: src/components/routes/welcome/index.tsx:84
msgid "Awesome Event"
msgstr "Impresionante evento"

#: src/components/forms/OrganizerForm/index.tsx:28
msgid "Awesome Organizer Ltd."
msgstr "Impresionante organizador Ltd."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:42
msgid "Back to all events"
msgstr "Volver a todos los eventos"

#: src/components/layouts/Checkout/index.tsx:73
#: src/components/routes/product-widget/CollectInformation/index.tsx:249
#: src/components/routes/product-widget/CollectInformation/index.tsx:261
msgid "Back to event page"
msgstr "Volver a la página del evento"

#: src/components/routes/auth/ForgotPassword/index.tsx:59
#: src/components/routes/auth/ResetPassword/index.tsx:66
msgid "Back to login"
msgstr "Atrás para iniciar sesión"

#: src/components/common/WidgetEditor/index.tsx:175
msgid "Background Color"
msgstr "Color de fondo"

#: src/components/routes/event/HomepageDesigner/index.tsx:144
msgid "Background Type"
msgstr "Tipo de fondo"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#~ msgid "Basic Details"
#~ msgstr "Detalles básicos"

#: src/components/modals/SendMessageModal/index.tsx:278
msgid "Before you send!"
msgstr "¡Antes de enviar!"

#: src/components/routes/event/GettingStarted/index.tsx:60
#~ msgid "Before your event can go live, there are a few things you need to do."
#~ msgstr "Antes de que su evento pueda comenzar, hay algunas cosas que debe hacer."

#: src/components/routes/event/GettingStarted/index.tsx:92
msgid "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."
msgstr "Antes de que tu evento pueda estar en línea, hay algunas cosas que debes hacer. Completa todos los pasos a continuación para comenzar."

#: src/components/routes/auth/Register/index.tsx:66
#~ msgid "Begin selling products in minutes"
#~ msgstr "Comience a vender productos en minutos"

#: src/components/routes/auth/Register/index.tsx:49
#~ msgid "Begin selling tickets in minutes"
#~ msgstr "Comience a vender boletos en minutos"

#: src/components/routes/product-widget/CollectInformation/index.tsx:313
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:142
msgid "Billing Address"
msgstr "Dirección de facturación"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:167
msgid "Billing Settings"
msgstr "Configuración de facturación"

#: src/components/layouts/AuthLayout/index.tsx:83
#~ msgid "Brand Control"
#~ msgstr "Control de marca"

#: src/components/common/LanguageSwitcher/index.tsx:28
msgid "Brazilian Portuguese"
msgstr "Portugués brasileño"

#: src/components/routes/auth/Register/index.tsx:133
msgid "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."
msgstr "Al registrarte, aceptas nuestras <0>Condiciones de servicio</0> y nuestra <1>Política de privacidad</1>."

#: src/components/forms/TaxAndFeeForm/index.tsx:52
msgid "Calculation Type"
msgstr "Tipo de cálculo"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:140
msgid "California"
msgstr "California"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:175
msgid "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."
msgstr "Se negó el permiso de la cámara. <0>Solicita permiso</0> nuevamente o, si esto no funciona, deberás <1>otorgar a esta página</1> acceso a tu cámara en la configuración de tu navegador."

#: src/components/common/AttendeeTable/index.tsx:182
#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/QuestionAndAnswerList/index.tsx:149
#: src/components/layouts/CheckIn/index.tsx:225
#: src/utilites/confirmationDialog.tsx:11
msgid "Cancel"
msgstr "Cancelar"

#: src/components/routes/profile/ManageProfile/index.tsx:126
msgid "Cancel email change"
msgstr "Cancelar cambio de correo electrónico"

#: src/components/common/OrdersTable/index.tsx:190
#: src/components/modals/RefundOrderModal/index.tsx:112
msgid "Cancel order"
msgstr "Cancelar orden"

#: src/components/modals/CancelOrderModal/index.tsx:59
msgid "Cancel Order"
msgstr "Cancelar orden"

#: src/components/modals/CancelOrderModal/index.tsx:44
msgid "Cancel Order {0}"
msgstr "Cancelar pedido {0}"

#: src/components/common/AttendeeDetails/index.tsx:32
#~ msgid "Canceled"
#~ msgstr "Cancelado"

#: src/components/modals/CancelOrderModal/index.tsx:54
msgid "Canceling will cancel all products associated with this order, and release the products back into the available pool."
msgstr "Cancelar cancelará todos los productos asociados con este pedido y devolverá los productos al inventario disponible."

#: src/components/modals/CancelOrderModal/index.tsx:54
#~ msgid "Canceling will cancel all tickets associated with this order, and release the tickets back into the available pool."
#~ msgstr "La cancelación cancelará todos los boletos asociados con este pedido y los liberará nuevamente al grupo disponible."

#: src/components/common/AttendeeTicket/index.tsx:61
#: src/components/common/OrderStatusBadge/index.tsx:12
#: src/components/routes/event/orders.tsx:24
msgid "Cancelled"
msgstr "Cancelado"

#: src/components/layouts/CheckIn/index.tsx:173
msgid "Cannot Check In"
msgstr "No se puede registrar"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:42
#: src/components/layouts/Event/index.tsx:103
msgid "Capacity"
msgstr "Capacidad"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:36
msgid "Capacity Assignment created successfully"
msgstr "Asignación de Capacidad creada con éxito"

#: src/components/common/CapacityAssignmentList/index.tsx:31
msgid "Capacity Assignment deleted successfully"
msgstr "Asignación de Capacidad eliminada con éxito"

#: src/components/routes/event/CapacityAssignments/index.tsx:32
msgid "Capacity Management"
msgstr "Gestión de Capacidad"

#: src/components/modals/CreateProductCategoryModal/index.tsx:56
msgid "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."
msgstr "Las categorías le permiten agrupar productos. Por ejemplo, puede tener una categoría para \"Entradas\" y otra para \"Mercancía\"."

#: src/components/forms/ProductForm/index.tsx:267
msgid "Categories help you organize your products. This title will be displayed on the public event page."
msgstr "Las categorías le ayudan a organizar sus productos. Este título se mostrará en la página pública del evento."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:91
msgid "Categories reordered successfully."
msgstr "Categorías reordenadas con éxito."

#: src/components/routes/event/products.tsx:96
msgid "Category"
msgstr "Categoría"

#: src/components/modals/CreateProductCategoryModal/index.tsx:37
msgid "Category Created Successfully"
msgstr "Categoría creada con éxito"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Change Cover"
msgstr "Cubierta de cambio"

#: src/components/routes/profile/ManageProfile/index.tsx:201
msgid "Change password"
msgstr "Cambiar la contraseña"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check in"
#~ msgstr "registrarse"

#: src/components/layouts/CheckIn/index.tsx:180
msgid "Check In"
msgstr "Registrarse"

#: src/components/layouts/CheckIn/index.tsx:193
msgid "Check in {0} {1}"
msgstr "Registrar {0} {1}"

#: src/components/layouts/CheckIn/index.tsx:218
msgid "Check in and mark order as paid"
msgstr "Registrar y marcar pedido como pagado"

#: src/components/layouts/CheckIn/index.tsx:209
msgid "Check in only"
msgstr "Solo registrar"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check out"
#~ msgstr "verificar"

#: src/components/layouts/CheckIn/index.tsx:177
msgid "Check Out"
msgstr "Salir"

#: src/components/layouts/Checkout/index.tsx:106
msgid "Check out this event!"
msgstr "¡Mira este evento!"

#: src/components/common/EventCard/index.tsx:156
msgid "Check-in"
msgstr "Registrarse"

#: src/components/layouts/Event/index.tsx:52
#~ msgid "Check-In"
#~ msgstr "Registrarse"

#: src/components/forms/WebhookForm/index.tsx:100
msgid "Check-in Created"
msgstr "Registro de entrada creado"

#: src/components/forms/WebhookForm/index.tsx:106
msgid "Check-in Deleted"
msgstr "Registro de entrada eliminado"

#: src/components/modals/CreateCheckInListModal/index.tsx:40
msgid "Check-In List created successfully"
msgstr "Lista de registro creada con éxito"

#: src/components/common/CheckInListList/index.tsx:33
msgid "Check-In List deleted successfully"
msgstr "Lista de registro eliminada con éxito"

#: src/components/layouts/CheckIn/index.tsx:326
msgid "Check-in list has expired"
msgstr "La lista de registro ha expirado"

#: src/components/layouts/CheckIn/index.tsx:343
msgid "Check-in list is not active"
msgstr "La lista de registro no está activa"

#: src/components/layouts/CheckIn/index.tsx:311
msgid "Check-in list not found"
msgstr "Lista de registro no encontrada"

#: src/components/layouts/Event/index.tsx:104
#: src/components/routes/event/CheckInLists/index.tsx:32
msgid "Check-In Lists"
msgstr "Listas de registro"

#: src/components/common/CheckInListList/index.tsx:162
msgid "Check-In URL copied to clipboard"
msgstr "URL de registro copiada al portapapeles"

#: src/components/forms/QuestionForm/index.tsx:114
msgid "Checkbox options allow multiple selections"
msgstr "Las opciones de casilla de verificación permiten múltiples selecciones"

#: src/components/forms/QuestionForm/index.tsx:112
msgid "Checkboxes"
msgstr "Casillas de verificación"

#: src/components/common/AttendeeDetails/index.tsx:29
msgid "Checked In"
msgstr "Registrado"

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "Checked in successfully"
#~ msgstr "Registrado con éxito"

#: src/components/layouts/Checkout/index.tsx:83
#: src/components/routes/event/Settings/index.tsx:40
msgid "Checkout"
msgstr "Pagar"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:61
msgid "Checkout Settings"
msgstr "Configuración de pago"

#: src/locales.ts:46
#~ msgid "Chinese"
#~ msgstr "Chino"

#: src/components/common/LanguageSwitcher/index.tsx:30
msgid "Chinese (Simplified)"
msgstr "Chino simplificado"

#: src/components/common/LanguageSwitcher/index.tsx:32
msgid "Chinese (Traditional)"
msgstr "Chino (Tradicional)"

#: src/components/routes/event/HomepageDesigner/index.tsx:133
msgid "Choose a color for your background"
msgstr "Elige un color para tu fondo"

#: src/components/modals/ChooseAccountModal/index.tsx:13
msgid "Choose an account"
msgstr "elige una cuenta"

#: src/components/common/CheckoutQuestion/index.tsx:162
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:134
#: src/components/routes/product-widget/CollectInformation/index.tsx:333
#: src/components/routes/product-widget/CollectInformation/index.tsx:334
msgid "City"
msgstr "Ciudad"

#: src/components/common/SearchBar/index.tsx:75
msgid "Clear Search Text"
msgstr "Borrar texto de búsqueda"

#: src/components/routes/product-widget/SelectProducts/index.tsx:288
#~ msgid "click here"
#~ msgstr "haga clic aquí"

#: src/components/common/PromoCodeTable/index.tsx:98
msgid "Click to copy"
msgstr "Haga clic para copiar"

#: src/components/routes/product-widget/SelectProducts/index.tsx:533
msgid "close"
msgstr "cerrar"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:186
#: src/components/modals/RefundOrderModal/index.tsx:125
#: src/components/routes/product-widget/SelectProducts/index.tsx:534
msgid "Close"
msgstr "Cerca"

#: src/components/layouts/Event/index.tsx:281
msgid "Close sidebar"
msgstr "Cerrar barra lateral"

#: src/components/common/PromoCodeTable/index.tsx:69
#: src/components/forms/PromoCodeForm/index.tsx:34
msgid "Code"
msgstr "Código"

#: src/components/modals/CreatePromoCodeModal/index.tsx:26
#: src/components/modals/EditPromoCodeModal/index.tsx:38
msgid "Code must be between 3 and 50 characters long"
msgstr "El código debe tener entre 3 y 50 caracteres."

#: src/components/forms/ProductForm/index.tsx:401
msgid "Collapse this product when the event page is initially loaded"
msgstr "Colapsar este producto cuando la página del evento se cargue inicialmente"

#: src/components/forms/TicketForm/index.tsx:346
#~ msgid "Collapse this ticket when the event page is initially loaded"
#~ msgstr "Colapsar este ticket cuando se cargue inicialmente la página del evento"

#: src/components/routes/event/HomepageDesigner/index.tsx:131
msgid "Color"
msgstr "Color"

#: src/components/common/WidgetEditor/index.tsx:21
msgid "Color must be a valid hex color code. Example: #ffffff"
msgstr "El color debe ser un código de color hexadecimal válido. Ejemplo: #ffffff"

#: src/components/common/WidgetEditor/index.tsx:166
#: src/components/routes/event/HomepageDesigner/index.tsx:124
msgid "Colors"
msgstr "Colores"

#: src/components/layouts/Event/index.tsx:158
msgid "Coming Soon"
msgstr "Muy pronto"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
msgid "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"
msgstr "Palabras clave separadas por comas que describen el evento. Estos serán utilizados por los motores de búsqueda para ayudar a categorizar e indexar el evento."

#: src/components/routes/product-widget/CollectInformation/index.tsx:453
msgid "Complete Order"
msgstr "Completar Orden"

#: src/components/routes/product-widget/CollectInformation/index.tsx:223
msgid "Complete payment"
msgstr "El pago completo"

#: src/components/routes/product-widget/Payment/index.tsx:131
msgid "Complete Payment"
msgstr "El pago completo"

#: src/components/layouts/AuthLayout/index.tsx:68
#~ msgid "Complete Store"
#~ msgstr "Tienda completa"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:202
#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Complete Stripe Setup"
msgstr "Completar configuración de Stripe"

#: src/components/routes/event/EventDashboard/index.tsx:127
msgid "Complete these steps to start selling tickets for your event."
msgstr "Complete estos pasos para comenzar a vender entradas para su evento."

#: src/components/modals/SendMessageModal/index.tsx:230
#: src/components/routes/event/GettingStarted/index.tsx:70
#: src/components/routes/event/orders.tsx:23
msgid "Completed"
msgstr "Terminado"

#: src/components/common/StatBoxes/index.tsx:51
msgid "Completed orders"
msgstr "Pedidos completados"

#: src/components/routes/event/EventDashboard/index.tsx:237
msgid "Completed Orders"
msgstr "Pedidos completados"

#: src/components/common/WidgetEditor/index.tsx:287
msgid "Component Code"
msgstr "Código de componente"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:35
msgid "Configured Discount"
msgstr "Descuento configurado"

#: src/utilites/confirmationDialog.tsx:11
msgid "Confirm"
msgstr "Confirmar"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:51
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:56
msgid "Confirm Email Change"
msgstr "Confirmar cambio de correo electrónico"

#: src/components/routes/profile/ManageProfile/index.tsx:199
msgid "Confirm New Password"
msgstr "Confirmar nueva contraseña"

#: src/components/routes/auth/Register/index.tsx:115
msgid "Confirm password"
msgstr "Confirmar Contraseña"

#: src/components/routes/auth/AcceptInvitation/index.tsx:112
#: src/components/routes/auth/Register/index.tsx:114
#: src/components/routes/auth/ResetPassword/index.tsx:60
msgid "Confirm Password"
msgstr "confirmar Contraseña"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:36
msgid "Confirming email address..."
msgstr "Confirmando dirección de correo electrónico..."

#: src/components/routes/event/GettingStarted/index.tsx:88
msgid "Congratulations on creating an event!"
msgstr "¡Felicidades por crear un evento!"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:173
msgid "Connect Documentation"
msgstr "Documentación de conexión"

#: src/components/routes/event/EventDashboard/index.tsx:192
msgid "Connect payment processing"
msgstr "Conectar procesamiento de pagos"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:52
#~ msgid "Connect Stripe"
#~ msgstr "Conectar raya"

#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Connect to Stripe"
msgstr "Conectar con Stripe"

#: src/components/layouts/AuthLayout/index.tsx:89
msgid "Connect with CRM and automate tasks using webhooks and integrations"
msgstr "Conéctese con el CRM y automatice tareas mediante webhooks e integraciones"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:201
#: src/components/routes/event/GettingStarted/index.tsx:154
msgid "Connect with Stripe"
msgstr "Conéctate con Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:150
msgid "Connect your Stripe account to start receiving payments."
msgstr "Conecte su cuenta Stripe para comenzar a recibir pagos."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:148
msgid "Connected to Stripe"
msgstr "Conectado a Stripe"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:98
msgid "Connection Details"
msgstr "Detalles de conexión"

#: src/components/modals/SendMessageModal/index.tsx:167
msgid "Contact Support"
msgstr "Contactar con soporte"

#: src/components/modals/SendMessageModal/index.tsx:158
msgid "Contact us to enable messaging"
msgstr "Contáctanos para habilitar la mensajería"

#: src/components/routes/event/HomepageDesigner/index.tsx:155
msgid "Content background color"
msgstr "Color de fondo del contenido"

#: src/components/common/WidgetEditor/index.tsx:31
#: src/components/common/WidgetEditor/index.tsx:217
#: src/components/layouts/Checkout/CheckoutFooter/index.tsx:38
#: src/components/routes/product-widget/CollectInformation/index.tsx:444
#: src/components/routes/product-widget/SelectProducts/index.tsx:486
msgid "Continue"
msgstr "Continuar"

#: src/components/routes/event/HomepageDesigner/index.tsx:164
msgid "Continue button text"
msgstr "Texto del botón Continuar"

#: src/components/common/WidgetEditor/index.tsx:216
msgid "Continue Button Text"
msgstr "Texto del botón Continuar"

#: src/components/modals/CreateEventModal/index.tsx:153
msgid "Continue Event Setup"
msgstr "Continuar configuración del evento"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Continue set up"
msgstr "Continuar configurando"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
#~ msgid "Continue Stripe Connect Setup"
#~ msgstr "Continuar con la configuración de Stripe Connect"

#: src/components/routes/product-widget/SelectProducts/index.tsx:337
msgid "Continue to Checkout"
msgstr "Continuar al pago"

#: src/components/routes/product-widget/CollectInformation/index.tsx:380
#~ msgid "Continue To Payment"
#~ msgstr "Continuar con el pago"

#: src/components/common/AttendeeTicket/index.tsx:91
#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:243
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copied"
msgstr "copiado"

#: src/components/common/PromoCodeTable/index.tsx:106
msgid "copied to clipboard"
msgstr "Copiado al portapapeles"

#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copy"
msgstr "Copiar"

#: src/components/common/CheckInListList/index.tsx:156
msgid "Copy Check-In URL"
msgstr "Copiar URL de registro"

#: src/components/routes/product-widget/CollectInformation/index.tsx:306
msgid "Copy details to all attendees"
msgstr "Copiar detalles a todos los asistentes."

#: src/components/common/AttendeeTicket/index.tsx:91
msgid "Copy Link"
msgstr "Copiar link"

#: src/components/common/PromoCodeTable/index.tsx:177
#: src/components/modals/ShareModal/index.tsx:243
msgid "Copy URL"
msgstr "Copiar URL"

#: src/components/common/CheckoutQuestion/index.tsx:174
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:152
#: src/components/routes/product-widget/CollectInformation/index.tsx:354
msgid "Country"
msgstr "País"

#: src/components/routes/event/HomepageDesigner/index.tsx:117
msgid "Cover"
msgstr "Cubrir"

#: src/components/routes/event/attendees.tsx:71
#: src/components/routes/event/products.tsx:74
msgid "Create"
msgstr "Crear"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
msgid "Create {0}"
msgstr "Crear {0}"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:59
msgid "Create a Product"
msgstr "Crear un producto"

#: src/components/common/PromoCodeTable/index.tsx:57
msgid "Create a Promo Code"
msgstr "Crear un código promocional"

#: src/components/modals/CreateCheckInListModal/index.tsx:63
msgid "Create a Ticket"
msgstr "Crear un boleto"

#: src/components/routes/auth/Register/index.tsx:69
msgid "Create an account or <0>{0}</0> to get started"
msgstr "Cree una cuenta o <0>{0}</0> para comenzar"

#: src/components/modals/CreateEventModal/index.tsx:120
msgid "create an organizer"
msgstr "crear un organizador"

#: src/components/layouts/AuthLayout/index.tsx:27
msgid "Create and customize your event page instantly"
msgstr "Crea y personaliza tu página de evento al instante"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Create Attendee"
msgstr "Crear asistente"

#: src/components/common/CapacityAssignmentList/index.tsx:63
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:68
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:79
#: src/components/routes/event/CapacityAssignments/index.tsx:46
msgid "Create Capacity Assignment"
msgstr "Crear Asignación de Capacidad"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:128
#: src/components/common/ProductsTable/SortableCategory/index.tsx:129
msgid "Create category"
msgstr "Crear categoría"

#: src/components/modals/CreateProductCategoryModal/index.tsx:45
#: src/components/modals/CreateProductCategoryModal/index.tsx:60
msgid "Create Category"
msgstr "Crear categoría"

#: src/components/common/CheckInListList/index.tsx:61
#: src/components/modals/CreateCheckInListModal/index.tsx:72
#: src/components/modals/CreateCheckInListModal/index.tsx:82
#: src/components/routes/event/CheckInLists/index.tsx:46
msgid "Create Check-In List"
msgstr "Crear lista de registro"

#: src/components/common/NoEventsBlankSlate/index.tsx:27
#: src/components/modals/CreateEventModal/index.tsx:85
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:81
#: src/components/routes/welcome/index.tsx:108
msgid "Create Event"
msgstr "Crear evento"

#: src/components/routes/events/Dashboard/index.tsx:91
msgid "Create new"
msgstr "Crear nuevo"

#: src/components/forms/OrganizerForm/index.tsx:111
#: src/components/modals/CreateEventModal/index.tsx:93
#: src/components/modals/CreateOrganizerModal/index.tsx:16
msgid "Create Organizer"
msgstr "Crear organizador"

#: src/components/modals/CreateProductModal/index.tsx:80
#: src/components/modals/CreateProductModal/index.tsx:88
msgid "Create Product"
msgstr "Crear producto"

#: src/components/routes/event/GettingStarted/index.tsx:70
#~ msgid "Create products for your event, set prices, and manage available quantity."
#~ msgstr "Cree productos para su evento, establezca precios y gestione la cantidad disponible."

#: src/components/modals/CreatePromoCodeModal/index.tsx:51
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
msgid "Create Promo Code"
msgstr "Crear código promocional"

#: src/components/modals/CreateQuestionModal/index.tsx:69
#: src/components/modals/CreateQuestionModal/index.tsx:74
msgid "Create Question"
msgstr "Crear pregunta"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:41
msgid "Create Tax or Fee"
msgstr "Crear impuesto o tarifa"

#: src/components/modals/CreateTicketModal/index.tsx:83
#: src/components/modals/CreateTicketModal/index.tsx:91
#: src/components/routes/event/tickets.tsx:50
#~ msgid "Create Ticket"
#~ msgstr "Crear Ticket"

#: src/components/routes/event/GettingStarted/index.tsx:122
msgid "Create tickets for your event, set prices, and manage available quantity."
msgstr "Crea entradas para tu evento, establece precios y gestiona la cantidad disponible."

#: src/components/modals/CreateWebhookModal/index.tsx:57
#: src/components/modals/CreateWebhookModal/index.tsx:67
msgid "Create Webhook"
msgstr "Crear Webhook"

#: src/components/common/OrdersTable/index.tsx:208
#: src/components/common/OrdersTable/index.tsx:281
msgid "Created"
msgstr "Creado"

#: src/components/forms/OrganizerForm/index.tsx:45
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:73
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:100
msgid "Currency"
msgstr "Divisa"

#: src/components/routes/profile/ManageProfile/index.tsx:191
msgid "Current Password"
msgstr "Contraseña actual"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:159
msgid "Custom Maps URL"
msgstr "URL de mapas personalizados"

#: src/components/common/ReportTable/index.tsx:53
msgid "Custom Range"
msgstr "Rango personalizado"

#: src/components/common/OrdersTable/index.tsx:205
msgid "Customer"
msgstr "Cliente"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:55
msgid "Customize the email and notification settings for this event"
msgstr "Personalice la configuración de correo electrónico y notificaciones para este evento"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:62
msgid "Customize the event homepage and checkout messaging"
msgstr "Personaliza la página de inicio del evento y los mensajes de pago"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:70
msgid "Customize the miscellaneous settings for this event"
msgstr "Personaliza las configuraciones diversas para este evento."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:57
msgid "Customize the SEO settings for this event"
msgstr "Personaliza la configuración de SEO para este evento"

#: src/components/routes/event/GettingStarted/index.tsx:169
msgid "Customize your event page"
msgstr "Personaliza la página de tu evento"

#: src/components/layouts/AuthLayout/index.tsx:84
msgid "Customize your event page and widget design to match your brand perfectly"
msgstr "Personalice su página de evento y el diseño del widget para que coincida perfectamente con su marca"

#: src/components/routes/event/GettingStarted/index.tsx:165
msgid "Customize your event page to match your brand and style."
msgstr "Personalice la página de su evento para que coincida con su marca y estilo."

#: src/components/routes/event/Reports/index.tsx:23
msgid "Daily Sales Report"
msgstr "Informe de ventas diarias"

#: src/components/routes/event/Reports/index.tsx:24
msgid "Daily sales, tax, and fee breakdown"
msgstr "Desglose de ventas diarias, impuestos y tarifas"

#: src/components/common/CapacityAssignmentList/index.tsx:156
#: src/components/common/CheckInListList/index.tsx:176
#: src/components/common/OrdersTable/index.tsx:186
#: src/components/common/ProductsTable/SortableProduct/index.tsx:287
#: src/components/common/PromoCodeTable/index.tsx:181
#: src/components/common/QuestionsTable/index.tsx:115
#: src/components/common/WebhookTable/index.tsx:116
msgid "Danger zone"
msgstr "Zona peligrosa"

#: src/components/common/AttendeeTable/index.tsx:179
msgid "Danger Zone"
msgstr "Zona de Peligro"

#: src/components/layouts/Event/index.tsx:89
msgid "Dashboard"
msgstr "Panel"

#: src/components/common/OrderDetails/index.tsx:39
#: src/components/forms/QuestionForm/index.tsx:136
msgid "Date"
msgstr "Fecha"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:40
#~ msgid "Date & Time"
#~ msgstr "Fecha y hora"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:38
msgid "Day one capacity"
msgstr "Capacidad del primer día"

#: src/components/forms/CheckInListForm/index.tsx:21
#~ msgid "Day one check-in list"
#~ msgstr "Lista de registro del primer día"

#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/TaxAndFeeList/index.tsx:73
msgid "Delete"
msgstr "Borrar"

#: src/components/common/CapacityAssignmentList/index.tsx:159
msgid "Delete Capacity"
msgstr "Eliminar Capacidad"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:146
#: src/components/common/ProductsTable/SortableCategory/index.tsx:147
msgid "Delete category"
msgstr "Eliminar categoría"

#: src/components/common/CheckInListList/index.tsx:179
msgid "Delete Check-In List"
msgstr "Eliminar lista de registro"

#: src/components/common/PromoCodeTable/index.tsx:185
msgid "Delete code"
msgstr "Eliminar código"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:113
msgid "Delete Cover"
msgstr "Eliminar portada"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:98
msgid "Delete Image"
msgstr "Eliminar Imagen"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:292
msgid "Delete product"
msgstr "Eliminar producto"

#: src/components/common/QuestionsTable/index.tsx:120
msgid "Delete question"
msgstr "Eliminar pregunta"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:199
#~ msgid "Delete ticket"
#~ msgstr "Eliminar billete"

#: src/components/common/WebhookTable/index.tsx:127
msgid "Delete webhook"
msgstr "Eliminar webhook"

#: src/components/forms/ProductForm/index.tsx:258
#: src/components/forms/TaxAndFeeForm/index.tsx:81
#: src/components/modals/CreateEventModal/index.tsx:135
#: src/components/modals/DuplicateEventModal/index.tsx:84
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:78
msgid "Description"
msgstr "Descripción"

#: src/components/forms/CheckInListForm/index.tsx:36
msgid "Description for check-in staff"
msgstr "Descripción para el personal de registro"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Details"
msgstr "Detalles"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
#~ msgid "Disable this capacity track capacity without stopping product sales"
#~ msgstr "Desactivar esta capacidad de seguimiento sin detener las ventas de productos"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:26
#~ msgid "Disable this capacity track capacity without stopping ticket sales"
#~ msgstr "Desactiva esta capacidad para rastrear la capacidad sin detener la venta de entradas"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
msgid "Disabling this capacity will track sales but not stop them when the limit is reached"
msgstr "Deshabilitar esta capacidad rastreará las ventas pero no las detendrá cuando se alcance el límite"

#: src/components/common/PromoCodeTable/index.tsx:70
msgid "Discount"
msgstr "Descuento"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount %"
msgstr "Descuento %"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount in {0}"
msgstr "Descuento en {0}"

#: src/components/forms/PromoCodeForm/index.tsx:43
msgid "Discount Type"
msgstr "Tipo de descuento"

#: src/components/routes/product-widget/SelectProducts/index.tsx:297
#~ msgid "Dismiss"
#~ msgstr "Despedir"

#: src/components/routes/product-widget/SelectProducts/index.tsx:354
msgid "Dismiss this message"
msgstr "Descartar este mensaje"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:190
msgid "Document Label"
msgstr "Etiqueta del documento"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:222
msgid "Documentation"
msgstr "Documentación"

#: src/components/layouts/AuthLayout/index.tsx:19
#~ msgid "Does not exist"
#~ msgstr "No existe"

#: src/components/routes/auth/Login/index.tsx:57
msgid "Don't have an account?   <0>Sign up</0>"
msgstr "¿No tienes una cuenta?   <0>Regístrate</0>"

#: src/components/routes/auth/Login/index.tsx:72
#~ msgid "Don't have an account?   <0>Sign Up</0>"
#~ msgstr "¿No tienes una cuenta?   <0>Registrarse</0>"

#: src/components/forms/ProductForm/index.tsx:157
msgid "Donation / Pay what you'd like product"
msgstr "Donación / Producto de paga lo que quieras"

#: src/components/forms/TicketForm/index.tsx:139
#~ msgid "Donation / Pay what you'd like ticket"
#~ msgstr "Donación / Pague la entrada que desee"

#: src/components/common/AddEventToCalendarButton/index.tsx:137
msgid "Download .ics"
msgstr "Descargar .ics"

#: src/components/common/DownloadCsvButton/index.tsx:48
msgid "Download CSV"
msgstr "Descargar CSV"

#: src/components/common/OrdersTable/index.tsx:162
msgid "Download invoice"
msgstr "Descargar factura"

#: src/components/layouts/Checkout/index.tsx:126
msgid "Download Invoice"
msgstr "Descargar factura"

#: src/components/modals/ShareModal/index.tsx:224
msgid "Download QR Code"
msgstr "Descargar código QR"

#: src/components/common/OrdersTable/index.tsx:111
#: src/components/layouts/Checkout/index.tsx:49
msgid "Downloading Invoice"
msgstr "Descargando factura"

#: src/components/layouts/Event/index.tsx:188
msgid "Draft"
msgstr "Borrador"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:83
msgid "Drag and drop or click"
msgstr "Arrastra y suelta o haz clic"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Drag to sort"
#~ msgstr "Arrastra para ordenar"

#: src/components/forms/QuestionForm/index.tsx:124
msgid "Dropdown selection"
msgstr "Selección desplegable"

#: src/components/modals/SendMessageModal/index.tsx:159
msgid ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."
msgstr ""
"Debido al alto riesgo de spam, requerimos una verificación manual antes de que puedas enviar mensajes.\n"
"Por favor contáctanos para solicitar acceso."

#: src/components/modals/DuplicateEventModal/index.tsx:124
msgid "Duplicate Capacity Assignments"
msgstr "Duplicar asignaciones de capacidad"

#: src/components/modals/DuplicateEventModal/index.tsx:128
msgid "Duplicate Check-In Lists"
msgstr "Duplicar listas de registro"

#: src/components/common/EventCard/index.tsx:162
msgid "Duplicate event"
msgstr "Duplicar evento"

#: src/components/modals/DuplicateEventModal/index.tsx:69
#: src/components/modals/DuplicateEventModal/index.tsx:142
msgid "Duplicate Event"
msgstr "Duplicar evento"

#: src/components/modals/DuplicateEventModal/index.tsx:132
msgid "Duplicate Event Cover Image"
msgstr "Duplicar la imagen de portada del evento"

#: src/components/modals/DuplicateEventModal/index.tsx:103
msgid "Duplicate Options"
msgstr "Duplicar opciones"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:285
#: src/components/modals/DuplicateProductModal/index.tsx:109
#: src/components/modals/DuplicateProductModal/index.tsx:113
msgid "Duplicate Product"
msgstr "Duplicar producto"

#: src/components/modals/DuplicateEventModal/index.tsx:108
msgid "Duplicate Products"
msgstr "Duplicar productos"

#: src/components/modals/DuplicateEventModal/index.tsx:120
msgid "Duplicate Promo Codes"
msgstr "Duplicar códigos promocionales"

#: src/components/modals/DuplicateEventModal/index.tsx:112
msgid "Duplicate Questions"
msgstr "Duplicar preguntas"

#: src/components/modals/DuplicateEventModal/index.tsx:116
msgid "Duplicate Settings"
msgstr "Duplicar configuraciones"

#: src/components/modals/DuplicateEventModal/index.tsx:107
#~ msgid "Duplicate Tickets"
#~ msgstr "Duplicar boletos"

#: src/components/modals/DuplicateEventModal/index.tsx:137
msgid "Duplicate Webhooks"
msgstr "Duplicar Webhooks"

#: src/components/common/LanguageSwitcher/index.tsx:24
msgid "Dutch"
msgstr "Holandés"

#: src/components/forms/ProductForm/index.tsx:78
msgid "Early bird"
msgstr "Madrugador"

#: src/components/common/TaxAndFeeList/index.tsx:65
#: src/components/modals/ManageAttendeeModal/index.tsx:221
#: src/components/modals/ManageOrderModal/index.tsx:203
msgid "Edit"
msgstr "Editar"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
msgid "Edit {0}"
msgstr "Editar {0}"

#: src/components/common/QuestionAndAnswerList/index.tsx:159
msgid "Edit Answer"
msgstr "Editar respuesta"

#: src/components/common/AttendeeTable/index.tsx:174
#~ msgid "Edit attendee"
#~ msgstr "Editar asistente"

#: src/components/modals/EditAttendeeModal/index.tsx:72
#: src/components/modals/EditAttendeeModal/index.tsx:110
#~ msgid "Edit Attendee"
#~ msgstr "Editar asistente"

#: src/components/common/CapacityAssignmentList/index.tsx:146
msgid "Edit Capacity"
msgstr "Editar Capacidad"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:66
#: src/components/modals/EditCapacityAssignmentModal/index.tsx:74
msgid "Edit Capacity Assignment"
msgstr "Editar Asignación de Capacidad"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:137
#: src/components/common/ProductsTable/SortableCategory/index.tsx:138
msgid "Edit category"
msgstr "Editar categoría"

#: src/components/common/CheckInListList/index.tsx:148
#: src/components/modals/EditCheckInListModal/index.tsx:69
#: src/components/modals/EditCheckInListModal/index.tsx:91
msgid "Edit Check-In List"
msgstr "Editar lista de registro"

#: src/components/common/PromoCodeTable/index.tsx:170
msgid "Edit Code"
msgstr "Editar código"

#: src/components/modals/EditOrganizerModal/index.tsx:63
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:51
msgid "Edit Organizer"
msgstr "Editar organizador"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:280
#: src/components/modals/EditProductModal/index.tsx:100
#: src/components/modals/EditProductModal/index.tsx:108
msgid "Edit Product"
msgstr "Editar producto"

#: src/components/modals/EditProductCategoryModal/index.tsx:62
#: src/components/modals/EditProductCategoryModal/index.tsx:70
msgid "Edit Product Category"
msgstr "Editar categoría de producto"

#: src/components/modals/EditPromoCodeModal/index.tsx:79
#: src/components/modals/EditPromoCodeModal/index.tsx:84
msgid "Edit Promo Code"
msgstr "Editar código promocional"

#: src/components/common/QuestionsTable/index.tsx:112
msgid "Edit question"
msgstr "Editar pregunta"

#: src/components/modals/EditQuestionModal/index.tsx:95
#: src/components/modals/EditQuestionModal/index.tsx:101
msgid "Edit Question"
msgstr "Editar pregunta"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:191
#: src/components/modals/EditTicketModal/index.tsx:96
#: src/components/modals/EditTicketModal/index.tsx:104
#~ msgid "Edit Ticket"
#~ msgstr "Editar ticket"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Edit user"
msgstr "Editar usuario"

#: src/components/modals/EditUserModal/index.tsx:62
#: src/components/modals/EditUserModal/index.tsx:120
msgid "Edit User"
msgstr "editar usuario"

#: src/components/common/WebhookTable/index.tsx:104
msgid "Edit webhook"
msgstr "Editar webhook"

#: src/components/modals/EditWebhookModal/index.tsx:66
#: src/components/modals/EditWebhookModal/index.tsx:87
msgid "Edit Webhook"
msgstr "Editar Webhook"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 2.50 for $2.50"
msgstr "p.ej. 2,50 por $2,50"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 23.5 for 23.5%"
msgstr "p.ej. 23,5 para 23,5%"

#: src/components/common/AttendeeDetails/index.tsx:21
#: src/components/common/AttendeeTable/index.tsx:109
#: src/components/common/OrderDetails/index.tsx:31
#: src/components/forms/OrganizerForm/index.tsx:32
#: src/components/modals/EditUserModal/index.tsx:80
#: src/components/modals/InviteUserModal/index.tsx:61
#: src/components/routes/auth/AcceptInvitation/index.tsx:98
#: src/components/routes/auth/Login/index.tsx:68
#: src/components/routes/auth/Register/index.tsx:97
#: src/components/routes/event/Settings/index.tsx:52
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:117
#: src/components/routes/profile/ManageProfile/index.tsx:138
msgid "Email"
msgstr "Correo electrónico"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
msgid "Email & Notification Settings"
msgstr "Configuración de correo electrónico y notificaciones"

#: src/components/modals/CreateAttendeeModal/index.tsx:132
#: src/components/modals/ManageAttendeeModal/index.tsx:114
#: src/components/modals/ManageOrderModal/index.tsx:157
msgid "Email address"
msgstr "Dirección de correo electrónico"

#: src/components/common/QuestionsTable/index.tsx:220
#: src/components/common/QuestionsTable/index.tsx:221
#: src/components/routes/product-widget/CollectInformation/index.tsx:298
#: src/components/routes/product-widget/CollectInformation/index.tsx:299
#: src/components/routes/product-widget/CollectInformation/index.tsx:408
#: src/components/routes/product-widget/CollectInformation/index.tsx:409
msgid "Email Address"
msgstr "Dirección de correo electrónico"

#: src/components/routes/profile/ManageProfile/index.tsx:77
msgid "Email change cancelled successfully"
msgstr "Cambio de correo electrónico cancelado exitosamente"

#: src/components/routes/profile/ManageProfile/index.tsx:116
msgid "Email change pending"
msgstr "Cambio de correo electrónico pendiente"

#: src/components/routes/profile/ManageProfile/index.tsx:151
msgid "Email confirmation resent"
msgstr "Confirmación por correo electrónico reenviada"

#: src/components/routes/profile/ManageProfile/index.tsx:90
msgid "Email confirmation resent successfully"
msgstr "La confirmación por correo electrónico se reenvió correctamente"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:66
msgid "Email footer message"
msgstr "Mensaje de pie de página de correo electrónico"

#: src/components/routes/profile/ManageProfile/index.tsx:141
msgid "Email not verified"
msgstr "Correo electrónico no verificado"

#: src/components/common/WidgetEditor/index.tsx:272
msgid "Embed Code"
msgstr "Código de inserción"

#: src/components/common/WidgetEditor/index.tsx:260
msgid "Embed Script"
msgstr "Insertar secuencia de comandos"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:181
msgid "Enable Invoicing"
msgstr "Habilitar facturación"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:21
msgid "Enable this capacity to stop product sales when the limit is reached"
msgstr "Activar esta capacidad para detener las ventas de productos cuando se alcance el límite"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:20
#~ msgid "Enable this capacity to stop ticket sales when the limit is reached"
#~ msgstr "Activa esta capacidad para detener la venta de entradas cuando se alcance el límite"

#: src/components/forms/WebhookForm/index.tsx:19
msgid "Enabled"
msgstr "Habilitado"

#: src/components/modals/CreateEventModal/index.tsx:149
#: src/components/modals/DuplicateEventModal/index.tsx:98
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:92
#: src/components/routes/welcome/index.tsx:96
msgid "End Date"
msgstr "Fecha final"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:26
#: src/components/common/EventStatusBadge/index.tsx:14
msgid "Ended"
msgstr "Terminado"

#: src/components/routes/events/Dashboard/index.tsx:57
msgid "Ended Events"
msgstr "Eventos finalizados"

#: src/components/common/LanguageSwitcher/index.tsx:16
#: src/components/common/LanguageSwitcher/index.tsx:50
#: src/components/modals/CreateAttendeeModal/index.tsx:145
#: src/components/routes/profile/ManageProfile/index.tsx:173
msgid "English"
msgstr "Inglés"

#: src/components/modals/CreateAttendeeModal/index.tsx:170
msgid "Enter an amount excluding taxes and fees."
msgstr "Ingrese un monto sin incluir impuestos ni tarifas."

#: src/components/common/OrdersTable/index.tsx:119
#: src/components/layouts/Checkout/index.tsx:57
msgid "Error"
msgstr "Error"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:27
msgid "Error confirming email address"
msgstr "Error al confirmar la dirección de correo electrónico"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:35
msgid "Error confirming email change"
msgstr "Error al confirmar el cambio de correo electrónico"

#: src/components/modals/WebhookLogsModal/index.tsx:166
msgid "Error loading logs"
msgstr "Error al cargar los registros"

#: src/components/forms/OrganizerForm/index.tsx:46
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:74
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:101
msgid "EUR"
msgstr "EUR"

#: src/components/routes/events/Dashboard/index.tsx:103
msgid "Event"
msgstr "Evento"

#: src/components/modals/CreateEventModal/index.tsx:76
#~ msgid "Event created successfully 🎉"
#~ msgstr "Evento creado exitosamente 🎉"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:161
msgid "Event Date"
msgstr "Fecha del evento"

#: src/components/routes/account/ManageAccount/sections/EventDefaultsSettings/index.tsx:5
msgid "Event Defaults"
msgstr "Valores predeterminados de eventos"

#: src/components/routes/event/Settings/index.tsx:28
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:276
msgid "Event Details"
msgstr "Detalles del evento"

#: src/components/modals/DuplicateEventModal/index.tsx:58
msgid "Event duplicated successfully"
msgstr "Evento duplicado con éxito"

#: src/components/layouts/Checkout/index.tsx:79
msgid "Event Homepage"
msgstr "Página principal del evento"

#: src/components/layouts/Event/index.tsx:166
#~ msgid "Event is not visible to the public"
#~ msgstr "El evento no es visible para el público."

#: src/components/layouts/Event/index.tsx:165
#~ msgid "Event is visible to the public"
#~ msgstr "El evento es visible para el público."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:85
msgid "Event location & venue details"
msgstr "Ubicación del evento y detalles del lugar"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:12
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
msgid "Event Not Available"
msgstr "Evento no disponible"

#: src/components/layouts/Event/index.tsx:187
#~ msgid "Event page"
#~ msgstr "Página del evento"

#: src/components/layouts/Event/index.tsx:212
msgid "Event Page"
msgstr "Página del evento"

#: src/components/common/EventCard/index.tsx:70
#: src/components/layouts/Event/index.tsx:80
#: src/components/routes/event/EventDashboard/index.tsx:76
#: src/components/routes/event/GettingStarted/index.tsx:63
msgid "Event status update failed. Please try again later"
msgstr "Error al actualizar el estado del evento. Por favor, inténtelo de nuevo más tarde"

#: src/components/common/EventCard/index.tsx:67
#: src/components/layouts/Event/index.tsx:77
#: src/components/routes/event/EventDashboard/index.tsx:73
#: src/components/routes/event/GettingStarted/index.tsx:60
msgid "Event status updated"
msgstr "Estado del evento actualizado"

#: src/components/common/WebhookTable/index.tsx:237
#: src/components/forms/WebhookForm/index.tsx:122
msgid "Event Types"
msgstr "Tipos de eventos"

#: src/components/modals/ShareModal/index.tsx:237
msgid "Event URL"
msgstr "URL del evento"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
#~ msgid "Event wdwdeNot Available"
#~ msgstr ""

#: src/components/layouts/Event/index.tsx:226
msgid "Events"
msgstr "Eventos"

#: src/components/forms/CheckInListForm/index.tsx:56
msgid "Expiration date"
msgstr "Fecha de vencimiento"

#: src/components/common/PromoCodeTable/index.tsx:73
msgid "Expires"
msgstr "Vence"

#: src/components/forms/PromoCodeForm/index.tsx:79
msgid "Expiry Date"
msgstr "Fecha de caducidad"

#: src/components/routes/event/attendees.tsx:80
#: src/components/routes/event/orders.tsx:140
msgid "Export"
msgstr "Exportar"

#: src/components/common/QuestionsTable/index.tsx:267
msgid "Export answers"
msgstr "Exportar respuestas"

#: src/mutations/useExportAnswers.ts:39
msgid "Export failed. Please try again."
msgstr "Error en la exportación. Por favor, inténtelo de nuevo."

#: src/mutations/useExportAnswers.ts:17
msgid "Export started. Preparing file..."
msgstr "Exportación iniciada. Preparando archivo..."

#: src/components/routes/event/attendees.tsx:39
msgid "Exporting Attendees"
msgstr "Exportando asistentes"

#: src/mutations/useExportAnswers.ts:33
msgid "Exporting complete. Downloading file..."
msgstr "Exportación completada. Descargando archivo..."

#: src/components/routes/event/orders.tsx:90
msgid "Exporting Orders"
msgstr "Exportando pedidos"

#: src/components/common/AttendeeTable/index.tsx:97
msgid "Failed to cancel attendee"
msgstr "No se pudo cancelar el asistente"

#: src/components/modals/CancelOrderModal/index.tsx:33
msgid "Failed to cancel order"
msgstr "No se pudo cancelar el pedido"

#: src/components/common/QuestionsTable/index.tsx:175
msgid "Failed to delete message. Please try again."
msgstr "No se pudo eliminar el mensaje. Inténtalo de nuevo."

#: src/components/common/OrdersTable/index.tsx:120
#: src/components/layouts/Checkout/index.tsx:58
msgid "Failed to download invoice. Please try again."
msgstr "No se pudo descargar la factura. Inténtalo de nuevo."

#: src/components/routes/event/attendees.tsx:48
msgid "Failed to export attendees"
msgstr "Error al exportar asistentes"

#: src/components/routes/event/attendees.tsx:39
#~ msgid "Failed to export attendees. Please try again."
#~ msgstr "No se pudieron exportar los asistentes. Inténtalo de nuevo."

#: src/components/routes/event/orders.tsx:99
msgid "Failed to export orders"
msgstr "Error al exportar pedidos"

#: src/components/routes/event/orders.tsx:88
#~ msgid "Failed to export orders. Please try again."
#~ msgstr "No se pudieron exportar los pedidos. Inténtalo de nuevo."

#: src/components/modals/EditCheckInListModal/index.tsx:79
msgid "Failed to load Check-In List"
msgstr "No se pudo cargar la lista de registro"

#: src/components/modals/EditWebhookModal/index.tsx:75
msgid "Failed to load Webhook"
msgstr "Error al cargar el Webhook"

#: src/components/common/AttendeeTable/index.tsx:51
#~ msgid "Failed to resend product email"
#~ msgstr "Error al reenviar el correo electrónico del producto"

#: src/components/common/AttendeeTable/index.tsx:50
msgid "Failed to resend ticket email"
msgstr "No se pudo reenviar el correo electrónico del ticket"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:179
msgid "Failed to sort products"
msgstr "Error al ordenar productos"

#: src/mutations/useExportAnswers.ts:15
msgid "Failed to start export job"
msgstr "No se pudo iniciar la exportación"

#: src/components/common/QuestionAndAnswerList/index.tsx:102
msgid "Failed to update answer."
msgstr "No se pudo actualizar la respuesta."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:64
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:69
msgid "Failed to upload image."
msgstr "No se pudo subir la imagen."

#: src/components/forms/TaxAndFeeForm/index.tsx:18
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Fee"
msgstr "Tarifa"

#: src/components/common/GlobalMenu/index.tsx:30
#~ msgid "Feedback"
#~ msgstr "Comentario"

#: src/components/forms/ProductForm/index.tsx:366
msgid "Fees"
msgstr "Honorarios"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:80
msgid "Fees are subject to change. You will be notified of any changes to your fee structure."
msgstr "Las tarifas están sujetas a cambios. Se te notificará cualquier cambio en la estructura de tarifas."

#: src/components/routes/event/orders.tsx:121
msgid "Filter Orders"
msgstr "Filtrar pedidos"

#: src/components/common/FilterModal/index.tsx:75
#: src/components/common/FilterModal/index.tsx:223
msgid "Filters"
msgstr "Filtros"

#: src/components/common/FilterModal/index.tsx:223
msgid "Filters ({activeFilterCount})"
msgstr "Filtros ({activeFilterCount})"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:206
msgid "First Invoice Number"
msgstr "Primer número de factura"

#: src/components/common/QuestionsTable/index.tsx:208
#: src/components/modals/CreateAttendeeModal/index.tsx:118
#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:144
#: src/components/routes/product-widget/CollectInformation/index.tsx:284
#: src/components/routes/product-widget/CollectInformation/index.tsx:395
msgid "First name"
msgstr "Primer Nombre"

#: src/components/common/QuestionsTable/index.tsx:207
#: src/components/modals/EditUserModal/index.tsx:71
#: src/components/modals/InviteUserModal/index.tsx:57
#: src/components/routes/auth/AcceptInvitation/index.tsx:92
#: src/components/routes/auth/Register/index.tsx:83
#: src/components/routes/product-widget/CollectInformation/index.tsx:283
#: src/components/routes/product-widget/CollectInformation/index.tsx:394
#: src/components/routes/profile/ManageProfile/index.tsx:135
msgid "First Name"
msgstr "Primer Nombre"

#: src/components/routes/auth/AcceptInvitation/index.tsx:29
msgid "First name must be between 1 and 50 characters"
msgstr "El nombre debe tener entre 1 y 50 caracteres."

#: src/components/common/QuestionsTable/index.tsx:349
msgid "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."
msgstr "Nombre, apellido y dirección de correo electrónico son preguntas predeterminadas y siempre se incluyen en el proceso de pago."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
msgid "First Used"
msgstr "Usado por primera vez"

#: src/components/forms/TaxAndFeeForm/index.tsx:33
msgid "Fixed"
msgstr "Fijado"

#: src/components/forms/PromoCodeForm/index.tsx:55
msgid "Fixed amount"
msgstr "Cantidad fija"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:65
msgid "Fixed Fee:"
msgstr "Tarifa fija:"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:123
msgid "Flash is not available on this device"
msgstr "Flash no está disponible en este dispositivo"

#: src/components/layouts/AuthLayout/index.tsx:58
msgid "Flexible Ticketing"
msgstr "Venta de entradas flexible"

#: src/components/routes/auth/Login/index.tsx:80
msgid "Forgot password?"
msgstr "¿Has olvidado tu contraseña?"

#: src/components/common/AttendeeTicket/index.tsx:40
#: src/components/common/Currency/index.tsx:90
#: src/components/common/OrderSummary/index.tsx:46
#: src/components/common/OrderSummary/index.tsx:99
#: src/components/common/ProductsTable/SortableProduct/index.tsx:93
#: src/components/common/ProductsTable/SortableProduct/index.tsx:103
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:53
msgid "Free"
msgstr "Gratis"

#: src/components/forms/ProductForm/index.tsx:151
msgid "Free Product"
msgstr "Producto gratuito"

#: src/components/forms/ProductForm/index.tsx:153
msgid "Free product, no payment information required"
msgstr "Producto gratuito, no se requiere información de pago"

#: src/components/forms/TicketForm/index.tsx:133
#~ msgid "Free Ticket"
#~ msgstr "Boleto gratis"

#: src/components/forms/TicketForm/index.tsx:135
#~ msgid "Free ticket, no payment information required"
#~ msgstr "Entrada gratuita, no se requiere información de pago."

#: src/components/common/LanguageSwitcher/index.tsx:20
msgid "French"
msgstr "Francés"

#: src/components/layouts/AuthLayout/index.tsx:88
msgid "Fully Integrated"
msgstr "Totalmente integrado"

#: src/components/forms/ProductForm/index.tsx:136
msgid "General"
msgstr "General"

#: src/components/common/LanguageSwitcher/index.tsx:14
msgid "German"
msgstr "Alemán"

#: src/components/layouts/AuthLayout/index.tsx:35
msgid "Get started for free, no subscription fees"
msgstr "Comienza gratis, sin tarifas de suscripción"

#: src/components/routes/event/EventDashboard/index.tsx:125
msgid "Get your event ready"
msgstr "Prepare su evento"

#: src/components/layouts/Event/index.tsx:88
msgid "Getting Started"
msgstr "Empezando"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:23
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:42
msgid "Go back to profile"
msgstr "volver al perfil"

#: src/components/routes/product-widget/CollectInformation/index.tsx:239
msgid "Go to event homepage"
msgstr "Ir a la página de inicio del evento"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:53
msgid "Go to Hi.Events"
msgstr "Ir a Hi.Events"

#: src/components/common/ErrorDisplay/index.tsx:64
msgid "Go to home page"
msgstr "Ir a la página de inicio"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:162
msgid "Go to Stripe Dashboard"
msgstr "Ir al panel de Stripe"

#: src/components/common/AddEventToCalendarButton/index.tsx:128
msgid "Google Calendar"
msgstr "Google Calendar"

#: src/components/common/EventCard/index.tsx:133
msgid "gross sales"
msgstr "ventas brutas"

#: src/components/common/StatBoxes/index.tsx:39
msgid "Gross sales"
msgstr "Ventas brutas"

#: src/components/routes/event/EventDashboard/index.tsx:274
msgid "Gross Sales"
msgstr "Ventas brutas"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:281
msgid "Guests"
msgstr "Huéspedes"

#: src/components/routes/product-widget/SelectProducts/index.tsx:495
msgid "Have a promo code?"
msgstr "¿Tienes un código de promoción?"

#: src/components/forms/OrganizerForm/index.tsx:33
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/GlobalMenu/index.tsx:24
#~ msgid "Help & Support"
#~ msgstr "Ayuda y Soporte"

#: src/components/common/WidgetEditor/index.tsx:295
msgid "Here is an example of how you can use the component in your application."
msgstr "A continuación se muestra un ejemplo de cómo puede utilizar el componente en su aplicación."

#: src/components/common/WidgetEditor/index.tsx:284
msgid "Here is the React component you can use to embed the widget in your application."
msgstr "Aquí está el componente React que puede usar para incrustar el widget en su aplicación."

#: src/components/routes/event/EventDashboard/index.tsx:101
msgid "Hi {0} 👋"
msgstr "Hola {0} 👋"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:43
msgid "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."
msgstr "Hi.Events cobra tarifas de plataforma para mantener y mejorar nuestros servicios. Estas tarifas se deducen automáticamente de cada transacción."

#: src/components/modals/CreateEventModal/index.tsx:130
#: src/components/modals/DuplicateEventModal/index.tsx:79
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:73
msgid "Hi.Events Conference {0}"
msgstr "Hola.Eventos Conferencia {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:117
msgid "Hi.Events Conference Center"
msgstr "Centro de conferencias Hi.Events"

#: src/components/layouts/AuthLayout/index.tsx:131
msgid "hi.events logo"
msgstr "hola.eventos logo"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:79
msgid "Hidden from public view"
msgstr "Oculto de la vista del público"

#: src/components/common/QuestionsTable/index.tsx:290
msgid "hidden question"
msgstr "pregunta oculta"

#: src/components/common/QuestionsTable/index.tsx:291
msgid "hidden questions"
msgstr "preguntas ocultas"

#: src/components/forms/QuestionForm/index.tsx:218
msgid "Hidden questions are only visible to the event organizer and not to the customer."
msgstr "Las preguntas ocultas sólo son visibles para el organizador del evento y no para el cliente."

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/routes/product-widget/SelectProducts/index.tsx:460
msgid "Hide"
msgstr "Esconder"

#: src/components/common/AttendeeList/index.tsx:84
msgid "Hide Answers"
msgstr "Ocultar respuestas"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:89
msgid "Hide getting started page"
msgstr "Ocultar página de inicio"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Hide hidden questions"
msgstr "Ocultar preguntas ocultas"

#: src/components/forms/ProductForm/index.tsx:399
msgid "Hide product after sale end date"
msgstr "Ocultar producto después de la fecha de finalización de la venta"

#: src/components/forms/ProductForm/index.tsx:397
msgid "Hide product before sale start date"
msgstr "Ocultar producto antes de la fecha de inicio de la venta"

#: src/components/forms/ProductForm/index.tsx:412
msgid "Hide product unless user has applicable promo code"
msgstr "Ocultar producto a menos que el usuario tenga un código promocional aplicable"

#: src/components/forms/ProductForm/index.tsx:405
msgid "Hide product when sold out"
msgstr "Ocultar producto cuando esté agotado"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:90
msgid "Hide the getting started page from the sidebar"
msgstr "Ocultar la página de inicio de la barra lateral"

#: src/components/forms/ProductForm/index.tsx:417
msgid "Hide this product from customers"
msgstr "Ocultar este producto a los clientes"

#: src/components/forms/QuestionForm/index.tsx:219
msgid "Hide this question"
msgstr "Ocultar esta pregunta"

#: src/components/forms/TicketForm/index.tsx:362
#~ msgid "Hide this ticket from customers"
#~ msgstr "Ocultar este ticket a los clientes"

#: src/components/forms/ProductForm/index.tsx:103
msgid "Hide this tier from users"
msgstr "Ocultar este nivel a los usuarios"

#: src/components/forms/TicketForm/index.tsx:344
#~ msgid "Hide ticket after sale end date"
#~ msgstr "Ocultar boleto después de la fecha de finalización de la venta"

#: src/components/forms/TicketForm/index.tsx:342
#~ msgid "Hide ticket before sale start date"
#~ msgstr "Ocultar boleto antes de la fecha de inicio de venta"

#: src/components/forms/TicketForm/index.tsx:357
#~ msgid "Hide ticket unless user has applicable promo code"
#~ msgstr "Ocultar ticket a menos que el usuario tenga un código de promoción aplicable"

#: src/components/forms/TicketForm/index.tsx:350
#~ msgid "Hide ticket when sold out"
#~ msgstr "Ocultar entrada cuando esté agotada"

#: src/components/forms/ProductForm/index.tsx:101
msgid "Hiding a product will prevent users from seeing it on the event page."
msgstr "Ocultar un producto impedirá que los usuarios lo vean en la página del evento."

#: src/components/forms/TicketForm/index.tsx:98
#~ msgid "Hiding a ticket will prevent users from seeing it on the event page."
#~ msgstr "Ocultar una entrada evitará que los usuarios la vean en la página del evento."

#: src/components/routes/event/HomepageDesigner/index.tsx:115
msgid "Homepage Design"
msgstr "Diseño de página de inicio"

#: src/components/layouts/Event/index.tsx:109
msgid "Homepage Designer"
msgstr "Diseñador de página de inicio"

#: src/components/routes/event/HomepageDesigner/index.tsx:174
msgid "Homepage Preview"
msgstr "Vista previa de la página de inicio"

#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:145
msgid "Homer"
msgstr "Homero"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
msgid "How many minutes the customer has to complete their order. We recommend at least 15 minutes"
msgstr "Cuántos minutos tiene el cliente para completar su pedido. Recomendamos al menos 15 minutos."

#: src/components/forms/PromoCodeForm/index.tsx:84
msgid "How many times can this code be used?"
msgstr "¿Cuántas veces se puede utilizar este código?"

#: src/components/common/Editor/index.tsx:75
msgid "HTML character limit exceeded: {htmlLength}/{maxLength}"
msgstr "Límite de caracteres HTML excedido: {htmlLength}/{maxLength}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:160
msgid "https://example-maps-service.com/..."
msgstr "https://example-maps-service.com/..."

#: src/components/forms/WebhookForm/index.tsx:118
msgid "https://webhook-domain.com/webhook"
msgstr "https://webhook-domain.com/webhook"

#: src/components/routes/auth/AcceptInvitation/index.tsx:118
msgid "I agree to the <0>terms and conditions</0>"
msgstr "Acepto los <0>términos y condiciones</0>"

#: src/components/routes/product-widget/Payment/index.tsx:106
msgid "I would like to pay using an offline method"
msgstr "Me gustaría pagar utilizando un método offline"

#: src/components/routes/product-widget/Payment/index.tsx:107
msgid "I would like to pay using an online method (credit card etc.)"
msgstr "Me gustaría pagar utilizando un método online (tarjeta de crédito, etc.)"

#: src/components/routes/product-widget/SelectProducts/index.tsx:315
msgid "If a new tab did not open automatically, please click the button below to continue to checkout."
msgstr "Si no se abrió una nueva pestaña automáticamente, haz clic en el botón de abajo para continuar al pago."

#: src/components/routes/product-widget/SelectProducts/index.tsx:284
#~ msgid "If a new tab did not open, please  <0><1>{0}</1>.</0>"
#~ msgstr "Si no se abrió una nueva pestaña, <0><1>{0}</1>.</0>"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:149
#~ msgid "If blank, the address will be used to generate a Google map link"
#~ msgstr "Si está en blanco, la dirección se utilizará para generar un enlace al mapa de Google."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:158
msgid "If blank, the address will be used to generate a Google Mapa link"
msgstr "Si está en blanco, se usará la dirección para generar un enlace de Google Maps"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:158
msgid "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."
msgstr "Si está habilitado, el personal de registro puede marcar a los asistentes como registrados o marcar el pedido como pagado y registrar a los asistentes. Si está deshabilitado, los asistentes asociados con pedidos no pagados no pueden registrarse."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:76
msgid "If enabled, the organizer will receive an email notification when a new order is placed"
msgstr "Si está habilitado, el organizador recibirá una notificación por correo electrónico cuando se realice un nuevo pedido."

#: src/components/routes/profile/ManageProfile/index.tsx:122
msgid "If you did not request this change, please immediately change your password."
msgstr "Si no solicitó este cambio, cambie inmediatamente su contraseña."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
msgid ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."
msgstr "Si tienes una cuenta con nosotros, recibirás un correo con instrucciones para restablecer tu contraseña."

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:108
msgid "Image deleted successfully"
msgstr "Imagen eliminada exitosamente"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
#~ msgid "Image dimensions must be between 3000px by 2000px. With a max height of 2000px and max width of 3000px"
#~ msgstr "Las dimensiones de la imagen deben estar entre 3000 px por 2000 px. Con una altura máxima de 2000 px y un ancho máximo de 3000 px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
msgid "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"
msgstr "Las dimensiones de la imagen deben estar entre 4000px por 4000px. Con una altura máxima de 4000px y un ancho máximo de 4000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:49
msgid "Image must be less than 5MB"
msgstr "La imagen debe tener menos de 5 MB."

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:63
msgid "Image uploaded successfully"
msgstr "Imagen cargada exitosamente"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:111
msgid "Image URL"
msgstr "URL de imagen"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
msgid "Image width must be at least 900px and height at least 50px"
msgstr "El ancho de la imagen debe ser de al menos 900 px y la altura de al menos 50 px."

#: src/components/layouts/AuthLayout/index.tsx:53
msgid "In-depth Analytics"
msgstr "Análisis en profundidad"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:25
#: src/components/modals/EditUserModal/index.tsx:111
msgid "Inactive"
msgstr "Inactivo"

#: src/components/modals/EditUserModal/index.tsx:107
msgid "Inactive users cannot log in."
msgstr "Los usuarios inactivos no pueden iniciar sesión."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee product page"
#~ msgstr "Incluya los detalles de conexión para su evento en línea. Estos detalles se mostrarán en la página de resumen del pedido y en la página del producto del asistente"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page"
#~ msgstr "Incluya detalles de conexión para su evento en línea. Estos detalles se mostrarán en la página de resumen del pedido y en la página de boletos de asistente."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:102
msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."
msgstr "Incluya los detalles de conexión para su evento en línea. Estos detalles se mostrarán en la página de resumen del pedido y en la página del boleto del asistente."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:54
msgid "Include tax and fees in the price"
msgstr "Incluye impuestos y tasas en el precio."

#: src/components/common/CheckInListList/index.tsx:103
msgid "Includes {0} products"
msgstr "Incluye {0} productos"

#: src/components/common/CheckInListList/index.tsx:112
#~ msgid "Includes {0} tickets"
#~ msgstr "Incluye {0} boletos"

#: src/components/common/CheckInListList/index.tsx:104
msgid "Includes 1 product"
msgstr "Incluye 1 producto"

#: src/components/common/CheckInListList/index.tsx:113
#~ msgid "Includes 1 ticket"
#~ msgstr "Incluye 1 boleto"

#: src/components/common/MessageList/index.tsx:20
msgid "Individual attendees"
msgstr "Asistentes individuales"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:100
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:121
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:137
msgid "Insert Image"
msgstr "Insertar imagen"

#: src/components/layouts/Event/index.tsx:54
#~ msgid "Integrations"
#~ msgstr "Integraciones"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation resent!"
msgstr "¡Invitación resentida!"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation revoked!"
msgstr "¡Invitación revocada!"

#: src/components/modals/InviteUserModal/index.tsx:54
#: src/components/modals/InviteUserModal/index.tsx:74
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invite User"
msgstr "Invitar usuario"

#: src/components/common/OrdersTable/index.tsx:116
#: src/components/layouts/Checkout/index.tsx:54
msgid "Invoice downloaded successfully"
msgstr "Factura descargada con éxito"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:252
msgid "Invoice Notes"
msgstr "Notas de la factura"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:197
msgid "Invoice Numbering"
msgstr "Numeración de facturas"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:177
msgid "Invoice Settings"
msgstr "Configuración de facturación"

#: src/components/modals/RefundOrderModal/index.tsx:115
msgid "Issue refund"
msgstr "Reembolso de emisión"

#: src/components/common/LanguageSwitcher/index.tsx:22
msgid "Italian"
msgstr "Italiano"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Item"
msgstr "Artículo"

#: src/components/routes/auth/Register/index.tsx:84
msgid "John"
msgstr "John"

#: src/components/modals/CreateAttendeeModal/index.tsx:126
msgid "Johnson"
msgstr "Johnson"

#: src/components/forms/ProductForm/index.tsx:77
msgid "Label"
msgstr "Etiqueta"

#: src/components/common/AttendeeDetails/index.tsx:45
#: src/components/modals/CreateAttendeeModal/index.tsx:144
#: src/components/routes/profile/ManageProfile/index.tsx:172
msgid "Language"
msgstr "Idioma"

#: src/components/common/ReportTable/index.tsx:52
msgid "Last 12 months"
msgstr "Últimos 12 meses"

#: src/components/common/ReportTable/index.tsx:47
msgid "Last 14 days"
msgstr "Últimos 14 días"

#: src/components/common/ReportTable/index.tsx:44
msgid "Last 24 hours"
msgstr "Últimas 24 horas"

#: src/components/common/ReportTable/index.tsx:48
msgid "Last 30 days"
msgstr "Últimos 30 días"

#: src/components/common/ReportTable/index.tsx:45
msgid "Last 48 hours"
msgstr "Últimas 48 horas"

#: src/components/common/ReportTable/index.tsx:50
msgid "Last 6 months"
msgstr "Últimos 6 meses"

#: src/components/common/ReportTable/index.tsx:46
msgid "Last 7 days"
msgstr "Últimos 7 días"

#: src/components/common/ReportTable/index.tsx:49
msgid "Last 90 days"
msgstr "Últimos 90 días"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Last login"
msgstr "Último acceso"

#: src/components/modals/CreateAttendeeModal/index.tsx:125
#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:150
msgid "Last name"
msgstr "Apellido"

#: src/components/common/QuestionsTable/index.tsx:212
#: src/components/common/QuestionsTable/index.tsx:213
#: src/components/modals/EditUserModal/index.tsx:72
#: src/components/modals/InviteUserModal/index.tsx:58
#: src/components/routes/auth/AcceptInvitation/index.tsx:94
#: src/components/routes/auth/Register/index.tsx:89
#: src/components/routes/product-widget/CollectInformation/index.tsx:289
#: src/components/routes/product-widget/CollectInformation/index.tsx:290
#: src/components/routes/product-widget/CollectInformation/index.tsx:400
#: src/components/routes/product-widget/CollectInformation/index.tsx:401
#: src/components/routes/profile/ManageProfile/index.tsx:137
msgid "Last Name"
msgstr "Apellido"

#: src/components/common/WebhookTable/index.tsx:239
msgid "Last Response"
msgstr "Última respuesta"

#: src/components/common/WebhookTable/index.tsx:240
msgid "Last Triggered"
msgstr "Última activación"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
msgid "Last Used"
msgstr "Última vez usado"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:56
#~ msgid "Learn more about Stripe"
#~ msgstr "Más información sobre la raya"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:191
msgid "Leave blank to use the default word \"Invoice\""
msgstr "Dejar en blanco para usar la palabra predeterminada \"Factura\""

#: src/components/routes/welcome/index.tsx:20
msgid "Let's get started by creating your first organizer"
msgstr "Comencemos creando tu primer organizador."

#: src/components/routes/event/EventDashboard/index.tsx:194
msgid "Link your Stripe account to receive funds from ticket sales."
msgstr "Vincula tu cuenta de Stripe para recibir fondos de la venta de entradas."

#: src/components/layouts/Event/index.tsx:190
msgid "Live"
msgstr "En vivo"

#: src/components/routes/event/Webhooks/index.tsx:21
msgid "Loading Webhooks"
msgstr "Cargando webhooks"

#: src/components/common/ReportTable/index.tsx:210
msgid "Loading..."
msgstr "Cargando..."

#: src/components/routes/event/Settings/index.tsx:34
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:84
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:167
msgid "Location"
msgstr "Ubicación"

#: src/components/routes/auth/Login/index.tsx:84
#: src/components/routes/auth/Register/index.tsx:71
msgid "Log in"
msgstr "Acceso"

#: src/components/routes/auth/Login/index.tsx:84
msgid "Logging in"
msgstr "Iniciando sesión"

#: src/components/routes/auth/Register/index.tsx:70
#~ msgid "Login"
#~ msgstr "Acceso"

#: src/components/common/GlobalMenu/index.tsx:47
msgid "Logout"
msgstr "Cerrar sesión"

#: src/components/common/WidgetEditor/index.tsx:331
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:170
msgid "Make billing address mandatory during checkout"
msgstr "Hacer obligatoria la dirección de facturación durante el pago"

#: src/components/routes/event/EventDashboard/index.tsx:172
#~ msgid "Make Event Live"
#~ msgstr "Hacer que el evento esté en vivo"

#: src/components/forms/QuestionForm/index.tsx:212
msgid "Make this question mandatory"
msgstr "Haz que esta pregunta sea obligatoria"

#: src/components/routes/event/EventDashboard/index.tsx:148
msgid "Make your event live"
msgstr "Haga que su evento esté en vivo"

#: src/components/common/CapacityAssignmentList/index.tsx:143
#: src/components/common/CheckInListList/index.tsx:145
#: src/components/common/EventCard/index.tsx:180
#: src/components/common/OrdersTable/index.tsx:139
#: src/components/common/OrdersTable/index.tsx:154
#: src/components/common/ProductsTable/SortableProduct/index.tsx:256
#: src/components/common/PromoCodeTable/index.tsx:167
#: src/components/layouts/Event/index.tsx:97
msgid "Manage"
msgstr "Administrar"

#: src/components/common/AttendeeTable/index.tsx:161
msgid "Manage attendee"
msgstr "Gestionar asistente"

#: src/components/common/EventCard/index.tsx:150
msgid "Manage event"
msgstr "Administrar evento"

#: src/components/common/OrdersTable/index.tsx:156
msgid "Manage order"
msgstr "Gestionar pedido"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:99
msgid "Manage payment and invoicing settings for this event."
msgstr "Gestionar las configuraciones de pago y facturación para este evento."

#: src/components/modals/CreateAttendeeModal/index.tsx:106
#~ msgid "Manage products"
#~ msgstr "Gestionar productos"

#: src/components/routes/profile/ManageProfile/index.tsx:101
msgid "Manage Profile"
msgstr "Administrar perfil"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
msgid "Manage taxes and fees which can be applied to your products"
msgstr "Gestionar impuestos y tasas que se pueden aplicar a sus productos"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
#~ msgid "Manage taxes and fees which can be applied to your tickets"
#~ msgstr "Gestiona los impuestos y tasas que se pueden aplicar a tus billetes"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
msgid "Manage tickets"
msgstr "Gestionar entradas"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:54
msgid "Manage your account details and default settings"
msgstr "Administre los detalles de su cuenta y la configuración predeterminada"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:241
msgid "Manage your payment processing and view platform fees"
msgstr "Administra tu procesamiento de pagos y consulta las tarifas de la plataforma"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:91
#~ msgid "Manage your Stripe payment details"
#~ msgstr "Gestiona tus datos de pago de Stripe"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Manage your users and their permissions"
msgstr "Gestiona tus usuarios y sus permisos"

#: src/components/forms/QuestionForm/index.tsx:211
msgid "Mandatory questions must be answered before the customer can checkout."
msgstr "Las preguntas obligatorias deben responderse antes de que el cliente pueda realizar el pago."

#: src/components/common/AttendeeTable/index.tsx:67
msgid "Manually add an Attendee"
msgstr "Agregar manualmente un asistente"

#: src/components/modals/CreateAttendeeModal/index.tsx:97
#: src/components/modals/CreateAttendeeModal/index.tsx:113
msgid "Manually Add Attendee"
msgstr "Agregar asistente manualmente"

#: src/components/modals/CreateAttendeeModal/index.tsx:148
#~ msgid "Manually adding an attendee will adjust ticket quantity."
#~ msgstr "Agregar manualmente un asistente ajustará la cantidad de entradas."

#: src/components/common/OrdersTable/index.tsx:167
msgid "Mark as paid"
msgstr "Marcar como pagado"

#: src/components/layouts/AuthLayout/index.tsx:83
msgid "Match Your Brand"
msgstr "Adapta tu marca"

#: src/components/forms/ProductForm/index.tsx:383
msgid "Maximum Per Order"
msgstr "Máximo por pedido"

#: src/components/common/AttendeeTable/index.tsx:166
msgid "Message attendee"
msgstr "Asistente del mensaje"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:273
msgid "Message Attendees"
msgstr "Mensaje a los asistentes"

#: src/components/modals/SendMessageModal/index.tsx:165
#~ msgid "Message attendees with specific products"
#~ msgstr "Enviar mensaje a asistentes con productos específicos"

#: src/components/modals/SendMessageModal/index.tsx:206
msgid "Message attendees with specific tickets"
msgstr "Enviar mensajes a los asistentes con entradas específicas"

#: src/components/layouts/AuthLayout/index.tsx:74
msgid "Message attendees, manage orders, and handle refunds all in one place"
msgstr "Envíe mensajes a los asistentes, gestione pedidos y procese reembolsos en un solo lugar"

#: src/components/common/OrdersTable/index.tsx:158
msgid "Message buyer"
msgstr "Mensaje del comprador"

#: src/components/modals/SendMessageModal/index.tsx:250
msgid "Message Content"
msgstr "Contenido del mensaje"

#: src/components/modals/SendMessageModal/index.tsx:71
msgid "Message individual attendees"
msgstr "Enviar mensajes a asistentes individuales"

#: src/components/modals/SendMessageModal/index.tsx:218
msgid "Message order owners with specific products"
msgstr "Enviar mensajes a los propietarios de pedidos con productos específicos"

#: src/components/modals/SendMessageModal/index.tsx:118
msgid "Message Sent"
msgstr "Mensaje enviado"

#: src/components/layouts/Event/index.tsx:105
#: src/components/routes/event/messages.tsx:31
msgid "Messages"
msgstr "Mensajes"

#: src/components/forms/ProductForm/index.tsx:381
msgid "Minimum Per Order"
msgstr "Mínimo por pedido"

#: src/components/forms/ProductForm/index.tsx:285
msgid "Minimum Price"
msgstr "Precio mínimo"

#: src/components/routes/event/Settings/index.tsx:58
msgid "Miscellaneous"
msgstr "Misceláneo"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:69
msgid "Miscellaneous Settings"
msgstr "Otras configuraciones"

#: src/components/layouts/AuthLayout/index.tsx:63
msgid "Mobile Check-in"
msgstr "Registro móvil"

#: src/components/forms/QuestionForm/index.tsx:106
msgid "Multi line text box"
msgstr "Cuadro de texto de varias líneas"

#: src/components/forms/ProductForm/index.tsx:165
msgid "Multiple price options. Perfect for early bird products etc."
msgstr "Múltiples opciones de precio. Perfecto para productos anticipados, etc."

#: src/components/forms/TicketForm/index.tsx:147
#~ msgid "Multiple price options. Perfect for early bird tickets etc."
#~ msgstr "Múltiples opciones de precios. Perfecto para entradas anticipadas, etc."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:71
msgid "My amazing event description..."
msgstr "Mi increíble descripción del evento..."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:65
msgid "My amazing event title..."
msgstr "El increíble título de mi evento..."

#: src/components/common/GlobalMenu/index.tsx:27
msgid "My Profile"
msgstr "Mi perfil"

#: src/components/common/QuestionAndAnswerList/index.tsx:178
msgid "N/A"
msgstr "No disponible"

#: src/components/common/WidgetEditor/index.tsx:354
msgid "Nam placerat elementum..."
msgstr "Nam placerat elementum..."

#: src/components/common/AttendeeDetails/index.tsx:13
#: src/components/common/AttendeeTable/index.tsx:108
#: src/components/common/OrderDetails/index.tsx:23
#: src/components/forms/CapaciyAssigmentForm/index.tsx:37
#: src/components/forms/CheckInListForm/index.tsx:20
#: src/components/forms/ProductForm/index.tsx:253
#: src/components/forms/TaxAndFeeForm/index.tsx:61
#: src/components/modals/CreateEventModal/index.tsx:129
#: src/components/modals/DuplicateEventModal/index.tsx:78
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:63
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:72
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:107
#: src/components/routes/welcome/index.tsx:83
msgid "Name"
msgstr "Nombre"

#: src/components/modals/CreateEventModal/index.tsx:38
msgid "Name should be less than 150 characters"
msgstr "El nombre debe tener menos de 150 caracteres."

#: src/components/common/QuestionAndAnswerList/index.tsx:181
#: src/components/common/QuestionAndAnswerList/index.tsx:289
msgid "Navigate to Attendee"
msgstr "Navegar al asistente"

#: src/components/common/PromoCodeTable/index.tsx:158
#: src/components/common/WebhookTable/index.tsx:266
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Never"
msgstr "Nunca"

#: src/components/routes/auth/AcceptInvitation/index.tsx:111
#: src/components/routes/auth/ResetPassword/index.tsx:59
#: src/components/routes/profile/ManageProfile/index.tsx:195
msgid "New Password"
msgstr "Nueva contraseña"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "No"
msgstr "No"

#: src/components/common/QuestionAndAnswerList/index.tsx:104
#~ msgid "No {0} available."
#~ msgstr "No hay {0} disponible."

#: src/components/routes/event/Webhooks/index.tsx:22
msgid "No Active Webhooks"
msgstr "No hay webhooks activos"

#: src/components/common/NoEventsBlankSlate/index.tsx:21
msgid "No archived events to show."
msgstr "No hay eventos archivados para mostrar."

#: src/components/common/AttendeeList/index.tsx:23
msgid "No attendees found for this order."
msgstr "No se encontraron asistentes para este pedido."

#: src/components/modals/ManageOrderModal/index.tsx:132
msgid "No attendees have been added to this order."
msgstr "No se han añadido asistentes a este pedido."

#: src/components/common/AttendeeTable/index.tsx:56
msgid "No Attendees to show"
msgstr "No hay asistentes para mostrar"

#: src/components/forms/CheckInListForm/index.tsx:48
msgid "No attendees will be able to check in before this date using this list"
msgstr "Ningún asistente podrá registrarse antes de esta fecha usando esta lista"

#: src/components/common/CapacityAssignmentList/index.tsx:42
msgid "No Capacity Assignments"
msgstr "No hay Asignaciones de Capacidad"

#: src/components/common/CheckInListList/index.tsx:44
msgid "No Check-In Lists"
msgstr "No hay listas de registro"

#: src/components/layouts/AuthLayout/index.tsx:34
msgid "No Credit Card Required"
msgstr "No se requiere tarjeta de crédito"

#: src/components/common/ReportTable/index.tsx:218
msgid "No data available"
msgstr "No hay datos disponibles"

#: src/components/common/ReportTable/index.tsx:214
msgid "No data to show. Please select a date range"
msgstr "No hay datos para mostrar. Por favor selecciona un rango de fechas"

#: src/components/forms/PromoCodeForm/index.tsx:47
msgid "No Discount"
msgstr "Sin descuento"

#: src/components/common/NoEventsBlankSlate/index.tsx:20
msgid "No ended events to show."
msgstr "No hay eventos finalizados para mostrar."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:88
#~ msgid "No events for this organizer"
#~ msgstr "No hay eventos para este organizador"

#: src/components/common/NoEventsBlankSlate/index.tsx:14
msgid "No events to show"
msgstr "No hay eventos para mostrar"

#: src/components/common/FilterModal/index.tsx:230
msgid "No filters available"
msgstr "No hay filtros disponibles"

#: src/components/modals/WebhookLogsModal/index.tsx:177
msgid "No logs found"
msgstr "No se encontraron registros"

#: src/components/common/MessageList/index.tsx:69
msgid "No messages to show"
msgstr "No hay mensajes para mostrar"

#: src/components/common/OrdersTable/index.tsx:63
msgid "No orders to show"
msgstr "No hay pedidos para mostrar"

#: src/components/routes/product-widget/Payment/index.tsx:76
msgid "No payment methods are currently available. Please contact the event organizer for assistance."
msgstr "No hay métodos de pago disponibles actualmente. Por favor, contacte al organizador del evento para obtener ayuda."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:39
msgid "No Payment Required"
msgstr "No se requiere pago"

#: src/components/modals/ManageAttendeeModal/index.tsx:177
msgid "No product associated with this attendee."
msgstr "No hay ningún producto asociado con este asistente."

#: src/components/common/ProductSelector/index.tsx:33
msgid "No products available for selection"
msgstr "No hay productos disponibles para seleccionar"

#: src/components/modals/CreateProductCategoryModal/index.tsx:22
msgid "No products available in this category."
msgstr "No hay productos disponibles en esta categoría."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:39
msgid "No Products Yet"
msgstr "Aún no hay productos"

#: src/components/common/PromoCodeTable/index.tsx:46
msgid "No Promo Codes to show"
msgstr "No hay códigos promocionales para mostrar"

#: src/components/modals/ManageAttendeeModal/index.tsx:193
msgid "No questions answered by this attendee."
msgstr "No hay preguntas respondidas por este asistente."

#: src/components/modals/ViewAttendeeModal/index.tsx:75
#~ msgid "No questions have been answered by this attendee."
#~ msgstr "Este asistente no ha respondido a ninguna pregunta."

#: src/components/modals/ManageOrderModal/index.tsx:119
msgid "No questions have been asked for this order."
msgstr "No se han hecho preguntas para este pedido."

#: src/components/common/WebhookTable/index.tsx:174
msgid "No response"
msgstr "Sin respuesta"

#: src/components/common/WebhookTable/index.tsx:141
msgid "No responses yet"
msgstr "Aún no hay respuestas"

#: src/components/common/NoResultsSplash/index.tsx:24
msgid "No results"
msgstr "No hay resultados"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:20
msgid "No Search Results"
msgstr "Sin resultados de búsqueda"

#: src/components/common/NoResultsSplash/index.tsx:29
msgid "No search results."
msgstr "Sin resultados de búsqueda."

#: src/components/common/TaxAndFeeList/index.tsx:101
msgid "No Taxes or Fees have been added."
msgstr "No se han agregado impuestos ni tarifas."

#: src/components/common/TicketsTable/index.tsx:65
#~ msgid "No tickets to show"
#~ msgstr "No hay entradas para mostrar"

#: src/components/modals/WebhookLogsModal/index.tsx:180
msgid "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."
msgstr "Aún no se han registrado eventos de webhook para este punto de acceso. Los eventos aparecerán aquí una vez que se activen."

#: src/components/common/WebhookTable/index.tsx:201
msgid "No Webhooks"
msgstr "No hay Webhooks"

#: src/components/common/PromoCodeTable/index.tsx:82
msgid "None"
msgstr "Ninguno"

#: src/components/common/ProductPriceAvailability/index.tsx:27
#: src/components/common/ProductPriceAvailability/index.tsx:48
msgid "Not available"
msgstr "No disponible"

#: src/components/common/AttendeeCheckInTable/index.tsx:125
#~ msgid "Not Checked In"
#~ msgstr "No registrado"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "Not On Sale"
msgstr "No a la venta"

#: src/components/modals/ManageAttendeeModal/index.tsx:130
#: src/components/modals/ManageOrderModal/index.tsx:164
msgid "Notes"
msgstr "Notas"

#: src/components/common/ReportTable/index.tsx:240
msgid "Nothing to show yet"
msgstr "Nada que mostrar aún"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:72
msgid "Notification Settings"
msgstr "Configuración de las notificaciones"

#: src/components/modals/RefundOrderModal/index.tsx:108
msgid "Notify buyer of refund"
msgstr "Notificar al comprador del reembolso"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:75
msgid "Notify organizer of new orders"
msgstr "Notificar al organizador de nuevos pedidos"

#: src/components/routes/welcome/index.tsx:72
msgid "Now let's create your first event"
msgstr "Ahora creemos tu primer evento."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:217
msgid "Number of days allowed for payment (leave blank to omit payment terms from invoices)"
msgstr "Número de días permitidos para el pago (dejar en blanco para omitir los términos de pago en las facturas)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:199
msgid "Number Prefix"
msgstr "Prefijo del número"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:145
msgid "Offline orders are not reflected in event statistics until the order is marked as paid."
msgstr "Los pedidos offline no se reflejan en las estadísticas del evento hasta que el pedido se marque como pagado."

#: src/components/routes/product-widget/Payment/index.tsx:66
msgid "Offline payment failed. Please try again or contact the event organizer."
msgstr "El pago offline ha fallado. Por favor, inténtelo de nuevo o contacte al organizador del evento."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:151
msgid "Offline Payment Instructions"
msgstr "Instrucciones de pago offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:90
msgid "Offline Payments"
msgstr "Pagos offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:139
msgid "Offline Payments Information"
msgstr "Información de pagos offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:134
msgid "Offline Payments Settings"
msgstr "Configuración de pagos offline"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:71
msgid "On sale"
msgstr "En venta"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "On Sale"
msgstr "En venta"

#: src/components/common/NoEventsBlankSlate/index.tsx:19
msgid "Once you create an event, you'll see it here."
msgstr "Una vez que crees un evento, lo verás aquí."

#: src/components/common/ReportTable/index.tsx:245
msgid "Once you start collecting data, you'll see it here."
msgstr "Una vez que comiences a recopilar datos, los verás aquí."

#: src/components/routes/event/GettingStarted/index.tsx:179
msgid "Once you're ready, set your event live and start selling products."
msgstr "Una vez que esté listo, ponga su evento en vivo y comience a vender productos."

#: src/components/routes/event/GettingStarted/index.tsx:108
#~ msgid "Once you're ready, set your event live and start selling tickets."
#~ msgstr "Una vez que esté listo, configure su evento en vivo y comience a vender entradas."

#: src/components/common/EventStatusBadge/index.tsx:18
msgid "Ongoing"
msgstr "En curso"

#: src/components/common/EventCard/index.tsx:118
msgid "Online event"
msgstr "evento en línea"

#: src/components/common/OnlineEventDetails/index.tsx:9
msgid "Online Event Details"
msgstr "Detalles del evento en línea"

#: src/components/modals/SendMessageModal/index.tsx:279
msgid ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."
msgstr ""
"Sólo los correos electrónicos importantes que estén directamente relacionados con este evento deben enviarse mediante este formulario.\n"
"Cualquier uso indebido, incluido el envío de correos electrónicos promocionales, dará lugar a la prohibición inmediata de la cuenta."

#: src/components/modals/SendMessageModal/index.tsx:226
msgid "Only send to orders with these statuses"
msgstr "Enviar solo a pedidos con estos estados"

#: src/components/common/CheckInListList/index.tsx:167
msgid "Open Check-In Page"
msgstr "Abrir la página de registro"

#: src/components/layouts/Event/index.tsx:288
msgid "Open sidebar"
msgstr "Abrir barra lateral"

#: src/components/forms/QuestionForm/index.tsx:45
msgid "Option {i}"
msgstr "Opción {i}"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:253
msgid "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"
msgstr "Información adicional opcional que aparecerá en todas las facturas (por ejemplo, términos de pago, cargos por pago atrasado, política de devoluciones)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:200
msgid "Optional prefix for invoice numbers (e.g., INV-)"
msgstr "Prefijo opcional para los números de factura (por ejemplo, INV-)"

#: src/components/forms/QuestionForm/index.tsx:28
msgid "Options"
msgstr "Opciones"

#: src/components/modals/CreateEventModal/index.tsx:118
msgid "or"
msgstr "o"

#: src/components/common/AttendeeTable/index.tsx:110
msgid "Order"
msgstr "Orden"

#: src/components/common/OrdersTable/index.tsx:212
#~ msgid "Order #"
#~ msgstr "Orden #"

#: src/components/forms/WebhookForm/index.tsx:76
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:63
msgid "Order Cancelled"
msgstr "Orden cancelada"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:62
msgid "Order Completed"
msgstr "Pedido completado"

#: src/components/forms/WebhookForm/index.tsx:52
msgid "Order Created"
msgstr "Pedido creado"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:122
msgid "Order Date"
msgstr "Fecha de orden"

#: src/components/modals/ManageAttendeeModal/index.tsx:166
#: src/components/modals/ManageOrderModal/index.tsx:86
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:266
msgid "Order Details"
msgstr "Detalles del pedido"

#: src/components/modals/ViewOrderModal/index.tsx:30
#~ msgid "Order Details {0}"
#~ msgstr "Detalles del pedido {0}"

#: src/components/modals/CancelOrderModal/index.tsx:29
msgid "Order has been canceled and the order owner has been notified."
msgstr "El pedido ha sido cancelado y se ha notificado al propietario del pedido."

#: src/components/common/OrdersTable/index.tsx:79
msgid "Order marked as paid"
msgstr "Pedido marcado como pagado"

#: src/components/forms/WebhookForm/index.tsx:64
msgid "Order Marked as Paid"
msgstr "Pedido marcado como pagado"

#: src/components/modals/ManageOrderModal/index.tsx:92
msgid "Order Notes"
msgstr "Notas del pedido"

#: src/components/common/MessageList/index.tsx:23
msgid "Order owner"
msgstr "Propietario del pedido"

#: src/components/modals/SendMessageModal/index.tsx:191
msgid "Order owners with a specific product"
msgstr "Propietarios de pedidos con un producto específico"

#: src/components/common/MessageList/index.tsx:19
msgid "Order owners with products"
msgstr "Propietarios de pedidos con productos"

#: src/components/common/QuestionsTable/index.tsx:313
#: src/components/common/QuestionsTable/index.tsx:355
msgid "Order questions"
msgstr "Preguntas sobre pedidos"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:112
msgid "Order Reference"
msgstr "Pedir Referencia"

#: src/components/forms/WebhookForm/index.tsx:70
msgid "Order Refunded"
msgstr "Pedido reembolsado"

#: src/components/routes/event/orders.tsx:45
msgid "Order Status"
msgstr "Estado del pedido"

#: src/components/modals/SendMessageModal/index.tsx:228
msgid "Order statuses"
msgstr "Estados de los pedidos"

#: src/components/layouts/Checkout/CheckoutSidebar/index.tsx:28
#: src/components/modals/ManageOrderModal/index.tsx:105
msgid "Order Summary"
msgstr "Resumen del pedido"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:90
msgid "Order timeout"
msgstr "Tiempo de espera del pedido"

#: src/components/forms/WebhookForm/index.tsx:58
msgid "Order Updated"
msgstr "Pedido actualizado"

#: src/components/layouts/Event/index.tsx:100
#: src/components/routes/event/orders.tsx:113
msgid "Orders"
msgstr "Pedidos"

#: src/components/common/StatBoxes/index.tsx:51
#: src/components/routes/event/EventDashboard/index.tsx:105
#~ msgid "Orders Created"
#~ msgstr "Órdenes creadas"

#: src/components/routes/event/orders.tsx:94
msgid "Orders Exported"
msgstr "Pedidos exportados"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:235
msgid "Organization Address"
msgstr "Dirección de la organización"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:226
msgid "Organization Details"
msgstr "Detalles de la organización"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:228
msgid "Organization Name"
msgstr "Nombre de la organización"

#: src/components/modals/EditUserModal/index.tsx:55
#: src/components/modals/InviteUserModal/index.tsx:47
#: src/components/routes/events/Dashboard/index.tsx:113
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:186
msgid "Organizer"
msgstr "Organizador"

#: src/components/modals/CreateEventModal/index.tsx:41
msgid "Organizer is required"
msgstr "Se requiere organizador"

#: src/components/forms/OrganizerForm/index.tsx:27
msgid "Organizer Name"
msgstr "Nombre del organizador"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
msgid "Organizers can only manage events and products. They cannot manage users, account settings or billing information."
msgstr "Los organizadores solo pueden gestionar eventos y productos. No pueden gestionar usuarios, configuraciones de cuenta o información de facturación."

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
#~ msgid "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."
#~ msgstr "Los organizadores sólo pueden gestionar eventos y entradas. No pueden administrar usuarios, configuraciones de cuenta o información de facturación."

#: src/components/layouts/Event/index.tsx:87
msgid "Overview"
msgstr "Resumen"

#: src/components/common/WidgetEditor/index.tsx:223
msgid "Padding"
msgstr "Relleno"

#: src/components/routes/event/HomepageDesigner/index.tsx:150
msgid "Page background color"
msgstr "Color de fondo de la página"

#: src/components/common/ErrorDisplay/index.tsx:13
msgid "Page not found"
msgstr "Página no encontrada"

#: src/components/common/StatBoxes/index.tsx:45
msgid "Page views"
msgstr "Vistas de página"

#: src/components/forms/ProductForm/index.tsx:358
msgid "page."
msgstr "página."

#: src/components/modals/CreateAttendeeModal/index.tsx:180
msgid "paid"
msgstr "pagado"

#: src/components/forms/ProductForm/index.tsx:145
msgid "Paid Product"
msgstr "Producto de pago"

#: src/components/forms/TicketForm/index.tsx:127
#~ msgid "Paid Ticket"
#~ msgstr "Boleto pagado"

#: src/components/routes/event/orders.tsx:30
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:54
msgid "Partially Refunded"
msgstr "Reembolsado parcialmente"

#: src/components/routes/auth/Login/index.tsx:73
#: src/components/routes/auth/Register/index.tsx:106
#: src/components/routes/profile/ManageProfile/index.tsx:109
msgid "Password"
msgstr "Contraseña"

#: src/components/routes/auth/AcceptInvitation/index.tsx:30
msgid "Password must be a minimum  of 8 characters"
msgstr "La contraseña debe tener un mínimo de 8 caracteres."

#: src/components/routes/auth/Register/index.tsx:36
msgid "Password must be at least 8 characters"
msgstr "La contraseña debe tener al menos 8 caracteres"

#: src/components/routes/auth/ResetPassword/index.tsx:46
msgid "Password reset successfully. Please login with your new password."
msgstr "Restablecimiento de contraseña exitoso. Por favor inicie sesión con su nueva contraseña."

#: src/components/routes/auth/AcceptInvitation/index.tsx:31
#: src/components/routes/auth/Register/index.tsx:37
msgid "Passwords are not the same"
msgstr "Las contraseñas no son similares"

#: src/components/common/WidgetEditor/index.tsx:269
msgid "Paste this where you want the widget to appear."
msgstr "Pega esto donde quieras que aparezca el widget."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:104
msgid "Paste URL"
msgstr "Pega la URL"

#: src/components/modals/CreateAttendeeModal/index.tsx:119
msgid "Patrick"
msgstr "Patricio"

#: src/components/modals/CreateAttendeeModal/index.tsx:133
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/forms/WebhookForm/index.tsx:25
msgid "Paused"
msgstr "Pausado"

#: src/components/forms/StripeCheckoutForm/index.tsx:123
msgid "Payment"
msgstr "Pago"

#: src/components/routes/event/Settings/index.tsx:64
msgid "Payment & Invoicing"
msgstr "Pago y facturación"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:98
msgid "Payment & Invoicing Settings"
msgstr "Configuración de pago y facturación"

#: src/components/routes/account/ManageAccount/index.tsx:38
msgid "Payment & Plan"
msgstr "Pago y plan"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:216
msgid "Payment Due Period"
msgstr "Período de vencimiento del pago"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:41
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:64
msgid "Payment Failed"
msgstr "Pago fallido"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:228
#: src/components/routes/product-widget/Payment/PaymentMethods/Offline/index.tsx:14
msgid "Payment Instructions"
msgstr "Instrucciones de pago"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:105
msgid "Payment Methods"
msgstr "Métodos de pago"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:138
msgid "Payment Processing"
msgstr "Procesamiento de pagos"

#: src/components/common/OrderDetails/index.tsx:76
msgid "Payment provider"
msgstr "Proveedor de pago"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:42
msgid "Payment Received"
msgstr "Pago recibido"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:240
msgid "Payment Settings"
msgstr "Configuración de pagos"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:135
msgid "Payment Status"
msgstr "Estado del pago"

#: src/components/forms/StripeCheckoutForm/index.tsx:60
msgid "Payment succeeded!"
msgstr "¡Pago exitoso!"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:214
msgid "Payment Terms"
msgstr "Términos de pago"

#: src/components/forms/PromoCodeForm/index.tsx:51
#: src/components/forms/TaxAndFeeForm/index.tsx:27
msgid "Percentage"
msgstr "Porcentaje"

#: src/components/forms/TaxAndFeeForm/index.tsx:71
msgid "Percentage Amount"
msgstr "Monto porcentual"

#: src/components/routes/product-widget/Payment/index.tsx:122
msgid "Place Order"
msgstr "Realizar pedido"

#: src/components/common/WidgetEditor/index.tsx:257
msgid "Place this in the <head> of your website."
msgstr "Coloque esto en el <head> de su sitio web."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:40
msgid "Platform Fees"
msgstr "Tarifas de la plataforma"

#: src/components/forms/QuestionForm/index.tsx:31
msgid "Please add at least one option"
msgstr "Por favor agregue al menos una opción"

#: src/hooks/useFormErrorResponseHandler.tsx:18
msgid "Please check the provided information is correct"
msgstr "Por favor verifique que la información proporcionada sea correcta."

#: src/components/routes/auth/Login/index.tsx:41
msgid "Please check your email and password and try again"
msgstr "Por favor revisa tu correo electrónico y contraseña y vuelve a intentarlo."

#: src/components/routes/auth/AcceptInvitation/index.tsx:32
#: src/components/routes/auth/Register/index.tsx:38
msgid "Please check your email is valid"
msgstr "Por favor verifique que su correo electrónico sea válido"

#: src/components/routes/profile/ManageProfile/index.tsx:152
msgid "Please check your email to confirm your email address"
msgstr "Por favor revise su correo electrónico para confirmar su dirección de correo electrónico."

#: src/components/routes/auth/AcceptInvitation/index.tsx:86
msgid "Please complete the form below to accept your invitation"
msgstr "Por favor complete el siguiente formulario para aceptar su invitación."

#: src/components/routes/product-widget/SelectProducts/index.tsx:306
msgid "Please continue in the new tab"
msgstr "Por favor continúa en la nueva pestaña."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:47
msgid "Please create a product"
msgstr "Por favor, cree un producto"

#: src/components/modals/CreateCheckInListModal/index.tsx:51
msgid "Please create a ticket"
msgstr "Por favor, crea un ticket"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:45
msgid "Please enter a valid image URL that points to an image."
msgstr "Por favor, introduzca una URL de imagen válida que apunte a una imagen."

#: src/components/modals/CreateWebhookModal/index.tsx:30
msgid "Please enter a valid URL"
msgstr "Por favor, introduce una URL válida"

#: src/components/routes/auth/ResetPassword/index.tsx:55
msgid "Please enter your new password"
msgstr "Por favor ingrese su nueva contraseña"

#: src/components/modals/CancelOrderModal/index.tsx:52
msgid "Please Note"
msgstr "Tenga en cuenta"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:34
msgid "Please provide an image."
msgstr "Por favor, proporciona una imagen."

#: src/components/common/TicketsTable/index.tsx:84
#~ msgid "Please remove filters and set sorting to \"Homepage order\" to enable sorting"
#~ msgstr "Elimine los filtros y configure la clasificación en \"Orden de página de inicio\" para habilitar la clasificación."

#: src/components/layouts/Checkout/index.tsx:159
msgid "Please return to the event page to start over."
msgstr "Por favor, regresa a la página del evento para comenzar de nuevo."

#: src/components/modals/SendMessageModal/index.tsx:195
msgid "Please select"
msgstr "Por favor seleccione"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:53
msgid "Please select an image."
msgstr "Por favor, selecciona una imagen."

#: src/components/routes/product-widget/SelectProducts/index.tsx:237
msgid "Please select at least one product"
msgstr "Por favor, seleccione al menos un producto"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:227
#~ msgid "Please select at least one ticket"
#~ msgstr "Por favor seleccione al menos un boleto"

#: src/components/routes/event/attendees.tsx:49
#: src/components/routes/event/orders.tsx:100
msgid "Please try again."
msgstr "Por favor, inténtalo de nuevo."

#: src/components/routes/profile/ManageProfile/index.tsx:142
msgid "Please verify your email address to access all features"
msgstr "Verifique su dirección de correo electrónico para acceder a todas las funciones."

#: src/components/routes/event/attendees.tsx:40
msgid "Please wait while we prepare your attendees for export..."
msgstr "Por favor, espera mientras preparamos la exportación de tus asistentes..."

#: src/components/common/OrdersTable/index.tsx:112
#: src/components/layouts/Checkout/index.tsx:50
msgid "Please wait while we prepare your invoice..."
msgstr "Por favor, espere mientras preparamos su factura..."

#: src/components/routes/event/orders.tsx:91
msgid "Please wait while we prepare your orders for export..."
msgstr "Por favor, espera mientras preparamos la exportación de tus pedidos..."

#: src/components/common/LanguageSwitcher/index.tsx:26
msgid "Portuguese"
msgstr "Portugués"

#: src/locales.ts:47
#~ msgid "Portuguese (Brazil)"
#~ msgstr "Portugués (Brasil)"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:74
msgid "Post Checkout message"
msgstr "Mensaje posterior al pago"


#: src/components/forms/StripeCheckoutForm/index.tsx:137
#~ msgid "Powered by Stripe"
#~ msgstr "Desarrollado por Stripe"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:67
msgid "Pre Checkout message"
msgstr "Mensaje previo al pago"

#: src/components/common/QuestionsTable/index.tsx:347
msgid "Preview"
msgstr "Avance"

#: src/components/layouts/Event/index.tsx:205
#: src/components/layouts/Event/index.tsx:209
msgid "Preview Event page"
msgstr "Vista previa de la página del evento"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:236
#: src/components/forms/ProductForm/index.tsx:73
#: src/components/forms/ProductForm/index.tsx:285
msgid "Price"
msgstr "Precio"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:78
msgid "Price display mode"
msgstr "Modo de visualización de precios"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:88
msgid "Price not set"
msgstr "Precio no establecido"

#: src/components/forms/ProductForm/index.tsx:322
msgid "Price tiers"
msgstr "Niveles de precios"

#: src/components/forms/ProductForm/index.tsx:228
msgid "Price Type"
msgstr "Tipo de precio"

#: src/components/common/WidgetEditor/index.tsx:182
msgid "Primary Color"
msgstr "Color primario"

#: src/components/routes/event/HomepageDesigner/index.tsx:156
msgid "Primary Colour"
msgstr "Color primario"

#: src/components/common/WidgetEditor/index.tsx:190
#: src/components/routes/event/HomepageDesigner/index.tsx:158
msgid "Primary Text Color"
msgstr "Color de texto principal"

#: src/components/common/AttendeeTicket/index.tsx:81
msgid "Print"
msgstr "Imprimir"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:288
msgid "Print All Tickets"
msgstr "Imprimir todas las entradas"

#: src/components/layouts/Checkout/index.tsx:114
msgid "Print Tickets"
msgstr "Imprimir boletos"

#: src/components/common/AttendeeTable/index.tsx:190
#~ msgid "product"
#~ msgstr "producto"

#: src/components/common/AttendeeDetails/index.tsx:37
#: src/components/modals/ManageAttendeeModal/index.tsx:119
msgid "Product"
msgstr "Producto"

#: src/components/forms/ProductForm/index.tsx:266
msgid "Product Category"
msgstr "Categoría de producto"

#: src/components/modals/EditProductCategoryModal/index.tsx:53
msgid "Product category updated successfully."
msgstr "Categoría de producto actualizada con éxito."

#: src/components/forms/WebhookForm/index.tsx:34
msgid "Product Created"
msgstr "Producto creado"

#: src/components/forms/WebhookForm/index.tsx:46
msgid "Product Deleted"
msgstr "Producto eliminado"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:55
msgid "Product deleted successfully"
msgstr "Producto eliminado con éxito"

#: src/components/common/AttendeeTable/index.tsx:50
#~ msgid "Product email has been resent to attendee"
#~ msgstr "El correo electrónico del producto se ha reenviado al asistente"

#: src/components/forms/ProductForm/index.tsx:236
msgid "Product Price Type"
msgstr "Tipo de precio del producto"

#: src/components/common/QuestionsTable/index.tsx:328
msgid "Product questions"
msgstr "Preguntas sobre el producto"

#: src/components/routes/event/EventDashboard/index.tsx:217
#: src/components/routes/event/Reports/index.tsx:17
msgid "Product Sales"
msgstr "Ventas de productos"

#: src/components/routes/event/Reports/index.tsx:18
msgid "Product sales, revenue, and tax breakdown"
msgstr "Desglose de ventas de productos, ingresos e impuestos"

#: src/components/common/ProductSelector/index.tsx:63
msgid "Product Tier"
msgstr "Nivel del producto"

#: src/components/forms/ProductForm/index.tsx:214
#: src/components/forms/ProductForm/index.tsx:221
msgid "Product Type"
msgstr "Tipo de producto"

#: src/components/forms/WebhookForm/index.tsx:40
msgid "Product Updated"
msgstr "Producto actualizado"

#: src/components/common/WidgetEditor/index.tsx:313
msgid "Product Widget Preview"
msgstr "Vista previa del widget de producto"

#: src/components/common/PromoCodeTable/index.tsx:148
msgid "Product(s)"
msgstr "Producto(s)"

#: src/components/common/PromoCodeTable/index.tsx:72
msgid "Products"
msgstr "Productos"

#: src/components/common/EventCard/index.tsx:126
msgid "products sold"
msgstr "productos vendidos"

#: src/components/common/StatBoxes/index.tsx:21
msgid "Products sold"
msgstr "Productos vendidos"

#: src/components/routes/event/EventDashboard/index.tsx:238
msgid "Products Sold"
msgstr "Productos vendidos"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:178
msgid "Products sorted successfully"
msgstr "Productos ordenados con éxito"

#: src/components/layouts/AuthLayout/index.tsx:43
msgid "Products, merchandise, and flexible pricing options"
msgstr "Productos, mercancía y opciones de precios flexibles"

#: src/components/routes/profile/ManageProfile/index.tsx:106
msgid "Profile"
msgstr "Perfil"

#: src/components/routes/profile/ManageProfile/index.tsx:59
msgid "Profile updated successfully"
msgstr "perfil actualizado con éxito"

#: src/components/routes/product-widget/SelectProducts/index.tsx:178
msgid "Promo {promo_code} code applied"
msgstr "Código promocional {promo_code} aplicado"

#: src/components/common/OrderDetails/index.tsx:86
msgid "Promo code"
msgstr "Código promocional"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:20
msgid "Promo Code"
msgstr "Código promocional"

#: src/components/forms/ProductForm/index.tsx:409
msgid "Promo Code page"
msgstr "Página de códigos promocionales"

#: src/components/routes/event/Reports/index.tsx:30
msgid "Promo code usage and discount breakdown"
msgstr "Uso de códigos promocionales y desglose de descuentos"

#: src/components/layouts/Event/index.tsx:106
#: src/components/routes/event/promo-codes.tsx:31
msgid "Promo Codes"
msgstr "Códigos promocionales"

#: src/components/common/PromoCodeTable/index.tsx:51
msgid "Promo codes can be used to offer discounts, presale access, or provide special access to your event."
msgstr "Los códigos promocionales se pueden utilizar para ofrecer descuentos, acceso de preventa o proporcionar acceso especial a su evento."

#: src/components/routes/event/Reports/index.tsx:29
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:95
msgid "Promo Codes Report"
msgstr "Informe de códigos promocionales"

#: src/components/forms/QuestionForm/index.tsx:189
msgid ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."
msgstr "Proporcione contexto adicional o instrucciones para esta pregunta. Utilice este campo para agregar términos y condiciones, directrices o cualquier información importante que los asistentes necesiten saber antes de responder."

#: src/components/routes/event/EventDashboard/index.tsx:159
msgid "Publish Event"
msgstr "Publicar Evento"

#: src/components/common/GlobalMenu/index.tsx:25
#~ msgid "Purchase License"
#~ msgstr "Licencia de compra"

#: src/components/modals/ShareModal/index.tsx:126
msgid "QR Code"
msgstr "Código QR"

#: src/components/layouts/AuthLayout/index.tsx:64
msgid "QR code scanning with instant feedback and secure sharing for staff access"
msgstr "Escaneo de códigos QR con retroalimentación instantánea y uso compartido seguro para el acceso del personal"

#: src/components/forms/ProductForm/index.tsx:85
#: src/components/forms/ProductForm/index.tsx:302
msgid "Quantity Available"
msgstr "cantidad disponible"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
msgid "Quantity Sold"
msgstr "Cantidad vendida"

#: src/components/common/QuestionsTable/index.tsx:170
msgid "Question deleted"
msgstr "Pregunta eliminada"

#: src/components/forms/QuestionForm/index.tsx:188
msgid "Question Description"
msgstr "Descripción de la pregunta"

#: src/components/forms/QuestionForm/index.tsx:178
msgid "Question Title"
msgstr "Título de la pregunta"

#: src/components/common/QuestionsTable/index.tsx:275
#: src/components/layouts/Event/index.tsx:102
msgid "Questions"
msgstr "Preguntas"

#: src/components/modals/ManageAttendeeModal/index.tsx:184
#: src/components/modals/ManageOrderModal/index.tsx:111
msgid "Questions & Answers"
msgstr "Preguntas y respuestas"

#: src/components/common/QuestionsTable/index.tsx:145
msgid "Questions sorted successfully"
msgstr "Preguntas ordenadas correctamente"

#: src/components/forms/QuestionForm/index.tsx:118
msgid "Radio Option"
msgstr "Opción de radio"

#: src/components/common/MessageList/index.tsx:59
msgid "Read less"
msgstr "Leer menos"

#: src/components/modals/SendMessageModal/index.tsx:36
msgid "Recipient"
msgstr "Recipiente"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:110
#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:195
msgid "Redirecting to Stripe..."
msgstr "Redirigiendo a Stripe..."

#: src/components/common/OrdersTable/index.tsx:204
#: src/components/common/OrdersTable/index.tsx:274
msgid "Reference"
msgstr "Referencia"

#: src/components/modals/RefundOrderModal/index.tsx:104
msgid "Refund amount ({0})"
msgstr "Importe del reembolso ({0})"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:52
msgid "Refund Failed"
msgstr "Reembolso fallido"

#: src/components/common/OrdersTable/index.tsx:172
msgid "Refund order"
msgstr "Orden de reembolso"

#: src/components/modals/RefundOrderModal/index.tsx:151
msgid "Refund Order"
msgstr "Orden de reembolso"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:51
msgid "Refund Pending"
msgstr "Reembolso pendiente"

#: src/components/routes/event/orders.tsx:51
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:128
msgid "Refund Status"
msgstr "Estado del reembolso"

#: src/components/common/OrderSummary/index.tsx:80
#: src/components/common/StatBoxes/index.tsx:33
#: src/components/routes/event/orders.tsx:29
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:53
msgid "Refunded"
msgstr "Reintegrado"

#: src/components/routes/auth/Register/index.tsx:129
msgid "Register"
msgstr "Registro"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:82
msgid "Remaining Uses"
msgstr "Usos restantes"

#: src/components/routes/product-widget/SelectProducts/index.tsx:504
msgid "remove"
msgstr "eliminar"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:140
#: src/components/routes/product-widget/SelectProducts/index.tsx:505
msgid "Remove"
msgstr "Eliminar"

#: src/components/layouts/Event/index.tsx:92
#: src/components/routes/event/Reports/index.tsx:39
msgid "Reports"
msgstr "Informes"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:169
msgid "Require Billing Address"
msgstr "Requerir dirección de facturación"

#: src/components/routes/event/GettingStarted/index.tsx:197
msgid "Resend confirmation email"
msgstr "Reenviar correo electrónico de confirmación"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resend email confirmation"
msgstr "Reenviar confirmación por correo electrónico"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Resend invitation"
msgstr "Reenviar invitacíon"

#: src/components/common/OrdersTable/index.tsx:179
msgid "Resend order email"
msgstr "Reenviar correo electrónico del pedido"

#: src/components/common/AttendeeTable/index.tsx:179
#~ msgid "Resend product email"
#~ msgstr "Reenviar correo electrónico del producto"

#: src/components/common/AttendeeTable/index.tsx:171
msgid "Resend ticket email"
msgstr "Reenviar correo electrónico del ticket"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resending..."
msgstr "Reenviando..."

#: src/components/common/FilterModal/index.tsx:248
msgid "Reset"
msgstr "Restablecer"

#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Reset password"
msgstr "Restablecer la contraseña"

#: src/components/routes/auth/ResetPassword/index.tsx:54
msgid "Reset Password"
msgstr "Restablecer la contraseña"

#: src/components/routes/auth/ForgotPassword/index.tsx:50
msgid "Reset your password"
msgstr "Restablece tu contraseña"

#: src/components/common/EventCard/index.tsx:167
msgid "Restore event"
msgstr "Restaurar evento"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:50
#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:63
msgid "Return to event page"
msgstr "Volver a la página del evento"

#: src/components/layouts/Checkout/index.tsx:165
msgid "Return to Event Page"
msgstr "Volver a la página del evento"

#: src/components/routes/event/EventDashboard/index.tsx:249
msgid "Revenue"
msgstr "Ganancia"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Revoke invitation"
msgstr "Revocar invitación"

#: src/components/modals/EditUserModal/index.tsx:93
#: src/components/modals/InviteUserModal/index.tsx:64
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Role"
msgstr "Role"

#: src/components/forms/ProductForm/index.tsx:96
#: src/components/forms/ProductForm/index.tsx:391
msgid "Sale End Date"
msgstr "Fecha de finalización de la venta"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:75
msgid "Sale ended"
msgstr "Venta finalizada"

#: src/components/forms/ProductForm/index.tsx:91
#: src/components/forms/ProductForm/index.tsx:389
msgid "Sale Start Date"
msgstr "Fecha de inicio de la venta"

#: src/components/common/ProductPriceAvailability/index.tsx:13
#: src/components/common/ProductPriceAvailability/index.tsx:35
msgid "Sales ended"
msgstr "Ventas terminadas"

#: src/components/common/ProductPriceAvailability/index.tsx:19
#: src/components/common/ProductPriceAvailability/index.tsx:40
msgid "Sales start"
msgstr "Inicio de ventas"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:135
msgid "San Francisco"
msgstr "San Francisco"

#: src/components/common/QuestionAndAnswerList/index.tsx:142
#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:80
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:114
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:96
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:166
#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:94
#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:265
#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:85
msgid "Save"
msgstr "Guardar"

#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/event/HomepageDesigner/index.tsx:166
msgid "Save Changes"
msgstr "Guardar cambios"

#: src/components/modals/EditOrganizerModal/index.tsx:70
msgid "Save Organizer"
msgstr "Guardar organizador"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:90
msgid "Save Settings"
msgstr "Guardar ajustes"

#: src/components/layouts/CheckIn/index.tsx:398
#: src/components/layouts/CheckIn/index.tsx:400
msgid "Scan QR Code"
msgstr "Escanear código QR"

#: src/components/modals/ShareModal/index.tsx:229
msgid "Scan this QR code to access the event page or share it with others"
msgstr "Escanea este código QR para acceder a la página del evento o compártelo con otros"

#: src/components/layouts/CheckIn/index.tsx:288
#~ msgid "Seach by name, order #, attendee # or email..."
#~ msgstr "Busque por nombre, número de pedido, número de asistente o correo electrónico..."

#: src/components/routes/event/attendees.tsx:64
msgid "Search by attendee name, email or order #..."
msgstr "Busque por nombre del asistente, correo electrónico o número de pedido..."

#: src/components/routes/events/Dashboard/index.tsx:69
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:66
msgid "Search by event name..."
msgstr "Buscar por nombre del evento..."

#: src/components/routes/event/orders.tsx:126
msgid "Search by name, email, or order #..."
msgstr "Busque por nombre, correo electrónico o número de pedido..."

#: src/components/layouts/CheckIn/index.tsx:394
msgid "Search by name, order #, attendee # or email..."
msgstr "Buscar por nombre, número de pedido, número de asistente o correo electrónico..."

#: src/components/routes/event/promo-codes.tsx:34
msgid "Search by name..."
msgstr "Buscar por nombre..."

#: src/components/routes/event/products.tsx:43
#~ msgid "Search by product name..."
#~ msgstr ""

#: src/components/routes/event/messages.tsx:34
msgid "Search by subject or content..."
msgstr "Buscar por tema o contenido..."

#: src/components/routes/event/tickets.tsx:43
#~ msgid "Search by ticket name..."
#~ msgstr "Buscar por nombre del billete..."

#: src/components/routes/event/CapacityAssignments/index.tsx:37
msgid "Search capacity assignments..."
msgstr "Buscar asignaciones de capacidad..."

#: src/components/routes/event/CheckInLists/index.tsx:37
msgid "Search check-in lists..."
msgstr "Buscar listas de registro..."

#: src/components/routes/event/products.tsx:52
msgid "Search products"
msgstr "Buscar productos"

#: src/components/common/SearchBar/index.tsx:36
msgid "Search..."
msgstr "Buscar..."

#: src/components/routes/event/HomepageDesigner/index.tsx:160
msgid "Secondary color"
msgstr "Color secundario"

#: src/components/common/WidgetEditor/index.tsx:198
msgid "Secondary Color"
msgstr "Color secundario"

#: src/components/routes/event/HomepageDesigner/index.tsx:162
msgid "Secondary text color"
msgstr "Color de texto secundario"

#: src/components/common/WidgetEditor/index.tsx:206
msgid "Secondary Text Color"
msgstr "Color de texto secundario"

#: src/components/common/FilterModal/index.tsx:150
msgid "Select {0}"
msgstr "Seleccionar {0}"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:214
msgid "Select Camera"
msgstr "Seleccionar cámara"

#: src/components/forms/ProductForm/index.tsx:269
msgid "Select category..."
msgstr "Seleccionar categoría..."

#: src/components/forms/WebhookForm/index.tsx:124
msgid "Select event types"
msgstr "Seleccionar tipos de eventos"

#: src/components/modals/CreateEventModal/index.tsx:110
msgid "Select organizer"
msgstr "Seleccionar organizador"

#: src/components/modals/ManageAttendeeModal/index.tsx:118
msgid "Select Product"
msgstr "Seleccionar producto"

#: src/components/common/ProductSelector/index.tsx:65
msgid "Select Product Tier"
msgstr "Seleccionar nivel de producto"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:49
#: src/components/modals/SendMessageModal/index.tsx:219
msgid "Select products"
msgstr "Seleccionar productos"

#: src/components/modals/EditUserModal/index.tsx:104
msgid "Select status"
msgstr "Seleccionar estado"

#: src/components/modals/CreateAttendeeModal/index.tsx:150
msgid "Select Ticket"
msgstr "Seleccionar billete"

#: src/components/modals/CreateAttendeeModal/index.tsx:163
#: src/components/modals/EditAttendeeModal/index.tsx:117
#~ msgid "Select Ticket Tier"
#~ msgstr "Seleccionar nivel de boleto"

#: src/components/forms/CheckInListForm/index.tsx:26
#: src/components/modals/SendMessageModal/index.tsx:207
msgid "Select tickets"
msgstr "Seleccionar boletos"

#: src/components/common/ReportTable/index.tsx:261
msgid "Select time period"
msgstr "Seleccionar período de tiempo"

#: src/components/forms/WebhookForm/index.tsx:123
msgid "Select which events will trigger this webhook"
msgstr "Selecciona qué eventos activarán este webhook"

#: src/components/forms/ProductForm/index.tsx:352
msgid "Select..."
msgstr "Seleccionar..."

#: src/components/layouts/AuthLayout/index.tsx:68
msgid "Sell Anything"
msgstr "Vende cualquier cosa"

#: src/components/layouts/AuthLayout/index.tsx:69
msgid "Sell merchandise alongside tickets with integrated tax and promo code support"
msgstr "Vende productos junto con las entradas con soporte integrado para impuestos y códigos promocionales"

#: src/components/layouts/AuthLayout/index.tsx:42
msgid "Sell More Than Tickets"
msgstr "Vende más que boletos"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send"
msgstr "Enviar"

#: src/components/modals/SendMessageModal/index.tsx:259
msgid "Send a copy to <0>{0}</0>"
msgstr "Enviar una copia a <0>{0}</0>"

#: src/components/modals/SendMessageModal/index.tsx:139
msgid "Send a message"
msgstr "Enviar un mensaje"

#: src/components/modals/SendMessageModal/index.tsx:269
msgid "Send as a test. This will send the message to your email address instead of the recipients."
msgstr "Enviar como prueba. Esto enviará el mensaje a su dirección de correo electrónico en lugar de a los destinatarios."

#: src/components/routes/event/messages.tsx:41
msgid "Send Message"
msgstr "Enviar Mensaje"

#: src/components/modals/CreateAttendeeModal/index.tsx:191
#~ msgid "Send order confirmation and product email"
#~ msgstr "Enviar confirmación del pedido y correo electrónico del producto"

#: src/components/modals/CreateAttendeeModal/index.tsx:192
msgid "Send order confirmation and ticket email"
msgstr "Enviar confirmación del pedido y correo electrónico del billete."

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send Test"
msgstr "Enviar prueba"

#: src/components/routes/event/Settings/index.tsx:46
msgid "SEO"
msgstr "SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:70
msgid "SEO Description"
msgstr "Descripción SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:76
msgid "SEO Keywords"
msgstr "Palabras clave SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:56
msgid "SEO Settings"
msgstr "Configuración de SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:64
msgid "SEO Title"
msgstr "Título SEO"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "Service Fee"
msgstr "Tarifa de servicio"

#: src/components/forms/ProductForm/index.tsx:159
msgid "Set a minimum price and let users pay more if they choose"
msgstr "Fijar un precio mínimo y dejar que los usuarios paguen más si lo desean."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:207
msgid "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."
msgstr "Establezca el número inicial para la numeración de facturas. Esto no se puede cambiar una vez que las facturas se hayan generado."

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Set up your event"
msgstr "Configura tu evento"

#: src/components/routes/event/EventDashboard/index.tsx:190
#~ msgid "Set up your payment processing to receive funds from ticket sales."
#~ msgstr "Configure su procesamiento de pagos para recibir fondos de la venta de entradas."

#: src/components/routes/event/GettingStarted/index.tsx:183
msgid "Set your event live"
msgstr "Configura tu evento en vivo"

#: src/components/layouts/Event/index.tsx:98
#: src/components/routes/event/Settings/index.tsx:103
msgid "Settings"
msgstr "Ajustes"

#: src/components/layouts/AuthLayout/index.tsx:26
msgid "Setup in Minutes"
msgstr "Configura en minutos"

#: src/components/common/ShareIcon/index.tsx:28
#: src/components/modals/ShareModal/index.tsx:123
#: src/components/modals/ShareModal/index.tsx:141
msgid "Share"
msgstr "Compartir"

#: src/components/layouts/Event/index.tsx:251
#: src/components/modals/ShareModal/index.tsx:116
msgid "Share Event"
msgstr "Compartir evento"

#: src/components/modals/ShareModal/index.tsx:169
msgid "Share to Facebook"
msgstr "Compartir en Facebook"

#: src/components/modals/ShareModal/index.tsx:163
msgid "Share to LinkedIn"
msgstr "Compartir en LinkedIn"

#: src/components/modals/ShareModal/index.tsx:193
msgid "Share to Pinterest"
msgstr "Compartir en Pinterest"

#: src/components/modals/ShareModal/index.tsx:187
msgid "Share to Reddit"
msgstr "Compartir en Reddit"

#: src/components/modals/ShareModal/index.tsx:148
msgid "Share to Social"
msgstr "Compartir en redes sociales"

#: src/components/modals/ShareModal/index.tsx:181
msgid "Share to Telegram"
msgstr "Compartir en Telegram"

#: src/components/modals/ShareModal/index.tsx:175
msgid "Share to WhatsApp"
msgstr "Compartir en WhatsApp"

#: src/components/modals/ShareModal/index.tsx:157
msgid "Share to X"
msgstr "Compartir en X"

#: src/components/modals/ShareModal/index.tsx:199
msgid "Share via Email"
msgstr "Compartir por correo electrónico"

#: src/components/forms/ProductForm/index.tsx:344
msgid "Show"
msgstr "Espectáculo"

#: src/components/forms/ProductForm/index.tsx:403
msgid "Show available product quantity"
msgstr "Mostrar cantidad disponible del producto"

#: src/components/forms/TicketForm/index.tsx:348
#~ msgid "Show available ticket quantity"
#~ msgstr "Mostrar cantidad de entradas disponibles"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Show hidden questions"
msgstr "Mostrar preguntas ocultas"

#: src/components/routes/product-widget/SelectProducts/index.tsx:459
msgid "Show more"
msgstr "Mostrar más"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:60
msgid "Show tax and fees separately"
msgstr "Mostrar impuestos y tarifas por separado"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:76
#~ msgid "Shown to the customer after they checkout, on the order summary page"
#~ msgstr "Se muestra al cliente después de realizar el pago, en la página de resumen del pedido."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
msgid "Shown to the customer after they checkout, on the order summary page."
msgstr "Se muestra al cliente después de finalizar la compra, en la página de resumen del pedido."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:69
msgid "Shown to the customer before they checkout"
msgstr "Se muestra al cliente antes de realizar el pago."

#: src/components/forms/QuestionForm/index.tsx:132
msgid "Shows common address fields, including country"
msgstr "Muestra campos de dirección comunes, incluido el país."

#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:151
msgid "Simpson"
msgstr "simpson"

#: src/components/forms/QuestionForm/index.tsx:100
msgid "Single line text box"
msgstr "Cuadro de texto de una sola línea"

#: src/components/routes/welcome/index.tsx:137
msgid "Skip this step"
msgstr "Salta este paso"

#: src/components/layouts/AuthLayout/index.tsx:78
msgid "Smart Check-in"
msgstr "Registro inteligente"

#: src/components/layouts/AuthLayout/index.tsx:53
#~ msgid "Smart Dashboard"
#~ msgstr "Panel de control inteligente"

#: src/components/routes/auth/Register/index.tsx:90
msgid "Smith"
msgstr "Herrero"

#: src/components/common/ProductPriceAvailability/index.tsx:9
#: src/components/common/ProductPriceAvailability/index.tsx:32
msgid "Sold out"
msgstr "Agotado"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:67
msgid "Sold Out"
msgstr "Agotado"

#: src/components/common/ErrorDisplay/index.tsx:14
#: src/components/routes/auth/AcceptInvitation/index.tsx:47
msgid "Something went wrong"
msgstr "Algo salió mal"

#: src/components/common/TaxAndFeeList/index.tsx:39
msgid "Something went wrong while deleting the Tax or Fee"
msgstr "Algo salió mal al eliminar el impuesto o tarifa"

#: src/components/routes/auth/ForgotPassword/index.tsx:31
msgid "Something went wrong, please try again, or contact support if the problem persists"
msgstr "Algo salió mal, inténtalo de nuevo o contacta con soporte si el problema persiste"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Something went wrong! Please try again"
msgstr "¡Algo salió mal! Inténtalo de nuevo"

#: src/components/forms/StripeCheckoutForm/index.tsx:69
msgid "Something went wrong."
msgstr "Algo salió mal."

#: src/components/routes/profile/ManageProfile/index.tsx:80
#: src/components/routes/profile/ManageProfile/index.tsx:94
msgid "Something went wrong. Please try again."
msgstr "Algo salió mal. Inténtalo de nuevo."

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:61
msgid "Sorry, something has gone wrong. Please restart the checkout process."
msgstr "Lo sentimos, algo ha ido mal. Reinicie el proceso de pago."

#: src/components/routes/product-widget/CollectInformation/index.tsx:259
msgid "Sorry, something went wrong loading this page."
msgstr "Lo sentimos, algo salió mal al cargar esta página."

#: src/components/routes/product-widget/CollectInformation/index.tsx:247
msgid "Sorry, this order no longer exists."
msgstr "Lo siento, este pedido ya no existe."

#: src/components/routes/product-widget/SelectProducts/index.tsx:246
msgid "Sorry, this promo code is not recognized"
msgstr "Lo sentimos, este código de promoción no se reconoce"

#: src/components/layouts/Checkout/index.tsx:26
#~ msgid "Sorry, your order has expired. Please start a new order."
#~ msgstr "Lo sentimos, tu pedido ha caducado. Por favor inicie un nuevo pedido."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Sorting is disabled while filters and sorting are applied"
#~ msgstr "La clasificación está deshabilitada mientras se aplican filtros y clasificación"

#: src/components/common/LanguageSwitcher/index.tsx:18
msgid "Spanish"
msgstr "Español"

#: src/components/forms/ProductForm/index.tsx:147
msgid "Standard product with a fixed price"
msgstr "Producto estándar con precio fijo"

#: src/components/forms/TicketForm/index.tsx:129
#~ msgid "Standard ticket with a fixed price"
#~ msgstr "Billete estándar con precio fijo."

#: src/components/modals/CreateEventModal/index.tsx:144
#: src/components/modals/DuplicateEventModal/index.tsx:93
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:87
#: src/components/routes/welcome/index.tsx:90
msgid "Start Date"
msgstr "Fecha de inicio"

#: src/components/common/CheckoutQuestion/index.tsx:165
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:139
#: src/components/routes/product-widget/CollectInformation/index.tsx:339
#: src/components/routes/product-widget/CollectInformation/index.tsx:340
msgid "State or Region"
msgstr "Estado o región"

#: src/components/common/AttendeeTable/index.tsx:112
#: src/components/common/OrderDetails/index.tsx:51
#: src/components/common/OrdersTable/index.tsx:209
#: src/components/common/ProductsTable/SortableProduct/index.tsx:222
#: src/components/common/WebhookTable/index.tsx:238
#: src/components/forms/CapaciyAssigmentForm/index.tsx:56
#: src/components/forms/WebhookForm/index.tsx:133
#: src/components/modals/EditUserModal/index.tsx:103
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:88
msgid "Status"
msgstr "Estado"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:85
msgid "Stripe"
msgstr "Stripe"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:48
msgid "Stripe payments are not enabled for this event."
msgstr "Los pagos con Stripe no están habilitados para este evento."

#: src/components/modals/SendMessageModal/index.tsx:245
msgid "Subject"
msgstr "Sujeto"

#: src/components/common/OrderSummary/index.tsx:41
msgid "Subtotal"
msgstr "Total parcial"

#: src/components/common/OrdersTable/index.tsx:115
#: src/components/layouts/Checkout/index.tsx:53
msgid "Success"
msgstr "Éxito"

#: src/components/modals/EditUserModal/index.tsx:40
#: src/components/modals/InviteUserModal/index.tsx:32
msgid "Success! {0} will receive an email shortly."
msgstr "¡Éxito! {0} recibirá un correo electrónico en breve."

#: src/components/common/AttendeeTable/index.tsx:90
msgid "Successfully {0} attendee"
msgstr "{0} asistente con éxito"

#: src/components/common/AttendeeCheckInTable/index.tsx:47
#: src/components/common/AttendeeCheckInTable/index.tsx:71
#~ msgid "Successfully checked <0>{0} {1}</0> {2}"
#~ msgstr "Comprobado correctamente <0>{0} {1}</0> {2}"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:23
msgid "Successfully confirmed email address"
msgstr "Dirección de correo electrónico confirmada correctamente"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:31
msgid "Successfully confirmed email change"
msgstr "Cambio de correo electrónico confirmado exitosamente"

#: src/components/modals/CreateAttendeeModal/index.tsx:82
msgid "Successfully created attendee"
msgstr "Asistente creado exitosamente"

#: src/components/modals/CreateProductModal/index.tsx:54
msgid "Successfully Created Product"
msgstr "Producto creado con éxito"

#: src/components/modals/CreatePromoCodeModal/index.tsx:39
msgid "Successfully Created Promo Code"
msgstr "Código promocional creado correctamente"

#: src/components/modals/CreateQuestionModal/index.tsx:45
#: src/components/modals/EditQuestionModal/index.tsx:66
msgid "Successfully Created Question"
msgstr "Pregunta creada correctamente"

#: src/components/modals/CreateTicketModal/index.tsx:50
#~ msgid "Successfully Created Ticket"
#~ msgstr "Boleto creado exitosamente"

#: src/components/modals/DuplicateProductModal/index.tsx:95
msgid "Successfully Duplicated Product"
msgstr "Producto duplicado con éxito"

#: src/components/modals/ManageAttendeeModal/index.tsx:96
msgid "Successfully updated attendee"
msgstr "Asistente actualizado correctamente"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:47
msgid "Successfully updated Capacity Assignment"
msgstr "Asignación de Capacidad actualizada con éxito"

#: src/components/modals/EditCheckInListModal/index.tsx:49
msgid "Successfully updated Check-In List"
msgstr "Lista de registro actualizada con éxito"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:43
msgid "Successfully Updated Email Settings"
msgstr "Configuración de correo electrónico actualizada correctamente"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:54
msgid "Successfully Updated Event"
msgstr "Evento actualizado con éxito"

#: src/components/routes/event/HomepageDesigner/index.tsx:84
msgid "Successfully Updated Homepage Design"
msgstr "Diseño de página de inicio actualizado con éxito"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:50
msgid "Successfully Updated Homepage Settings"
msgstr "Configuración de la página de inicio actualizada correctamente"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:73
msgid "Successfully Updated Location"
msgstr "Ubicación actualizada correctamente"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:43
msgid "Successfully Updated Misc Settings"
msgstr "Configuraciones varias actualizadas exitosamente"

#: src/components/modals/ManageOrderModal/index.tsx:74
msgid "Successfully updated order"
msgstr "Pedido actualizado con éxito"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:74
msgid "Successfully Updated Payment & Invoicing Settings"
msgstr "Configuraciones de pago y facturación actualizadas con éxito"

#: src/components/modals/EditProductModal/index.tsx:89
msgid "Successfully updated product"
msgstr "Producto actualizado con éxito"

#: src/components/modals/EditPromoCodeModal/index.tsx:52
msgid "Successfully Updated Promo Code"
msgstr "Código promocional actualizado correctamente"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:45
msgid "Successfully Updated Seo Settings"
msgstr "Configuración de SEO actualizada con éxito"

#: src/components/modals/EditTicketModal/index.tsx:85
#~ msgid "Successfully updated ticket"
#~ msgstr "Boleto actualizado exitosamente"

#: src/components/modals/EditWebhookModal/index.tsx:44
msgid "Successfully updated Webhook"
msgstr "Webhook actualizado con éxito"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:128
msgid "Suite 100"
msgstr "habitación 100"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
msgid "Support Email"
msgstr "Correo electrónico de soporte"

#: src/components/layouts/AuthLayout/index.tsx:59
msgid "Support for tiered, donation-based, and product sales with customizable pricing and capacity"
msgstr "Soporte para ventas escalonadas, basadas en donaciones y de productos con precios y capacidad personalizables"

#: src/components/forms/ProductForm/index.tsx:254
msgid "T-shirt"
msgstr "Camiseta"

#: src/components/forms/TaxAndFeeForm/index.tsx:12
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Tax"
msgstr "Impuesto"

#: src/components/routes/account/ManageAccount/index.tsx:27
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:16
msgid "Tax & Fees"
msgstr "Impuestos y tarifas"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:243
msgid "Tax Details"
msgstr "Detalles de impuestos"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:244
msgid "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"
msgstr "Información fiscal que aparecerá en la parte inferior de todas las facturas (por ejemplo, número de IVA, registro fiscal)"

#: src/components/common/TaxAndFeeList/index.tsx:36
msgid "Tax or Fee deleted successfully"
msgstr "Impuesto o tasa eliminados correctamente"

#: src/components/forms/ProductForm/index.tsx:363
msgid "Taxes"
msgstr "Impuestos"

#: src/components/forms/ProductForm/index.tsx:351
#: src/components/forms/ProductForm/index.tsx:358
msgid "Taxes and Fees"
msgstr "Impuestos y honorarios"

#: src/components/routes/product-widget/SelectProducts/index.tsx:138
#: src/components/routes/product-widget/SelectProducts/index.tsx:186
msgid "That promo code is invalid"
msgstr "Ese código de promoción no es válido."

#: src/components/layouts/CheckIn/index.tsx:316
msgid "The check-in list you are looking for does not exist."
msgstr "La lista de registro que buscas no existe."

#: src/components/forms/OrganizerForm/index.tsx:47
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:75
msgid "The default currency for your events."
msgstr "La moneda predeterminada para tus eventos."

#: src/components/forms/OrganizerForm/index.tsx:56
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:84
msgid "The default timezone for your events."
msgstr "La zona horaria predeterminada para sus eventos."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:16
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:43
msgid "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."
msgstr "El evento que buscas no está disponible en este momento. Puede que haya sido eliminado, haya caducado o la URL sea incorrecta."

#: src/components/modals/CreateAttendeeModal/index.tsx:146
msgid "The language the attendee will receive emails in."
msgstr "El idioma en el que el asistente recibirá los correos electrónicos."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:22
msgid "The link you clicked is invalid."
msgstr "El enlace en el que hizo clic no es válido."

#: src/components/routes/product-widget/SelectProducts/index.tsx:444
msgid "The maximum number of products for {0}is {1}"
msgstr "El número máximo de productos para {0} es {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:351
#~ msgid "The maximum numbers number of tickets for {0}is {1}"
#~ msgstr "El número máximo de entradas para {0} es {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:318
#~ msgid "The maximum numbers number of tickets for Generals is {0}"
#~ msgstr "El número máximo de entradas para Generales es {0}"

#: src/components/common/ErrorDisplay/index.tsx:17
msgid "The page you are looking for does not exist"
msgstr "La página que buscas no existe"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:56
msgid "The price displayed to the customer will include taxes and fees."
msgstr "El precio mostrado al cliente incluirá impuestos y tasas."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:62
msgid "The price displayed to the customer will not include taxes and fees. They will be shown separately"
msgstr "El precio mostrado al cliente no incluirá impuestos ni tasas. Se mostrarán por separado."

#: src/components/common/WidgetEditor/index.tsx:169
msgid "The styling settings you choose apply only to copied HTML and won't be stored."
msgstr "La configuración de estilo que elija se aplica sólo al HTML copiado y no se almacenará."

#: src/components/forms/ProductForm/index.tsx:355
msgid "The taxes and fees to apply to this product. You can create new taxes and fees on the"
msgstr "Los impuestos y tasas que se aplicarán a este producto. Puede crear nuevos impuestos y tasas en el"

#: src/components/forms/TicketForm/index.tsx:300
#~ msgid "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "Los impuestos y tasas a aplicar a este billete. Puede crear nuevos impuestos y tarifas en el"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:63
msgid "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"
msgstr "El título del evento que se mostrará en los resultados del motor de búsqueda y al compartirlo en las redes sociales. De forma predeterminada, se utilizará el título del evento."

#: src/components/routes/product-widget/SelectProducts/index.tsx:272
msgid "There are no products available for this event"
msgstr "No hay productos disponibles para este evento"

#: src/components/routes/product-widget/SelectProducts/index.tsx:375
msgid "There are no products available in this category"
msgstr "No hay productos disponibles en esta categoría"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:260
#~ msgid "There are no tickets available for this event"
#~ msgstr "No hay entradas disponibles para este evento."

#: src/components/modals/RefundOrderModal/index.tsx:137
msgid "There is a refund pending. Please wait for it to complete before requesting another refund."
msgstr "Hay un reembolso pendiente. Espere a que se complete antes de solicitar otro reembolso."

#: src/components/common/OrdersTable/index.tsx:80
msgid "There was an error marking the order as paid"
msgstr "Hubo un error al marcar el pedido como pagado"

#: src/hooks/useFormErrorResponseHandler.tsx:28
msgid "There was an error processing your request. Please try again."
msgstr "Hubo un error al procesar su solicitud. Inténtalo de nuevo."

#: src/components/common/OrdersTable/index.tsx:95
msgid "There was an error sending your message"
msgstr "Hubo un error al enviar tu mensaje"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:105
msgid "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."
msgstr "Estos detalles solo se mostrarán si el pedido se completa con éxito. Los pedidos en espera de pago no mostrarán este mensaje."

#: src/components/layouts/CheckIn/index.tsx:201
msgid "This attendee has an unpaid order."
msgstr "Este asistente tiene un pedido no pagado."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:61
msgid "This category doesn't have any products yet."
msgstr "Esta categoría aún no tiene productos."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:112
msgid "This category is hidden from public view"
msgstr "Esta categoría está oculta de la vista pública"

#: src/components/common/CheckInListList/index.tsx:75
msgid "This check-in list has expired"
msgstr "Esta lista de registro ha expirado"

#: src/components/layouts/CheckIn/index.tsx:331
msgid "This check-in list has expired and is no longer available for check-ins."
msgstr "Esta lista de registro ha expirado y ya no está disponible para registros."

#: src/components/common/CheckInListList/index.tsx:82
msgid "This check-in list is active"
msgstr "Esta lista de registro está activa"

#: src/components/common/CheckInListList/index.tsx:79
msgid "This check-in list is not active yet"
msgstr "Esta lista de registro aún no está activa"

#: src/components/layouts/CheckIn/index.tsx:348
msgid "This check-in list is not yet active and is not available for check-ins."
msgstr "Esta lista de registro aún no está activa y no está disponible para registros."

#: src/components/forms/CheckInListForm/index.tsx:37
msgid "This description will be shown to the check-in staff"
msgstr "Esta descripción se mostrará al personal de registro"

#: src/components/modals/SendMessageModal/index.tsx:285
msgid "This email is not promotional and is directly related to the event."
msgstr "Este correo electrónico no es promocional y está directamente relacionado con el evento."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:8
#~ msgid "This event is not available at the moment. Please check back later."
#~ msgstr "Este evento no está disponible en este momento. Por favor, vuelva más tarde."

#: src/components/layouts/EventHomepage/index.tsx:49
#~ msgid "This event is not available."
#~ msgstr "Este evento no está disponible."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
msgid "This information will be shown on the payment page, order summary page, and order confirmation email."
msgstr "Esta información se mostrará en la página de pago, en la página de resumen del pedido y en el correo electrónico de confirmación del pedido."

#: src/components/forms/ProductForm/index.tsx:138
msgid "This is a general product, like a t-shirt or a mug. No ticket will be issued"
msgstr "Este es un producto general, como una camiseta o una taza. No se emitirá un boleto"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:91
msgid "This is an online event"
msgstr "Este es un evento en línea"

#: src/components/forms/CheckInListForm/index.tsx:57
msgid "This list will no longer be available for check-ins after this date"
msgstr "Esta lista ya no estará disponible para registros después de esta fecha"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:68
msgid "This message will be included in the footer of all emails sent from this event"
msgstr "Este mensaje se incluirá en el pie de página de todos los correos electrónicos enviados desde este evento."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
msgid "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"
msgstr "Este mensaje solo se mostrará si el pedido se completa con éxito. Los pedidos en espera de pago no mostrarán este mensaje."

#: src/components/forms/StripeCheckoutForm/index.tsx:93
msgid "This order has already been paid."
msgstr "Este pedido ya ha sido pagado."

#: src/components/modals/RefundOrderModal/index.tsx:141
msgid "This order has already been refunded."
msgstr "Este pedido ya ha sido reembolsado."

#: src/components/routes/product-widget/CollectInformation/index.tsx:237
msgid "This order has been cancelled"
msgstr "Esta orden ha sido cancelada"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:209
msgid "This order has been cancelled."
msgstr "Esta orden ha sido cancelada."

#: src/components/routes/product-widget/CollectInformation/index.tsx:210
msgid "This order has expired. Please start again."
msgstr "Este pedido ha expirado. Por favor, comienza de nuevo."

#: src/components/routes/product-widget/CollectInformation/index.tsx:221
msgid "This order is awaiting payment"
msgstr "Este pedido está pendiente de pago."

#: src/components/routes/product-widget/CollectInformation/index.tsx:229
msgid "This order is complete"
msgstr "Este pedido está completo."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:211
msgid "This order is complete."
msgstr "Este pedido está completo."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:204
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:207
msgid "This order is processing."
msgstr "Este pedido se está procesando."

#: src/components/forms/StripeCheckoutForm/index.tsx:103
msgid "This order page is no longer available."
msgstr "Esta página de pedidos ya no está disponible."

#: src/components/forms/ProductForm/index.tsx:415
msgid "This overrides all visibility settings and will hide the product from all customers."
msgstr "Esto sobrescribe todas las configuraciones de visibilidad y ocultará el producto a todos los clientes."

#: src/components/forms/TicketForm/index.tsx:360
#~ msgid "This overrides all visibility settings and will hide the ticket from all customers."
#~ msgstr "Esto anula todas las configuraciones de visibilidad y ocultará el ticket a todos los clientes."

#: src/components/common/ProductsTable/SortableProduct/index.tsx:59
msgid "This product cannot be deleted because it is associated with an order. You can hide it instead."
msgstr "Este producto no se puede eliminar porque está asociado con un pedido. Puede ocultarlo en su lugar."

#: src/components/forms/ProductForm/index.tsx:132
msgid "This product is a ticket. Buyers will be issued a ticket upon purchase"
msgstr "Este producto es un boleto. Se emitirá un boleto a los compradores al realizar la compra"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:215
msgid "This product is hidden from public view"
msgstr "Este producto está oculto de la vista pública"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:216
msgid "This product is hidden unless targeted by a Promo Code"
msgstr "Este producto está oculto a menos que sea dirigido por un código promocional"

#: src/components/common/QuestionsTable/index.tsx:90
msgid "This question is only visible to the event organizer"
msgstr "Esta pregunta solo es visible para el organizador del evento."

#: src/components/routes/auth/ResetPassword/index.tsx:29
msgid "This reset password link is invalid or expired."
msgstr "Este enlace para restablecer la contraseña no es válido o ha caducado."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:56
#~ msgid ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."
#~ msgstr ""
#~ "Este ticket no se puede eliminar porque está\n"
#~ "asociado a un pedido. Puedes ocultarlo en su lugar."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:131
#~ msgid "This ticket is hidden from public view"
#~ msgstr "Este billete está oculto a la vista del público."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:132
#~ msgid "This ticket is hidden unless targeted by a Promo Code"
#~ msgstr "Este ticket está oculto a menos que esté dirigido a un código promocional."

#: src/components/modals/EditUserModal/index.tsx:65
msgid "This user is not active, as they have not accepted their invitation."
msgstr "Este usuario no está activo porque no ha aceptado su invitación."

#: src/components/common/AttendeeTable/index.tsx:182
msgid "ticket"
msgstr "boleto"

#: src/components/common/AttendeeTable/index.tsx:111
#: src/components/forms/ProductForm/index.tsx:130
#: src/components/modals/CreateAttendeeModal/index.tsx:151
msgid "Ticket"
msgstr "Boleto"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:52
#~ msgid "Ticket deleted successfully"
#~ msgstr "Boleto eliminado exitosamente"

#: src/components/common/AttendeeTable/index.tsx:49
msgid "Ticket email has been resent to attendee"
msgstr "El correo electrónico del ticket se ha reenviado al asistente."

#: src/components/common/MessageList/index.tsx:22
msgid "Ticket holders"
msgstr "Poseedores de entradas"

#: src/components/routes/event/products.tsx:86
msgid "Ticket or Product"
msgstr "Entrada o producto"

#: src/components/routes/event/EventDashboard/index.tsx:53
#~ msgid "Ticket Sales"
#~ msgstr "Venta de boletos"

#: src/components/modals/CreateAttendeeModal/index.tsx:161
#: src/components/modals/EditAttendeeModal/index.tsx:115
#~ msgid "Ticket Tier"
#~ msgstr "Nivel de boletos"

#: src/components/forms/TicketForm/index.tsx:189
#: src/components/forms/TicketForm/index.tsx:196
#~ msgid "Ticket Type"
#~ msgstr "Tipo de billete"

#: src/components/common/WidgetEditor/index.tsx:311
#~ msgid "Ticket Widget Preview"
#~ msgstr "Vista previa del widget de ticket"

#: src/components/common/PromoCodeTable/index.tsx:147
#~ msgid "Ticket(s)"
#~ msgstr "Entradas)"

#: src/components/common/PromoCodeTable/index.tsx:71
#: src/components/layouts/Event/index.tsx:40
#: src/components/layouts/EventHomepage/index.tsx:80
#: src/components/routes/event/tickets.tsx:39
#~ msgid "Tickets"
#~ msgstr "Entradas"

#: src/components/layouts/Event/index.tsx:101
#: src/components/routes/event/products.tsx:46
msgid "Tickets & Products"
msgstr "Entradas y productos"

#: src/components/routes/product-widget/PrintOrder/index.tsx:43
msgid "Tickets for"
msgstr "Entradas para"

#: src/components/common/EventCard/index.tsx:126
#~ msgid "tickets sold"
#~ msgstr "entradas vendidas"

#: src/components/common/StatBoxes/index.tsx:21
#~ msgid "Tickets sold"
#~ msgstr "Entradas vendidas"

#: src/components/routes/event/EventDashboard/index.tsx:73
#~ msgid "Tickets Sold"
#~ msgstr "Entradas vendidas"

#: src/components/common/TicketsTable/index.tsx:44
#~ msgid "Tickets sorted successfully"
#~ msgstr "Entradas ordenadas correctamente"

#: src/components/forms/ProductForm/index.tsx:67
msgid "Tier {0}"
msgstr "Nivel {0}"

#: src/components/forms/ProductForm/index.tsx:163
msgid "Tiered Product"
msgstr "Producto escalonado"

#: src/components/forms/ProductForm/index.tsx:240
#~ msgid ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""

#: src/components/forms/ProductForm/index.tsx:243
msgid "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."
msgstr "Los productos escalonados le permiten ofrecer múltiples opciones de precio para el mismo producto. Esto es perfecto para productos anticipados o para ofrecer diferentes opciones de precio a diferentes grupos de personas.\" # es"

#: src/components/forms/TicketForm/index.tsx:145
#~ msgid "Tiered Ticket"
#~ msgstr "Boleto escalonado"

#: src/components/forms/TicketForm/index.tsx:203
#~ msgid ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""
#~ "Los boletos escalonados le permiten ofrecer múltiples opciones de precios para el mismo boleto.\n"
#~ "Esto es perfecto para entradas anticipadas o para ofrecer precios diferentes.\n"
#~ "opciones para diferentes grupos de personas."

#: src/components/layouts/Checkout/index.tsx:90
msgid "Time left:"
msgstr "Tiempo restante:"

#: src/components/common/PromoCodeTable/index.tsx:71
msgid "Times used"
msgstr "Tiempos utilizados"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:25
msgid "Times Used"
msgstr "Veces usado"

#: src/components/forms/OrganizerForm/index.tsx:54
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:82
#: src/components/routes/auth/AcceptInvitation/index.tsx:105
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:109
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:181
#: src/components/routes/profile/ManageProfile/index.tsx:161
msgid "Timezone"
msgstr "Zona horaria"

#: src/components/forms/PromoCodeForm/index.tsx:36
msgid "TIP"
msgstr "CONSEJO"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:206
msgid "Title"
msgstr "Título"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:182
msgid "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."
msgstr "Para recibir pagos con tarjeta de crédito, debes conectar tu cuenta de Stripe. Stripe es nuestro socio de procesamiento de pagos que garantiza transacciones seguras y pagos puntuales."

#: src/components/layouts/Event/index.tsx:108
msgid "Tools"
msgstr "Herramientas"

#: src/components/common/OrderSummary/index.tsx:92
msgid "Total"
msgstr "Total"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:52
msgid "Total Before Discounts"
msgstr "Total antes de descuentos"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:58
msgid "Total Discount Amount"
msgstr "Monto total de descuento"

#: src/components/routes/event/EventDashboard/index.tsx:273
msgid "Total Fees"
msgstr "Tarifas totales"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:46
msgid "Total Gross Sales"
msgstr "Total de ventas brutas"

#: src/components/common/OrderDetails/index.tsx:59
#: src/components/modals/RefundOrderModal/index.tsx:74
msgid "Total order amount"
msgstr "Monto total del pedido"

#: src/components/common/OrderDetails/index.tsx:67
#: src/components/modals/RefundOrderModal/index.tsx:82
msgid "Total refunded"
msgstr "Total reembolsado"

#: src/components/routes/event/EventDashboard/index.tsx:276
msgid "Total Refunded"
msgstr "Total reembolsado"

#: src/components/modals/RefundOrderModal/index.tsx:90
msgid "Total remaining"
msgstr "Total restante"

#: src/components/routes/event/EventDashboard/index.tsx:275
msgid "Total Tax"
msgstr "Total impuestos"

#: src/components/layouts/AuthLayout/index.tsx:54
msgid "Track revenue, page views, and sales with detailed analytics and exportable reports"
msgstr "Rastrea ingresos, vistas de página y ventas con análisis detallados e informes exportables"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
msgid "Transaction Fee:"
msgstr "Tarifa de transacción:"

#: src/components/forms/TaxAndFeeForm/index.tsx:44
msgid "Type"
msgstr "Tipo"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "Unable to {0} attendee"
#~ msgstr "No se puede {0} asistir"

#: src/components/layouts/CheckIn/index.tsx:93
msgid "Unable to check in attendee"
msgstr "No se pudo registrar al asistente"

#: src/components/layouts/CheckIn/index.tsx:114
msgid "Unable to check out attendee"
msgstr "No se pudo retirar al asistente"

#: src/components/modals/CreateProductModal/index.tsx:63
msgid "Unable to create product. Please check the your details"
msgstr "No se pudo crear el producto. Por favor, revise sus datos"

#: src/components/routes/product-widget/SelectProducts/index.tsx:123
msgid "Unable to create product. Please check your details"
msgstr "No se pudo crear el producto. Por favor, revise sus datos"

#: src/components/modals/CreateQuestionModal/index.tsx:60
msgid "Unable to create question. Please check the your details"
msgstr "No se puede crear una pregunta. Por favor revisa tus datos"

#: src/components/modals/CreateTicketModal/index.tsx:65
#: src/components/routes/ticket-widget/SelectTickets/index.tsx:117
#~ msgid "Unable to create ticket. Please check the your details"
#~ msgstr "No se puede crear el ticket. Por favor revisa tus datos"

#: src/components/modals/DuplicateProductModal/index.tsx:103
msgid "Unable to duplicate product. Please check the your details"
msgstr "No se pudo duplicar el producto. Por favor, revisa tus datos"

#: src/components/layouts/CheckIn/index.tsx:145
msgid "Unable to fetch attendee"
msgstr "No se pudo obtener el asistente"

#: src/components/modals/EditQuestionModal/index.tsx:84
msgid "Unable to update question. Please check the your details"
msgstr "No se puede actualizar la pregunta. Por favor revisa tus datos"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:30
msgid "Unique Customers"
msgstr "Clientes únicos"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:153
msgid "United States"
msgstr "Estados Unidos"

#: src/components/common/QuestionAndAnswerList/index.tsx:284
msgid "Unknown Attendee"
msgstr "Asistente desconocido"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:43
#: src/components/forms/ProductForm/index.tsx:83
#: src/components/forms/ProductForm/index.tsx:299
#: src/components/forms/PromoCodeForm/index.tsx:82
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:78
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:84
msgid "Unlimited"
msgstr "Ilimitado"

#: src/components/routes/product-widget/SelectProducts/index.tsx:407
msgid "Unlimited available"
msgstr "Ilimitado disponible"

#: src/components/common/PromoCodeTable/index.tsx:126
msgid "Unlimited usages allowed"
msgstr "Usos ilimitados permitidos"

#: src/components/layouts/CheckIn/index.tsx:200
msgid "Unpaid Order"
msgstr "Pedido no pagado"

#: src/components/routes/event/EventDashboard/index.tsx:170
msgid "Unpublish Event"
msgstr "Despublicar Evento"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:20
#: src/components/common/EventStatusBadge/index.tsx:16
msgid "Upcoming"
msgstr "Próximo"

#: src/components/routes/events/Dashboard/index.tsx:55
msgid "Upcoming Events"
msgstr "Próximos eventos"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Update {0}"
msgstr "Actualizar {0}"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:66
msgid "Update event name, description and dates"
msgstr "Actualizar el nombre del evento, la descripción y las fechas."

#: src/components/routes/profile/ManageProfile/index.tsx:177
msgid "Update profile"
msgstr "Actualización del perfil"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Upload Cover"
msgstr "Subir portada"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:105
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:152
msgid "Upload Image"
msgstr "Subir imagen"

#: src/components/common/WebhookTable/index.tsx:236
#: src/components/modals/ShareModal/index.tsx:129
msgid "URL"
msgstr "URL"

#: src/components/common/PromoCodeTable/index.tsx:175
msgid "URL copied to clipboard"
msgstr "URL copiada en el portapapeles"

#: src/components/modals/CreateWebhookModal/index.tsx:25
msgid "URL is required"
msgstr "La URL es obligatoria"

#: src/components/common/WidgetEditor/index.tsx:298
msgid "Usage Example"
msgstr "Ejemplo de uso"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:76
msgid "Usage Limit"
msgstr "Límite de uso"

#: src/components/routes/event/HomepageDesigner/index.tsx:139
msgid "Use a blurred version of the cover image as the background"
msgstr "Utilice una versión borrosa de la imagen de portada como fondo"

#: src/components/routes/event/HomepageDesigner/index.tsx:137
msgid "Use cover image"
msgstr "Usar imagen de portada"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User"
msgstr "Usuario"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User Management"
msgstr "Gestión de usuarios"

#: src/components/routes/account/ManageAccount/index.tsx:32
msgid "Users"
msgstr "Usuarios"

#: src/components/modals/EditUserModal/index.tsx:81
msgid "Users can change their email in <0>Profile Settings</0>"
msgstr "Los usuarios pueden cambiar su correo electrónico en <0>Configuración de perfil</0>"

#: src/components/forms/OrganizerForm/index.tsx:55
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:83
#: src/components/routes/auth/AcceptInvitation/index.tsx:106
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:110
#: src/components/routes/profile/ManageProfile/index.tsx:162
msgid "UTC"
msgstr "UTC"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "VAT"
msgstr "IVA"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:116
msgid "Venue Name"
msgstr "Nombre del lugar"

#: src/components/common/LanguageSwitcher/index.tsx:34
msgid "Vietnamese"
msgstr "Vietnamita"

#: src/components/modals/ManageAttendeeModal/index.tsx:220
#: src/components/modals/ManageOrderModal/index.tsx:200
msgid "View"
msgstr "Ver"

#: src/components/routes/event/Reports/index.tsx:38
msgid "View and download reports for your event. Please note, only completed orders are included in these reports."
msgstr "Consulta y descarga informes de tu evento. Ten en cuenta que solo se incluyen pedidos completados en estos informes."

#: src/components/common/AttendeeList/index.tsx:84
msgid "View Answers"
msgstr "Ver respuestas"

#: src/components/common/AttendeeTable/index.tsx:164
#~ msgid "View attendee"
#~ msgstr "Ver asistente"

#: src/components/common/AttendeeList/index.tsx:103
msgid "View Attendee Details"
msgstr "Ver detalles del asistente"

#: src/components/layouts/Checkout/index.tsx:55
#~ msgid "View event homepage"
#~ msgstr "Ver página de inicio del evento"

#: src/components/common/EventCard/index.tsx:145
msgid "View event page"
msgstr "Ver página del evento"

#: src/components/common/MessageList/index.tsx:59
msgid "View full message"
msgstr "Ver mensaje completo"

#: src/components/common/WebhookTable/index.tsx:113
msgid "View logs"
msgstr "Ver registros"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View map"
msgstr "Ver el mapa"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View on Google Maps"
msgstr "Ver en Google Maps"

#: src/components/common/OrdersTable/index.tsx:111
#~ msgid "View order"
#~ msgstr "Ver pedido"

#: src/components/forms/StripeCheckoutForm/index.tsx:94
#: src/components/forms/StripeCheckoutForm/index.tsx:104
#: src/components/routes/product-widget/CollectInformation/index.tsx:231
msgid "View order details"
msgstr "Ver detalles de la orden"

#: src/components/forms/CheckInListForm/index.tsx:21
msgid "VIP check-in list"
msgstr "Lista de registro VIP"

#: src/components/forms/ProductForm/index.tsx:254
#~ msgid "VIP Product"
#~ msgstr "Producto VIP"

#: src/components/forms/ProductForm/index.tsx:254
msgid "VIP Ticket"
msgstr "Entrada VIP"

#: src/components/forms/ProductForm/index.tsx:394
msgid "Visibility"
msgstr "Visibilidad"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:77
msgid "We could not process your payment. Please try again or contact support."
msgstr "No pudimos procesar su pago. Inténtelo de nuevo o comuníquese con el soporte."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:53
msgid "We couldn't delete the category. Please try again."
msgstr "No pudimos eliminar la categoría. Por favor, inténtelo de nuevo."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:24
msgid "We couldn't find any tickets matching {0}"
msgstr "No pudimos encontrar ningún boleto que coincida con {0}"

#: src/components/common/ErrorLoadingMessage/index.tsx:13
msgid "We couldn't load the data. Please try again."
msgstr "No pudimos cargar los datos. Inténtalo de nuevo."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:94
msgid "We couldn't reorder the categories. Please try again."
msgstr "No pudimos reordenar las categorías. Por favor, inténtelo de nuevo."

#: src/components/routes/event/HomepageDesigner/index.tsx:118
msgid "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"
msgstr "Recomendamos dimensiones de 2160 px por 1080 px y un tamaño de archivo máximo de 5 MB."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:42
#~ msgid "We use Stripe to process payments. Connect your Stripe account to start receiving payments."
#~ msgstr "Usamos Stripe para procesar pagos. Conecte su cuenta Stripe para comenzar a recibir pagos."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:84
msgid "We were unable to confirm your payment. Please try again or contact support."
msgstr "No pudimos confirmar su pago. Inténtelo de nuevo o comuníquese con el soporte."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:79
msgid "We're processing your order. Please wait..."
msgstr "Estamos procesando tu pedido. Espere por favor..."

#: src/components/modals/CreateWebhookModal/index.tsx:46
msgid "Webhook created successfully"
msgstr "Webhook creado con éxito"

#: src/components/common/WebhookTable/index.tsx:53
msgid "Webhook deleted successfully"
msgstr "Webhook eliminado con éxito"

#: src/components/modals/WebhookLogsModal/index.tsx:151
msgid "Webhook Logs"
msgstr "Registros del Webhook"

#: src/components/forms/WebhookForm/index.tsx:117
msgid "Webhook URL"
msgstr "URL del Webhook"

#: src/components/forms/WebhookForm/index.tsx:27
msgid "Webhook will not send notifications"
msgstr "El webhook no enviará notificaciones"

#: src/components/forms/WebhookForm/index.tsx:21
msgid "Webhook will send notifications"
msgstr "El webhook enviará notificaciones"

#: src/components/layouts/Event/index.tsx:111
#: src/components/routes/event/Webhooks/index.tsx:30
msgid "Webhooks"
msgstr "Webhooks"

#: src/components/routes/auth/AcceptInvitation/index.tsx:76
msgid "Welcome aboard! Please login to continue."
msgstr "¡Bienvenido a bordo! Por favor inicie sesión para continuar."

#: src/components/routes/auth/Login/index.tsx:55
msgid "Welcome back 👋"
msgstr "Bienvenido de nuevo 👋"

#: src/components/routes/event/EventDashboard/index.tsx:95
msgid "Welcome back{0} 👋"
msgstr "Bienvenido de nuevo{0} 👋"

#: src/components/routes/auth/Register/index.tsx:67
msgid "Welcome to Hi.Events 👋"
msgstr "Bienvenido a Hi.Events 👋"

#: src/components/routes/welcome/index.tsx:125
msgid "Welcome to Hi.Events, {0} 👋"
msgstr "Bienvenido a Hola.Eventos, {0} 👋"

#: src/components/forms/ProductForm/index.tsx:242
msgid "What are Tiered Products?"
msgstr "¿Qué son los productos escalonados?"

#: src/components/forms/TicketForm/index.tsx:202
#~ msgid "What are Tiered Tickets?"
#~ msgstr "¿Qué son los boletos escalonados?"

#: src/components/forms/CheckInListForm/index.tsx:50
msgid "What date should this check-in list become active?"
msgstr "¿En qué fecha debe activarse esta lista de registro?"

#: src/components/modals/CreateProductCategoryModal/index.tsx:53
msgid "What is a Category?"
msgstr "¿Qué es una categoría?"

#: src/components/routes/event/Webhooks/index.tsx:31
#~ msgid "What is a webhook?"
#~ msgstr "¿Qué es un webhook?"

#: src/components/forms/QuestionForm/index.tsx:159
msgid "What products does this code apply to?"
msgstr "¿A qué productos se aplica este código?"

#: src/components/forms/PromoCodeForm/index.tsx:68
msgid "What products does this code apply to? (Applies to all by default)"
msgstr "¿A qué productos se aplica este código? (Se aplica a todos por defecto)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:48
msgid "What products should this capacity apply to?"
msgstr "¿A qué productos debería aplicarse esta capacidad?"

#: src/components/forms/PromoCodeForm/index.tsx:68
#~ msgid "What tickets does this code apply to? (Applies to all by default)"
#~ msgstr "¿A qué billetes se aplica este código? (Se aplica a todos de forma predeterminada)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:47
#: src/components/forms/QuestionForm/index.tsx:159
#~ msgid "What tickets should this question be apply to?"
#~ msgstr "¿A qué billetes debería aplicarse esta pregunta?"

#: src/components/forms/QuestionForm/index.tsx:179
msgid "What time will you be arriving?"
msgstr "¿A qué hora llegarás?"

#: src/components/forms/QuestionForm/index.tsx:170
msgid "What type of question is this?"
msgstr "¿Qué tipo de pregunta es esta?"

#: src/components/forms/WebhookForm/index.tsx:108
msgid "When a check-in is deleted"
msgstr "Cuando se elimina un registro de entrada"

#: src/components/forms/WebhookForm/index.tsx:84
msgid "When a new attendee is created"
msgstr "Cuando se crea un nuevo asistente"

#: src/components/forms/WebhookForm/index.tsx:54
msgid "When a new order is created"
msgstr "Cuando se crea un nuevo pedido"

#: src/components/forms/WebhookForm/index.tsx:36
msgid "When a new product is created"
msgstr "Cuando se crea un nuevo producto"

#: src/components/forms/WebhookForm/index.tsx:48
msgid "When a product is deleted"
msgstr "Cuando se elimina un producto"

#: src/components/forms/WebhookForm/index.tsx:42
msgid "When a product is updated"
msgstr "Cuando se actualiza un producto"

#: src/components/forms/WebhookForm/index.tsx:96
msgid "When an attendee is cancelled"
msgstr "Cuando se cancela un asistente"

#: src/components/forms/WebhookForm/index.tsx:102
msgid "When an attendee is checked in"
msgstr "Cuando un asistente se registra"

#: src/components/forms/WebhookForm/index.tsx:90
msgid "When an attendee is updated"
msgstr "Cuando se actualiza un asistente"

#: src/components/forms/WebhookForm/index.tsx:78
msgid "When an order is cancelled"
msgstr "Cuando se cancela un pedido"

#: src/components/forms/WebhookForm/index.tsx:66
msgid "When an order is marked as paid"
msgstr "Cuando un pedido se marca como pagado"

#: src/components/forms/WebhookForm/index.tsx:72
msgid "When an order is refunded"
msgstr "Cuando se reembolsa un pedido"

#: src/components/forms/WebhookForm/index.tsx:60
msgid "When an order is updated"
msgstr "Cuando se actualiza un pedido"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:182
msgid "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."
msgstr "Cuando esté habilitado, se generarán facturas para los pedidos de boletos. Las facturas se enviarán junto con el correo electrónico de confirmación del pedido. Los asistentes también pueden descargar sus facturas desde la página de confirmación del pedido."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:141
msgid "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."
msgstr "Cuando los pagos offline estén habilitados, los usuarios podrán completar sus pedidos y recibir sus boletos. Sus boletos indicarán claramente que el pedido no está pagado, y la herramienta de registro notificará al personal si un pedido requiere pago."

#: src/components/forms/CheckInListForm/index.tsx:59
msgid "When should this check-in list expire?"
msgstr "¿Cuándo debe expirar esta lista de registro?"

#: src/components/forms/CheckInListForm/index.tsx:25
msgid "Which tickets should be associated with this check-in list?"
msgstr "¿Qué boletos deben asociarse con esta lista de registro?"

#: src/components/modals/CreateEventModal/index.tsx:107
msgid "Who is organizing this event?"
msgstr "¿Quién organiza este evento?"

#: src/components/modals/SendMessageModal/index.tsx:194
msgid "Who is this message to?"
msgstr "¿A quién va dirigido este mensaje?"

#: src/components/forms/QuestionForm/index.tsx:151
msgid "Who should be asked this question?"
msgstr "¿A quién se le debería hacer esta pregunta?"

#: src/components/layouts/Event/index.tsx:110
msgid "Widget Embed"
msgstr "Insertar widget"

#: src/components/common/WidgetEditor/index.tsx:162
msgid "Widget Settings"
msgstr "Configuración de widgets"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Working"
msgstr "Laboral"

#: src/components/modals/CreateProductCategoryModal/index.tsx:60
#: src/components/modals/CreateProductModal/index.tsx:88
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
#: src/components/modals/CreateQuestionModal/index.tsx:74
#: src/components/modals/DuplicateEventModal/index.tsx:142
#: src/components/modals/DuplicateProductModal/index.tsx:113
#: src/components/modals/EditProductCategoryModal/index.tsx:70
#: src/components/modals/EditProductModal/index.tsx:108
#: src/components/modals/EditPromoCodeModal/index.tsx:84
#: src/components/modals/EditQuestionModal/index.tsx:101
#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/Register/index.tsx:129
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Working..."
msgstr "Laboral..."

#: src/components/common/ReportTable/index.tsx:51
msgid "Year to date"
msgstr "Año hasta la fecha"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "Yes"
msgstr "Sí"

#: src/components/forms/ProductForm/index.tsx:376
msgid "Yes, remove them"
msgstr "Si, eliminarlos"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:77
msgid "You already scanned this ticket"
msgstr "Ya has escaneado este boleto"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:53
msgid "You are changing your email to <0>{0}</0>."
msgstr "Estás cambiando tu correo electrónico a <0>{0}</0>."

#: src/components/layouts/CheckIn/index.tsx:88
#: src/components/layouts/CheckIn/index.tsx:110
msgid "You are offline"
msgstr "Estás desconectado"

#: src/components/forms/ProductForm/index.tsx:407
msgid "You can create a promo code which targets this product on the"
msgstr "Puede crear un código promocional que se dirija a este producto en el"

#: src/components/forms/TicketForm/index.tsx:352
#~ msgid "You can create a promo code which targets this ticket on the"
#~ msgstr "Puedes crear un código de promoción dirigido a este ticket en el"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:31
#~ msgid "You can now start receiving payments through Stripe."
#~ msgstr "Ahora puedes empezar a recibir pagos a través de Stripe."

#: src/components/forms/ProductForm/index.tsx:208
msgid "You cannot change the product type as there are attendees associated with this product."
msgstr "No puede cambiar el tipo de producto ya que hay asistentes asociados con este producto."

#: src/components/forms/TicketForm/index.tsx:184
#~ msgid "You cannot change the ticket type as there are attendees associated with this ticket."
#~ msgstr "No puede cambiar el tipo de entrada ya que hay asistentes asociados a esta entrada."

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "You cannot check in attendees with unpaid orders."
#~ msgstr "No puede registrar a asistentes con pedidos no pagados."

#: src/components/layouts/CheckIn/index.tsx:129
#: src/components/layouts/CheckIn/index.tsx:164
msgid "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."
msgstr "No puede registrar a asistentes con pedidos no pagados. Esta configuración se puede cambiar en los ajustes del evento."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:40
msgid "You cannot delete the last category."
msgstr "No puede eliminar la última categoría."

#: src/components/forms/ProductForm/index.tsx:57
msgid "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."
msgstr "No puede eliminar este nivel de precios porque ya hay productos vendidos para este nivel. Puede ocultarlo en su lugar."

#: src/components/forms/TicketForm/index.tsx:54
#~ msgid "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."
#~ msgstr "No puedes eliminar este nivel de precios porque ya hay boletos vendidos para este nivel. Puedes ocultarlo en su lugar."

#: src/components/modals/EditUserModal/index.tsx:88
msgid "You cannot edit the role or status of the account owner."
msgstr "No puede editar la función o el estado del propietario de la cuenta."

#: src/components/modals/RefundOrderModal/index.tsx:132
msgid "You cannot refund a manually created order."
msgstr "No puede reembolsar un pedido creado manualmente."

#: src/components/common/QuestionsTable/index.tsx:252
msgid "You created a hidden question but disabled the option to show hidden questions. It has been enabled."
msgstr "Creaste una pregunta oculta pero deshabilitaste la opción para mostrar preguntas ocultas. Ha sido habilitado."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:77
#~ msgid "You do not have permission to access this page"
#~ msgstr "Usted no tiene permiso para acceder a esta página"

#: src/components/modals/ChooseAccountModal/index.tsx:14
msgid "You have access to multiple accounts. Please choose one to continue."
msgstr "Tienes acceso a múltiples cuentas. Por favor elige uno para continuar."

#: src/components/routes/auth/AcceptInvitation/index.tsx:57
msgid "You have already accepted this invitation. Please login to continue."
msgstr "Ya has aceptado esta invitación. Por favor inicie sesión para continuar."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:29
#~ msgid "You have connected your Stripe account"
#~ msgstr "Has conectado tu cuenta Stripe"

#: src/components/common/QuestionsTable/index.tsx:338
msgid "You have no attendee questions."
msgstr "No tienes preguntas de los asistentes."

#: src/components/common/QuestionsTable/index.tsx:323
msgid "You have no order questions."
msgstr "No tienes preguntas sobre pedidos."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:41
msgid "You have no pending email change."
msgstr "No tienes ningún cambio de correo electrónico pendiente."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:39
#~ msgid "You have not completed your Stripe Connect setup"
#~ msgstr "No has completado la configuración de Stripe Connect"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:38
#~ msgid "You have not connected your Stripe account"
#~ msgstr "No has conectado tu cuenta de Stripe"

#: src/components/layouts/Checkout/index.tsx:156
msgid "You have run out of time to complete your order."
msgstr "Se te ha acabado el tiempo para completar tu pedido."

#: src/components/forms/ProductForm/index.tsx:374
#~ msgid "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"
#~ msgstr "Tiene impuestos y tasas añadidos a un producto gratuito. ¿Le gustaría eliminarlos u ocultarlos?"

#: src/components/forms/ProductForm/index.tsx:374
msgid "You have taxes and fees added to a Free Product. Would you like to remove them?"
msgstr "Has añadido impuestos y tarifas a un producto gratuito. ¿Te gustaría eliminarlos?"

#: src/components/forms/TicketForm/index.tsx:319
#~ msgid "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"
#~ msgstr "Tiene impuestos y tarifas agregados a un boleto gratis. ¿Le gustaría eliminarlos u ocultarlos?"

#: src/components/common/MessageList/index.tsx:74
msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."
msgstr "Aún no ha enviado ningún mensaje. Puede enviar mensajes a todos los asistentes o a titulares de productos específicos."

#: src/components/common/MessageList/index.tsx:64
#~ msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."
#~ msgstr "Aún no has enviado ningún mensaje. Puede enviar mensajes a todos los asistentes o a poseedores de entradas específicas."

#: src/components/modals/SendMessageModal/index.tsx:108
msgid "You must acknowledge that this email is not promotional"
msgstr "Debes reconocer que este correo electrónico no es promocional."

#: src/components/routes/auth/AcceptInvitation/index.tsx:33
msgid "You must agree to the terms and conditions"
msgstr "Debes aceptar los términos y condiciones."

#: src/components/routes/event/GettingStarted/index.tsx:193
msgid "You must confirm your email address before your event can go live."
msgstr "Debes confirmar tu dirección de correo electrónico antes de que tu evento pueda comenzar."

#: src/components/modals/CreateAttendeeModal/index.tsx:98
msgid "You must create a ticket before you can manually add an attendee."
msgstr "Debe crear un ticket antes de poder agregar manualmente un asistente."

#: src/components/forms/ProductForm/index.tsx:60
msgid "You must have at least one price tier"
msgstr "Debes tener al menos un nivel de precios"

#: src/components/modals/SendMessageModal/index.tsx:136
#~ msgid "You need to verify your account before you can send messages."
#~ msgstr "Debes verificar tu cuenta antes de poder enviar mensajes."

#: src/components/modals/SendMessageModal/index.tsx:151
msgid "You need to verify your account email before you can send messages."
msgstr "Debes verificar el correo electrónico de tu cuenta antes de poder enviar mensajes."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:143
msgid "You will have to mark an order as paid manually. This can be done on the manage order page."
msgstr "Deberá marcar un pedido como pagado manualmente. Esto se puede hacer en la página de gestión de pedidos."

#: src/components/modals/CreateCheckInListModal/index.tsx:55
msgid "You'll need a ticket before you can create a check-in list."
msgstr "Necesitarás un boleto antes de poder crear una lista de registro."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
msgid "You'll need at a product before you can create a capacity assignment."
msgstr "Necesitará un producto antes de poder crear una asignación de capacidad."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
#~ msgid "You'll need at a ticket before you can create a capacity assignment."
#~ msgstr "Necesitarás un ticket antes de poder crear una asignación de capacidad."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:43
msgid "You'll need at least one product to get started. Free, paid or let the user decide what to pay."
msgstr "Necesitará al menos un producto para comenzar. Gratis, de pago o deje que el usuario decida cuánto pagar."

#: src/components/common/TicketsTable/index.tsx:69
#~ msgid "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."
#~ msgstr "Necesitará al menos un boleto para comenzar. Gratis, de pago o deja que el usuario decida qué pagar."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:93
msgid "You're going to {0}! 🎉"
msgstr "¡Vas a {0}! 🎉"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:64
msgid "Your account name is used on event pages and in emails."
msgstr "Su nombre de cuenta se utiliza en las páginas de eventos y en los correos electrónicos."

#: src/components/routes/event/attendees.tsx:44
msgid "Your attendees have been exported successfully."
msgstr "Tus asistentes se han exportado con éxito."

#: src/components/common/AttendeeTable/index.tsx:61
msgid "Your attendees will appear here once they have registered for your event. You can also manually add attendees."
msgstr "Sus asistentes aparecerán aquí una vez que se hayan registrado para su evento. También puede agregar asistentes manualmente."

#: src/components/common/WidgetEditor/index.tsx:329
msgid "Your awesome website 🎉"
msgstr "Tu increíble sitio web 🎉"

#: src/components/routes/product-widget/CollectInformation/index.tsx:276
msgid "Your Details"
msgstr "Tus detalles"

#: src/components/routes/auth/ForgotPassword/index.tsx:52
msgid "Your Email"
msgstr "Tu correo electrónico"

#: src/components/routes/profile/ManageProfile/index.tsx:118
msgid "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"
msgstr "Su solicitud de cambio de correo electrónico a <0>{0}</0> está pendiente. Por favor revisa tu correo para confirmar"

#: src/components/routes/event/EventDashboard/index.tsx:150
msgid "Your event must be live before you can sell tickets to attendees."
msgstr "Tu evento debe estar activo antes de que puedas vender entradas a los asistentes."

#: src/components/routes/event/EventDashboard/index.tsx:164
#~ msgid "Your event must be live before you can sell tickets."
#~ msgstr "Su evento debe estar en vivo antes de que pueda vender entradas."

#: src/components/common/OrdersTable/index.tsx:88
msgid "Your message has been sent"
msgstr "Tu mensaje ha sido enviado"

#: src/components/layouts/Checkout/index.tsx:84
msgid "Your Order"
msgstr "Tu pedido"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:94
msgid "Your order has been cancelled"
msgstr "Tu pedido ha sido cancelado"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:96
msgid "Your order is awaiting payment 🏦"
msgstr "Su pedido está en espera de pago 🏦"

#: src/components/routes/event/orders.tsx:95
msgid "Your orders have been exported successfully."
msgstr "Tus pedidos se han exportado con éxito."

#: src/components/common/OrdersTable/index.tsx:66
msgid "Your orders will appear here once they start rolling in."
msgstr "Tus pedidos aparecerán aquí una vez que comiencen a llegar."

#: src/components/routes/auth/Login/index.tsx:74
#: src/components/routes/auth/Register/index.tsx:107
msgid "Your password"
msgstr "Tu contraseña"

#: src/components/forms/StripeCheckoutForm/index.tsx:63
msgid "Your payment is processing."
msgstr "Su pago se está procesando."

#: src/components/forms/StripeCheckoutForm/index.tsx:66
msgid "Your payment was not successful, please try again."
msgstr "Su pago no fue exitoso, inténtelo nuevamente."

#: src/components/forms/StripeCheckoutForm/index.tsx:126
msgid "Your payment was unsuccessful. Please try again."
msgstr "Su pago no fue exitoso. Inténtalo de nuevo."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#~ msgid "Your product for"
#~ msgstr "Su producto para"

#: src/components/modals/RefundOrderModal/index.tsx:58
msgid "Your refund is processing."
msgstr "Su reembolso se está procesando."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:153
msgid "Your Stripe account is connected and ready to process payments."
msgstr "Tu cuenta de Stripe está conectada y lista para procesar pagos."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#: src/components/routes/product-widget/PrintProduct/index.tsx:42
msgid "Your ticket for"
msgstr "Tu billete para"

#: src/components/routes/product-widget/CollectInformation/index.tsx:348
msgid "ZIP / Postal Code"
msgstr "Código postal"

#: src/components/common/CheckoutQuestion/index.tsx:170
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:146
msgid "Zip or Postal Code"
msgstr "CP o Código Postal"

#: src/components/routes/product-widget/CollectInformation/index.tsx:349
msgid "ZIP or Postal Code"
msgstr "Código postal"
