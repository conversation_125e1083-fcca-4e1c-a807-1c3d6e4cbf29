/* QuestionAndAnswerList.module.scss */
.container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.sectionHeader {
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--mantine-color-gray-2);
}

.questionsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.questionCard {
  padding: 0.75rem;
  background: #ffffff;
  border: 1px solid var(--mantine-color-gray-2);
  border-radius: var(--mantine-radius-sm);
}

.questionCompact {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--mantine-color-gray-1);

  &:last-child {
    border-bottom: none;
  }
}

.productTitle {
  color: var(--mantine-color-gray-6);
  margin-bottom: 0.5rem;
  font-size: var(--mantine-font-size-xs);
}

.questionTitle {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.375rem;
  align-items: flex-start;
}

.answerContainer {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.answer {
  flex: 1;
  color: var(--mantine-color-dark-6);
  word-break: break-word;
}

.editContainer {
  padding: 0.5rem;
  background: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-2);
  border-radius: var(--mantine-radius-sm);
}

.editActions {
  margin-top: 0.5rem;
}

.attendeeInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.625rem;
  padding-top: 0.625rem;
  border-top: 1px solid var(--mantine-color-gray-1);
  color: var(--mantine-color-gray-6);
}

.emptyState {
  padding: 1.5rem;
  text-align: center;
}

.questionText {
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 4px;
}

/* New styles for attendee-focused layout */
.attendeeQuestionsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.attendeeSection {
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-sm);
  overflow: hidden;
  background: var(--mantine-color-gray-0);
}

.attendeeHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: var(--mantine-color-gray-1);
  border-bottom: 1px solid var(--mantine-color-gray-3);
}

.attendeeQuestions {
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* In compact mode, adjust spacing in attendee sections */
.questionCompact:first-child {
  padding-top: 0.25rem;
}

.questionCompact:last-child {
  padding-bottom: 0.25rem;
}
