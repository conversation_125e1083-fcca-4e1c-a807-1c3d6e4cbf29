msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Hi.Events\n"
"Language: fr\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/components/layouts/Event/index.tsx:189
msgid "- Click to Publish"
msgstr "- Cliquez pour publier"

#: src/components/layouts/Event/index.tsx:191
msgid "- Click to Unpublish"
msgstr "- Cliquez pour dépublier"

#: src/components/common/NoResultsSplash/index.tsx:14
msgid "'There\\'s nothing to show yet'"
msgstr "\"Il n'y a rien à montrer pour l'instant\""

#: src/components/modals/SendMessageModal/index.tsx:159
#~ msgid ""
#~ "\"\"Due to the high risk of spam, we require manual verification before you can send messages.\n"
#~ "\"\"Please contact us to request access."
#~ msgstr ""
#~ "En raison du risque élevé de spam, une vérification manuelle est requise avant d’envoyer des messages.\n"
#~ "Veuillez nous contacter pour demander l'accès."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
#~ msgid ""
#~ "\"\"If you have an account with us, you will receive an email with instructions on how to reset your\n"
#~ "\"\"password."
#~ msgstr ""
#~ "Si vous avez un compte chez nous, vous recevrez un e-mail avec des instructions pour réinitialiser votre\n"
#~ "mot de passe."

#: src/components/common/ReportTable/index.tsx:303
#: src/locales.ts:37
msgid "{0}"
msgstr "{0}"

#: src/components/layouts/CheckIn/index.tsx:82
msgid "{0} <0>checked in</0> successfully"
msgstr "{0} <0>enregistré</0> avec succès"

#: src/components/layouts/CheckIn/index.tsx:106
msgid "{0} <0>checked out</0> successfully"
msgstr "{0} <0>sorti</0> avec succès"

#: src/components/routes/event/Webhooks/index.tsx:24
msgid "{0} Active Webhooks"
msgstr "{0} webhooks actifs"

#: src/components/routes/product-widget/SelectProducts/index.tsx:412
msgid "{0} available"
msgstr "{0} disponible"

#: src/components/common/AttendeeCheckInTable/index.tsx:155
#~ msgid "{0} checked in"
#~ msgstr "{0} s'est enregistré"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
msgid "{0} created successfully"
msgstr "{0} créé avec succès"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
msgid "{0} updated successfully"
msgstr "{0} mis à jour avec succès"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:57
msgid "{0}'s Events"
msgstr "Les événements de {0}"

#: src/components/layouts/CheckIn/index.tsx:449
msgid "{0}/{1} checked in"
msgstr "{0}/{1} enregistré"

#: src/components/common/Countdown/index.tsx:68
msgid "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{days} jours, {hours} heures, {minutes} minutes, et {seconds} secondes"

#: src/components/common/WebhookTable/index.tsx:79
msgid "{eventCount} events"
msgstr "{eventCount} événements"

#: src/components/common/Countdown/index.tsx:70
msgid "{hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{hours} heures, {minutes} minutes, et {seconds} secondes"

#: src/components/common/Countdown/index.tsx:72
msgid "{minutes} minutes and {seconds} seconds"
msgstr "{minutes} minutes et {secondes} secondes"

#: src/components/routes/welcome/index.tsx:59
msgid "{organizerName}'s first event"
msgstr "Premier événement de {organizerName}"

#: src/components/common/ReportTable/index.tsx:256
#~ msgid "{title}"
#~ msgstr ""

#: src/components/common/CapacityAssignmentList/index.tsx:47
msgid "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"
msgstr "<0>Les affectations de capacité vous permettent de gérer la capacité des billets ou d'un événement entier. Idéal pour les événements de plusieurs jours, les ateliers et plus encore, où le contrôle de l'assistance est crucial.</0><1>Par exemple, vous pouvez associer une affectation de capacité avec un billet <2>Jour Un</2> et <3>Tous les Jours</3>. Une fois la capacité atteinte, les deux billets ne seront plus disponibles à la vente.</1>"

#: src/components/common/CheckInListList/index.tsx:49
msgid "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"
msgstr "<0>Les listes de pointage aident à gérer l'entrée des participants à votre événement. Vous pouvez associer plusieurs billets à une liste de pointage et vous assurer que seuls ceux avec des billets valides peuvent entrer.</0>"

#: src/components/common/WidgetEditor/index.tsx:324
msgid "<0>https://</0>your-website.com"
msgstr "<0>https://</0>votre-siteweb.com"

#: src/components/forms/ProductForm/index.tsx:287
msgid "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"
msgstr "<0>Veuillez entrer le prix hors taxes et frais.</0><1>Les taxes et frais peuvent être ajoutés ci-dessous.</1>"

#: src/components/forms/ProductForm/index.tsx:304
msgid "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"
msgstr "<0>Le nombre de produits disponibles pour ce produit</0><1>Cette valeur peut être remplacée s'il existe des <2>Limites de Capacité</2> associées à ce produit.</1>"

#: src/components/forms/TicketForm/index.tsx:249
#~ msgid "<0>The number of tickets available for this ticket</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this ticket.</1>"
#~ msgstr "<0>Le nombre de billets disponibles pour ce billet</0><1>Cette valeur peut être remplacée s'il y a des <2>Limites de Capacité</2> associées à ce billet.</1>"

#: src/components/common/WebhookTable/index.tsx:205
msgid "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"
msgstr "<0>Les webhooks notifient instantanément les services externes lorsqu'un événement se produit, comme l'ajout d'un nouvel inscrit à votre CRM ou à votre liste de diffusion lors de l'inscription, garantissant une automatisation fluide.</0><1>Utilisez des services tiers comme <2>Zapier</2>, <3>IFTTT</3> ou <4>Make</4> pour créer des workflows personnalisés et automatiser des tâches.</1>"

#: src/components/routes/event/GettingStarted/index.tsx:134
msgid "⚡️ Set up your event"
msgstr "⚡️ Organisez votre événement"

#: src/components/routes/event/GettingStarted/index.tsx:190
msgid "✉️ Confirm your email address"
msgstr "✉️ Confirmez votre adresse email"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "🎉 Congratulations on creating an event!"
#~ msgstr "🎉 Félicitations pour la création d'un événement !"

#: src/components/routes/event/GettingStarted/index.tsx:67
#~ msgid "🎟️ Add products"
#~ msgstr "🎟️ Ajouter des produits"

#: src/components/routes/event/GettingStarted/index.tsx:119
msgid "🎟️ Add tickets"
msgstr "🎟️ Ajouter des billets"

#: src/components/routes/event/GettingStarted/index.tsx:162
msgid "🎨 Customize your event page"
msgstr "🎨 Personnalisez votre page d'événement"

#: src/components/routes/event/GettingStarted/index.tsx:147
msgid "💳 Connect with Stripe"
msgstr "💳 Connectez-vous avec Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:176
msgid "🚀 Set your event live"
msgstr "🚀 Mettez votre événement en direct"

#: src/components/common/Countdown/index.tsx:34
msgid "0 minutes and 0 seconds"
msgstr "0 minute et 0 seconde"

#: src/components/routes/event/Webhooks/index.tsx:23
msgid "1 Active Webhook"
msgstr "1 webhook actif"

#: src/components/modals/RefundOrderModal/index.tsx:105
msgid "10.00"
msgstr "10h00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:123
msgid "123 Main Street"
msgstr "123, rue Principale"

#: src/components/common/WidgetEditor/index.tsx:226
msgid "20"
msgstr "20"

#: src/components/routes/welcome/index.tsx:91
msgid "2024-01-01 10:00"
msgstr "2024-01-01 10:00"

#: src/components/routes/welcome/index.tsx:97
msgid "2024-01-01 18:00"
msgstr "2024-01-01 18:00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:147
msgid "94103"
msgstr "94103"

#: src/components/forms/QuestionForm/index.tsx:138
msgid "A date input. Perfect for asking for a date of birth etc."
msgstr "Un champ de date. Parfait pour demander une date de naissance, etc."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
msgid "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."
msgstr "Un {type} par défaut est automatiquement appliqué à tous les nouveaux produits. Vous pouvez le remplacer pour chaque produit."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
#~ msgid "A default {type} is automaticaly applied to all new tickets. You can override this on a per ticket basis."
#~ msgstr "Un {type} par défaut est automatiquement appliqué à tous les nouveaux tickets. Vous pouvez remplacer cela par ticket."

#: src/components/forms/QuestionForm/index.tsx:126
msgid "A Dropdown input allows only one selection"
msgstr "Une entrée déroulante ne permet qu'une seule sélection"

#: src/components/forms/TaxAndFeeForm/index.tsx:20
msgid "A fee, like a booking fee or a service fee"
msgstr "Des frais, comme des frais de réservation ou des frais de service"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
msgid "A fixed amount per product. E.g, $0.50 per product"
msgstr "Un montant fixe par produit. Par exemple, 0,50 $ par produit"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
#~ msgid "A fixed amount per ticket. E.g, $0.50 per ticket"
#~ msgstr "Un montant fixe par billet. Par exemple, 0,50 $ par billet"

#: src/components/forms/QuestionForm/index.tsx:108
msgid "A multi line text input"
msgstr "Une saisie de texte sur plusieurs lignes"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
msgid "A percentage of the product price. E.g., 3.5% of the product price"
msgstr "Un pourcentage du prix du produit. Par exemple, 3,5 % du prix du produit"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
#~ msgid "A percentage of the ticket price. E.g., 3.5% of the ticket price"
#~ msgstr "Un pourcentage du prix du billet. Par exemple, 3,5 % du prix du billet"

#: src/components/forms/PromoCodeForm/index.tsx:37
msgid "A promo code with no discount can be used to reveal hidden products."
msgstr "Un code promo sans réduction peut être utilisé pour révéler des produits cachés."

#: src/components/forms/PromoCodeForm/index.tsx:36
#~ msgid "A promo code with no discount can be used to reveal hidden tickets."
#~ msgstr "Un code promotionnel sans réduction peut être utilisé pour révéler des billets cachés."

#: src/components/forms/QuestionForm/index.tsx:120
msgid "A Radio option has multiple options but only one can be selected."
msgstr "Une option Radio comporte plusieurs options, mais une seule peut être sélectionnée."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:69
msgid "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"
msgstr "Une brève description de l'événement qui sera affichée dans les résultats des moteurs de recherche et lors du partage sur les réseaux sociaux. Par défaut, la description de l'événement sera utilisée"

#: src/components/forms/QuestionForm/index.tsx:102
msgid "A single line text input"
msgstr "Une saisie de texte sur une seule ligne"

#: src/components/forms/QuestionForm/index.tsx:92
#~ msgid "A single question per attendee. E.g, What is your preferred meal?"
#~ msgstr "Une seule question par participant. Par exemple, quel est votre repas préféré ?"

#: src/components/forms/QuestionForm/index.tsx:86
#~ msgid "A single question per order. E.g, What is your company name?"
#~ msgstr "Une seule question par commande. Par exemple, quel est le nom de votre entreprise ?"

#: src/components/forms/QuestionForm/index.tsx:87
msgid "A single question per order. E.g, What is your shipping address?"
msgstr "Une seule question par commande. Par exemple, Quelle est votre adresse de livraison ?"

#: src/components/forms/QuestionForm/index.tsx:93
msgid "A single question per product. E.g, What is your t-shirt size?"
msgstr "Une seule question par produit. Par exemple, Quelle est votre taille de t-shirt ?"

#: src/components/forms/TaxAndFeeForm/index.tsx:14
msgid "A standard tax, like VAT or GST"
msgstr "Une taxe standard, comme la TVA ou la TPS"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:76
msgid "About"
msgstr "À propos"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:211
msgid "About Stripe Connect"
msgstr "À propos de Stripe Connect"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:79
#~ msgid "About the event"
#~ msgstr "À propos de l'événement"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:91
msgid "Accept bank transfers, checks, or other offline payment methods"
msgstr "Accepter les virements bancaires, chèques ou autres méthodes de paiement hors ligne"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:86
msgid "Accept credit card payments with Stripe"
msgstr "Accepter les paiements par carte bancaire avec Stripe"

#: src/components/routes/auth/AcceptInvitation/index.tsx:126
msgid "Accept Invitation"
msgstr "Accepter l'invitation"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:125
msgid "Access Denied"
msgstr "Accès refusé"

#: src/components/routes/account/ManageAccount/index.tsx:24
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:53
msgid "Account"
msgstr "Compte"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:62
msgid "Account Name"
msgstr "Nom du compte"

#: src/components/common/GlobalMenu/index.tsx:32
#: src/components/routes/account/ManageAccount/index.tsx:19
msgid "Account Settings"
msgstr "Paramètres du compte"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:42
msgid "Account updated successfully"
msgstr "Compte mis à jour avec succès"

#: src/components/common/AttendeeTable/index.tsx:158
#: src/components/common/ProductsTable/SortableProduct/index.tsx:267
#: src/components/common/QuestionsTable/index.tsx:108
msgid "Actions"
msgstr "Actions"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "Activate"
msgstr "Activer"

#: src/components/forms/CheckInListForm/index.tsx:47
msgid "Activation date"
msgstr "Date d'activation"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:19
#: src/components/modals/EditUserModal/index.tsx:110
msgid "Active"
msgstr "Actif"

#: src/components/routes/event/attendees.tsx:59
#~ msgid "Add"
#~ msgstr "Ajouter"

#: src/components/forms/CheckInListForm/index.tsx:39
msgid "Add a description for this check-in list"
msgstr "Ajouter une description pour cette liste de pointage"

#: src/components/modals/ManageAttendeeModal/index.tsx:131
msgid "Add any notes about the attendee. These will not be visible to the attendee."
msgstr "Ajoutez des notes sur le participant. Celles-ci ne seront pas visibles par le participant."

#: src/components/modals/ManageAttendeeModal/index.tsx:133
msgid "Add any notes about the attendee..."
msgstr "Ajoutez des notes sur le participant..."

#: src/components/modals/ManageOrderModal/index.tsx:165
msgid "Add any notes about the order. These will not be visible to the customer."
msgstr "Ajoutez des notes concernant la commande. Elles ne seront pas visibles par le client."

#: src/components/modals/ManageOrderModal/index.tsx:169
msgid "Add any notes about the order..."
msgstr "Ajoutez des notes concernant la commande..."

#: src/components/forms/QuestionForm/index.tsx:202
msgid "Add description"
msgstr "Ajouter une description"

#: src/components/routes/event/GettingStarted/index.tsx:85
#~ msgid "Add event details and and manage event settings."
#~ msgstr "Ajoutez des détails sur l'événement et gérez les paramètres de l'événement."

#: src/components/routes/event/GettingStarted/index.tsx:137
msgid "Add event details and manage event settings."
msgstr "Ajoutez les détails de l’événement et gérez les paramètres."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:153
msgid "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"
msgstr "Ajoutez des instructions pour les paiements hors ligne (par exemple, les détails du virement bancaire, où envoyer les chèques, les délais de paiement)"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add More products"
#~ msgstr "Ajouter plus de produits"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add More tickets"
msgstr "Ajouter plus de billets"

#: src/components/common/HeadingCard/index.tsx:27
msgid "Add New"
msgstr "Ajouter un nouveau"

#: src/components/forms/QuestionForm/index.tsx:68
msgid "Add Option"
msgstr "Ajouter une option"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:68
msgid "Add Product"
msgstr "Ajouter un produit"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:51
msgid "Add Product to Category"
msgstr "Ajouter un produit à la catégorie"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add products"
#~ msgstr "Ajouter des produits"

#: src/components/common/QuestionsTable/index.tsx:281
msgid "Add question"
msgstr "Ajouter une question"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:18
msgid "Add Tax or Fee"
msgstr "Ajouter une taxe ou des frais"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add tickets"
msgstr "Ajouter des billets"

#: src/components/forms/ProductForm/index.tsx:337
msgid "Add tier"
msgstr "Ajouter un niveau"

#: src/components/common/AddEventToCalendarButton/index.tsx:112
#: src/components/common/AddEventToCalendarButton/index.tsx:120
msgid "Add to Calendar"
msgstr "Ajouter au calendrier"

#: src/components/common/WebhookTable/index.tsx:223
#: src/components/routes/event/Webhooks/index.tsx:35
msgid "Add Webhook"
msgstr "Ajouter un webhook"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:219
msgid "Additional Information"
msgstr "Informations supplémentaires"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/forms/ProductForm/index.tsx:348
msgid "Additional Options"
msgstr "Options additionelles"

#: src/components/common/OrderDetails/index.tsx:96
#: src/components/forms/QuestionForm/index.tsx:130
msgid "Address"
msgstr "Adresse"

#: src/components/common/CheckoutQuestion/index.tsx:156
msgid "Address line 1"
msgstr "Adresse Ligne 1"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:122
#: src/components/routes/product-widget/CollectInformation/index.tsx:319
#: src/components/routes/product-widget/CollectInformation/index.tsx:320
msgid "Address Line 1"
msgstr "Adresse Ligne 1"

#: src/components/common/CheckoutQuestion/index.tsx:159
msgid "Address line 2"
msgstr "Adresse Ligne 2"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:127
#: src/components/routes/product-widget/CollectInformation/index.tsx:324
#: src/components/routes/product-widget/CollectInformation/index.tsx:325
msgid "Address Line 2"
msgstr "Adresse Ligne 2"

#: src/components/modals/EditUserModal/index.tsx:49
#: src/components/modals/InviteUserModal/index.tsx:41
msgid "Admin"
msgstr "Administrateur"

#: src/components/modals/EditUserModal/index.tsx:51
#: src/components/modals/InviteUserModal/index.tsx:43
msgid "Admin users have full access to events and account settings."
msgstr "Les utilisateurs administrateurs ont un accès complet aux événements et aux paramètres du compte."

#: src/components/layouts/Event/index.tsx:53
#~ msgid "Affiliates"
#~ msgstr "Affiliés"

#: src/components/common/MessageList/index.tsx:21
msgid "All attendees"
msgstr "Tous les participants"

#: src/components/modals/SendMessageModal/index.tsx:187
msgid "All attendees of this event"
msgstr "Tous les participants à cet événement"

#: src/components/routes/events/Dashboard/index.tsx:52
#~ msgid "All Events"
#~ msgstr "Tous les évènements"

#: src/components/common/PromoCodeTable/index.tsx:131
msgid "All Products"
msgstr "Tous les produits"

#: src/components/common/PromoCodeTable/index.tsx:130
#: src/components/forms/PromoCodeForm/index.tsx:67
#~ msgid "All Tickets"
#~ msgstr "Tous les billets"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:157
msgid "Allow attendees associated with unpaid orders to check in"
msgstr "Autoriser les participants associés à des commandes impayées à s'enregistrer"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:82
msgid "Allow search engine indexing"
msgstr "Autoriser l'indexation des moteurs de recherche"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:81
msgid "Allow search engines to index this event"
msgstr "Autoriser les moteurs de recherche à indexer cet événement"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:78
msgid "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."
msgstr "Presque là! Nous attendons juste que votre paiement soit traité. Cela ne devrait prendre que quelques secondes."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:77
msgid "Amazing, Event, Keywords..."
msgstr "Incroyable, Événement, Mots-clés..."

#: src/components/common/OrdersTable/index.tsx:207
#: src/components/forms/TaxAndFeeForm/index.tsx:71
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:36
msgid "Amount"
msgstr "Montant"

#: src/components/modals/CreateAttendeeModal/index.tsx:165
msgid "Amount paid ({0})"
msgstr "Montant payé ({0})"

#: src/mutations/useExportAnswers.ts:45
msgid "An error occurred while checking export status."
msgstr "Une erreur s'est produite lors de la vérification du statut d'exportation."

#: src/components/common/ErrorDisplay/index.tsx:18
msgid "An error occurred while loading the page"
msgstr "Une erreur s'est produite lors du chargement de la page"

#: src/components/common/QuestionsTable/index.tsx:148
msgid "An error occurred while sorting the questions. Please try again or refresh the page"
msgstr "Une erreur s'est produite lors du tri des questions. Veuillez réessayer ou actualiser la page"

#: src/components/common/TicketsTable/index.tsx:47
#~ msgid "An error occurred while sorting the tickets. Please try again or refresh the page"
#~ msgstr "Une erreur s'est produite lors du tri des tickets. Veuillez réessayer ou actualiser la page"

#: src/components/routes/welcome/index.tsx:75
#~ msgid "An event is the actual event you are hosting. You can add more details later."
#~ msgstr "Un événement est l’événement réel que vous organisez. Vous pourrez ajouter plus de détails ultérieurement."

#: src/components/routes/welcome/index.tsx:75
msgid "An event is the gathering or occasion you’re organizing. You can add more details later."
msgstr "Un événement est le rassemblement ou l'occasion que vous organisez. Vous pouvez ajouter plus de détails plus tard."

#: src/components/routes/welcome/index.tsx:23
msgid "An organizer is the company or person who is hosting the event"
msgstr "Un organisateur est l'entreprise ou la personne qui organise l'événement"

#: src/components/forms/StripeCheckoutForm/index.tsx:40
msgid "An unexpected error occurred."
msgstr "Une erreur inattendue est apparue."

#: src/hooks/useFormErrorResponseHandler.tsx:48
msgid "An unexpected error occurred. Please try again."
msgstr "Une erreur inattendue est apparue. Veuillez réessayer."

#: src/components/common/QuestionAndAnswerList/index.tsx:97
msgid "Answer updated successfully."
msgstr "Réponse mise à jour avec succès."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
msgid "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
msgstr "Toute question des détenteurs de produits sera envoyée à cette adresse e-mail. Elle sera également utilisée comme adresse de réponse pour tous les e-mails envoyés depuis cet événement"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
#~ msgid "Any queries from ticket holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
#~ msgstr "Toutes les questions des détenteurs de billets seront envoyées à cette adresse e-mail. Cette adresse sera également utilisée comme adresse de « réponse » pour tous les e-mails envoyés à partir de cet événement."

#: src/components/common/WidgetEditor/index.tsx:213
msgid "Appearance"
msgstr "Apparence"

#: src/components/routes/product-widget/SelectProducts/index.tsx:500
msgid "applied"
msgstr "appliqué"

#: src/components/common/CapacityAssignmentList/index.tsx:97
msgid "Applies to {0} products"
msgstr "S'applique à {0} produits"

#: src/components/common/CapacityAssignmentList/index.tsx:97
#~ msgid "Applies to {0} tickets"
#~ msgstr "S'applique à {0} billets"

#: src/components/common/CapacityAssignmentList/index.tsx:98
msgid "Applies to 1 product"
msgstr "S'applique à 1 produit"

#: src/components/common/CapacityAssignmentList/index.tsx:98
#~ msgid "Applies to 1 ticket"
#~ msgstr "S'applique à 1 billet"

#: src/components/common/FilterModal/index.tsx:253
msgid "Apply"
msgstr "Appliquer"

#: src/components/routes/product-widget/SelectProducts/index.tsx:528
msgid "Apply Promo Code"
msgstr "Appliquer le code promotionnel"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
msgid "Apply this {type} to all new products"
msgstr "Appliquer ce {type} à tous les nouveaux produits"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
#~ msgid "Apply this {type} to all new tickets"
#~ msgstr "Appliquez ce {type} à tous les nouveaux tickets"

#: src/components/common/EventCard/index.tsx:167
msgid "Archive event"
msgstr "Archiver l'événement"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:32
msgid "Archived"
msgstr "Archivé"

#: src/components/routes/events/Dashboard/index.tsx:59
msgid "Archived Events"
msgstr "Événements archivés"

#: src/components/common/AttendeeTable/index.tsx:76
msgid "Are you sure you want to activate this attendee?"
msgstr "Êtes-vous sûr de vouloir activer ce participant ?"

#: src/components/common/EventCard/index.tsx:58
msgid "Are you sure you want to archive this event?"
msgstr "Êtes-vous sûr de vouloir archiver cet événement ?"

#: src/components/common/AttendeeTable/index.tsx:78
#~ msgid "Are you sure you want to cancel this attendee? This will void their product"
#~ msgstr "Êtes-vous sûr de vouloir annuler cet invité ? Cela annulera son produit"

#: src/components/common/AttendeeTable/index.tsx:77
msgid "Are you sure you want to cancel this attendee? This will void their ticket"
msgstr "Êtes-vous sûr de vouloir annuler ce participant ? Cela annulera leur billet"

#: src/components/common/PromoCodeTable/index.tsx:37
msgid "Are you sure you want to delete this promo code?"
msgstr "Etes-vous sûr de vouloir supprimer ce code promo ?"

#: src/components/common/QuestionsTable/index.tsx:164
msgid "Are you sure you want to delete this question?"
msgstr "Êtes-vous sûr de vouloir supprimer cette question ?"

#: src/components/common/WebhookTable/index.tsx:122
msgid "Are you sure you want to delete this webhook?"
msgstr "Êtes-vous sûr de vouloir supprimer ce webhook ?"

#: src/components/layouts/Event/index.tsx:68
#: src/components/routes/event/EventDashboard/index.tsx:64
msgid "Are you sure you want to make this event draft? This will make the event invisible to the public"
msgstr "Êtes-vous sûr de vouloir créer un brouillon pour cet événement ? Cela rendra l'événement invisible au public"

#: src/components/layouts/Event/index.tsx:69
#: src/components/routes/event/EventDashboard/index.tsx:65
msgid "Are you sure you want to make this event public? This will make the event visible to the public"
msgstr "Êtes-vous sûr de vouloir rendre cet événement public ? Cela rendra l'événement visible au public"

#: src/components/common/EventCard/index.tsx:59
msgid "Are you sure you want to restore this event? It will be restored as a draft event."
msgstr "Êtes-vous sûr de vouloir restaurer cet événement ? Il sera restauré en tant que brouillon."

#: src/components/common/CapacityAssignmentList/index.tsx:163
msgid "Are you sure you would like to delete this Capacity Assignment?"
msgstr "Êtes-vous sûr de vouloir supprimer cette Affectation de Capacité?"

#: src/components/common/CheckInListList/index.tsx:183
msgid "Are you sure you would like to delete this Check-In List?"
msgstr "Êtes-vous sûr de vouloir supprimer cette liste de pointage ?"

#: src/components/forms/QuestionForm/index.tsx:90
#~ msgid "Ask once per attendee"
#~ msgstr "Demander une fois par participant"

#: src/components/forms/QuestionForm/index.tsx:85
msgid "Ask once per order"
msgstr "Demander une fois par commande"

#: src/components/forms/QuestionForm/index.tsx:91
msgid "Ask once per product"
msgstr "Demander une fois par produit"

#: src/components/modals/CreateWebhookModal/index.tsx:33
msgid "At least one event type must be selected"
msgstr "Au moins un type d'événement doit être sélectionné"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Attendee"
msgstr "Participant"

#: src/components/forms/WebhookForm/index.tsx:94
msgid "Attendee Cancelled"
msgstr "Participant annulé"

#: src/components/forms/WebhookForm/index.tsx:82
msgid "Attendee Created"
msgstr "Participant créé"

#: src/components/modals/ManageAttendeeModal/index.tsx:147
msgid "Attendee Details"
msgstr "Détails des participants"

#: src/components/layouts/AuthLayout/index.tsx:73
msgid "Attendee Management"
msgstr "Gestion des participants"

#: src/components/layouts/CheckIn/index.tsx:150
msgid "Attendee not found"
msgstr "Participant non trouvé"

#: src/components/modals/ManageAttendeeModal/index.tsx:153
msgid "Attendee Notes"
msgstr "Notes du participant"

#: src/components/common/QuestionsTable/index.tsx:369
msgid "Attendee questions"
msgstr "Questions des participants"

#: src/components/modals/ManageAttendeeModal/index.tsx:172
msgid "Attendee Ticket"
msgstr "Billet de l'invité"

#: src/components/forms/WebhookForm/index.tsx:88
msgid "Attendee Updated"
msgstr "Participant mis à jour"

#: src/components/common/OrdersTable/index.tsx:206
#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
#: src/components/common/StatBoxes/index.tsx:27
#: src/components/layouts/Event/index.tsx:99
#: src/components/modals/ManageOrderModal/index.tsx:126
#: src/components/routes/event/attendees.tsx:59
msgid "Attendees"
msgstr "Participants"

#: src/components/routes/event/attendees.tsx:43
msgid "Attendees Exported"
msgstr "Participants exportés"

#: src/components/routes/event/EventDashboard/index.tsx:239
msgid "Attendees Registered"
msgstr "Invités enregistrés"

#: src/components/modals/SendMessageModal/index.tsx:146
#~ msgid "Attendees with a specific product"
#~ msgstr "Invités avec un produit spécifique"

#: src/components/modals/SendMessageModal/index.tsx:183
msgid "Attendees with a specific ticket"
msgstr "Participants avec un ticket spécifique"

#: src/components/common/WidgetEditor/index.tsx:233
msgid "Auto Resize"
msgstr "Redimensionnement automatique"

#: src/components/layouts/AuthLayout/index.tsx:88
#~ msgid "Auto Workflow"
#~ msgstr "Flux de travail automatisé"

#: src/components/layouts/AuthLayout/index.tsx:79
msgid "Automated entry management with multiple check-in lists and real-time validation"
msgstr "Gestion automatisée des entrées avec plusieurs listes d'enregistrement et validation en temps réel"

#: src/components/common/WidgetEditor/index.tsx:236
msgid "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."
msgstr "Redimensionnez automatiquement la hauteur du widget en fonction du contenu. Lorsqu'il est désactivé, le widget remplira la hauteur du conteneur."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
#~ msgid "Avg Discount/Order"
#~ msgstr "Réduction moyenne/Commande"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
#~ msgid "Avg Order Value"
#~ msgstr "Valeur moyenne de la commande"

#: src/components/common/OrderStatusBadge/index.tsx:15
#: src/components/modals/SendMessageModal/index.tsx:231
msgid "Awaiting offline payment"
msgstr "En attente d'un paiement hors ligne"

#: src/components/routes/event/orders.tsx:25
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:43
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:66
msgid "Awaiting Offline Payment"
msgstr "En attente d'un paiement hors ligne"

#: src/components/layouts/CheckIn/index.tsx:263
msgid "Awaiting payment"
msgstr "En attente de paiement"

#: src/components/common/AttendeeTicket/index.tsx:67
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:40
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:65
msgid "Awaiting Payment"
msgstr "En attente de paiement"

#: src/components/routes/welcome/index.tsx:84
msgid "Awesome Event"
msgstr "Événement génial"

#: src/components/forms/OrganizerForm/index.tsx:28
msgid "Awesome Organizer Ltd."
msgstr "Organisateur génial Ltd."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:42
msgid "Back to all events"
msgstr "Retour à tous les événements"

#: src/components/layouts/Checkout/index.tsx:73
#: src/components/routes/product-widget/CollectInformation/index.tsx:249
#: src/components/routes/product-widget/CollectInformation/index.tsx:261
msgid "Back to event page"
msgstr "Retour à la page de l'événement"

#: src/components/routes/auth/ForgotPassword/index.tsx:59
#: src/components/routes/auth/ResetPassword/index.tsx:66
msgid "Back to login"
msgstr "Retour connexion"

#: src/components/common/WidgetEditor/index.tsx:175
msgid "Background Color"
msgstr "Couleur de l'arrière plan"

#: src/components/routes/event/HomepageDesigner/index.tsx:144
msgid "Background Type"
msgstr "Type d'arrière-plan"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#~ msgid "Basic Details"
#~ msgstr "Détails de base"

#: src/components/modals/SendMessageModal/index.tsx:278
msgid "Before you send!"
msgstr "Avant d'envoyer !"

#: src/components/routes/event/GettingStarted/index.tsx:60
#~ msgid "Before your event can go live, there are a few things you need to do."
#~ msgstr "Avant que votre événement puisse être mis en ligne, vous devez faire certaines choses."

#: src/components/routes/event/GettingStarted/index.tsx:92
msgid "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."
msgstr "Avant que votre événement puisse être mis en ligne, vous devez effectuer quelques étapes. Complétez toutes les étapes ci-dessous pour commencer."

#: src/components/routes/auth/Register/index.tsx:66
#~ msgid "Begin selling products in minutes"
#~ msgstr "Commencez à vendre des produits en quelques minutes"

#: src/components/routes/auth/Register/index.tsx:49
#~ msgid "Begin selling tickets in minutes"
#~ msgstr "Commencez à vendre des billets en quelques minutes"

#: src/components/routes/product-widget/CollectInformation/index.tsx:313
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:142
msgid "Billing Address"
msgstr "Adresse de facturation"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:167
msgid "Billing Settings"
msgstr "Paramètres de facturation"

#: src/components/layouts/AuthLayout/index.tsx:83
#~ msgid "Brand Control"
#~ msgstr "Contrôle de la marque"

#: src/components/common/LanguageSwitcher/index.tsx:28
msgid "Brazilian Portuguese"
msgstr "Portugais brésilien"

#: src/components/routes/auth/Register/index.tsx:133
msgid "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."
msgstr "En vous inscrivant, vous acceptez nos <0>Conditions d'utilisation</0> et notre <1>Politique de confidentialité</1>."

#: src/components/forms/TaxAndFeeForm/index.tsx:52
msgid "Calculation Type"
msgstr "Type de calcul"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:140
msgid "California"
msgstr "Californie"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:175
msgid "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."
msgstr "L'autorisation de la caméra a été refusée. <0>Demander l'autorisation</0> à nouveau, ou si cela ne fonctionne pas, vous devrez <1>accorder à cette page</1> l'accès à votre caméra dans les paramètres de votre navigateur."

#: src/components/common/AttendeeTable/index.tsx:182
#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/QuestionAndAnswerList/index.tsx:149
#: src/components/layouts/CheckIn/index.tsx:225
#: src/utilites/confirmationDialog.tsx:11
msgid "Cancel"
msgstr "Annuler"

#: src/components/routes/profile/ManageProfile/index.tsx:126
msgid "Cancel email change"
msgstr "Annuler le changement d'e-mail"

#: src/components/common/OrdersTable/index.tsx:190
#: src/components/modals/RefundOrderModal/index.tsx:112
msgid "Cancel order"
msgstr "Annuler la commande"

#: src/components/modals/CancelOrderModal/index.tsx:59
msgid "Cancel Order"
msgstr "annuler la commande"

#: src/components/modals/CancelOrderModal/index.tsx:44
msgid "Cancel Order {0}"
msgstr "Annuler la commande {0}"

#: src/components/common/AttendeeDetails/index.tsx:32
#~ msgid "Canceled"
#~ msgstr "Annulé"

#: src/components/modals/CancelOrderModal/index.tsx:54
msgid "Canceling will cancel all products associated with this order, and release the products back into the available pool."
msgstr "Annuler annulera tous les produits associés à cette commande et les remettra dans le stock disponible."

#: src/components/modals/CancelOrderModal/index.tsx:54
#~ msgid "Canceling will cancel all tickets associated with this order, and release the tickets back into the available pool."
#~ msgstr "L'annulation annulera tous les billets associés à cette commande et remettra les billets dans le pool disponible."

#: src/components/common/AttendeeTicket/index.tsx:61
#: src/components/common/OrderStatusBadge/index.tsx:12
#: src/components/routes/event/orders.tsx:24
msgid "Cancelled"
msgstr "Annulé"

#: src/components/layouts/CheckIn/index.tsx:173
msgid "Cannot Check In"
msgstr "Impossible de s'enregistrer"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:42
#: src/components/layouts/Event/index.tsx:103
msgid "Capacity"
msgstr "Capacité"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:36
msgid "Capacity Assignment created successfully"
msgstr "Affectation de Capacité créée avec succès"

#: src/components/common/CapacityAssignmentList/index.tsx:31
msgid "Capacity Assignment deleted successfully"
msgstr "Affectation de Capacité supprimée avec succès"

#: src/components/routes/event/CapacityAssignments/index.tsx:32
msgid "Capacity Management"
msgstr "Gestion de la Capacité"

#: src/components/modals/CreateProductCategoryModal/index.tsx:56
msgid "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."
msgstr "Les catégories vous permettent de regrouper des produits ensemble. Par exemple, vous pouvez avoir une catégorie pour \"Billets\" et une autre pour \"Marchandise\"."

#: src/components/forms/ProductForm/index.tsx:267
msgid "Categories help you organize your products. This title will be displayed on the public event page."
msgstr "Les catégories vous aident à organiser vos produits. Ce titre sera affiché sur la page publique de l'événement."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:91
msgid "Categories reordered successfully."
msgstr "Catégories réorganisées avec succès."

#: src/components/routes/event/products.tsx:96
msgid "Category"
msgstr "Catégorie"

#: src/components/modals/CreateProductCategoryModal/index.tsx:37
msgid "Category Created Successfully"
msgstr "Catégorie créée avec succès"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Change Cover"
msgstr "Changer de couverture"

#: src/components/routes/profile/ManageProfile/index.tsx:201
msgid "Change password"
msgstr "Changer le mot de passe"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check in"
#~ msgstr "enregistrement"

#: src/components/layouts/CheckIn/index.tsx:180
msgid "Check In"
msgstr "Enregistrement"

#: src/components/layouts/CheckIn/index.tsx:193
msgid "Check in {0} {1}"
msgstr "Enregistrer {0} {1}"

#: src/components/layouts/CheckIn/index.tsx:218
msgid "Check in and mark order as paid"
msgstr "Enregistrer et marquer la commande comme payée"

#: src/components/layouts/CheckIn/index.tsx:209
msgid "Check in only"
msgstr "Enregistrer uniquement"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check out"
#~ msgstr "vérifier"

#: src/components/layouts/CheckIn/index.tsx:177
msgid "Check Out"
msgstr "Sortir"

#: src/components/layouts/Checkout/index.tsx:106
msgid "Check out this event!"
msgstr "Découvrez cet événement !"

#: src/components/common/EventCard/index.tsx:156
msgid "Check-in"
msgstr "Enregistrement"

#: src/components/layouts/Event/index.tsx:52
#~ msgid "Check-In"
#~ msgstr "Enregistrement"

#: src/components/forms/WebhookForm/index.tsx:100
msgid "Check-in Created"
msgstr "Enregistrement créé"

#: src/components/forms/WebhookForm/index.tsx:106
msgid "Check-in Deleted"
msgstr "Enregistrement supprimé"

#: src/components/modals/CreateCheckInListModal/index.tsx:40
msgid "Check-In List created successfully"
msgstr "Liste de pointage créée avec succès"

#: src/components/common/CheckInListList/index.tsx:33
msgid "Check-In List deleted successfully"
msgstr "Liste de pointage supprimée avec succès"

#: src/components/layouts/CheckIn/index.tsx:326
msgid "Check-in list has expired"
msgstr "La liste de pointage a expiré"

#: src/components/layouts/CheckIn/index.tsx:343
msgid "Check-in list is not active"
msgstr "La liste de pointage n'est pas active"

#: src/components/layouts/CheckIn/index.tsx:311
msgid "Check-in list not found"
msgstr "Liste de pointage non trouvée"

#: src/components/layouts/Event/index.tsx:104
#: src/components/routes/event/CheckInLists/index.tsx:32
msgid "Check-In Lists"
msgstr "Listes de pointage"

#: src/components/common/CheckInListList/index.tsx:162
msgid "Check-In URL copied to clipboard"
msgstr "URL de pointage copiée dans le presse-papiers"

#: src/components/forms/QuestionForm/index.tsx:114
msgid "Checkbox options allow multiple selections"
msgstr "Les options de case à cocher permettent plusieurs sélections"

#: src/components/forms/QuestionForm/index.tsx:112
msgid "Checkboxes"
msgstr "Cases à cocher"

#: src/components/common/AttendeeDetails/index.tsx:29
msgid "Checked In"
msgstr "Enregistré"

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "Checked in successfully"
#~ msgstr "Enregistré avec succès"

#: src/components/layouts/Checkout/index.tsx:83
#: src/components/routes/event/Settings/index.tsx:40
msgid "Checkout"
msgstr "Paiement"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:61
msgid "Checkout Settings"
msgstr "Paramètres de paiement"

#: src/locales.ts:46
#~ msgid "Chinese"
#~ msgstr "Chinois"

#: src/components/common/LanguageSwitcher/index.tsx:30
msgid "Chinese (Simplified)"
msgstr "Chinois simplifié"

#: src/components/common/LanguageSwitcher/index.tsx:32
msgid "Chinese (Traditional)"
msgstr "Chinois (traditionnel)"

#: src/components/routes/event/HomepageDesigner/index.tsx:133
msgid "Choose a color for your background"
msgstr "Choisissez une couleur pour votre arrière-plan"

#: src/components/modals/ChooseAccountModal/index.tsx:13
msgid "Choose an account"
msgstr "Choisissez un compte"

#: src/components/common/CheckoutQuestion/index.tsx:162
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:134
#: src/components/routes/product-widget/CollectInformation/index.tsx:333
#: src/components/routes/product-widget/CollectInformation/index.tsx:334
msgid "City"
msgstr "Ville"

#: src/components/common/SearchBar/index.tsx:75
msgid "Clear Search Text"
msgstr "Effacer le texte de recherche"

#: src/components/routes/product-widget/SelectProducts/index.tsx:288
#~ msgid "click here"
#~ msgstr "Cliquez ici"

#: src/components/common/PromoCodeTable/index.tsx:98
msgid "Click to copy"
msgstr "Cliquez pour copier"

#: src/components/routes/product-widget/SelectProducts/index.tsx:533
msgid "close"
msgstr "fermer"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:186
#: src/components/modals/RefundOrderModal/index.tsx:125
#: src/components/routes/product-widget/SelectProducts/index.tsx:534
msgid "Close"
msgstr "Fermer"

#: src/components/layouts/Event/index.tsx:281
msgid "Close sidebar"
msgstr "Fermer la barre latérale"

#: src/components/common/PromoCodeTable/index.tsx:69
#: src/components/forms/PromoCodeForm/index.tsx:34
msgid "Code"
msgstr "Code"

#: src/components/modals/CreatePromoCodeModal/index.tsx:26
#: src/components/modals/EditPromoCodeModal/index.tsx:38
msgid "Code must be between 3 and 50 characters long"
msgstr "Le code doit comporter entre 3 et 50 caractères"

#: src/components/forms/ProductForm/index.tsx:401
msgid "Collapse this product when the event page is initially loaded"
msgstr "Réduire ce produit lorsque la page de l'événement est initialement chargée"

#: src/components/forms/TicketForm/index.tsx:346
#~ msgid "Collapse this ticket when the event page is initially loaded"
#~ msgstr "Réduire ce billet lors du chargement initial de la page de l'événement"

#: src/components/routes/event/HomepageDesigner/index.tsx:131
msgid "Color"
msgstr "Couleur"

#: src/components/common/WidgetEditor/index.tsx:21
msgid "Color must be a valid hex color code. Example: #ffffff"
msgstr "La couleur doit être un code couleur hexadécimal valide. Exemple : #ffffff"

#: src/components/common/WidgetEditor/index.tsx:166
#: src/components/routes/event/HomepageDesigner/index.tsx:124
msgid "Colors"
msgstr "Couleurs"

#: src/components/layouts/Event/index.tsx:158
msgid "Coming Soon"
msgstr "À venir"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
msgid "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"
msgstr "Mots-clés séparés par des virgules qui décrivent l'événement. Ceux-ci seront utilisés par les moteurs de recherche pour aider à catégoriser et indexer l'événement."

#: src/components/routes/product-widget/CollectInformation/index.tsx:453
msgid "Complete Order"
msgstr "Complétez la commande"

#: src/components/routes/product-widget/CollectInformation/index.tsx:223
msgid "Complete payment"
msgstr "Paiement complet"

#: src/components/routes/product-widget/Payment/index.tsx:131
msgid "Complete Payment"
msgstr "Paiement complet"

#: src/components/layouts/AuthLayout/index.tsx:68
#~ msgid "Complete Store"
#~ msgstr "Boutique complète"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:202
#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Complete Stripe Setup"
msgstr "Finaliser la configuration de Stripe"

#: src/components/routes/event/EventDashboard/index.tsx:127
msgid "Complete these steps to start selling tickets for your event."
msgstr "Complétez ces étapes pour commencer à vendre des billets pour votre événement."

#: src/components/modals/SendMessageModal/index.tsx:230
#: src/components/routes/event/GettingStarted/index.tsx:70
#: src/components/routes/event/orders.tsx:23
msgid "Completed"
msgstr "Complété"

#: src/components/common/StatBoxes/index.tsx:51
msgid "Completed orders"
msgstr "Commandes terminées"

#: src/components/routes/event/EventDashboard/index.tsx:237
msgid "Completed Orders"
msgstr "Commandes terminées"

#: src/components/common/WidgetEditor/index.tsx:287
msgid "Component Code"
msgstr "Code composant"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:35
msgid "Configured Discount"
msgstr "Réduction configurée"

#: src/utilites/confirmationDialog.tsx:11
msgid "Confirm"
msgstr "Confirmer"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:51
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:56
msgid "Confirm Email Change"
msgstr "Confirmer le changement d'e-mail"

#: src/components/routes/profile/ManageProfile/index.tsx:199
msgid "Confirm New Password"
msgstr "Confirmer le nouveau mot de passe"

#: src/components/routes/auth/Register/index.tsx:115
msgid "Confirm password"
msgstr "Confirmez le mot de passe"

#: src/components/routes/auth/AcceptInvitation/index.tsx:112
#: src/components/routes/auth/Register/index.tsx:114
#: src/components/routes/auth/ResetPassword/index.tsx:60
msgid "Confirm Password"
msgstr "Confirmez le mot de passe"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:36
msgid "Confirming email address..."
msgstr "Confirmation de l'adresse e-mail..."

#: src/components/routes/event/GettingStarted/index.tsx:88
msgid "Congratulations on creating an event!"
msgstr "Félicitations pour la création de votre événement !"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:173
msgid "Connect Documentation"
msgstr "Documentation de connexion"

#: src/components/routes/event/EventDashboard/index.tsx:192
msgid "Connect payment processing"
msgstr "Connecter le traitement des paiements"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:52
#~ msgid "Connect Stripe"
#~ msgstr "Connecter la bande"

#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Connect to Stripe"
msgstr "Se connecter à Stripe"

#: src/components/layouts/AuthLayout/index.tsx:89
msgid "Connect with CRM and automate tasks using webhooks and integrations"
msgstr "Connectez-vous au CRM et automatisez les tâches à l'aide de webhooks et d'intégrations"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:201
#: src/components/routes/event/GettingStarted/index.tsx:154
msgid "Connect with Stripe"
msgstr "Connectez-vous avec Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:150
msgid "Connect your Stripe account to start receiving payments."
msgstr "Connectez votre compte Stripe pour commencer à recevoir des paiements."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:148
msgid "Connected to Stripe"
msgstr "Connecté à Stripe"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:98
msgid "Connection Details"
msgstr "Détails de connexion"

#: src/components/modals/SendMessageModal/index.tsx:167
msgid "Contact Support"
msgstr "Contacter le support"

#: src/components/modals/SendMessageModal/index.tsx:158
msgid "Contact us to enable messaging"
msgstr "Contactez-nous pour activer la messagerie"

#: src/components/routes/event/HomepageDesigner/index.tsx:155
msgid "Content background color"
msgstr "Couleur d'arrière-plan du contenu"

#: src/components/common/WidgetEditor/index.tsx:31
#: src/components/common/WidgetEditor/index.tsx:217
#: src/components/layouts/Checkout/CheckoutFooter/index.tsx:38
#: src/components/routes/product-widget/CollectInformation/index.tsx:444
#: src/components/routes/product-widget/SelectProducts/index.tsx:486
msgid "Continue"
msgstr "Continuer"

#: src/components/routes/event/HomepageDesigner/index.tsx:164
msgid "Continue button text"
msgstr "Texte du bouton Continuer"

#: src/components/common/WidgetEditor/index.tsx:216
msgid "Continue Button Text"
msgstr "Texte du bouton Continuer"

#: src/components/modals/CreateEventModal/index.tsx:153
msgid "Continue Event Setup"
msgstr "Continuer la configuration de l'événement"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Continue set up"
msgstr "Continuer la configuration"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
#~ msgid "Continue Stripe Connect Setup"
#~ msgstr "Continuer la configuration de Stripe Connect"

#: src/components/routes/product-widget/SelectProducts/index.tsx:337
msgid "Continue to Checkout"
msgstr "Passer à la caisse"

#: src/components/routes/product-widget/CollectInformation/index.tsx:380
#~ msgid "Continue To Payment"
#~ msgstr "Continuer vers le paiement"

#: src/components/common/AttendeeTicket/index.tsx:91
#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:243
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copied"
msgstr "Copié"

#: src/components/common/PromoCodeTable/index.tsx:106
msgid "copied to clipboard"
msgstr "copié dans le presse-papier"

#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copy"
msgstr "Copie"

#: src/components/common/CheckInListList/index.tsx:156
msgid "Copy Check-In URL"
msgstr "Copier l'URL de pointage"

#: src/components/routes/product-widget/CollectInformation/index.tsx:306
msgid "Copy details to all attendees"
msgstr "Copier les détails à tous les participants"

#: src/components/common/AttendeeTicket/index.tsx:91
msgid "Copy Link"
msgstr "Copier le lien"

#: src/components/common/PromoCodeTable/index.tsx:177
#: src/components/modals/ShareModal/index.tsx:243
msgid "Copy URL"
msgstr "Copier le lien"

#: src/components/common/CheckoutQuestion/index.tsx:174
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:152
#: src/components/routes/product-widget/CollectInformation/index.tsx:354
msgid "Country"
msgstr "Pays"

#: src/components/routes/event/HomepageDesigner/index.tsx:117
msgid "Cover"
msgstr "Couverture"

#: src/components/routes/event/attendees.tsx:71
#: src/components/routes/event/products.tsx:74
msgid "Create"
msgstr "Créer"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
msgid "Create {0}"
msgstr "Créer {0}"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:59
msgid "Create a Product"
msgstr "Créer un produit"

#: src/components/common/PromoCodeTable/index.tsx:57
msgid "Create a Promo Code"
msgstr "Créer un code promotionnel"

#: src/components/modals/CreateCheckInListModal/index.tsx:63
msgid "Create a Ticket"
msgstr "Créer un billet"

#: src/components/routes/auth/Register/index.tsx:69
msgid "Create an account or <0>{0}</0> to get started"
msgstr "Créez un compte ou <0>{0}</0> pour commencer"

#: src/components/modals/CreateEventModal/index.tsx:120
msgid "create an organizer"
msgstr "créer un organisateur"

#: src/components/layouts/AuthLayout/index.tsx:27
msgid "Create and customize your event page instantly"
msgstr "Créez et personnalisez votre page d'événement instantanément"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Create Attendee"
msgstr "Créer un participant"

#: src/components/common/CapacityAssignmentList/index.tsx:63
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:68
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:79
#: src/components/routes/event/CapacityAssignments/index.tsx:46
msgid "Create Capacity Assignment"
msgstr "Créer une Affectation de Capacité"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:128
#: src/components/common/ProductsTable/SortableCategory/index.tsx:129
msgid "Create category"
msgstr "Créer une catégorie"

#: src/components/modals/CreateProductCategoryModal/index.tsx:45
#: src/components/modals/CreateProductCategoryModal/index.tsx:60
msgid "Create Category"
msgstr "Créer une catégorie"

#: src/components/common/CheckInListList/index.tsx:61
#: src/components/modals/CreateCheckInListModal/index.tsx:72
#: src/components/modals/CreateCheckInListModal/index.tsx:82
#: src/components/routes/event/CheckInLists/index.tsx:46
msgid "Create Check-In List"
msgstr "Créer une liste de pointage"

#: src/components/common/NoEventsBlankSlate/index.tsx:27
#: src/components/modals/CreateEventModal/index.tsx:85
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:81
#: src/components/routes/welcome/index.tsx:108
msgid "Create Event"
msgstr "Créer un évènement"

#: src/components/routes/events/Dashboard/index.tsx:91
msgid "Create new"
msgstr "Créer un nouveau"

#: src/components/forms/OrganizerForm/index.tsx:111
#: src/components/modals/CreateEventModal/index.tsx:93
#: src/components/modals/CreateOrganizerModal/index.tsx:16
msgid "Create Organizer"
msgstr "Créer un organisateur"

#: src/components/modals/CreateProductModal/index.tsx:80
#: src/components/modals/CreateProductModal/index.tsx:88
msgid "Create Product"
msgstr "Créer un produit"

#: src/components/routes/event/GettingStarted/index.tsx:70
#~ msgid "Create products for your event, set prices, and manage available quantity."
#~ msgstr "Créez des produits pour votre événement, définissez les prix et gérez la quantité disponible."

#: src/components/modals/CreatePromoCodeModal/index.tsx:51
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
msgid "Create Promo Code"
msgstr "Créer un code promotionnel"

#: src/components/modals/CreateQuestionModal/index.tsx:69
#: src/components/modals/CreateQuestionModal/index.tsx:74
msgid "Create Question"
msgstr "Créer une question"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:41
msgid "Create Tax or Fee"
msgstr "Créer une taxe ou des frais"

#: src/components/modals/CreateTicketModal/index.tsx:83
#: src/components/modals/CreateTicketModal/index.tsx:91
#: src/components/routes/event/tickets.tsx:50
#~ msgid "Create Ticket"
#~ msgstr "Créer un ticket"

#: src/components/routes/event/GettingStarted/index.tsx:122
msgid "Create tickets for your event, set prices, and manage available quantity."
msgstr "Créez des billets pour votre événement, fixez les prix et gérez la quantité disponible."

#: src/components/modals/CreateWebhookModal/index.tsx:57
#: src/components/modals/CreateWebhookModal/index.tsx:67
msgid "Create Webhook"
msgstr "Créer un webhook"

#: src/components/common/OrdersTable/index.tsx:208
#: src/components/common/OrdersTable/index.tsx:281
msgid "Created"
msgstr "Créé"

#: src/components/forms/OrganizerForm/index.tsx:45
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:73
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:100
msgid "Currency"
msgstr "Devise"

#: src/components/routes/profile/ManageProfile/index.tsx:191
msgid "Current Password"
msgstr "Mot de passe actuel"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:159
msgid "Custom Maps URL"
msgstr "URL des cartes personnalisées"

#: src/components/common/ReportTable/index.tsx:53
msgid "Custom Range"
msgstr "Plage personnalisée"

#: src/components/common/OrdersTable/index.tsx:205
msgid "Customer"
msgstr "Client"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:55
msgid "Customize the email and notification settings for this event"
msgstr "Personnaliser les paramètres de courrier électronique et de notification pour cet événement"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:62
msgid "Customize the event homepage and checkout messaging"
msgstr "Personnalisez la page d'accueil de l'événement et la messagerie de paiement"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:70
msgid "Customize the miscellaneous settings for this event"
msgstr "Personnaliser les divers paramètres de cet événement"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:57
msgid "Customize the SEO settings for this event"
msgstr "Personnalisez les paramètres SEO pour cet événement"

#: src/components/routes/event/GettingStarted/index.tsx:169
msgid "Customize your event page"
msgstr "Personnalisez votre page d'événement"

#: src/components/layouts/AuthLayout/index.tsx:84
msgid "Customize your event page and widget design to match your brand perfectly"
msgstr "Personnalisez votre page événement et la conception du widget pour correspondre parfaitement à votre marque"

#: src/components/routes/event/GettingStarted/index.tsx:165
msgid "Customize your event page to match your brand and style."
msgstr "Personnalisez votre page d'événement en fonction de votre marque et de votre style."

#: src/components/routes/event/Reports/index.tsx:23
msgid "Daily Sales Report"
msgstr "Rapport des ventes quotidiennes"

#: src/components/routes/event/Reports/index.tsx:24
msgid "Daily sales, tax, and fee breakdown"
msgstr "Détail des ventes quotidiennes, taxes et frais"

#: src/components/common/CapacityAssignmentList/index.tsx:156
#: src/components/common/CheckInListList/index.tsx:176
#: src/components/common/OrdersTable/index.tsx:186
#: src/components/common/ProductsTable/SortableProduct/index.tsx:287
#: src/components/common/PromoCodeTable/index.tsx:181
#: src/components/common/QuestionsTable/index.tsx:115
#: src/components/common/WebhookTable/index.tsx:116
msgid "Danger zone"
msgstr "Zone dangereuse"

#: src/components/common/AttendeeTable/index.tsx:179
msgid "Danger Zone"
msgstr "Zone de Danger"

#: src/components/layouts/Event/index.tsx:89
msgid "Dashboard"
msgstr "Tableau de bord"

#: src/components/common/OrderDetails/index.tsx:39
#: src/components/forms/QuestionForm/index.tsx:136
msgid "Date"
msgstr "Date"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:40
#~ msgid "Date & Time"
#~ msgstr "Date et heure"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:38
msgid "Day one capacity"
msgstr "Capacité du premier jour"

#: src/components/forms/CheckInListForm/index.tsx:21
#~ msgid "Day one check-in list"
#~ msgstr "Liste de pointage du premier jour"

#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/TaxAndFeeList/index.tsx:73
msgid "Delete"
msgstr "Supprimer"

#: src/components/common/CapacityAssignmentList/index.tsx:159
msgid "Delete Capacity"
msgstr "Supprimer la Capacité"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:146
#: src/components/common/ProductsTable/SortableCategory/index.tsx:147
msgid "Delete category"
msgstr "Supprimer la catégorie"

#: src/components/common/CheckInListList/index.tsx:179
msgid "Delete Check-In List"
msgstr "Supprimer la liste de pointage"

#: src/components/common/PromoCodeTable/index.tsx:185
msgid "Delete code"
msgstr "Supprimer le code"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:113
msgid "Delete Cover"
msgstr "Supprimer la couverture"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:98
msgid "Delete Image"
msgstr "Supprimer l'image"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:292
msgid "Delete product"
msgstr "Supprimer le produit"

#: src/components/common/QuestionsTable/index.tsx:120
msgid "Delete question"
msgstr "Supprimer la question"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:199
#~ msgid "Delete ticket"
#~ msgstr "Supprimer le billet"

#: src/components/common/WebhookTable/index.tsx:127
msgid "Delete webhook"
msgstr "Supprimer le webhook"

#: src/components/forms/ProductForm/index.tsx:258
#: src/components/forms/TaxAndFeeForm/index.tsx:81
#: src/components/modals/CreateEventModal/index.tsx:135
#: src/components/modals/DuplicateEventModal/index.tsx:84
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:78
msgid "Description"
msgstr "Description"

#: src/components/forms/CheckInListForm/index.tsx:36
msgid "Description for check-in staff"
msgstr "Description pour le personnel de pointage"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Details"
msgstr "Détails"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
#~ msgid "Disable this capacity track capacity without stopping product sales"
#~ msgstr "Désactiver cette capacité pour suivre la capacité sans arrêter les ventes de produits"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:26
#~ msgid "Disable this capacity track capacity without stopping ticket sales"
#~ msgstr "Désactivez cette capacité pour suivre la capacité sans arrêter la vente de billets"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
msgid "Disabling this capacity will track sales but not stop them when the limit is reached"
msgstr "Désactiver cette capacité suivra les ventes mais ne les arrêtera pas lorsque la limite sera atteinte"

#: src/components/common/PromoCodeTable/index.tsx:70
msgid "Discount"
msgstr "Rabais"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount %"
msgstr "Rabais %"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount in {0}"
msgstr "Remise en {0}"

#: src/components/forms/PromoCodeForm/index.tsx:43
msgid "Discount Type"
msgstr "Type de remise"

#: src/components/routes/product-widget/SelectProducts/index.tsx:297
#~ msgid "Dismiss"
#~ msgstr "Rejeter"

#: src/components/routes/product-widget/SelectProducts/index.tsx:354
msgid "Dismiss this message"
msgstr "Ignorer ce message"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:190
msgid "Document Label"
msgstr "Étiquette du document"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:222
msgid "Documentation"
msgstr "Documentation"

# French (fr)
#: src/components/layouts/AuthLayout/index.tsx:19
#~ msgid "Does not exist"
#~ msgstr "N'existe pas"

#: src/components/routes/auth/Login/index.tsx:57
msgid "Don't have an account?   <0>Sign up</0>"
msgstr "Vous n'avez pas de compte ?   <0>Inscrivez-vous</0>"

#: src/components/routes/auth/Login/index.tsx:72
#~ msgid "Don't have an account?   <0>Sign Up</0>"
#~ msgstr "Vous n'avez pas de compte ?   <0>Inscrivez-vous</0>"

#: src/components/forms/ProductForm/index.tsx:157
msgid "Donation / Pay what you'd like product"
msgstr "Produit de don / Payez ce que vous voulez"

#: src/components/forms/TicketForm/index.tsx:139
#~ msgid "Donation / Pay what you'd like ticket"
#~ msgstr "Donation / Billet à tarif préférentiel"

#: src/components/common/AddEventToCalendarButton/index.tsx:137
msgid "Download .ics"
msgstr "Télécharger .ics"

#: src/components/common/DownloadCsvButton/index.tsx:48
msgid "Download CSV"
msgstr "Télécharger CSV"

#: src/components/common/OrdersTable/index.tsx:162
msgid "Download invoice"
msgstr "Télécharger la facture"

#: src/components/layouts/Checkout/index.tsx:126
msgid "Download Invoice"
msgstr "Télécharger la facture"

#: src/components/modals/ShareModal/index.tsx:224
msgid "Download QR Code"
msgstr "Télécharger le code QR"

#: src/components/common/OrdersTable/index.tsx:111
#: src/components/layouts/Checkout/index.tsx:49
msgid "Downloading Invoice"
msgstr "Téléchargement de la facture"

#: src/components/layouts/Event/index.tsx:188
msgid "Draft"
msgstr "Brouillon"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:83
msgid "Drag and drop or click"
msgstr "Glisser-déposer ou cliquer"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Drag to sort"
#~ msgstr "Faites glisser pour trier"

#: src/components/forms/QuestionForm/index.tsx:124
msgid "Dropdown selection"
msgstr "Sélection déroulante"

#: src/components/modals/SendMessageModal/index.tsx:159
msgid ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."
msgstr ""
"En raison du risque élevé de spam, une vérification manuelle est requise avant de pouvoir envoyer des messages.\n"
"Veuillez nous contacter pour demander l'accès."

#: src/components/modals/DuplicateEventModal/index.tsx:124
msgid "Duplicate Capacity Assignments"
msgstr "Dupliquer les attributions de capacité"

#: src/components/modals/DuplicateEventModal/index.tsx:128
msgid "Duplicate Check-In Lists"
msgstr "Dupliquer les listes d'enregistrement"

#: src/components/common/EventCard/index.tsx:162
msgid "Duplicate event"
msgstr "Dupliquer l'événement"

#: src/components/modals/DuplicateEventModal/index.tsx:69
#: src/components/modals/DuplicateEventModal/index.tsx:142
msgid "Duplicate Event"
msgstr "Dupliquer l'événement"

#: src/components/modals/DuplicateEventModal/index.tsx:132
msgid "Duplicate Event Cover Image"
msgstr "Dupliquer l'image de couverture de l'événement"

#: src/components/modals/DuplicateEventModal/index.tsx:103
msgid "Duplicate Options"
msgstr "Options de duplication"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:285
#: src/components/modals/DuplicateProductModal/index.tsx:109
#: src/components/modals/DuplicateProductModal/index.tsx:113
msgid "Duplicate Product"
msgstr "Dupliquer le produit"

#: src/components/modals/DuplicateEventModal/index.tsx:108
msgid "Duplicate Products"
msgstr "Dupliquer les produits"

#: src/components/modals/DuplicateEventModal/index.tsx:120
msgid "Duplicate Promo Codes"
msgstr "Dupliquer les codes promo"

#: src/components/modals/DuplicateEventModal/index.tsx:112
msgid "Duplicate Questions"
msgstr "Dupliquer les questions"

#: src/components/modals/DuplicateEventModal/index.tsx:116
msgid "Duplicate Settings"
msgstr "Dupliquer les paramètres"

#: src/components/modals/DuplicateEventModal/index.tsx:107
#~ msgid "Duplicate Tickets"
#~ msgstr "Dupliquer les billets"

#: src/components/modals/DuplicateEventModal/index.tsx:137
msgid "Duplicate Webhooks"
msgstr "Dupliquer les webhooks"

#: src/components/common/LanguageSwitcher/index.tsx:24
msgid "Dutch"
msgstr "Néerlandais"

#: src/components/forms/ProductForm/index.tsx:78
msgid "Early bird"
msgstr "Lève tôt"

#: src/components/common/TaxAndFeeList/index.tsx:65
#: src/components/modals/ManageAttendeeModal/index.tsx:221
#: src/components/modals/ManageOrderModal/index.tsx:203
msgid "Edit"
msgstr "Modifier"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
msgid "Edit {0}"
msgstr "Modifier {0}"

#: src/components/common/QuestionAndAnswerList/index.tsx:159
msgid "Edit Answer"
msgstr "Modifier la réponse"

#: src/components/common/AttendeeTable/index.tsx:174
#~ msgid "Edit attendee"
#~ msgstr "Modifier un participant"

#: src/components/modals/EditAttendeeModal/index.tsx:72
#: src/components/modals/EditAttendeeModal/index.tsx:110
#~ msgid "Edit Attendee"
#~ msgstr "Modifier le participant"

#: src/components/common/CapacityAssignmentList/index.tsx:146
msgid "Edit Capacity"
msgstr "Modifier la Capacité"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:66
#: src/components/modals/EditCapacityAssignmentModal/index.tsx:74
msgid "Edit Capacity Assignment"
msgstr "Modifier l'Affectation de Capacité"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:137
#: src/components/common/ProductsTable/SortableCategory/index.tsx:138
msgid "Edit category"
msgstr "Modifier la catégorie"

#: src/components/common/CheckInListList/index.tsx:148
#: src/components/modals/EditCheckInListModal/index.tsx:69
#: src/components/modals/EditCheckInListModal/index.tsx:91
msgid "Edit Check-In List"
msgstr "Modifier la liste de pointage"

#: src/components/common/PromoCodeTable/index.tsx:170
msgid "Edit Code"
msgstr "Modifier le code"

#: src/components/modals/EditOrganizerModal/index.tsx:63
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:51
msgid "Edit Organizer"
msgstr "Modifier l'organisateur"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:280
#: src/components/modals/EditProductModal/index.tsx:100
#: src/components/modals/EditProductModal/index.tsx:108
msgid "Edit Product"
msgstr "Modifier le produit"

#: src/components/modals/EditProductCategoryModal/index.tsx:62
#: src/components/modals/EditProductCategoryModal/index.tsx:70
msgid "Edit Product Category"
msgstr "Modifier la catégorie de produit"

#: src/components/modals/EditPromoCodeModal/index.tsx:79
#: src/components/modals/EditPromoCodeModal/index.tsx:84
msgid "Edit Promo Code"
msgstr "Modifier le code promotionnel"

#: src/components/common/QuestionsTable/index.tsx:112
msgid "Edit question"
msgstr "Modifier la question"

#: src/components/modals/EditQuestionModal/index.tsx:95
#: src/components/modals/EditQuestionModal/index.tsx:101
msgid "Edit Question"
msgstr "Modifier la question"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:191
#: src/components/modals/EditTicketModal/index.tsx:96
#: src/components/modals/EditTicketModal/index.tsx:104
#~ msgid "Edit Ticket"
#~ msgstr "Modifier le billet"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Edit user"
msgstr "Modifier l'utilisateur"

#: src/components/modals/EditUserModal/index.tsx:62
#: src/components/modals/EditUserModal/index.tsx:120
msgid "Edit User"
msgstr "Modifier l'utilisateur"

#: src/components/common/WebhookTable/index.tsx:104
msgid "Edit webhook"
msgstr "Modifier le webhook"

#: src/components/modals/EditWebhookModal/index.tsx:66
#: src/components/modals/EditWebhookModal/index.tsx:87
msgid "Edit Webhook"
msgstr "Modifier le webhook"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 2.50 for $2.50"
msgstr "par exemple. 2,50 pour 2,50$"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 23.5 for 23.5%"
msgstr "par exemple. 23,5 pour 23,5%"

#: src/components/common/AttendeeDetails/index.tsx:21
#: src/components/common/AttendeeTable/index.tsx:109
#: src/components/common/OrderDetails/index.tsx:31
#: src/components/forms/OrganizerForm/index.tsx:32
#: src/components/modals/EditUserModal/index.tsx:80
#: src/components/modals/InviteUserModal/index.tsx:61
#: src/components/routes/auth/AcceptInvitation/index.tsx:98
#: src/components/routes/auth/Login/index.tsx:68
#: src/components/routes/auth/Register/index.tsx:97
#: src/components/routes/event/Settings/index.tsx:52
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:117
#: src/components/routes/profile/ManageProfile/index.tsx:138
msgid "Email"
msgstr "E-mail"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
msgid "Email & Notification Settings"
msgstr "Paramètres de courrier électronique et de notification"

#: src/components/modals/CreateAttendeeModal/index.tsx:132
#: src/components/modals/ManageAttendeeModal/index.tsx:114
#: src/components/modals/ManageOrderModal/index.tsx:157
msgid "Email address"
msgstr "Adresse e-mail"

#: src/components/common/QuestionsTable/index.tsx:220
#: src/components/common/QuestionsTable/index.tsx:221
#: src/components/routes/product-widget/CollectInformation/index.tsx:298
#: src/components/routes/product-widget/CollectInformation/index.tsx:299
#: src/components/routes/product-widget/CollectInformation/index.tsx:408
#: src/components/routes/product-widget/CollectInformation/index.tsx:409
msgid "Email Address"
msgstr "Adresse e-mail"

#: src/components/routes/profile/ManageProfile/index.tsx:77
msgid "Email change cancelled successfully"
msgstr "Changement d'e-mail annulé avec succès"

#: src/components/routes/profile/ManageProfile/index.tsx:116
msgid "Email change pending"
msgstr "Changement d'e-mail en attente"

#: src/components/routes/profile/ManageProfile/index.tsx:151
msgid "Email confirmation resent"
msgstr "E-mail de confirmation renvoyé"

#: src/components/routes/profile/ManageProfile/index.tsx:90
msgid "Email confirmation resent successfully"
msgstr "E-mail de confirmation renvoyé avec succès"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:66
msgid "Email footer message"
msgstr "Message de pied de page de l'e-mail"

#: src/components/routes/profile/ManageProfile/index.tsx:141
msgid "Email not verified"
msgstr "E-mail non vérifié"

#: src/components/common/WidgetEditor/index.tsx:272
msgid "Embed Code"
msgstr "Code intégré"

#: src/components/common/WidgetEditor/index.tsx:260
msgid "Embed Script"
msgstr "Intégrer le script"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:181
msgid "Enable Invoicing"
msgstr "Activer la facturation"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:21
msgid "Enable this capacity to stop product sales when the limit is reached"
msgstr "Activer cette capacité pour arrêter les ventes de produits lorsque la limite est atteinte"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:20
#~ msgid "Enable this capacity to stop ticket sales when the limit is reached"
#~ msgstr "Activez cette capacité pour arrêter la vente de billets lorsque la limite est atteinte"

#: src/components/forms/WebhookForm/index.tsx:19
msgid "Enabled"
msgstr "Activé"

#: src/components/modals/CreateEventModal/index.tsx:149
#: src/components/modals/DuplicateEventModal/index.tsx:98
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:92
#: src/components/routes/welcome/index.tsx:96
msgid "End Date"
msgstr "Date de fin"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:26
#: src/components/common/EventStatusBadge/index.tsx:14
msgid "Ended"
msgstr "Terminé"

#: src/components/routes/events/Dashboard/index.tsx:57
msgid "Ended Events"
msgstr "Événements terminés"

#: src/components/common/LanguageSwitcher/index.tsx:16
#: src/components/common/LanguageSwitcher/index.tsx:50
#: src/components/modals/CreateAttendeeModal/index.tsx:145
#: src/components/routes/profile/ManageProfile/index.tsx:173
msgid "English"
msgstr "Anglais"

#: src/components/modals/CreateAttendeeModal/index.tsx:170
msgid "Enter an amount excluding taxes and fees."
msgstr "Saisissez un montant hors taxes et frais."

#: src/components/common/OrdersTable/index.tsx:119
#: src/components/layouts/Checkout/index.tsx:57
msgid "Error"
msgstr "Erreur"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:27
msgid "Error confirming email address"
msgstr "Erreur lors de la confirmation de l'adresse e-mail"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:35
msgid "Error confirming email change"
msgstr "Erreur lors de la confirmation du changement d'adresse e-mail"

#: src/components/modals/WebhookLogsModal/index.tsx:166
msgid "Error loading logs"
msgstr "Erreur lors du chargement des journaux"

#: src/components/forms/OrganizerForm/index.tsx:46
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:74
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:101
msgid "EUR"
msgstr "EUR"

#: src/components/routes/events/Dashboard/index.tsx:103
msgid "Event"
msgstr "Événement"

#: src/components/modals/CreateEventModal/index.tsx:76
#~ msgid "Event created successfully 🎉"
#~ msgstr "Événement créé avec succès 🎉"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:161
msgid "Event Date"
msgstr "Date de l'événement"

#: src/components/routes/account/ManageAccount/sections/EventDefaultsSettings/index.tsx:5
msgid "Event Defaults"
msgstr "Valeurs par défaut des événements"

#: src/components/routes/event/Settings/index.tsx:28
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:276
msgid "Event Details"
msgstr "Détails de l'événement"

#: src/components/modals/DuplicateEventModal/index.tsx:58
msgid "Event duplicated successfully"
msgstr "Événement dupliqué avec succès"

#: src/components/layouts/Checkout/index.tsx:79
msgid "Event Homepage"
msgstr "Page d'accueil de l'événement"

#: src/components/layouts/Event/index.tsx:166
#~ msgid "Event is not visible to the public"
#~ msgstr "L'événement n'est pas visible au public"

#: src/components/layouts/Event/index.tsx:165
#~ msgid "Event is visible to the public"
#~ msgstr "L'événement est visible au public"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:85
msgid "Event location & venue details"
msgstr "Lieu de l'événement et détails du lieu"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:12
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
msgid "Event Not Available"
msgstr "Événement non disponible"

#: src/components/layouts/Event/index.tsx:187
#~ msgid "Event page"
#~ msgstr "Page de l'événement"

#: src/components/layouts/Event/index.tsx:212
msgid "Event Page"
msgstr "Page de l'événement"

#: src/components/common/EventCard/index.tsx:70
#: src/components/layouts/Event/index.tsx:80
#: src/components/routes/event/EventDashboard/index.tsx:76
#: src/components/routes/event/GettingStarted/index.tsx:63
msgid "Event status update failed. Please try again later"
msgstr "La mise à jour du statut de l'événement a échoué. Veuillez réessayer plus tard"

#: src/components/common/EventCard/index.tsx:67
#: src/components/layouts/Event/index.tsx:77
#: src/components/routes/event/EventDashboard/index.tsx:73
#: src/components/routes/event/GettingStarted/index.tsx:60
msgid "Event status updated"
msgstr "Statut de l'événement mis à jour"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Event Ticketing by"
msgstr "Billetterie par"

#: src/components/common/WebhookTable/index.tsx:237
#: src/components/forms/WebhookForm/index.tsx:122
msgid "Event Types"
msgstr "Types d'événements"

#: src/components/modals/ShareModal/index.tsx:237
msgid "Event URL"
msgstr "URL de l'événement"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
#~ msgid "Event wdwdeNot Available"
#~ msgstr ""

#: src/components/layouts/Event/index.tsx:226
msgid "Events"
msgstr "Événements"

#: src/components/forms/CheckInListForm/index.tsx:56
msgid "Expiration date"
msgstr "Date d'expiration"

#: src/components/common/PromoCodeTable/index.tsx:73
msgid "Expires"
msgstr "Expire"

#: src/components/forms/PromoCodeForm/index.tsx:79
msgid "Expiry Date"
msgstr "Date d'expiration"

#: src/components/routes/event/attendees.tsx:80
#: src/components/routes/event/orders.tsx:140
msgid "Export"
msgstr "Exporter"

#: src/components/common/QuestionsTable/index.tsx:267
msgid "Export answers"
msgstr "Exporter les réponses"

#: src/mutations/useExportAnswers.ts:39
msgid "Export failed. Please try again."
msgstr "Échec de l'exportation. Veuillez réessayer."

#: src/mutations/useExportAnswers.ts:17
msgid "Export started. Preparing file..."
msgstr "Exportation commencée. Préparation du fichier..."

#: src/components/routes/event/attendees.tsx:39
msgid "Exporting Attendees"
msgstr "Exportation des participants"

#: src/mutations/useExportAnswers.ts:33
msgid "Exporting complete. Downloading file..."
msgstr "Exportation terminée. Téléchargement du fichier..."

#: src/components/routes/event/orders.tsx:90
msgid "Exporting Orders"
msgstr "Exportation des commandes"

#: src/components/common/AttendeeTable/index.tsx:97
msgid "Failed to cancel attendee"
msgstr "Échec de l'annulation du participant"

#: src/components/modals/CancelOrderModal/index.tsx:33
msgid "Failed to cancel order"
msgstr "Échec de l'annulation de la commande"

#: src/components/common/QuestionsTable/index.tsx:175
msgid "Failed to delete message. Please try again."
msgstr "Échec de la suppression du message. Veuillez réessayer."

#: src/components/common/OrdersTable/index.tsx:120
#: src/components/layouts/Checkout/index.tsx:58
msgid "Failed to download invoice. Please try again."
msgstr "Échec du téléchargement de la facture. Veuillez réessayer."

#: src/components/routes/event/attendees.tsx:48
msgid "Failed to export attendees"
msgstr "Échec de l'exportation des participants"

#: src/components/routes/event/attendees.tsx:39
#~ msgid "Failed to export attendees. Please try again."
#~ msgstr "Échec de l'exportation des participants. Veuillez réessayer."

#: src/components/routes/event/orders.tsx:99
msgid "Failed to export orders"
msgstr "Échec de l'exportation des commandes"

#: src/components/routes/event/orders.tsx:88
#~ msgid "Failed to export orders. Please try again."
#~ msgstr "Échec de l'exportation des commandes. Veuillez réessayer."

#: src/components/modals/EditCheckInListModal/index.tsx:79
msgid "Failed to load Check-In List"
msgstr "Échec du chargement de la liste de pointage"

#: src/components/modals/EditWebhookModal/index.tsx:75
msgid "Failed to load Webhook"
msgstr "Échec du chargement du webhook"

#: src/components/common/AttendeeTable/index.tsx:51
#~ msgid "Failed to resend product email"
#~ msgstr "Échec de l'envoi de l'e-mail produit"

#: src/components/common/AttendeeTable/index.tsx:50
msgid "Failed to resend ticket email"
msgstr "Échec du renvoi de l'e-mail du ticket"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:179
msgid "Failed to sort products"
msgstr "Échec du tri des produits"

#: src/mutations/useExportAnswers.ts:15
msgid "Failed to start export job"
msgstr "Échec du démarrage de l'exportation"

#: src/components/common/QuestionAndAnswerList/index.tsx:102
msgid "Failed to update answer."
msgstr "Échec de la mise à jour de la réponse."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:64
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:69
msgid "Failed to upload image."
msgstr "Échec du téléversement de l’image."

#: src/components/forms/TaxAndFeeForm/index.tsx:18
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Fee"
msgstr "Frais"

#: src/components/common/GlobalMenu/index.tsx:30
#~ msgid "Feedback"
#~ msgstr "Retour"

#: src/components/forms/ProductForm/index.tsx:366
msgid "Fees"
msgstr "Frais"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:80
msgid "Fees are subject to change. You will be notified of any changes to your fee structure."
msgstr "Les frais sont susceptibles de changer. Vous serez informé de toute modification de votre structure tarifaire."

#: src/components/routes/event/orders.tsx:121
msgid "Filter Orders"
msgstr "Filtrer les commandes"

#: src/components/common/FilterModal/index.tsx:75
#: src/components/common/FilterModal/index.tsx:223
msgid "Filters"
msgstr "Filtres"

#: src/components/common/FilterModal/index.tsx:223
msgid "Filters ({activeFilterCount})"
msgstr "Filtres ({activeFilterCount})"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:206
msgid "First Invoice Number"
msgstr "Premier numéro de facture"

#: src/components/common/QuestionsTable/index.tsx:208
#: src/components/modals/CreateAttendeeModal/index.tsx:118
#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:144
#: src/components/routes/product-widget/CollectInformation/index.tsx:284
#: src/components/routes/product-widget/CollectInformation/index.tsx:395
msgid "First name"
msgstr "Prénom"

#: src/components/common/QuestionsTable/index.tsx:207
#: src/components/modals/EditUserModal/index.tsx:71
#: src/components/modals/InviteUserModal/index.tsx:57
#: src/components/routes/auth/AcceptInvitation/index.tsx:92
#: src/components/routes/auth/Register/index.tsx:83
#: src/components/routes/product-widget/CollectInformation/index.tsx:283
#: src/components/routes/product-widget/CollectInformation/index.tsx:394
#: src/components/routes/profile/ManageProfile/index.tsx:135
msgid "First Name"
msgstr "Prénom"

#: src/components/routes/auth/AcceptInvitation/index.tsx:29
msgid "First name must be between 1 and 50 characters"
msgstr "Le prénom doit comporter entre 1 et 50 caractères"

#: src/components/common/QuestionsTable/index.tsx:349
msgid "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."
msgstr "Le prénom, le nom et l'adresse e-mail sont des questions par défaut et sont toujours inclus dans le processus de paiement."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
msgid "First Used"
msgstr "Première utilisation"

#: src/components/forms/TaxAndFeeForm/index.tsx:33
msgid "Fixed"
msgstr "Fixé"

#: src/components/forms/PromoCodeForm/index.tsx:55
msgid "Fixed amount"
msgstr "Montant fixé"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:65
msgid "Fixed Fee:"
msgstr "Frais fixes :"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:123
msgid "Flash is not available on this device"
msgstr "Flash n'est pas disponible sur cet appareil"

#: src/components/layouts/AuthLayout/index.tsx:58
msgid "Flexible Ticketing"
msgstr "Billetterie flexible"

#: src/components/routes/auth/Login/index.tsx:80
msgid "Forgot password?"
msgstr "Mot de passe oublié?"

#: src/components/common/AttendeeTicket/index.tsx:40
#: src/components/common/Currency/index.tsx:90
#: src/components/common/OrderSummary/index.tsx:46
#: src/components/common/OrderSummary/index.tsx:99
#: src/components/common/ProductsTable/SortableProduct/index.tsx:93
#: src/components/common/ProductsTable/SortableProduct/index.tsx:103
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:53
msgid "Free"
msgstr "Gratuit"

#: src/components/forms/ProductForm/index.tsx:151
msgid "Free Product"
msgstr "Produit gratuit"

#: src/components/forms/ProductForm/index.tsx:153
msgid "Free product, no payment information required"
msgstr "Produit gratuit, aucune information de paiement requise"

#: src/components/forms/TicketForm/index.tsx:133
#~ msgid "Free Ticket"
#~ msgstr "Billet gratuit"

#: src/components/forms/TicketForm/index.tsx:135
#~ msgid "Free ticket, no payment information required"
#~ msgstr "Billet gratuit, aucune information de paiement requise"

#: src/components/common/LanguageSwitcher/index.tsx:20
msgid "French"
msgstr "Français"

#: src/components/layouts/AuthLayout/index.tsx:88
msgid "Fully Integrated"
msgstr "Entièrement intégré"

#: src/components/forms/ProductForm/index.tsx:136
msgid "General"
msgstr "Général"

#: src/components/common/LanguageSwitcher/index.tsx:14
msgid "German"
msgstr "Allemand"

#: src/components/layouts/AuthLayout/index.tsx:35
msgid "Get started for free, no subscription fees"
msgstr "Commencez gratuitement, sans frais d'abonnement"

#: src/components/routes/event/EventDashboard/index.tsx:125
msgid "Get your event ready"
msgstr "Préparez votre événement"

#: src/components/layouts/Event/index.tsx:88
msgid "Getting Started"
msgstr "Commencer"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:23
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:42
msgid "Go back to profile"
msgstr "Revenir au profil"

#: src/components/routes/product-widget/CollectInformation/index.tsx:239
msgid "Go to event homepage"
msgstr "Aller à la page d'accueil de l'événement"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:53
msgid "Go to Hi.Events"
msgstr "Aller sur Hi.Events"

#: src/components/common/ErrorDisplay/index.tsx:64
msgid "Go to home page"
msgstr "Aller à la page d'accueil"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:162
msgid "Go to Stripe Dashboard"
msgstr "Aller au tableau de bord Stripe"

#: src/components/common/AddEventToCalendarButton/index.tsx:128
msgid "Google Calendar"
msgstr "Google Agenda"

#: src/components/common/EventCard/index.tsx:133
msgid "gross sales"
msgstr "ventes brutes"

#: src/components/common/StatBoxes/index.tsx:39
msgid "Gross sales"
msgstr "Ventes brutes"

#: src/components/routes/event/EventDashboard/index.tsx:274
msgid "Gross Sales"
msgstr "Ventes brutes"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:281
msgid "Guests"
msgstr "Invités"

#: src/components/routes/product-widget/SelectProducts/index.tsx:495
msgid "Have a promo code?"
msgstr "Avez vous un code de réduction?"

#: src/components/forms/OrganizerForm/index.tsx:33
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/GlobalMenu/index.tsx:24
#~ msgid "Help & Support"
#~ msgstr "Aide et Support"

#: src/components/common/WidgetEditor/index.tsx:295
msgid "Here is an example of how you can use the component in your application."
msgstr "Voici un exemple de la façon dont vous pouvez utiliser le composant dans votre application."

#: src/components/common/WidgetEditor/index.tsx:284
msgid "Here is the React component you can use to embed the widget in your application."
msgstr "Voici le composant React que vous pouvez utiliser pour intégrer le widget dans votre application."

#: src/components/routes/event/EventDashboard/index.tsx:101
msgid "Hi {0} 👋"
msgstr "Salut {0} 👋"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:43
msgid "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."
msgstr "Hi.Events facture des frais de plateforme pour maintenir et améliorer nos services. Ces frais sont automatiquement déduits de chaque transaction."

#: src/components/modals/CreateEventModal/index.tsx:130
#: src/components/modals/DuplicateEventModal/index.tsx:79
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:73
msgid "Hi.Events Conference {0}"
msgstr "Conférence Hi.Events {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:117
msgid "Hi.Events Conference Center"
msgstr "Centre de conférence Hi.Events"

#: src/components/layouts/AuthLayout/index.tsx:131
msgid "hi.events logo"
msgstr "logo hi.events"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:79
msgid "Hidden from public view"
msgstr "Caché à la vue du public"

#: src/components/common/QuestionsTable/index.tsx:290
msgid "hidden question"
msgstr "question cachée"

#: src/components/common/QuestionsTable/index.tsx:291
msgid "hidden questions"
msgstr "questions cachées"

#: src/components/forms/QuestionForm/index.tsx:218
msgid "Hidden questions are only visible to the event organizer and not to the customer."
msgstr "Les questions masquées ne sont visibles que par l'organisateur de l'événement et non par le client."

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/routes/product-widget/SelectProducts/index.tsx:460
msgid "Hide"
msgstr "Cacher"

#: src/components/common/AttendeeList/index.tsx:84
msgid "Hide Answers"
msgstr "Masquer les réponses"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:89
msgid "Hide getting started page"
msgstr "Masquer la page de démarrage"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Hide hidden questions"
msgstr "Masquer les questions cachées"

#: src/components/forms/ProductForm/index.tsx:399
msgid "Hide product after sale end date"
msgstr "Masquer le produit après la date de fin de vente"

#: src/components/forms/ProductForm/index.tsx:397
msgid "Hide product before sale start date"
msgstr "Masquer le produit avant la date de début de vente"

#: src/components/forms/ProductForm/index.tsx:412
msgid "Hide product unless user has applicable promo code"
msgstr "Masquer le produit sauf si l'utilisateur a un code promo applicable"

#: src/components/forms/ProductForm/index.tsx:405
msgid "Hide product when sold out"
msgstr "Masquer le produit lorsqu'il est épuisé"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:90
msgid "Hide the getting started page from the sidebar"
msgstr "Masquer la page de démarrage de la barre latérale"

#: src/components/forms/ProductForm/index.tsx:417
msgid "Hide this product from customers"
msgstr "Masquer ce produit des clients"

#: src/components/forms/QuestionForm/index.tsx:219
msgid "Hide this question"
msgstr "Cacher cette question"

#: src/components/forms/TicketForm/index.tsx:362
#~ msgid "Hide this ticket from customers"
#~ msgstr "Masquer ce ticket aux clients"

#: src/components/forms/ProductForm/index.tsx:103
msgid "Hide this tier from users"
msgstr "Masquer ce niveau aux utilisateurs"

#: src/components/forms/TicketForm/index.tsx:344
#~ msgid "Hide ticket after sale end date"
#~ msgstr "Masquer le billet après la date de fin de vente"

#: src/components/forms/TicketForm/index.tsx:342
#~ msgid "Hide ticket before sale start date"
#~ msgstr "Masquer le billet avant la date de début de la vente"

#: src/components/forms/TicketForm/index.tsx:357
#~ msgid "Hide ticket unless user has applicable promo code"
#~ msgstr "Masquer le billet sauf si l'utilisateur dispose du code promotionnel applicable"

#: src/components/forms/TicketForm/index.tsx:350
#~ msgid "Hide ticket when sold out"
#~ msgstr "Masquer le billet lorsqu'il est épuisé"

#: src/components/forms/ProductForm/index.tsx:101
msgid "Hiding a product will prevent users from seeing it on the event page."
msgstr "Masquer un produit empêchera les utilisateurs de le voir sur la page de l'événement."

#: src/components/forms/TicketForm/index.tsx:98
#~ msgid "Hiding a ticket will prevent users from seeing it on the event page."
#~ msgstr "Masquer un ticket empêchera les utilisateurs de le voir sur la page de l’événement."

#: src/components/routes/event/HomepageDesigner/index.tsx:115
msgid "Homepage Design"
msgstr "Conception de la page d'accueil"

#: src/components/layouts/Event/index.tsx:109
msgid "Homepage Designer"
msgstr "Concepteur de page d'accueil"

#: src/components/routes/event/HomepageDesigner/index.tsx:174
msgid "Homepage Preview"
msgstr "Aperçu de la page d'accueil"

#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:145
msgid "Homer"
msgstr "Homère"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
msgid "How many minutes the customer has to complete their order. We recommend at least 15 minutes"
msgstr "De combien de minutes le client dispose pour finaliser sa commande. Nous recommandons au moins 15 minutes"

#: src/components/forms/PromoCodeForm/index.tsx:84
msgid "How many times can this code be used?"
msgstr "Combien de fois ce code peut-il être utilisé ?"

#: src/components/common/Editor/index.tsx:75
msgid "HTML character limit exceeded: {htmlLength}/{maxLength}"
msgstr "Limite de caractères HTML dépassé: {htmlLength}/{maxLength}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:160
msgid "https://example-maps-service.com/..."
msgstr "https://exemple-maps-service.com/..."

#: src/components/forms/WebhookForm/index.tsx:118
msgid "https://webhook-domain.com/webhook"
msgstr "https://webhook-domain.com/webhook"

#: src/components/routes/auth/AcceptInvitation/index.tsx:118
msgid "I agree to the <0>terms and conditions</0>"
msgstr "J'accepte les <0>termes et conditions</0>"

#: src/components/routes/product-widget/Payment/index.tsx:106
msgid "I would like to pay using an offline method"
msgstr "Je souhaite payer en utilisant une méthode hors ligne"

#: src/components/routes/product-widget/Payment/index.tsx:107
msgid "I would like to pay using an online method (credit card etc.)"
msgstr "Je souhaite payer en utilisant une méthode en ligne (carte de crédit, etc.)"

#: src/components/routes/product-widget/SelectProducts/index.tsx:315
msgid "If a new tab did not open automatically, please click the button below to continue to checkout."
msgstr "Si un nouvel onglet ne s'est pas ouvert automatiquement, veuillez cliquer sur le bouton ci-dessous pour poursuivre le paiement."

#: src/components/routes/product-widget/SelectProducts/index.tsx:284
#~ msgid "If a new tab did not open, please  <0><1>{0}</1>.</0>"
#~ msgstr "Si aucun nouvel onglet ne s'ouvre, veuillez <0><1>{0}</1>.</0>"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:149
#~ msgid "If blank, the address will be used to generate a Google map link"
#~ msgstr "Si vide, l'adresse sera utilisée pour générer un lien Google map"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:158
msgid "If blank, the address will be used to generate a Google Mapa link"
msgstr "Si vide, l'adresse sera utilisée pour générer un lien Google Maps"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:158
msgid "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."
msgstr "Si activé, le personnel d'enregistrement peut marquer les participants comme enregistrés ou marquer la commande comme payée et enregistrer les participants. Si désactivé, les participants associés à des commandes impayées ne peuvent pas être enregistrés."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:76
msgid "If enabled, the organizer will receive an email notification when a new order is placed"
msgstr "Si activé, l'organisateur recevra une notification par e-mail lorsqu'une nouvelle commande sera passée"

#: src/components/routes/profile/ManageProfile/index.tsx:122
msgid "If you did not request this change, please immediately change your password."
msgstr "Si vous n'avez pas demandé ce changement, veuillez immédiatement modifier votre mot de passe."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
msgid ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."
msgstr "Si vous avez un compte chez nous, vous recevrez un e-mail contenant les instructions pour réinitialiser votre mot de passe."

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:108
msgid "Image deleted successfully"
msgstr "Image supprimée avec succès"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
#~ msgid "Image dimensions must be between 3000px by 2000px. With a max height of 2000px and max width of 3000px"
#~ msgstr "Les dimensions de l'image doivent être comprises entre 3 000 et 2 000 px. Avec une hauteur maximale de 2 000 px et une largeur maximale de 3 000 px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
msgid "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"
msgstr "Les dimensions de l'image doivent être comprises entre 4000px par 4000px. Avec une hauteur maximale de 4000px et une largeur maximale de 4000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:49
msgid "Image must be less than 5MB"
msgstr "L'image doit faire moins de 5 Mo"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:63
msgid "Image uploaded successfully"
msgstr "Image téléchargée avec succès"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:111
msgid "Image URL"
msgstr "URL de l'image"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
msgid "Image width must be at least 900px and height at least 50px"
msgstr "La largeur de l'image doit être d'au moins 900 px et la hauteur d'au moins 50 px."

#: src/components/layouts/AuthLayout/index.tsx:53
msgid "In-depth Analytics"
msgstr "Analyses approfondies"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:25
#: src/components/modals/EditUserModal/index.tsx:111
msgid "Inactive"
msgstr "Inactif"

#: src/components/modals/EditUserModal/index.tsx:107
msgid "Inactive users cannot log in."
msgstr "Les utilisateurs inactifs ne peuvent pas se connecter."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee product page"
#~ msgstr "Inclure les détails de connexion pour votre événement en ligne. Ces détails seront affichés sur la page de récapitulatif de commande et la page du produit de l'invité"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page"
#~ msgstr "Incluez les détails de connexion pour votre événement en ligne. Ces détails seront affichés sur la page récapitulative de la commande et sur la page des billets des participants."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:102
msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."
msgstr "Incluez les détails de connexion pour votre événement en ligne. Ces détails seront affichés sur la page récapitulative de la commande et sur le billet du participant."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:54
msgid "Include tax and fees in the price"
msgstr "Inclure les taxes et les frais dans le prix"

#: src/components/common/CheckInListList/index.tsx:103
msgid "Includes {0} products"
msgstr "Comprend {0} produits"

#: src/components/common/CheckInListList/index.tsx:112
#~ msgid "Includes {0} tickets"
#~ msgstr "Inclut {0} billets"

#: src/components/common/CheckInListList/index.tsx:104
msgid "Includes 1 product"
msgstr "Comprend 1 produit"

#: src/components/common/CheckInListList/index.tsx:113
#~ msgid "Includes 1 ticket"
#~ msgstr "Inclut 1 billet"

#: src/components/common/MessageList/index.tsx:20
msgid "Individual attendees"
msgstr "Participants individuels"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:100
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:121
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:137
msgid "Insert Image"
msgstr "Insérer une image"

#: src/components/layouts/Event/index.tsx:54
#~ msgid "Integrations"
#~ msgstr "Intégrations"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation resent!"
msgstr "Invitation renvoyée !"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation revoked!"
msgstr "Invitation révoquée !"

#: src/components/modals/InviteUserModal/index.tsx:54
#: src/components/modals/InviteUserModal/index.tsx:74
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invite User"
msgstr "Inviter un utilisateur"

#: src/components/common/OrdersTable/index.tsx:116
#: src/components/layouts/Checkout/index.tsx:54
msgid "Invoice downloaded successfully"
msgstr "Facture téléchargée avec succès"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:252
msgid "Invoice Notes"
msgstr "Notes de facture"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:197
msgid "Invoice Numbering"
msgstr "Numérotation des factures"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:177
msgid "Invoice Settings"
msgstr "Paramètres de facturation"

#: src/components/modals/RefundOrderModal/index.tsx:115
msgid "Issue refund"
msgstr "Émettre un remboursement"

#: src/components/common/LanguageSwitcher/index.tsx:22
msgid "Italian"
msgstr "Italien"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Item"
msgstr "Article"

#: src/components/routes/auth/Register/index.tsx:84
msgid "John"
msgstr "John"

#: src/components/modals/CreateAttendeeModal/index.tsx:126
msgid "Johnson"
msgstr "Johnson"

#: src/components/forms/ProductForm/index.tsx:77
msgid "Label"
msgstr "Étiquette"

#: src/components/common/AttendeeDetails/index.tsx:45
#: src/components/modals/CreateAttendeeModal/index.tsx:144
#: src/components/routes/profile/ManageProfile/index.tsx:172
msgid "Language"
msgstr "Langue"

#: src/components/common/ReportTable/index.tsx:52
msgid "Last 12 months"
msgstr "12 derniers mois"

#: src/components/common/ReportTable/index.tsx:47
msgid "Last 14 days"
msgstr "14 derniers jours"

#: src/components/common/ReportTable/index.tsx:44
msgid "Last 24 hours"
msgstr "Dernières 24 heures"

#: src/components/common/ReportTable/index.tsx:48
msgid "Last 30 days"
msgstr "30 derniers jours"

#: src/components/common/ReportTable/index.tsx:45
msgid "Last 48 hours"
msgstr "Dernières 48 heures"

#: src/components/common/ReportTable/index.tsx:50
msgid "Last 6 months"
msgstr "6 derniers mois"

#: src/components/common/ReportTable/index.tsx:46
msgid "Last 7 days"
msgstr "7 derniers jours"

#: src/components/common/ReportTable/index.tsx:49
msgid "Last 90 days"
msgstr "90 derniers jours"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Last login"
msgstr "Dernière connexion"

#: src/components/modals/CreateAttendeeModal/index.tsx:125
#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:150
msgid "Last name"
msgstr "Nom de famille"

#: src/components/common/QuestionsTable/index.tsx:212
#: src/components/common/QuestionsTable/index.tsx:213
#: src/components/modals/EditUserModal/index.tsx:72
#: src/components/modals/InviteUserModal/index.tsx:58
#: src/components/routes/auth/AcceptInvitation/index.tsx:94
#: src/components/routes/auth/Register/index.tsx:89
#: src/components/routes/product-widget/CollectInformation/index.tsx:289
#: src/components/routes/product-widget/CollectInformation/index.tsx:290
#: src/components/routes/product-widget/CollectInformation/index.tsx:400
#: src/components/routes/product-widget/CollectInformation/index.tsx:401
#: src/components/routes/profile/ManageProfile/index.tsx:137
msgid "Last Name"
msgstr "Nom de famille"

#: src/components/common/WebhookTable/index.tsx:239
msgid "Last Response"
msgstr "Dernière réponse"

#: src/components/common/WebhookTable/index.tsx:240
msgid "Last Triggered"
msgstr "Dernier déclenchement"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
msgid "Last Used"
msgstr "Dernière utilisation"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:56
#~ msgid "Learn more about Stripe"
#~ msgstr "En savoir plus sur Stripe"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:191
msgid "Leave blank to use the default word \"Invoice\""
msgstr "Laisser vide pour utiliser le mot par défaut \"Facture\""

#: src/components/routes/welcome/index.tsx:20
msgid "Let's get started by creating your first organizer"
msgstr "Commençons par créer votre premier organisateur"

#: src/components/routes/event/EventDashboard/index.tsx:194
msgid "Link your Stripe account to receive funds from ticket sales."
msgstr "Liez votre compte Stripe pour recevoir les fonds provenant des ventes de billets."

#: src/components/layouts/Event/index.tsx:190
msgid "Live"
msgstr "En ligne"

#: src/components/routes/event/Webhooks/index.tsx:21
msgid "Loading Webhooks"
msgstr "Chargement des webhooks"

#: src/components/common/ReportTable/index.tsx:210
msgid "Loading..."
msgstr "Chargement..."

#: src/components/routes/event/Settings/index.tsx:34
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:84
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:167
msgid "Location"
msgstr "Emplacement"

#: src/components/routes/auth/Login/index.tsx:84
#: src/components/routes/auth/Register/index.tsx:71
msgid "Log in"
msgstr "Se connecter"

#: src/components/routes/auth/Login/index.tsx:84
msgid "Logging in"
msgstr "Se connecter"

#: src/components/routes/auth/Register/index.tsx:70
#~ msgid "Login"
#~ msgstr "Se connecter"

#: src/components/common/GlobalMenu/index.tsx:47
msgid "Logout"
msgstr "Se déconnecter"

#: src/components/common/WidgetEditor/index.tsx:331
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nom placerat elementum..."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:170
msgid "Make billing address mandatory during checkout"
msgstr "Rendre l'adresse de facturation obligatoire lors du paiement"

#: src/components/routes/event/EventDashboard/index.tsx:172
#~ msgid "Make Event Live"
#~ msgstr "Publier l'événement"

#: src/components/forms/QuestionForm/index.tsx:212
msgid "Make this question mandatory"
msgstr "Rendre cette question obligatoire"

#: src/components/routes/event/EventDashboard/index.tsx:148
msgid "Make your event live"
msgstr "Rendre votre événement public"

#: src/components/common/CapacityAssignmentList/index.tsx:143
#: src/components/common/CheckInListList/index.tsx:145
#: src/components/common/EventCard/index.tsx:180
#: src/components/common/OrdersTable/index.tsx:139
#: src/components/common/OrdersTable/index.tsx:154
#: src/components/common/ProductsTable/SortableProduct/index.tsx:256
#: src/components/common/PromoCodeTable/index.tsx:167
#: src/components/layouts/Event/index.tsx:97
msgid "Manage"
msgstr "Gérer"

#: src/components/common/AttendeeTable/index.tsx:161
msgid "Manage attendee"
msgstr "Gérer le participant"

#: src/components/common/EventCard/index.tsx:150
msgid "Manage event"
msgstr "Gérer l'événement"

#: src/components/common/OrdersTable/index.tsx:156
msgid "Manage order"
msgstr "Gérer la commande"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:99
msgid "Manage payment and invoicing settings for this event."
msgstr "Gérer les paramètres de paiement et de facturation pour cet événement."

#: src/components/modals/CreateAttendeeModal/index.tsx:106
#~ msgid "Manage products"
#~ msgstr "Gérer les produits"

#: src/components/routes/profile/ManageProfile/index.tsx:101
msgid "Manage Profile"
msgstr "Gérer le profil"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
msgid "Manage taxes and fees which can be applied to your products"
msgstr "Gérer les taxes et les frais qui peuvent être appliqués à vos produits"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
#~ msgid "Manage taxes and fees which can be applied to your tickets"
#~ msgstr "Gérez les taxes et les frais qui peuvent être appliqués à vos billets"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
msgid "Manage tickets"
msgstr "Gérer les billets"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:54
msgid "Manage your account details and default settings"
msgstr "Gérez les détails de votre compte et les paramètres par défaut"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:241
msgid "Manage your payment processing and view platform fees"
msgstr "Gérez votre traitement des paiements et consultez les frais de plateforme"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:91
#~ msgid "Manage your Stripe payment details"
#~ msgstr "Gérez vos informations de paiement Stripe"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Manage your users and their permissions"
msgstr "Gérez vos utilisateurs et leurs autorisations"

#: src/components/forms/QuestionForm/index.tsx:211
msgid "Mandatory questions must be answered before the customer can checkout."
msgstr "Il faut répondre aux questions obligatoires avant que le client puisse passer à la caisse."

#: src/components/common/AttendeeTable/index.tsx:67
msgid "Manually add an Attendee"
msgstr "Ajouter manuellement un participant"

#: src/components/modals/CreateAttendeeModal/index.tsx:97
#: src/components/modals/CreateAttendeeModal/index.tsx:113
msgid "Manually Add Attendee"
msgstr "Ajouter manuellement un participant"

#: src/components/modals/CreateAttendeeModal/index.tsx:148
#~ msgid "Manually adding an attendee will adjust ticket quantity."
#~ msgstr "L’ajout manuel d’un participant ajustera la quantité de billets."

#: src/components/common/OrdersTable/index.tsx:167
msgid "Mark as paid"
msgstr "Marquer comme payé"

#: src/components/layouts/AuthLayout/index.tsx:83
msgid "Match Your Brand"
msgstr "Adaptez à votre marque"

#: src/components/forms/ProductForm/index.tsx:383
msgid "Maximum Per Order"
msgstr "Maximum par commande"

#: src/components/common/AttendeeTable/index.tsx:166
msgid "Message attendee"
msgstr "Message au participant"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:273
msgid "Message Attendees"
msgstr "Message aux participants"

#: src/components/modals/SendMessageModal/index.tsx:165
#~ msgid "Message attendees with specific products"
#~ msgstr "Envoyer un message aux invités avec des produits spécifiques"

#: src/components/modals/SendMessageModal/index.tsx:206
msgid "Message attendees with specific tickets"
msgstr "Envoyez des messages aux participants avec des billets spécifiques"

#: src/components/layouts/AuthLayout/index.tsx:74
msgid "Message attendees, manage orders, and handle refunds all in one place"
msgstr "Envoyez des messages aux participants, gérez les commandes et traitez les remboursements en un seul endroit"

#: src/components/common/OrdersTable/index.tsx:158
msgid "Message buyer"
msgstr "Message à l'acheteur"

#: src/components/modals/SendMessageModal/index.tsx:250
msgid "Message Content"
msgstr "Contenu du message"

#: src/components/modals/SendMessageModal/index.tsx:71
msgid "Message individual attendees"
msgstr "Envoyer un message à des participants individuels"

#: src/components/modals/SendMessageModal/index.tsx:218
msgid "Message order owners with specific products"
msgstr "Envoyer un message aux propriétaires de commandes avec des produits spécifiques"

#: src/components/modals/SendMessageModal/index.tsx:118
msgid "Message Sent"
msgstr "Message envoyé"

#: src/components/layouts/Event/index.tsx:105
#: src/components/routes/event/messages.tsx:31
msgid "Messages"
msgstr "messages"

#: src/components/forms/ProductForm/index.tsx:381
msgid "Minimum Per Order"
msgstr "Minimum par commande"

#: src/components/forms/ProductForm/index.tsx:285
msgid "Minimum Price"
msgstr "Prix minimum"

#: src/components/routes/event/Settings/index.tsx:58
msgid "Miscellaneous"
msgstr "Divers"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:69
msgid "Miscellaneous Settings"
msgstr "Paramètres divers"

#: src/components/layouts/AuthLayout/index.tsx:63
msgid "Mobile Check-in"
msgstr "Enregistrement mobile"

#: src/components/forms/QuestionForm/index.tsx:106
msgid "Multi line text box"
msgstr "Zone de texte multiligne"

#: src/components/forms/ProductForm/index.tsx:165
msgid "Multiple price options. Perfect for early bird products etc."
msgstr "Options de prix multiples. Parfait pour les produits en prévente, etc."

#: src/components/forms/TicketForm/index.tsx:147
#~ msgid "Multiple price options. Perfect for early bird tickets etc."
#~ msgstr "Plusieurs options de prix. Parfait pour les billets lève-tôt, etc."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:71
msgid "My amazing event description..."
msgstr "Mon incroyable description d'événement..."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:65
msgid "My amazing event title..."
msgstr "Mon incroyable titre d'événement..."

#: src/components/common/GlobalMenu/index.tsx:27
msgid "My Profile"
msgstr "Mon profil"

#: src/components/common/QuestionAndAnswerList/index.tsx:178
msgid "N/A"
msgstr "N/A"

#: src/components/common/WidgetEditor/index.tsx:354
msgid "Nam placerat elementum..."
msgstr "Nom placerat elementum..."

#: src/components/common/AttendeeDetails/index.tsx:13
#: src/components/common/AttendeeTable/index.tsx:108
#: src/components/common/OrderDetails/index.tsx:23
#: src/components/forms/CapaciyAssigmentForm/index.tsx:37
#: src/components/forms/CheckInListForm/index.tsx:20
#: src/components/forms/ProductForm/index.tsx:253
#: src/components/forms/TaxAndFeeForm/index.tsx:61
#: src/components/modals/CreateEventModal/index.tsx:129
#: src/components/modals/DuplicateEventModal/index.tsx:78
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:63
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:72
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:107
#: src/components/routes/welcome/index.tsx:83
msgid "Name"
msgstr "Nom"

#: src/components/modals/CreateEventModal/index.tsx:38
msgid "Name should be less than 150 characters"
msgstr "Le nom doit comporter moins de 150 caractères"

#: src/components/common/QuestionAndAnswerList/index.tsx:181
#: src/components/common/QuestionAndAnswerList/index.tsx:289
msgid "Navigate to Attendee"
msgstr "Accédez au participant"

#: src/components/common/PromoCodeTable/index.tsx:158
#: src/components/common/WebhookTable/index.tsx:266
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Never"
msgstr "Jamais"

#: src/components/routes/auth/AcceptInvitation/index.tsx:111
#: src/components/routes/auth/ResetPassword/index.tsx:59
#: src/components/routes/profile/ManageProfile/index.tsx:195
msgid "New Password"
msgstr "nouveau mot de passe"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "No"
msgstr "Non"

#: src/components/common/QuestionAndAnswerList/index.tsx:104
#~ msgid "No {0} available."
#~ msgstr "Aucun {0} disponible."

#: src/components/routes/event/Webhooks/index.tsx:22
msgid "No Active Webhooks"
msgstr "Aucun webhook actif"

#: src/components/common/NoEventsBlankSlate/index.tsx:21
msgid "No archived events to show."
msgstr "Aucun événement archivé à afficher."

#: src/components/common/AttendeeList/index.tsx:23
msgid "No attendees found for this order."
msgstr "Aucun invité trouvé pour cette commande."

#: src/components/modals/ManageOrderModal/index.tsx:132
msgid "No attendees have been added to this order."
msgstr "Aucun invité n'a été ajouté à cette commande."

#: src/components/common/AttendeeTable/index.tsx:56
msgid "No Attendees to show"
msgstr "Aucun participant à afficher"

#: src/components/forms/CheckInListForm/index.tsx:48
msgid "No attendees will be able to check in before this date using this list"
msgstr "Aucun participant ne pourra s'enregistrer avant cette date en utilisant cette liste"

#: src/components/common/CapacityAssignmentList/index.tsx:42
msgid "No Capacity Assignments"
msgstr "Aucune Affectation de Capacité"

#: src/components/common/CheckInListList/index.tsx:44
msgid "No Check-In Lists"
msgstr "Pas de listes de pointage"

#: src/components/layouts/AuthLayout/index.tsx:34
msgid "No Credit Card Required"
msgstr "Aucune carte de crédit requise"

#: src/components/common/ReportTable/index.tsx:218
msgid "No data available"
msgstr "Aucune donnée disponible"

#: src/components/common/ReportTable/index.tsx:214
msgid "No data to show. Please select a date range"
msgstr "Aucune donnée à afficher. Veuillez sélectionner une plage de dates"

#: src/components/forms/PromoCodeForm/index.tsx:47
msgid "No Discount"
msgstr "Pas de rabais"

#: src/components/common/NoEventsBlankSlate/index.tsx:20
msgid "No ended events to show."
msgstr "Aucun événement terminé à afficher."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:88
#~ msgid "No events for this organizer"
#~ msgstr "Aucun événement pour cet organisateur"

#: src/components/common/NoEventsBlankSlate/index.tsx:14
msgid "No events to show"
msgstr "Aucun événement à afficher"

#: src/components/common/FilterModal/index.tsx:230
msgid "No filters available"
msgstr "Aucun filtre disponible"

#: src/components/modals/WebhookLogsModal/index.tsx:177
msgid "No logs found"
msgstr "Aucun journal trouvé"

#: src/components/common/MessageList/index.tsx:69
msgid "No messages to show"
msgstr "Aucun message à afficher"

#: src/components/common/OrdersTable/index.tsx:63
msgid "No orders to show"
msgstr "Aucune commande à afficher"

#: src/components/routes/product-widget/Payment/index.tsx:76
msgid "No payment methods are currently available. Please contact the event organizer for assistance."
msgstr "Aucune méthode de paiement n'est actuellement disponible. Veuillez contacter l'organisateur de l'événement pour obtenir de l'aide."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:39
msgid "No Payment Required"
msgstr "Aucun paiement requis"

#: src/components/modals/ManageAttendeeModal/index.tsx:177
msgid "No product associated with this attendee."
msgstr "Aucun produit associé à cet invité."

#: src/components/common/ProductSelector/index.tsx:33
msgid "No products available for selection"
msgstr "Aucun produit disponible pour la sélection"

#: src/components/modals/CreateProductCategoryModal/index.tsx:22
msgid "No products available in this category."
msgstr "Aucun produit disponible dans cette catégorie."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:39
msgid "No Products Yet"
msgstr "Pas encore de produits"

#: src/components/common/PromoCodeTable/index.tsx:46
msgid "No Promo Codes to show"
msgstr "Aucun code promotionnel à afficher"

#: src/components/modals/ManageAttendeeModal/index.tsx:193
msgid "No questions answered by this attendee."
msgstr "Aucune question n’a été répondue par ce participant."

#: src/components/modals/ViewAttendeeModal/index.tsx:75
#~ msgid "No questions have been answered by this attendee."
#~ msgstr "Aucune question n'a été répondue par cet invité."

#: src/components/modals/ManageOrderModal/index.tsx:119
msgid "No questions have been asked for this order."
msgstr "Aucune question n'a été posée pour cette commande."

#: src/components/common/WebhookTable/index.tsx:174
msgid "No response"
msgstr "Aucune réponse"

#: src/components/common/WebhookTable/index.tsx:141
msgid "No responses yet"
msgstr "Aucune réponse pour le moment"

#: src/components/common/NoResultsSplash/index.tsx:24
msgid "No results"
msgstr "Aucun résultat"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:20
msgid "No Search Results"
msgstr "Aucun résultat de recherche"

#: src/components/common/NoResultsSplash/index.tsx:29
msgid "No search results."
msgstr "Aucun résultat trouvé."

#: src/components/common/TaxAndFeeList/index.tsx:101
msgid "No Taxes or Fees have been added."
msgstr "Aucune taxe ou frais n'a été ajouté."

#: src/components/common/TicketsTable/index.tsx:65
#~ msgid "No tickets to show"
#~ msgstr "Aucun billet à montrer"

#: src/components/modals/WebhookLogsModal/index.tsx:180
msgid "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."
msgstr "Aucun événement webhook n'a encore été enregistré pour ce point de terminaison. Les événements apparaîtront ici une fois qu'ils seront déclenchés."

#: src/components/common/WebhookTable/index.tsx:201
msgid "No Webhooks"
msgstr "Aucun webhook"

#: src/components/common/PromoCodeTable/index.tsx:82
msgid "None"
msgstr "Aucun"

#: src/components/common/ProductPriceAvailability/index.tsx:27
#: src/components/common/ProductPriceAvailability/index.tsx:48
msgid "Not available"
msgstr "Pas disponible"

#: src/components/common/AttendeeCheckInTable/index.tsx:125
#~ msgid "Not Checked In"
#~ msgstr "Non enregistré"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "Not On Sale"
msgstr "Pas en vente"

#: src/components/modals/ManageAttendeeModal/index.tsx:130
#: src/components/modals/ManageOrderModal/index.tsx:164
msgid "Notes"
msgstr "Notes"

#: src/components/common/ReportTable/index.tsx:240
msgid "Nothing to show yet"
msgstr "Rien à montrer pour le moment"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:72
msgid "Notification Settings"
msgstr "Paramètres de notification"

#: src/components/modals/RefundOrderModal/index.tsx:108
msgid "Notify buyer of refund"
msgstr "Informer l'acheteur du remboursement"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:75
msgid "Notify organizer of new orders"
msgstr "Informer l'organisateur des nouvelles commandes"

#: src/components/routes/welcome/index.tsx:72
msgid "Now let's create your first event"
msgstr "Créons maintenant votre premier événement"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:217
msgid "Number of days allowed for payment (leave blank to omit payment terms from invoices)"
msgstr "Nombre de jours autorisés pour le paiement (laisser vide pour omettre les conditions de paiement sur les factures)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:199
msgid "Number Prefix"
msgstr "Préfixe de numéro"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:145
msgid "Offline orders are not reflected in event statistics until the order is marked as paid."
msgstr "Les commandes hors ligne ne sont pas reflétées dans les statistiques de l'événement tant que la commande n'est pas marquée comme payée."

#: src/components/routes/product-widget/Payment/index.tsx:66
msgid "Offline payment failed. Please try again or contact the event organizer."
msgstr "Le paiement hors ligne a échoué. Veuillez réessayer ou contacter l'organisateur de l'événement."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:151
msgid "Offline Payment Instructions"
msgstr "Instructions pour le paiement hors ligne"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:90
msgid "Offline Payments"
msgstr "Paiements hors ligne"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:139
msgid "Offline Payments Information"
msgstr "Informations sur les paiements hors ligne"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:134
msgid "Offline Payments Settings"
msgstr "Paramètres des paiements hors ligne"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:71
msgid "On sale"
msgstr "En soldes"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "On Sale"
msgstr "En soldes"

#: src/components/common/NoEventsBlankSlate/index.tsx:19
msgid "Once you create an event, you'll see it here."
msgstr "Une fois que vous avez créé un événement, vous le verrez ici."

#: src/components/common/ReportTable/index.tsx:245
msgid "Once you start collecting data, you'll see it here."
msgstr "Une fois que vous commencerez à collecter des données, elles apparaîtront ici."

#: src/components/routes/event/GettingStarted/index.tsx:179
msgid "Once you're ready, set your event live and start selling products."
msgstr "Une fois prêt, mettez votre événement en ligne et commencez à vendre des produits."

#: src/components/routes/event/GettingStarted/index.tsx:108
#~ msgid "Once you're ready, set your event live and start selling tickets."
#~ msgstr "Une fois que vous êtes prêt, diffusez votre événement en direct et commencez à vendre des billets."

#: src/components/common/EventStatusBadge/index.tsx:18
msgid "Ongoing"
msgstr "En cours"

#: src/components/common/EventCard/index.tsx:118
msgid "Online event"
msgstr "Événement en ligne"

#: src/components/common/OnlineEventDetails/index.tsx:9
msgid "Online Event Details"
msgstr "Détails de l'événement en ligne"

#: src/components/modals/SendMessageModal/index.tsx:279
msgid ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."
msgstr ""
"Seuls les e-mails importants, directement liés à cet événement, doivent être envoyés via ce formulaire.\n"
"Toute utilisation abusive, y compris l'envoi d'e-mails promotionnels, entraînera un bannissement immédiat du compte."

#: src/components/modals/SendMessageModal/index.tsx:226
msgid "Only send to orders with these statuses"
msgstr "Envoyer uniquement aux commandes avec ces statuts"

#: src/components/common/CheckInListList/index.tsx:167
msgid "Open Check-In Page"
msgstr "Ouvrir la page de pointage"

#: src/components/layouts/Event/index.tsx:288
msgid "Open sidebar"
msgstr "Ouvrir la barre latérale"

#: src/components/forms/QuestionForm/index.tsx:45
msgid "Option {i}"
msgstr "Option {i}"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:253
msgid "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"
msgstr "Informations supplémentaires optionnelles à apparaître sur toutes les factures (par exemple, conditions de paiement, frais de retard, politique de retour)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:200
msgid "Optional prefix for invoice numbers (e.g., INV-)"
msgstr "Préfixe optionnel pour les numéros de facture (par exemple, INV-)"

#: src/components/forms/QuestionForm/index.tsx:28
msgid "Options"
msgstr "Possibilités"

#: src/components/modals/CreateEventModal/index.tsx:118
msgid "or"
msgstr "ou"

#: src/components/common/AttendeeTable/index.tsx:110
msgid "Order"
msgstr "Commande"

#: src/components/common/OrdersTable/index.tsx:212
#~ msgid "Order #"
#~ msgstr "Commande #"

#: src/components/forms/WebhookForm/index.tsx:76
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:63
msgid "Order Cancelled"
msgstr "Commande annulée"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:62
msgid "Order Completed"
msgstr "Commande terminée"

#: src/components/forms/WebhookForm/index.tsx:52
msgid "Order Created"
msgstr "Commande créée"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:122
msgid "Order Date"
msgstr "Date de commande"

#: src/components/modals/ManageAttendeeModal/index.tsx:166
#: src/components/modals/ManageOrderModal/index.tsx:86
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:266
msgid "Order Details"
msgstr "détails de la commande"

#: src/components/modals/ViewOrderModal/index.tsx:30
#~ msgid "Order Details {0}"
#~ msgstr "Détails de la commande {0}"

#: src/components/modals/CancelOrderModal/index.tsx:29
msgid "Order has been canceled and the order owner has been notified."
msgstr "La commande a été annulée et le propriétaire de la commande a été informé."

#: src/components/common/OrdersTable/index.tsx:79
msgid "Order marked as paid"
msgstr "Commande marquée comme payée"

#: src/components/forms/WebhookForm/index.tsx:64
msgid "Order Marked as Paid"
msgstr "Commande marquée comme payée"

#: src/components/modals/ManageOrderModal/index.tsx:92
msgid "Order Notes"
msgstr "Notes de commande"

#: src/components/common/MessageList/index.tsx:23
msgid "Order owner"
msgstr "Propriétaire de la commande"

#: src/components/modals/SendMessageModal/index.tsx:191
msgid "Order owners with a specific product"
msgstr "Propriétaires de commandes avec un produit spécifique"

#: src/components/common/MessageList/index.tsx:19
msgid "Order owners with products"
msgstr "Propriétaires de commandes avec des produits"

#: src/components/common/QuestionsTable/index.tsx:313
#: src/components/common/QuestionsTable/index.tsx:355
msgid "Order questions"
msgstr "Questions de commande"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:112
msgid "Order Reference"
msgstr "Référence de l'achat"

#: src/components/forms/WebhookForm/index.tsx:70
msgid "Order Refunded"
msgstr "Commande remboursée"

#: src/components/routes/event/orders.tsx:45
msgid "Order Status"
msgstr "Statut de la commande"

#: src/components/modals/SendMessageModal/index.tsx:228
msgid "Order statuses"
msgstr "Statuts des commandes"

#: src/components/layouts/Checkout/CheckoutSidebar/index.tsx:28
#: src/components/modals/ManageOrderModal/index.tsx:105
msgid "Order Summary"
msgstr "Récapitulatif de la commande"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:90
msgid "Order timeout"
msgstr "Délai d'expiration de la commande"

#: src/components/forms/WebhookForm/index.tsx:58
msgid "Order Updated"
msgstr "Commande mise à jour"

#: src/components/layouts/Event/index.tsx:100
#: src/components/routes/event/orders.tsx:113
msgid "Orders"
msgstr "Ordres"

#: src/components/common/StatBoxes/index.tsx:51
#: src/components/routes/event/EventDashboard/index.tsx:105
#~ msgid "Orders Created"
#~ msgstr "Commandes créées"

#: src/components/routes/event/orders.tsx:94
msgid "Orders Exported"
msgstr "Commandes exportées"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:235
msgid "Organization Address"
msgstr "Adresse de l'organisation"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:226
msgid "Organization Details"
msgstr "Détails de l'organisation"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:228
msgid "Organization Name"
msgstr "Nom de l'organisation"

#: src/components/modals/EditUserModal/index.tsx:55
#: src/components/modals/InviteUserModal/index.tsx:47
#: src/components/routes/events/Dashboard/index.tsx:113
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:186
msgid "Organizer"
msgstr "Organisateur"

#: src/components/modals/CreateEventModal/index.tsx:41
msgid "Organizer is required"
msgstr "Un organisateur est requis"

#: src/components/forms/OrganizerForm/index.tsx:27
msgid "Organizer Name"
msgstr "Nom de l'organisateur"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
msgid "Organizers can only manage events and products. They cannot manage users, account settings or billing information."
msgstr "Les organisateurs ne peuvent gérer que les événements et les produits. Ils ne peuvent pas gérer les utilisateurs, les paramètres du compte ou les informations de facturation."

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
#~ msgid "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."
#~ msgstr "Les organisateurs ne peuvent gérer que les événements et les billets. Ils ne peuvent pas gérer les utilisateurs, les paramètres de compte ou les informations de facturation."

#: src/components/layouts/Event/index.tsx:87
msgid "Overview"
msgstr "Aperçu"

#: src/components/common/WidgetEditor/index.tsx:223
msgid "Padding"
msgstr "Rembourrage"

#: src/components/routes/event/HomepageDesigner/index.tsx:150
msgid "Page background color"
msgstr "Couleur d’arrière-plan de la page"

#: src/components/common/ErrorDisplay/index.tsx:13
msgid "Page not found"
msgstr "Page non trouvée"

#: src/components/common/StatBoxes/index.tsx:45
msgid "Page views"
msgstr "Pages vues"

#: src/components/forms/ProductForm/index.tsx:358
msgid "page."
msgstr "page."

#: src/components/modals/CreateAttendeeModal/index.tsx:180
msgid "paid"
msgstr "payé"

#: src/components/forms/ProductForm/index.tsx:145
msgid "Paid Product"
msgstr "Produit payant"

#: src/components/forms/TicketForm/index.tsx:127
#~ msgid "Paid Ticket"
#~ msgstr "Billet payant"

#: src/components/routes/event/orders.tsx:30
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:54
msgid "Partially Refunded"
msgstr "Partiellement remboursé"

#: src/components/routes/auth/Login/index.tsx:73
#: src/components/routes/auth/Register/index.tsx:106
#: src/components/routes/profile/ManageProfile/index.tsx:109
msgid "Password"
msgstr "Mot de passe"

#: src/components/routes/auth/AcceptInvitation/index.tsx:30
msgid "Password must be a minimum  of 8 characters"
msgstr "Le mot de passe doit contenir au minimum 8 caractères"

#: src/components/routes/auth/Register/index.tsx:36
msgid "Password must be at least 8 characters"
msgstr "Mot de passe doit être d'au moins 8 caractères"

#: src/components/routes/auth/ResetPassword/index.tsx:46
msgid "Password reset successfully. Please login with your new password."
msgstr "Le mot de passe a été réinitialisé avec succès. Veuillez vous connecter avec votre nouveau mot de passe."

#: src/components/routes/auth/AcceptInvitation/index.tsx:31
#: src/components/routes/auth/Register/index.tsx:37
msgid "Passwords are not the same"
msgstr "les mots de passe ne sont pas les mêmes"

#: src/components/common/WidgetEditor/index.tsx:269
msgid "Paste this where you want the widget to appear."
msgstr "Collez-le à l'endroit où vous souhaitez que le widget apparaisse."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:104
msgid "Paste URL"
msgstr "Coller l’URL"

#: src/components/modals/CreateAttendeeModal/index.tsx:119
msgid "Patrick"
msgstr "Patrick"

#: src/components/modals/CreateAttendeeModal/index.tsx:133
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/forms/WebhookForm/index.tsx:25
msgid "Paused"
msgstr "En pause"

#: src/components/forms/StripeCheckoutForm/index.tsx:123
msgid "Payment"
msgstr "Paiement"

#: src/components/routes/event/Settings/index.tsx:64
msgid "Payment & Invoicing"
msgstr "Paiement et facturation"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:98
msgid "Payment & Invoicing Settings"
msgstr "Paramètres de paiement et de facturation"

#: src/components/routes/account/ManageAccount/index.tsx:38
msgid "Payment & Plan"
msgstr "Paiement et abonnement"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:216
msgid "Payment Due Period"
msgstr "Délai de paiement"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:41
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:64
msgid "Payment Failed"
msgstr "Paiement échoué"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:228
#: src/components/routes/product-widget/Payment/PaymentMethods/Offline/index.tsx:14
msgid "Payment Instructions"
msgstr "Instructions de paiement"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:105
msgid "Payment Methods"
msgstr "Méthodes de paiement"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:138
msgid "Payment Processing"
msgstr "Traitement des paiements"

#: src/components/common/OrderDetails/index.tsx:76
msgid "Payment provider"
msgstr "Fournisseur de paiement"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:42
msgid "Payment Received"
msgstr "Paiement reçu"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:240
msgid "Payment Settings"
msgstr "Paramètres de paiement"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:135
msgid "Payment Status"
msgstr "Statut du paiement"

#: src/components/forms/StripeCheckoutForm/index.tsx:60
msgid "Payment succeeded!"
msgstr "Paiement réussi !"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:214
msgid "Payment Terms"
msgstr "Conditions de paiement"

#: src/components/forms/PromoCodeForm/index.tsx:51
#: src/components/forms/TaxAndFeeForm/index.tsx:27
msgid "Percentage"
msgstr "Pourcentage"

#: src/components/forms/TaxAndFeeForm/index.tsx:71
msgid "Percentage Amount"
msgstr "Montant en pourcentage"

#: src/components/routes/product-widget/Payment/index.tsx:122
msgid "Place Order"
msgstr "Passer commande"

#: src/components/common/WidgetEditor/index.tsx:257
msgid "Place this in the <head> of your website."
msgstr "Placez-le dans le <head> de votre site Web."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:40
msgid "Platform Fees"
msgstr "Frais de plateforme"

#: src/components/forms/QuestionForm/index.tsx:31
msgid "Please add at least one option"
msgstr "Veuillez ajouter au moins une option"

#: src/hooks/useFormErrorResponseHandler.tsx:18
msgid "Please check the provided information is correct"
msgstr "Veuillez vérifier que les informations fournies sont correctes"

#: src/components/routes/auth/Login/index.tsx:41
msgid "Please check your email and password and try again"
msgstr "Veuillez vérifier votre e-mail et votre mot de passe et réessayer"

#: src/components/routes/auth/AcceptInvitation/index.tsx:32
#: src/components/routes/auth/Register/index.tsx:38
msgid "Please check your email is valid"
msgstr "Veuillez vérifier que votre email est valide"

#: src/components/routes/profile/ManageProfile/index.tsx:152
msgid "Please check your email to confirm your email address"
msgstr "Veuillez vérifier votre courrier électronique pour confirmer votre adresse e-mail"

#: src/components/routes/auth/AcceptInvitation/index.tsx:86
msgid "Please complete the form below to accept your invitation"
msgstr "Veuillez remplir le formulaire ci-dessous pour accepter votre invitation"

#: src/components/routes/product-widget/SelectProducts/index.tsx:306
msgid "Please continue in the new tab"
msgstr "Veuillez continuer dans le nouvel onglet"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:47
msgid "Please create a product"
msgstr "Veuillez créer un produit"

#: src/components/modals/CreateCheckInListModal/index.tsx:51
msgid "Please create a ticket"
msgstr "Veuillez créer un billet"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:45
msgid "Please enter a valid image URL that points to an image."
msgstr "Veuillez entrer une URL d'image valide qui pointe vers une image."

#: src/components/modals/CreateWebhookModal/index.tsx:30
msgid "Please enter a valid URL"
msgstr "Veuillez entrer une URL valide"

#: src/components/routes/auth/ResetPassword/index.tsx:55
msgid "Please enter your new password"
msgstr "Veuillez entrer votre nouveau mot de passe"

#: src/components/modals/CancelOrderModal/index.tsx:52
msgid "Please Note"
msgstr "Veuillez noter"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:34
msgid "Please provide an image."
msgstr "Veuillez fournir une image."

#: src/components/common/TicketsTable/index.tsx:84
#~ msgid "Please remove filters and set sorting to \"Homepage order\" to enable sorting"
#~ msgstr "Veuillez supprimer les filtres et définir le tri sur \"Ordre de la page d'accueil\" pour activer le tri."

#: src/components/layouts/Checkout/index.tsx:159
msgid "Please return to the event page to start over."
msgstr "Veuillez retourner sur la page de l'événement pour recommencer."

#: src/components/modals/SendMessageModal/index.tsx:195
msgid "Please select"
msgstr "Veuillez sélectionner"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:53
msgid "Please select an image."
msgstr "Veuillez sélectionner une image."

#: src/components/routes/product-widget/SelectProducts/index.tsx:237
msgid "Please select at least one product"
msgstr "Veuillez sélectionner au moins un produit"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:227
#~ msgid "Please select at least one ticket"
#~ msgstr "Veuillez sélectionner au moins un billet"

#: src/components/routes/event/attendees.tsx:49
#: src/components/routes/event/orders.tsx:100
msgid "Please try again."
msgstr "Veuillez réessayer."

#: src/components/routes/profile/ManageProfile/index.tsx:142
msgid "Please verify your email address to access all features"
msgstr "Veuillez vérifier votre adresse e-mail pour accéder à toutes les fonctionnalités"

#: src/components/routes/event/attendees.tsx:40
msgid "Please wait while we prepare your attendees for export..."
msgstr "Veuillez patienter pendant que nous préparons l'exportation de vos participants..."

#: src/components/common/OrdersTable/index.tsx:112
#: src/components/layouts/Checkout/index.tsx:50
msgid "Please wait while we prepare your invoice..."
msgstr "Veuillez patienter pendant que nous préparons votre facture..."

#: src/components/routes/event/orders.tsx:91
msgid "Please wait while we prepare your orders for export..."
msgstr "Veuillez patienter pendant que nous préparons l'exportation de vos commandes..."

#: src/components/common/LanguageSwitcher/index.tsx:26
msgid "Portuguese"
msgstr "Portugais"

#: src/locales.ts:47
#~ msgid "Portuguese (Brazil)"
#~ msgstr "Portugais (Brésil)"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:74
msgid "Post Checkout message"
msgstr "Message après le paiement"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Powered by"
msgstr "Alimenté par"

#: src/components/forms/StripeCheckoutForm/index.tsx:137
#~ msgid "Powered by Stripe"
#~ msgstr "Propulsé par Stripe"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:67
msgid "Pre Checkout message"
msgstr "Message de pré-commande"

#: src/components/common/QuestionsTable/index.tsx:347
msgid "Preview"
msgstr "Aperçu"

#: src/components/layouts/Event/index.tsx:205
#: src/components/layouts/Event/index.tsx:209
msgid "Preview Event page"
msgstr "Aperçu de la page de l’événement"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:236
#: src/components/forms/ProductForm/index.tsx:73
#: src/components/forms/ProductForm/index.tsx:285
msgid "Price"
msgstr "Prix"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:78
msgid "Price display mode"
msgstr "Mode d'affichage des prix"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:88
msgid "Price not set"
msgstr "Prix non défini"

#: src/components/forms/ProductForm/index.tsx:322
msgid "Price tiers"
msgstr "Niveaux de prix"

#: src/components/forms/ProductForm/index.tsx:228
msgid "Price Type"
msgstr "Type de prix"

#: src/components/common/WidgetEditor/index.tsx:182
msgid "Primary Color"
msgstr "Couleur primaire"

#: src/components/routes/event/HomepageDesigner/index.tsx:156
msgid "Primary Colour"
msgstr "Couleur primaire"

#: src/components/common/WidgetEditor/index.tsx:190
#: src/components/routes/event/HomepageDesigner/index.tsx:158
msgid "Primary Text Color"
msgstr "Couleur du texte principal"

#: src/components/common/AttendeeTicket/index.tsx:81
msgid "Print"
msgstr "Imprimer"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:288
msgid "Print All Tickets"
msgstr "Imprimer tous les billets"

#: src/components/layouts/Checkout/index.tsx:114
msgid "Print Tickets"
msgstr "Imprimer les billets"

#: src/components/common/AttendeeTable/index.tsx:190
#~ msgid "product"
#~ msgstr "produit"

#: src/components/common/AttendeeDetails/index.tsx:37
#: src/components/modals/ManageAttendeeModal/index.tsx:119
msgid "Product"
msgstr "Produit"

#: src/components/forms/ProductForm/index.tsx:266
msgid "Product Category"
msgstr "Catégorie de produit"

#: src/components/modals/EditProductCategoryModal/index.tsx:53
msgid "Product category updated successfully."
msgstr "Catégorie de produit mise à jour avec succès."

#: src/components/forms/WebhookForm/index.tsx:34
msgid "Product Created"
msgstr "Produit créé"

#: src/components/forms/WebhookForm/index.tsx:46
msgid "Product Deleted"
msgstr "Produit supprimé"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:55
msgid "Product deleted successfully"
msgstr "Produit supprimé avec succès"

#: src/components/common/AttendeeTable/index.tsx:50
#~ msgid "Product email has been resent to attendee"
#~ msgstr "L'e-mail du produit a été renvoyé à l'invité"

#: src/components/forms/ProductForm/index.tsx:236
msgid "Product Price Type"
msgstr "Type de prix du produit"

#: src/components/common/QuestionsTable/index.tsx:328
msgid "Product questions"
msgstr "Questions sur le produit"

#: src/components/routes/event/EventDashboard/index.tsx:217
#: src/components/routes/event/Reports/index.tsx:17
msgid "Product Sales"
msgstr "Ventes de produits"

#: src/components/routes/event/Reports/index.tsx:18
msgid "Product sales, revenue, and tax breakdown"
msgstr "Détail des ventes de produits, revenus et taxes"

#: src/components/common/ProductSelector/index.tsx:63
msgid "Product Tier"
msgstr "Niveau de produit"

#: src/components/forms/ProductForm/index.tsx:214
#: src/components/forms/ProductForm/index.tsx:221
msgid "Product Type"
msgstr "Type de produit"

#: src/components/forms/WebhookForm/index.tsx:40
msgid "Product Updated"
msgstr "Produit mis à jour"

#: src/components/common/WidgetEditor/index.tsx:313
msgid "Product Widget Preview"
msgstr "Aperçu du widget produit"

#: src/components/common/PromoCodeTable/index.tsx:148
msgid "Product(s)"
msgstr "Produit(s)"

#: src/components/common/PromoCodeTable/index.tsx:72
msgid "Products"
msgstr "Produits"

#: src/components/common/EventCard/index.tsx:126
msgid "products sold"
msgstr "produits vendus"

#: src/components/common/StatBoxes/index.tsx:21
msgid "Products sold"
msgstr "Produits vendus"

#: src/components/routes/event/EventDashboard/index.tsx:238
msgid "Products Sold"
msgstr "Produits vendus"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:178
msgid "Products sorted successfully"
msgstr "Produits triés avec succès"

#: src/components/layouts/AuthLayout/index.tsx:43
msgid "Products, merchandise, and flexible pricing options"
msgstr "Produits, marchandises et options de tarification flexibles"

#: src/components/routes/profile/ManageProfile/index.tsx:106
msgid "Profile"
msgstr "Profil"

#: src/components/routes/profile/ManageProfile/index.tsx:59
msgid "Profile updated successfully"
msgstr "Mise à jour du profil réussie"

#: src/components/routes/product-widget/SelectProducts/index.tsx:178
msgid "Promo {promo_code} code applied"
msgstr "Code promotionnel {promo_code} appliqué"

#: src/components/common/OrderDetails/index.tsx:86
msgid "Promo code"
msgstr "Code promo"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:20
msgid "Promo Code"
msgstr "Code promo"

#: src/components/forms/ProductForm/index.tsx:409
msgid "Promo Code page"
msgstr "Page des codes promotionnels"

#: src/components/routes/event/Reports/index.tsx:30
msgid "Promo code usage and discount breakdown"
msgstr "Utilisation des codes promo et détail des réductions"

#: src/components/layouts/Event/index.tsx:106
#: src/components/routes/event/promo-codes.tsx:31
msgid "Promo Codes"
msgstr "Code de promo"

#: src/components/common/PromoCodeTable/index.tsx:51
msgid "Promo codes can be used to offer discounts, presale access, or provide special access to your event."
msgstr "Les codes promotionnels peuvent être utilisés pour offrir des réductions, un accès en prévente ou fournir un accès spécial à votre événement."

#: src/components/routes/event/Reports/index.tsx:29
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:95
msgid "Promo Codes Report"
msgstr "Rapport des codes promo"

#: src/components/forms/QuestionForm/index.tsx:189
msgid ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."
msgstr "Fournissez un contexte ou des instructions supplémentaires pour cette question. Utilisez ce champ pour ajouter des termes et conditions, des directives ou toute information importante que les participants doivent connaître avant de répondre."

#: src/components/routes/event/EventDashboard/index.tsx:159
msgid "Publish Event"
msgstr "Publier l'événement"

#: src/components/common/GlobalMenu/index.tsx:25
#~ msgid "Purchase License"
#~ msgstr "Licence d'achat"

#: src/components/modals/ShareModal/index.tsx:126
msgid "QR Code"
msgstr "Code QR"

#: src/components/layouts/AuthLayout/index.tsx:64
msgid "QR code scanning with instant feedback and secure sharing for staff access"
msgstr "Scan de QR code avec retour instantané et partage sécurisé pour l'accès du personnel"

#: src/components/forms/ProductForm/index.tsx:85
#: src/components/forms/ProductForm/index.tsx:302
msgid "Quantity Available"
msgstr "Quantité disponible"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
msgid "Quantity Sold"
msgstr "Quantité vendue"

#: src/components/common/QuestionsTable/index.tsx:170
msgid "Question deleted"
msgstr "Question supprimée"

#: src/components/forms/QuestionForm/index.tsx:188
msgid "Question Description"
msgstr "Description de la question"

#: src/components/forms/QuestionForm/index.tsx:178
msgid "Question Title"
msgstr "titre de question"

#: src/components/common/QuestionsTable/index.tsx:275
#: src/components/layouts/Event/index.tsx:102
msgid "Questions"
msgstr "Des questions"

#: src/components/modals/ManageAttendeeModal/index.tsx:184
#: src/components/modals/ManageOrderModal/index.tsx:111
msgid "Questions & Answers"
msgstr "Questions et réponses"

#: src/components/common/QuestionsTable/index.tsx:145
msgid "Questions sorted successfully"
msgstr "Questions triées avec succès"

#: src/components/forms/QuestionForm/index.tsx:118
msgid "Radio Option"
msgstr "Option radio"

#: src/components/common/MessageList/index.tsx:59
msgid "Read less"
msgstr "Lire moins"

#: src/components/modals/SendMessageModal/index.tsx:36
msgid "Recipient"
msgstr "Destinataire"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:110
#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:195
msgid "Redirecting to Stripe..."
msgstr "Redirection vers Stripe..."

#: src/components/common/OrdersTable/index.tsx:204
#: src/components/common/OrdersTable/index.tsx:274
msgid "Reference"
msgstr "Référence"

#: src/components/modals/RefundOrderModal/index.tsx:104
msgid "Refund amount ({0})"
msgstr "Montant du remboursement ({0})"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:52
msgid "Refund Failed"
msgstr "Remboursement échoué"

#: src/components/common/OrdersTable/index.tsx:172
msgid "Refund order"
msgstr "Commande de remboursement"

#: src/components/modals/RefundOrderModal/index.tsx:151
msgid "Refund Order"
msgstr "Commande de remboursement"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:51
msgid "Refund Pending"
msgstr "Remboursement en attente"

#: src/components/routes/event/orders.tsx:51
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:128
msgid "Refund Status"
msgstr "Statut du remboursement"

#: src/components/common/OrderSummary/index.tsx:80
#: src/components/common/StatBoxes/index.tsx:33
#: src/components/routes/event/orders.tsx:29
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:53
msgid "Refunded"
msgstr "Remboursé"

#: src/components/routes/auth/Register/index.tsx:129
msgid "Register"
msgstr "Registre"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:82
msgid "Remaining Uses"
msgstr "Utilisations restantes"

#: src/components/routes/product-widget/SelectProducts/index.tsx:504
msgid "remove"
msgstr "retirer"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:140
#: src/components/routes/product-widget/SelectProducts/index.tsx:505
msgid "Remove"
msgstr "Retirer"

#: src/components/layouts/Event/index.tsx:92
#: src/components/routes/event/Reports/index.tsx:39
msgid "Reports"
msgstr "Rapports"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:169
msgid "Require Billing Address"
msgstr "Adresse de facturation requise"

#: src/components/routes/event/GettingStarted/index.tsx:197
msgid "Resend confirmation email"
msgstr "Renvoyer un courriel de confirmation"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resend email confirmation"
msgstr "Renvoyer l'e-mail de confirmation"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Resend invitation"
msgstr "Renvoyer l'invitation"

#: src/components/common/OrdersTable/index.tsx:179
msgid "Resend order email"
msgstr "Renvoyer l'e-mail de commande"

#: src/components/common/AttendeeTable/index.tsx:179
#~ msgid "Resend product email"
#~ msgstr "Renvoyer l'e-mail du produit"

#: src/components/common/AttendeeTable/index.tsx:171
msgid "Resend ticket email"
msgstr "Renvoyer l'e-mail du ticket"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resending..."
msgstr "Renvoi..."

#: src/components/common/FilterModal/index.tsx:248
msgid "Reset"
msgstr "Réinitialiser"

#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Reset password"
msgstr "Réinitialiser le mot de passe"

#: src/components/routes/auth/ResetPassword/index.tsx:54
msgid "Reset Password"
msgstr "réinitialiser le mot de passe"

#: src/components/routes/auth/ForgotPassword/index.tsx:50
msgid "Reset your password"
msgstr "Réinitialisez votre mot de passe"

#: src/components/common/EventCard/index.tsx:167
msgid "Restore event"
msgstr "Restaurer l'événement"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:50
#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:63
msgid "Return to event page"
msgstr "Retour à la page de l'événement"

#: src/components/layouts/Checkout/index.tsx:165
msgid "Return to Event Page"
msgstr "Retourner à la page de l'événement"

#: src/components/routes/event/EventDashboard/index.tsx:249
msgid "Revenue"
msgstr "Revenu"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Revoke invitation"
msgstr "Révoquer l'invitation"

#: src/components/modals/EditUserModal/index.tsx:93
#: src/components/modals/InviteUserModal/index.tsx:64
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Role"
msgstr "Rôle"

#: src/components/forms/ProductForm/index.tsx:96
#: src/components/forms/ProductForm/index.tsx:391
msgid "Sale End Date"
msgstr "Date de fin de vente"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:75
msgid "Sale ended"
msgstr "Vente terminée"

#: src/components/forms/ProductForm/index.tsx:91
#: src/components/forms/ProductForm/index.tsx:389
msgid "Sale Start Date"
msgstr "Date de début de la vente"

#: src/components/common/ProductPriceAvailability/index.tsx:13
#: src/components/common/ProductPriceAvailability/index.tsx:35
msgid "Sales ended"
msgstr "Ventes terminées"

#: src/components/common/ProductPriceAvailability/index.tsx:19
#: src/components/common/ProductPriceAvailability/index.tsx:40
msgid "Sales start"
msgstr "Début des ventes"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:135
msgid "San Francisco"
msgstr "San Francisco"

#: src/components/common/QuestionAndAnswerList/index.tsx:142
#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:80
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:114
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:96
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:166
#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:94
#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:265
#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:85
msgid "Save"
msgstr "Sauvegarder"

#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/event/HomepageDesigner/index.tsx:166
msgid "Save Changes"
msgstr "Sauvegarder les modifications"

#: src/components/modals/EditOrganizerModal/index.tsx:70
msgid "Save Organizer"
msgstr "Enregistrer l'organisateur"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:90
msgid "Save Settings"
msgstr "Enregistrer les paramètres"

#: src/components/layouts/CheckIn/index.tsx:398
#: src/components/layouts/CheckIn/index.tsx:400
msgid "Scan QR Code"
msgstr "Scanner le code QR"

#: src/components/modals/ShareModal/index.tsx:229
msgid "Scan this QR code to access the event page or share it with others"
msgstr "Scannez ce code QR pour accéder à la page de l'événement ou le partager avec d'autres"

#: src/components/layouts/CheckIn/index.tsx:288
#~ msgid "Seach by name, order #, attendee # or email..."
#~ msgstr "Recherchez par nom, numéro de commande, numéro de participant ou e-mail..."

#: src/components/routes/event/attendees.tsx:64
msgid "Search by attendee name, email or order #..."
msgstr "Recherchez par nom de participant, e-mail ou numéro de commande..."

#: src/components/routes/events/Dashboard/index.tsx:69
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:66
msgid "Search by event name..."
msgstr "Rechercher par nom d'événement..."

#: src/components/routes/event/orders.tsx:126
msgid "Search by name, email, or order #..."
msgstr "Recherchez par nom, e-mail ou numéro de commande..."

#: src/components/layouts/CheckIn/index.tsx:394
msgid "Search by name, order #, attendee # or email..."
msgstr "Rechercher par nom, numéro de commande, numéro de participant ou e-mail..."

#: src/components/routes/event/promo-codes.tsx:34
msgid "Search by name..."
msgstr "Rechercher par nom..."

#: src/components/routes/event/products.tsx:43
#~ msgid "Search by product name..."
#~ msgstr ""

#: src/components/routes/event/messages.tsx:34
msgid "Search by subject or content..."
msgstr "Recherche par sujet ou contenu..."

#: src/components/routes/event/tickets.tsx:43
#~ msgid "Search by ticket name..."
#~ msgstr "Rechercher par nom de billet..."

#: src/components/routes/event/CapacityAssignments/index.tsx:37
msgid "Search capacity assignments..."
msgstr "Rechercher des affectations de capacité..."

#: src/components/routes/event/CheckInLists/index.tsx:37
msgid "Search check-in lists..."
msgstr "Rechercher des listes de pointage..."

#: src/components/routes/event/products.tsx:52
msgid "Search products"
msgstr "Rechercher des produits"

#: src/components/common/SearchBar/index.tsx:36
msgid "Search..."
msgstr "Recherche..."

#: src/components/routes/event/HomepageDesigner/index.tsx:160
msgid "Secondary color"
msgstr "Couleur secondaire"

#: src/components/common/WidgetEditor/index.tsx:198
msgid "Secondary Color"
msgstr "Couleur secondaire"

#: src/components/routes/event/HomepageDesigner/index.tsx:162
msgid "Secondary text color"
msgstr "Couleur du texte secondaire"

#: src/components/common/WidgetEditor/index.tsx:206
msgid "Secondary Text Color"
msgstr "Couleur du texte secondaire"

#: src/components/common/FilterModal/index.tsx:150
msgid "Select {0}"
msgstr "Sélectionner {0}"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:214
msgid "Select Camera"
msgstr "Sélectionnez la caméra"

#: src/components/forms/ProductForm/index.tsx:269
msgid "Select category..."
msgstr "Sélectionner une catégorie..."

#: src/components/forms/WebhookForm/index.tsx:124
msgid "Select event types"
msgstr "Sélectionner les types d'événements"

#: src/components/modals/CreateEventModal/index.tsx:110
msgid "Select organizer"
msgstr "Sélectionnez l'organisateur"

#: src/components/modals/ManageAttendeeModal/index.tsx:118
msgid "Select Product"
msgstr "Sélectionner le produit"

#: src/components/common/ProductSelector/index.tsx:65
msgid "Select Product Tier"
msgstr "Sélectionner le niveau de produit"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:49
#: src/components/modals/SendMessageModal/index.tsx:219
msgid "Select products"
msgstr "Sélectionner des produits"

#: src/components/modals/EditUserModal/index.tsx:104
msgid "Select status"
msgstr "Sélectionnez le statut"

#: src/components/modals/CreateAttendeeModal/index.tsx:150
msgid "Select Ticket"
msgstr "Sélectionnez un billet"

#: src/components/modals/CreateAttendeeModal/index.tsx:163
#: src/components/modals/EditAttendeeModal/index.tsx:117
#~ msgid "Select Ticket Tier"
#~ msgstr "Sélectionnez le niveau de billet"

#: src/components/forms/CheckInListForm/index.tsx:26
#: src/components/modals/SendMessageModal/index.tsx:207
msgid "Select tickets"
msgstr "Sélectionner des billets"

#: src/components/common/ReportTable/index.tsx:261
msgid "Select time period"
msgstr "Sélectionnez la période"

#: src/components/forms/WebhookForm/index.tsx:123
msgid "Select which events will trigger this webhook"
msgstr "Sélectionnez les événements qui déclencheront ce webhook"

#: src/components/forms/ProductForm/index.tsx:352
msgid "Select..."
msgstr "Sélectionner..."

#: src/components/layouts/AuthLayout/index.tsx:68
msgid "Sell Anything"
msgstr "Vendez tout"

#: src/components/layouts/AuthLayout/index.tsx:69
msgid "Sell merchandise alongside tickets with integrated tax and promo code support"
msgstr "Vendez des marchandises avec les billets avec prise en charge intégrée des taxes et des codes promo"

#: src/components/layouts/AuthLayout/index.tsx:42
msgid "Sell More Than Tickets"
msgstr "Vendez plus que des billets"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send"
msgstr "Envoyer"

#: src/components/modals/SendMessageModal/index.tsx:259
msgid "Send a copy to <0>{0}</0>"
msgstr "Envoyer une copie à <0>{0}</0>"

#: src/components/modals/SendMessageModal/index.tsx:139
msgid "Send a message"
msgstr "Envoyer un message"

#: src/components/modals/SendMessageModal/index.tsx:269
msgid "Send as a test. This will send the message to your email address instead of the recipients."
msgstr "Envoyer comme test. Cela enverra le message à votre adresse e-mail au lieu des destinataires."

#: src/components/routes/event/messages.tsx:41
msgid "Send Message"
msgstr "Envoyer un Message"

#: src/components/modals/CreateAttendeeModal/index.tsx:191
#~ msgid "Send order confirmation and product email"
#~ msgstr "Envoyer la confirmation de commande et l'e-mail du produit"

#: src/components/modals/CreateAttendeeModal/index.tsx:192
msgid "Send order confirmation and ticket email"
msgstr "Envoyer la confirmation de commande et l'e-mail du ticket"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send Test"
msgstr "Envoyer le test"

#: src/components/routes/event/Settings/index.tsx:46
msgid "SEO"
msgstr "SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:70
msgid "SEO Description"
msgstr "Descriptif SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:76
msgid "SEO Keywords"
msgstr "Mots-clés SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:56
msgid "SEO Settings"
msgstr "Paramètres de référencement"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:64
msgid "SEO Title"
msgstr "Titre SEO"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "Service Fee"
msgstr "Frais de service"

#: src/components/forms/ProductForm/index.tsx:159
msgid "Set a minimum price and let users pay more if they choose"
msgstr "Fixer un prix minimum et laisser les utilisateurs payer plus s'ils le souhaitent"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:207
msgid "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."
msgstr "Définir le numéro de départ pour la numérotation des factures. Cela ne peut pas être modifié une fois que les factures ont été générées."

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Set up your event"
msgstr "Organisez votre événement"

#: src/components/routes/event/EventDashboard/index.tsx:190
#~ msgid "Set up your payment processing to receive funds from ticket sales."
#~ msgstr "Configurez votre traitement des paiements pour recevoir les fonds des ventes de billets."

#: src/components/routes/event/GettingStarted/index.tsx:183
msgid "Set your event live"
msgstr "Mettez votre événement en direct"

#: src/components/layouts/Event/index.tsx:98
#: src/components/routes/event/Settings/index.tsx:103
msgid "Settings"
msgstr "Paramètres"

#: src/components/layouts/AuthLayout/index.tsx:26
msgid "Setup in Minutes"
msgstr "Configuration en quelques minutes"

#: src/components/common/ShareIcon/index.tsx:28
#: src/components/modals/ShareModal/index.tsx:123
#: src/components/modals/ShareModal/index.tsx:141
msgid "Share"
msgstr "Partager"

#: src/components/layouts/Event/index.tsx:251
#: src/components/modals/ShareModal/index.tsx:116
msgid "Share Event"
msgstr "Partager l'événement"

#: src/components/modals/ShareModal/index.tsx:169
msgid "Share to Facebook"
msgstr "Partager sur Facebook"

#: src/components/modals/ShareModal/index.tsx:163
msgid "Share to LinkedIn"
msgstr "Partager sur LinkedIn"

#: src/components/modals/ShareModal/index.tsx:193
msgid "Share to Pinterest"
msgstr "Partager sur Pinterest"

#: src/components/modals/ShareModal/index.tsx:187
msgid "Share to Reddit"
msgstr "Partager sur Reddit"

#: src/components/modals/ShareModal/index.tsx:148
msgid "Share to Social"
msgstr "Partager sur les réseaux sociaux"

#: src/components/modals/ShareModal/index.tsx:181
msgid "Share to Telegram"
msgstr "Partager sur Telegram"

#: src/components/modals/ShareModal/index.tsx:175
msgid "Share to WhatsApp"
msgstr "Partager sur WhatsApp"

#: src/components/modals/ShareModal/index.tsx:157
msgid "Share to X"
msgstr "Partager sur X"

#: src/components/modals/ShareModal/index.tsx:199
msgid "Share via Email"
msgstr "Partager par e-mail"

#: src/components/forms/ProductForm/index.tsx:344
msgid "Show"
msgstr "Montrer"

#: src/components/forms/ProductForm/index.tsx:403
msgid "Show available product quantity"
msgstr "Afficher la quantité de produit disponible"

#: src/components/forms/TicketForm/index.tsx:348
#~ msgid "Show available ticket quantity"
#~ msgstr "Afficher la quantité de billets disponibles"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Show hidden questions"
msgstr "Afficher les questions masquées"

#: src/components/routes/product-widget/SelectProducts/index.tsx:459
msgid "Show more"
msgstr "Montre plus"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:60
msgid "Show tax and fees separately"
msgstr "Afficher les taxes et les frais séparément"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:76
#~ msgid "Shown to the customer after they checkout, on the order summary page"
#~ msgstr "Présenté au client après son paiement, sur la page récapitulative de la commande"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
msgid "Shown to the customer after they checkout, on the order summary page."
msgstr "Affiché au client après son paiement, sur la page récapitulative de la commande."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:69
msgid "Shown to the customer before they checkout"
msgstr "Montré au client avant son paiement"

#: src/components/forms/QuestionForm/index.tsx:132
msgid "Shows common address fields, including country"
msgstr "Affiche les champs d'adresse courants, y compris le pays"

#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:151
msgid "Simpson"
msgstr "Simpson"

#: src/components/forms/QuestionForm/index.tsx:100
msgid "Single line text box"
msgstr "Zone de texte sur une seule ligne"

#: src/components/routes/welcome/index.tsx:137
msgid "Skip this step"
msgstr "Passer cette étape"

#: src/components/layouts/AuthLayout/index.tsx:78
msgid "Smart Check-in"
msgstr "Enregistrement intelligent"

#: src/components/layouts/AuthLayout/index.tsx:53
#~ msgid "Smart Dashboard"
#~ msgstr "Tableau de bord intelligent"

#: src/components/routes/auth/Register/index.tsx:90
msgid "Smith"
msgstr "Forgeron"

#: src/components/common/ProductPriceAvailability/index.tsx:9
#: src/components/common/ProductPriceAvailability/index.tsx:32
msgid "Sold out"
msgstr "Épuisé"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:67
msgid "Sold Out"
msgstr "Épuisé"

#: src/components/common/ErrorDisplay/index.tsx:14
#: src/components/routes/auth/AcceptInvitation/index.tsx:47
msgid "Something went wrong"
msgstr "Quelque chose s'est mal passé"

#: src/components/common/TaxAndFeeList/index.tsx:39
msgid "Something went wrong while deleting the Tax or Fee"
msgstr "Une erreur s'est produite lors de la suppression de la taxe ou des frais"

#: src/components/routes/auth/ForgotPassword/index.tsx:31
msgid "Something went wrong, please try again, or contact support if the problem persists"
msgstr "Une erreur s'est produite, veuillez réessayer ou contacter le support si le problème persiste"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Something went wrong! Please try again"
msgstr "Quelque chose s'est mal passé ! Veuillez réessayer"

#: src/components/forms/StripeCheckoutForm/index.tsx:69
msgid "Something went wrong."
msgstr "Quelque chose s'est mal passé."

#: src/components/routes/profile/ManageProfile/index.tsx:80
#: src/components/routes/profile/ManageProfile/index.tsx:94
msgid "Something went wrong. Please try again."
msgstr "Quelque chose s'est mal passé. Veuillez réessayer."

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:61
msgid "Sorry, something has gone wrong. Please restart the checkout process."
msgstr "Désolé, quelque chose s'est mal passé. Veuillez redémarrer le processus de paiement."

#: src/components/routes/product-widget/CollectInformation/index.tsx:259
msgid "Sorry, something went wrong loading this page."
msgstr "Désolé, une erreur s'est produite lors du chargement de cette page."

#: src/components/routes/product-widget/CollectInformation/index.tsx:247
msgid "Sorry, this order no longer exists."
msgstr "Désolé, cette commande n'existe plus."

#: src/components/routes/product-widget/SelectProducts/index.tsx:246
msgid "Sorry, this promo code is not recognized"
msgstr "Désolé, ce code promo n'est pas reconnu"

#: src/components/layouts/Checkout/index.tsx:26
#~ msgid "Sorry, your order has expired. Please start a new order."
#~ msgstr "Désolé, votre commande a expiré. Veuillez démarrer une nouvelle commande."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Sorting is disabled while filters and sorting are applied"
#~ msgstr "Le tri est désactivé pendant que les filtres et le tri sont appliqués"

#: src/components/common/LanguageSwitcher/index.tsx:18
msgid "Spanish"
msgstr "Espagnol"

#: src/components/forms/ProductForm/index.tsx:147
msgid "Standard product with a fixed price"
msgstr "Produit standard avec un prix fixe"

#: src/components/forms/TicketForm/index.tsx:129
#~ msgid "Standard ticket with a fixed price"
#~ msgstr "Billet standard à prix fixe"

#: src/components/modals/CreateEventModal/index.tsx:144
#: src/components/modals/DuplicateEventModal/index.tsx:93
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:87
#: src/components/routes/welcome/index.tsx:90
msgid "Start Date"
msgstr "Date de début"

#: src/components/common/CheckoutQuestion/index.tsx:165
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:139
#: src/components/routes/product-widget/CollectInformation/index.tsx:339
#: src/components/routes/product-widget/CollectInformation/index.tsx:340
msgid "State or Region"
msgstr "État ou région"

#: src/components/common/AttendeeTable/index.tsx:112
#: src/components/common/OrderDetails/index.tsx:51
#: src/components/common/OrdersTable/index.tsx:209
#: src/components/common/ProductsTable/SortableProduct/index.tsx:222
#: src/components/common/WebhookTable/index.tsx:238
#: src/components/forms/CapaciyAssigmentForm/index.tsx:56
#: src/components/forms/WebhookForm/index.tsx:133
#: src/components/modals/EditUserModal/index.tsx:103
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:88
msgid "Status"
msgstr "Statut"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:85
msgid "Stripe"
msgstr "Stripe"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:48
msgid "Stripe payments are not enabled for this event."
msgstr "Les paiements Stripe ne sont pas activés pour cet événement."

#: src/components/modals/SendMessageModal/index.tsx:245
msgid "Subject"
msgstr "Sujet"

#: src/components/common/OrderSummary/index.tsx:41
msgid "Subtotal"
msgstr "Total"

#: src/components/common/OrdersTable/index.tsx:115
#: src/components/layouts/Checkout/index.tsx:53
msgid "Success"
msgstr "Succès"

#: src/components/modals/EditUserModal/index.tsx:40
#: src/components/modals/InviteUserModal/index.tsx:32
msgid "Success! {0} will receive an email shortly."
msgstr "Succès! {0} recevra un e-mail sous peu."

#: src/components/common/AttendeeTable/index.tsx:90
msgid "Successfully {0} attendee"
msgstr "{0} participant a réussi"

#: src/components/common/AttendeeCheckInTable/index.tsx:47
#: src/components/common/AttendeeCheckInTable/index.tsx:71
#~ msgid "Successfully checked <0>{0} {1}</0> {2}"
#~ msgstr "Vérification réussie <0>{0} {1}</0> {2}"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:23
msgid "Successfully confirmed email address"
msgstr "Adresse e-mail confirmée avec succès"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:31
msgid "Successfully confirmed email change"
msgstr "Changement d'e-mail confirmé avec succès"

#: src/components/modals/CreateAttendeeModal/index.tsx:82
msgid "Successfully created attendee"
msgstr "Participant créé avec succès"

#: src/components/modals/CreateProductModal/index.tsx:54
msgid "Successfully Created Product"
msgstr "Produit créé avec succès"

#: src/components/modals/CreatePromoCodeModal/index.tsx:39
msgid "Successfully Created Promo Code"
msgstr "Code promotionnel créé avec succès"

#: src/components/modals/CreateQuestionModal/index.tsx:45
#: src/components/modals/EditQuestionModal/index.tsx:66
msgid "Successfully Created Question"
msgstr "Question créée avec succès"

#: src/components/modals/CreateTicketModal/index.tsx:50
#~ msgid "Successfully Created Ticket"
#~ msgstr "Ticket créé avec succès"

#: src/components/modals/DuplicateProductModal/index.tsx:95
msgid "Successfully Duplicated Product"
msgstr "Produit dupliqué avec succès"

#: src/components/modals/ManageAttendeeModal/index.tsx:96
msgid "Successfully updated attendee"
msgstr "Participant mis à jour avec succès"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:47
msgid "Successfully updated Capacity Assignment"
msgstr "Affectation de Capacité mise à jour avec succès"

#: src/components/modals/EditCheckInListModal/index.tsx:49
msgid "Successfully updated Check-In List"
msgstr "Liste de pointage mise à jour avec succès"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:43
msgid "Successfully Updated Email Settings"
msgstr "Paramètres de messagerie mis à jour avec succès"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:54
msgid "Successfully Updated Event"
msgstr "Événement mis à jour avec succès"

#: src/components/routes/event/HomepageDesigner/index.tsx:84
msgid "Successfully Updated Homepage Design"
msgstr "Conception de la page d'accueil mise à jour avec succès"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:50
msgid "Successfully Updated Homepage Settings"
msgstr "Paramètres de la page d'accueil mis à jour avec succès"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:73
msgid "Successfully Updated Location"
msgstr "Emplacement mis à jour avec succès"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:43
msgid "Successfully Updated Misc Settings"
msgstr "Paramètres divers mis à jour avec succès"

#: src/components/modals/ManageOrderModal/index.tsx:74
msgid "Successfully updated order"
msgstr "Commande mise à jour avec succès"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:74
msgid "Successfully Updated Payment & Invoicing Settings"
msgstr "Paramètres de paiement et de facturation mis à jour avec succès"

#: src/components/modals/EditProductModal/index.tsx:89
msgid "Successfully updated product"
msgstr "Produit mis à jour avec succès"

#: src/components/modals/EditPromoCodeModal/index.tsx:52
msgid "Successfully Updated Promo Code"
msgstr "Code promotionnel mis à jour avec succès"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:45
msgid "Successfully Updated Seo Settings"
msgstr "Paramètres de référencement mis à jour avec succès"

#: src/components/modals/EditTicketModal/index.tsx:85
#~ msgid "Successfully updated ticket"
#~ msgstr "Billet mis à jour avec succès"

#: src/components/modals/EditWebhookModal/index.tsx:44
msgid "Successfully updated Webhook"
msgstr "Webhook mis à jour avec succès"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:128
msgid "Suite 100"
msgstr "Suite 100"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
msgid "Support Email"
msgstr "E-mail d'assistance"

#: src/components/layouts/AuthLayout/index.tsx:59
msgid "Support for tiered, donation-based, and product sales with customizable pricing and capacity"
msgstr "Prise en charge des ventes échelonnées, basées sur les dons et de produits avec des prix et des capacités personnalisables"

#: src/components/forms/ProductForm/index.tsx:254
msgid "T-shirt"
msgstr "T-shirt"

#: src/components/forms/TaxAndFeeForm/index.tsx:12
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Tax"
msgstr "Impôt"

#: src/components/routes/account/ManageAccount/index.tsx:27
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:16
msgid "Tax & Fees"
msgstr "Taxes et frais"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:243
msgid "Tax Details"
msgstr "Détails fiscaux"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:244
msgid "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"
msgstr "Informations fiscales à apparaître en bas de toutes les factures (par exemple, numéro de TVA, enregistrement fiscal)"

#: src/components/common/TaxAndFeeList/index.tsx:36
msgid "Tax or Fee deleted successfully"
msgstr "Taxe ou frais supprimés avec succès"

#: src/components/forms/ProductForm/index.tsx:363
msgid "Taxes"
msgstr "Impôts"

#: src/components/forms/ProductForm/index.tsx:351
#: src/components/forms/ProductForm/index.tsx:358
msgid "Taxes and Fees"
msgstr "Taxes et frais"

#: src/components/routes/product-widget/SelectProducts/index.tsx:138
#: src/components/routes/product-widget/SelectProducts/index.tsx:186
msgid "That promo code is invalid"
msgstr "Ce code promotionnel n'est pas valide"

#: src/components/layouts/CheckIn/index.tsx:316
msgid "The check-in list you are looking for does not exist."
msgstr "La liste de pointage que vous recherchez n'existe pas."

#: src/components/forms/OrganizerForm/index.tsx:47
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:75
msgid "The default currency for your events."
msgstr "La devise par défaut de vos événements."

#: src/components/forms/OrganizerForm/index.tsx:56
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:84
msgid "The default timezone for your events."
msgstr "Le fuseau horaire par défaut pour vos événements."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:16
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:43
msgid "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."
msgstr "L'événement que vous recherchez n'est pas disponible pour le moment. Il a peut-être été supprimé, expiré ou l'URL est incorrecte."

#: src/components/modals/CreateAttendeeModal/index.tsx:146
msgid "The language the attendee will receive emails in."
msgstr "La langue dans laquelle le participant recevra ses courriels."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:22
msgid "The link you clicked is invalid."
msgstr "Le lien sur lequel vous avez cliqué n'est pas valide."

#: src/components/routes/product-widget/SelectProducts/index.tsx:444
msgid "The maximum number of products for {0}is {1}"
msgstr "Le nombre maximum de produits pour {0} est {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:351
#~ msgid "The maximum numbers number of tickets for {0}is {1}"
#~ msgstr "Le nombre maximum de billets pour {0} est {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:318
#~ msgid "The maximum numbers number of tickets for Generals is {0}"
#~ msgstr "Le nombre maximum de billets pour les généraux est de {0}."

#: src/components/common/ErrorDisplay/index.tsx:17
msgid "The page you are looking for does not exist"
msgstr "La page que vous recherchez n'existe pas"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:56
msgid "The price displayed to the customer will include taxes and fees."
msgstr "Le prix affiché au client comprendra les taxes et frais."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:62
msgid "The price displayed to the customer will not include taxes and fees. They will be shown separately"
msgstr "Le prix affiché au client ne comprendra pas les taxes et frais. Ils seront présentés séparément"

#: src/components/common/WidgetEditor/index.tsx:169
msgid "The styling settings you choose apply only to copied HTML and won't be stored."
msgstr "Les paramètres de style que vous choisissez s'appliquent uniquement au code HTML copié et ne seront pas stockés."

#: src/components/forms/ProductForm/index.tsx:355
msgid "The taxes and fees to apply to this product. You can create new taxes and fees on the"
msgstr "Les taxes et frais à appliquer à ce produit. Vous pouvez créer de nouvelles taxes et frais sur le"

#: src/components/forms/TicketForm/index.tsx:300
#~ msgid "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "Les taxes et frais à appliquer sur ce billet. Vous pouvez créer de nouvelles taxes et frais sur le"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:63
msgid "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"
msgstr "Le titre de l'événement qui sera affiché dans les résultats des moteurs de recherche et lors du partage sur les réseaux sociaux. Par défaut, le titre de l'événement sera utilisé"

#: src/components/routes/product-widget/SelectProducts/index.tsx:272
msgid "There are no products available for this event"
msgstr "Il n'y a pas de produits disponibles pour cet événement"

#: src/components/routes/product-widget/SelectProducts/index.tsx:375
msgid "There are no products available in this category"
msgstr "Il n'y a pas de produits disponibles dans cette catégorie"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:260
#~ msgid "There are no tickets available for this event"
#~ msgstr "Il n'y a pas de billets disponibles pour cet événement"

#: src/components/modals/RefundOrderModal/index.tsx:137
msgid "There is a refund pending. Please wait for it to complete before requesting another refund."
msgstr "Un remboursement est en attente. Veuillez attendre qu'il soit terminé avant de demander un autre remboursement."

#: src/components/common/OrdersTable/index.tsx:80
msgid "There was an error marking the order as paid"
msgstr "Une erreur est survenue lors du marquage de la commande comme payée"

#: src/hooks/useFormErrorResponseHandler.tsx:28
msgid "There was an error processing your request. Please try again."
msgstr "Il y a eu une erreur lors du traitement de votre demande. Veuillez réessayer."

#: src/components/common/OrdersTable/index.tsx:95
msgid "There was an error sending your message"
msgstr "Une erreur est survenue lors de l'envoi de votre message"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:105
msgid "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."
msgstr "Ces détails ne seront affichés que si la commande est terminée avec succès. Les commandes en attente de paiement n'afficheront pas ce message."

#: src/components/layouts/CheckIn/index.tsx:201
msgid "This attendee has an unpaid order."
msgstr "Ce participant a une commande impayée."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:61
msgid "This category doesn't have any products yet."
msgstr "Cette catégorie n'a pas encore de produits."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:112
msgid "This category is hidden from public view"
msgstr "Cette catégorie est masquée de la vue publique"

#: src/components/common/CheckInListList/index.tsx:75
msgid "This check-in list has expired"
msgstr "Cette liste de pointage a expiré"

#: src/components/layouts/CheckIn/index.tsx:331
msgid "This check-in list has expired and is no longer available for check-ins."
msgstr "Cette liste de pointage a expiré et n'est plus disponible pour les enregistrements."

#: src/components/common/CheckInListList/index.tsx:82
msgid "This check-in list is active"
msgstr "Cette liste de pointage est active"

#: src/components/common/CheckInListList/index.tsx:79
msgid "This check-in list is not active yet"
msgstr "Cette liste de pointage n'est pas encore active"

#: src/components/layouts/CheckIn/index.tsx:348
msgid "This check-in list is not yet active and is not available for check-ins."
msgstr "Cette liste de pointage n'est pas encore active et n'est pas disponible pour les enregistrements."

#: src/components/forms/CheckInListForm/index.tsx:37
msgid "This description will be shown to the check-in staff"
msgstr "Cette description sera affichée au personnel de pointage"

#: src/components/modals/SendMessageModal/index.tsx:285
msgid "This email is not promotional and is directly related to the event."
msgstr "Cet email n'est pas promotionnel et est directement lié à l'événement."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:8
#~ msgid "This event is not available at the moment. Please check back later."
#~ msgstr "Cet événement n'est pas disponible pour le moment. Veuillez revenir plus tard."

#: src/components/layouts/EventHomepage/index.tsx:49
#~ msgid "This event is not available."
#~ msgstr "Cet événement n'est pas disponible."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
#~ msgid "This information will be shown on the payment page, order summary page, and"
#~ msgstr "Ces informations seront affichées sur la page de paiement, la page de résumé de commande et l'e-mail de confirmation de commande."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
msgid "This information will be shown on the payment page, order summary page, and order confirmation email."
msgstr "Ces informations seront affichées sur la page de paiement, la page de résumé de commande et l'e-mail de confirmation de commande."

#: src/components/forms/ProductForm/index.tsx:138
msgid "This is a general product, like a t-shirt or a mug. No ticket will be issued"
msgstr "C'est un produit général, comme un t-shirt ou une tasse. Aucun billet ne sera délivré"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:91
msgid "This is an online event"
msgstr "Ceci est un événement en ligne"

#: src/components/forms/CheckInListForm/index.tsx:57
msgid "This list will no longer be available for check-ins after this date"
msgstr "Cette liste ne sera plus disponible pour les enregistrements après cette date"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:68
msgid "This message will be included in the footer of all emails sent from this event"
msgstr "Ce message sera inclus dans le pied de page de tous les e-mails envoyés à partir de cet événement"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
msgid "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"
msgstr "Ce message ne sera affiché que si la commande est terminée avec succès. Les commandes en attente de paiement n'afficheront pas ce message."

#: src/components/forms/StripeCheckoutForm/index.tsx:93
msgid "This order has already been paid."
msgstr "Cette commande a déjà été payée."

#: src/components/modals/RefundOrderModal/index.tsx:141
msgid "This order has already been refunded."
msgstr "Cette commande a déjà été remboursée."

#: src/components/routes/product-widget/CollectInformation/index.tsx:237
msgid "This order has been cancelled"
msgstr "Cette commande a été annulée"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:209
msgid "This order has been cancelled."
msgstr "Cette commande a été annulée."

#: src/components/routes/product-widget/CollectInformation/index.tsx:210
msgid "This order has expired. Please start again."
msgstr "Cette commande a expiré. Veuillez recommencer."

#: src/components/routes/product-widget/CollectInformation/index.tsx:221
msgid "This order is awaiting payment"
msgstr "Cette commande est en attente de paiement"

#: src/components/routes/product-widget/CollectInformation/index.tsx:229
msgid "This order is complete"
msgstr "Cette commande est terminée"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:211
msgid "This order is complete."
msgstr "Cette commande est terminée."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:204
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:207
msgid "This order is processing."
msgstr "Cette commande est en cours de traitement."

#: src/components/forms/StripeCheckoutForm/index.tsx:103
msgid "This order page is no longer available."
msgstr "Cette page de commande n'est plus disponible."

#: src/components/forms/ProductForm/index.tsx:415
msgid "This overrides all visibility settings and will hide the product from all customers."
msgstr "Cela remplace tous les paramètres de visibilité et masquera le produit de tous les clients."

#: src/components/forms/TicketForm/index.tsx:360
#~ msgid "This overrides all visibility settings and will hide the ticket from all customers."
#~ msgstr "Cela remplace tous les paramètres de visibilité et masquera le ticket à tous les clients."

#: src/components/common/ProductsTable/SortableProduct/index.tsx:59
msgid "This product cannot be deleted because it is associated with an order. You can hide it instead."
msgstr "Ce produit ne peut pas être supprimé car il est associé à une commande. Vous pouvez le masquer à la place."

#: src/components/forms/ProductForm/index.tsx:132
msgid "This product is a ticket. Buyers will be issued a ticket upon purchase"
msgstr "Ce produit est un billet. Les acheteurs recevront un billet lors de l'achat"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:215
msgid "This product is hidden from public view"
msgstr "Ce produit est masqué de la vue publique"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:216
msgid "This product is hidden unless targeted by a Promo Code"
msgstr "Ce produit est masqué sauf s'il est ciblé par un code promo"

#: src/components/common/QuestionsTable/index.tsx:90
msgid "This question is only visible to the event organizer"
msgstr "Cette question n'est visible que par l'organisateur de l'événement"

#: src/components/routes/auth/ResetPassword/index.tsx:29
msgid "This reset password link is invalid or expired."
msgstr "Ce lien de réinitialisation du mot de passe est invalide ou a expiré."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:56
#~ msgid ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."
#~ msgstr ""
#~ "Ce ticket ne peut pas être supprimé car il est\n"
#~ "associé à une commande. Vous pouvez le cacher à la place."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:131
#~ msgid "This ticket is hidden from public view"
#~ msgstr "Ce ticket est masqué à la vue du public"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:132
#~ msgid "This ticket is hidden unless targeted by a Promo Code"
#~ msgstr "Ce ticket est masqué sauf s'il est ciblé par un code promotionnel"

#: src/components/modals/EditUserModal/index.tsx:65
msgid "This user is not active, as they have not accepted their invitation."
msgstr "Cet utilisateur n'est pas actif car il n'a pas accepté son invitation."

#: src/components/common/AttendeeTable/index.tsx:182
msgid "ticket"
msgstr "billet"

#: src/components/common/AttendeeTable/index.tsx:111
#: src/components/forms/ProductForm/index.tsx:130
#: src/components/modals/CreateAttendeeModal/index.tsx:151
msgid "Ticket"
msgstr "Billet"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:52
#~ msgid "Ticket deleted successfully"
#~ msgstr "Billet supprimé avec succès"

#: src/components/common/AttendeeTable/index.tsx:49
msgid "Ticket email has been resent to attendee"
msgstr "L'e-mail du ticket a été renvoyé au participant"

#: src/components/common/MessageList/index.tsx:22
msgid "Ticket holders"
msgstr "Détenteurs de billets"

#: src/components/routes/event/products.tsx:86
msgid "Ticket or Product"
msgstr "Billet ou produit"

#: src/components/routes/event/EventDashboard/index.tsx:53
#~ msgid "Ticket Sales"
#~ msgstr "La vente de billets"

#: src/components/modals/CreateAttendeeModal/index.tsx:161
#: src/components/modals/EditAttendeeModal/index.tsx:115
#~ msgid "Ticket Tier"
#~ msgstr "Niveau de billet"

#: src/components/forms/TicketForm/index.tsx:189
#: src/components/forms/TicketForm/index.tsx:196
#~ msgid "Ticket Type"
#~ msgstr "Type de billet"

#: src/components/common/WidgetEditor/index.tsx:311
#~ msgid "Ticket Widget Preview"
#~ msgstr "Aperçu du widget de billet"

#: src/components/common/PromoCodeTable/index.tsx:147
#~ msgid "Ticket(s)"
#~ msgstr "Des billets)"

#: src/components/common/PromoCodeTable/index.tsx:71
#: src/components/layouts/Event/index.tsx:40
#: src/components/layouts/EventHomepage/index.tsx:80
#: src/components/routes/event/tickets.tsx:39
#~ msgid "Tickets"
#~ msgstr "Des billets"

#: src/components/layouts/Event/index.tsx:101
#: src/components/routes/event/products.tsx:46
msgid "Tickets & Products"
msgstr "Billets et produits"

#: src/components/routes/product-widget/PrintOrder/index.tsx:43
msgid "Tickets for"
msgstr "Billets pour"

#: src/components/common/EventCard/index.tsx:126
#~ msgid "tickets sold"
#~ msgstr "billets vendus"

#: src/components/common/StatBoxes/index.tsx:21
#~ msgid "Tickets sold"
#~ msgstr "Billets vendus"

#: src/components/routes/event/EventDashboard/index.tsx:73
#~ msgid "Tickets Sold"
#~ msgstr "Billets vendus"

#: src/components/common/TicketsTable/index.tsx:44
#~ msgid "Tickets sorted successfully"
#~ msgstr "Billets triés avec succès"

#: src/components/forms/ProductForm/index.tsx:67
msgid "Tier {0}"
msgstr "Niveau {0}"

#: src/components/forms/ProductForm/index.tsx:163
msgid "Tiered Product"
msgstr "Produit par paliers"

#: src/components/forms/ProductForm/index.tsx:240
#~ msgid ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""

#: src/components/forms/ProductForm/index.tsx:243
msgid "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."
msgstr "Les produits à niveaux vous permettent de proposer plusieurs options de prix pour le même produit. C'est parfait pour les produits en prévente ou pour proposer différentes options de prix à différents groupes de personnes.\" # fr"

#: src/components/forms/TicketForm/index.tsx:145
#~ msgid "Tiered Ticket"
#~ msgstr "Billet à plusieurs niveaux"

#: src/components/forms/TicketForm/index.tsx:203
#~ msgid ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""
#~ "Les billets à plusieurs niveaux vous permettent de proposer plusieurs options de prix pour le même billet.\n"
#~ "C'est parfait pour les billets anticipés ou pour offrir des prix différents.\n"
#~ "options pour différents groupes de personnes."

#: src/components/layouts/Checkout/index.tsx:90
msgid "Time left:"
msgstr "Temps restant :"

#: src/components/common/PromoCodeTable/index.tsx:71
msgid "Times used"
msgstr "Temps utilisés"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:25
msgid "Times Used"
msgstr "Nombre d'utilisations"

#: src/components/forms/OrganizerForm/index.tsx:54
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:82
#: src/components/routes/auth/AcceptInvitation/index.tsx:105
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:109
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:181
#: src/components/routes/profile/ManageProfile/index.tsx:161
msgid "Timezone"
msgstr "Fuseau horaire"

#: src/components/forms/PromoCodeForm/index.tsx:36
msgid "TIP"
msgstr "CONSEIL"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:206
msgid "Title"
msgstr "Titre"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:182
msgid "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."
msgstr "Pour recevoir des paiements par carte bancaire, vous devez connecter votre compte Stripe. Stripe est notre partenaire de traitement des paiements qui garantit des transactions sécurisées et des paiements rapides."

#: src/components/layouts/Event/index.tsx:108
msgid "Tools"
msgstr "Outils"

#: src/components/common/OrderSummary/index.tsx:92
msgid "Total"
msgstr "Total"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:52
msgid "Total Before Discounts"
msgstr "Total avant réductions"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:58
msgid "Total Discount Amount"
msgstr "Montant total de la réduction"

#: src/components/routes/event/EventDashboard/index.tsx:273
msgid "Total Fees"
msgstr "Total des frais"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:46
msgid "Total Gross Sales"
msgstr "Total des ventes brutes"

#: src/components/common/OrderDetails/index.tsx:59
#: src/components/modals/RefundOrderModal/index.tsx:74
msgid "Total order amount"
msgstr "Montant total de la commande"

#: src/components/common/OrderDetails/index.tsx:67
#: src/components/modals/RefundOrderModal/index.tsx:82
msgid "Total refunded"
msgstr "Total remboursé"

#: src/components/routes/event/EventDashboard/index.tsx:276
msgid "Total Refunded"
msgstr "Total remboursé"

#: src/components/modals/RefundOrderModal/index.tsx:90
msgid "Total remaining"
msgstr "Total restant"

#: src/components/routes/event/EventDashboard/index.tsx:275
msgid "Total Tax"
msgstr "Taxe total"

#: src/components/layouts/AuthLayout/index.tsx:54
msgid "Track revenue, page views, and sales with detailed analytics and exportable reports"
msgstr "Suivez les revenus, les vues de page et les ventes avec des analyses détaillées et des rapports exportables"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
msgid "Transaction Fee:"
msgstr "Frais de transaction :"

#: src/components/forms/TaxAndFeeForm/index.tsx:44
msgid "Type"
msgstr "Taper"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "Unable to {0} attendee"
#~ msgstr "Impossible d'accéder à {0} participant"

#: src/components/layouts/CheckIn/index.tsx:93
msgid "Unable to check in attendee"
msgstr "Impossible d'enregistrer le participant"

#: src/components/layouts/CheckIn/index.tsx:114
msgid "Unable to check out attendee"
msgstr "Impossible de sortir le participant"

#: src/components/modals/CreateProductModal/index.tsx:63
msgid "Unable to create product. Please check the your details"
msgstr "Impossible de créer le produit. Veuillez vérifier vos détails"

#: src/components/routes/product-widget/SelectProducts/index.tsx:123
msgid "Unable to create product. Please check your details"
msgstr "Impossible de créer le produit. Veuillez vérifier vos détails"

#: src/components/modals/CreateQuestionModal/index.tsx:60
msgid "Unable to create question. Please check the your details"
msgstr "Impossible de créer une question. Veuillez vérifier vos coordonnées"

#: src/components/modals/CreateTicketModal/index.tsx:65
#: src/components/routes/ticket-widget/SelectTickets/index.tsx:117
#~ msgid "Unable to create ticket. Please check the your details"
#~ msgstr "Impossible de créer un ticket. Veuillez vérifier vos coordonnées"

#: src/components/modals/DuplicateProductModal/index.tsx:103
msgid "Unable to duplicate product. Please check the your details"
msgstr "Impossible de dupliquer le produit. Veuillez vérifier vos informations"

#: src/components/layouts/CheckIn/index.tsx:145
msgid "Unable to fetch attendee"
msgstr "Impossible de récupérer le participant"

#: src/components/modals/EditQuestionModal/index.tsx:84
msgid "Unable to update question. Please check the your details"
msgstr "Impossible de mettre à jour la question. Veuillez vérifier vos coordonnées"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:30
msgid "Unique Customers"
msgstr "Clients uniques"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:153
msgid "United States"
msgstr "États-Unis"

#: src/components/common/QuestionAndAnswerList/index.tsx:284
msgid "Unknown Attendee"
msgstr "Participant inconnu"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:43
#: src/components/forms/ProductForm/index.tsx:83
#: src/components/forms/ProductForm/index.tsx:299
#: src/components/forms/PromoCodeForm/index.tsx:82
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:78
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:84
msgid "Unlimited"
msgstr "Illimité"

#: src/components/routes/product-widget/SelectProducts/index.tsx:407
msgid "Unlimited available"
msgstr "Disponible illimité"

#: src/components/common/PromoCodeTable/index.tsx:126
msgid "Unlimited usages allowed"
msgstr "Utilisations illimitées autorisées"

#: src/components/layouts/CheckIn/index.tsx:200
msgid "Unpaid Order"
msgstr "Commande impayée"

#: src/components/routes/event/EventDashboard/index.tsx:170
msgid "Unpublish Event"
msgstr "Annuler la publication de l'événement"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:20
#: src/components/common/EventStatusBadge/index.tsx:16
msgid "Upcoming"
msgstr "A venir"

#: src/components/routes/events/Dashboard/index.tsx:55
msgid "Upcoming Events"
msgstr "Événements à venir"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Update {0}"
msgstr "Mettre à jour {0}"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:66
msgid "Update event name, description and dates"
msgstr "Mettre à jour le nom, la description et les dates de l'événement"

#: src/components/routes/profile/ManageProfile/index.tsx:177
msgid "Update profile"
msgstr "Mettre à jour le profil"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Upload Cover"
msgstr "Télécharger la couverture"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:105
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:152
msgid "Upload Image"
msgstr "Téléverser l’image"

#: src/components/common/WebhookTable/index.tsx:236
#: src/components/modals/ShareModal/index.tsx:129
msgid "URL"
msgstr "URL"

#: src/components/common/PromoCodeTable/index.tsx:175
msgid "URL copied to clipboard"
msgstr "URL copié dans le presse-papiers"

#: src/components/modals/CreateWebhookModal/index.tsx:25
msgid "URL is required"
msgstr "L'URL est requise"

#: src/components/common/WidgetEditor/index.tsx:298
msgid "Usage Example"
msgstr "Exemple d'utilisation"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:76
msgid "Usage Limit"
msgstr "Limite d'utilisation"

#: src/components/routes/event/HomepageDesigner/index.tsx:139
msgid "Use a blurred version of the cover image as the background"
msgstr "Utilisez une version floue de l'image de couverture comme arrière-plan"

#: src/components/routes/event/HomepageDesigner/index.tsx:137
msgid "Use cover image"
msgstr "Utiliser l'image de couverture"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User"
msgstr "Utilisateur"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User Management"
msgstr "Gestion des utilisateurs"

#: src/components/routes/account/ManageAccount/index.tsx:32
msgid "Users"
msgstr "Utilisateurs"

#: src/components/modals/EditUserModal/index.tsx:81
msgid "Users can change their email in <0>Profile Settings</0>"
msgstr "Les utilisateurs peuvent modifier leur adresse e-mail dans <0>Paramètres du profil</0>."

#: src/components/forms/OrganizerForm/index.tsx:55
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:83
#: src/components/routes/auth/AcceptInvitation/index.tsx:106
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:110
#: src/components/routes/profile/ManageProfile/index.tsx:162
msgid "UTC"
msgstr "UTC"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "VAT"
msgstr "T.V.A."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:116
msgid "Venue Name"
msgstr "nom de la place"

#: src/components/common/LanguageSwitcher/index.tsx:34
msgid "Vietnamese"
msgstr "Vietnamien"

#: src/components/modals/ManageAttendeeModal/index.tsx:220
#: src/components/modals/ManageOrderModal/index.tsx:200
msgid "View"
msgstr "Voir"

#: src/components/routes/event/Reports/index.tsx:38
msgid "View and download reports for your event. Please note, only completed orders are included in these reports."
msgstr "Consultez et téléchargez les rapports de votre événement. Veuillez noter que seuls les commandes complétées sont incluses dans ces rapports."

#: src/components/common/AttendeeList/index.tsx:84
msgid "View Answers"
msgstr "Voir les réponses"

#: src/components/common/AttendeeTable/index.tsx:164
#~ msgid "View attendee"
#~ msgstr "Afficher le participant"

#: src/components/common/AttendeeList/index.tsx:103
msgid "View Attendee Details"
msgstr "Voir les détails de l'invité"

#: src/components/layouts/Checkout/index.tsx:55
#~ msgid "View event homepage"
#~ msgstr "Afficher la page d'accueil de l'événement"

#: src/components/common/EventCard/index.tsx:145
msgid "View event page"
msgstr "Voir la page de l'événement"

#: src/components/common/MessageList/index.tsx:59
msgid "View full message"
msgstr "Afficher le message complet"

#: src/components/common/WebhookTable/index.tsx:113
msgid "View logs"
msgstr "Voir les journaux"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View map"
msgstr "Voir la carte"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View on Google Maps"
msgstr "Afficher sur Google Maps"

#: src/components/common/OrdersTable/index.tsx:111
#~ msgid "View order"
#~ msgstr "Voir l'ordre"

#: src/components/forms/StripeCheckoutForm/index.tsx:94
#: src/components/forms/StripeCheckoutForm/index.tsx:104
#: src/components/routes/product-widget/CollectInformation/index.tsx:231
msgid "View order details"
msgstr "Voir d'autres détails"

#: src/components/forms/CheckInListForm/index.tsx:21
msgid "VIP check-in list"
msgstr "Liste de pointage VIP"

#: src/components/forms/ProductForm/index.tsx:254
#~ msgid "VIP Product"
#~ msgstr "Produit VIP"

#: src/components/forms/ProductForm/index.tsx:254
msgid "VIP Ticket"
msgstr "Billet VIP"

#: src/components/forms/ProductForm/index.tsx:394
msgid "Visibility"
msgstr "Visibilité"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:77
msgid "We could not process your payment. Please try again or contact support."
msgstr "Nous n'avons pas pu traiter votre paiement. Veuillez réessayer ou contacter l'assistance."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:53
msgid "We couldn't delete the category. Please try again."
msgstr "Nous n'avons pas pu supprimer la catégorie. Veuillez réessayer."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:24
msgid "We couldn't find any tickets matching {0}"
msgstr "Nous n'avons trouvé aucun billet correspondant à {0}"

#: src/components/common/ErrorLoadingMessage/index.tsx:13
msgid "We couldn't load the data. Please try again."
msgstr "Nous n'avons pas pu charger les données. Veuillez réessayer."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:94
msgid "We couldn't reorder the categories. Please try again."
msgstr "Nous n'avons pas pu réorganiser les catégories. Veuillez réessayer."

#: src/components/routes/event/HomepageDesigner/index.tsx:118
msgid "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"
msgstr "Nous recommandons des dimensions de 2 160 px sur 1 080 px et une taille de fichier maximale de 5 Mo."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:42
#~ msgid "We use Stripe to process payments. Connect your Stripe account to start receiving payments."
#~ msgstr "Nous utilisons Stripe pour traiter les paiements. Connectez votre compte Stripe pour commencer à recevoir des paiements."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:84
msgid "We were unable to confirm your payment. Please try again or contact support."
msgstr "Nous n'avons pas pu confirmer votre paiement. Veuillez réessayer ou contacter l'assistance."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:79
msgid "We're processing your order. Please wait..."
msgstr "Nous traitons votre commande. S'il vous plaît, attendez..."

#: src/components/modals/CreateWebhookModal/index.tsx:46
msgid "Webhook created successfully"
msgstr "Webhook créé avec succès"

#: src/components/common/WebhookTable/index.tsx:53
msgid "Webhook deleted successfully"
msgstr "Webhook supprimé avec succès"

#: src/components/modals/WebhookLogsModal/index.tsx:151
msgid "Webhook Logs"
msgstr "Journaux du Webhook"

#: src/components/forms/WebhookForm/index.tsx:117
msgid "Webhook URL"
msgstr "URL du Webhook"

#: src/components/forms/WebhookForm/index.tsx:27
msgid "Webhook will not send notifications"
msgstr "Le webhook n'enverra pas de notifications"

#: src/components/forms/WebhookForm/index.tsx:21
msgid "Webhook will send notifications"
msgstr "Le webhook enverra des notifications"

#: src/components/layouts/Event/index.tsx:111
#: src/components/routes/event/Webhooks/index.tsx:30
msgid "Webhooks"
msgstr "Webhooks"

#: src/components/routes/auth/AcceptInvitation/index.tsx:76
msgid "Welcome aboard! Please login to continue."
msgstr "Bienvenue à bord! Merci de vous connecter pour continuer."

#: src/components/routes/auth/Login/index.tsx:55
msgid "Welcome back 👋"
msgstr "Bon retour 👋"

#: src/components/routes/event/EventDashboard/index.tsx:95
msgid "Welcome back{0} 👋"
msgstr "Bon retour{0} 👋"

#: src/components/routes/auth/Register/index.tsx:67
msgid "Welcome to Hi.Events 👋"
msgstr "Bienvenue sur Hi.Events 👋"

#: src/components/routes/welcome/index.tsx:125
msgid "Welcome to Hi.Events, {0} 👋"
msgstr "Bienvenue sur Hi.Events, {0} 👋"

#: src/components/forms/ProductForm/index.tsx:242
msgid "What are Tiered Products?"
msgstr "Quels sont les produits par paliers ?"

#: src/components/forms/TicketForm/index.tsx:202
#~ msgid "What are Tiered Tickets?"
#~ msgstr "Que sont les billets à plusieurs niveaux ?"

#: src/components/forms/CheckInListForm/index.tsx:50
msgid "What date should this check-in list become active?"
msgstr "À quelle date cette liste de pointage doit-elle devenir active ?"

#: src/components/modals/CreateProductCategoryModal/index.tsx:53
msgid "What is a Category?"
msgstr "Qu'est-ce qu'une catégorie ?"

#: src/components/routes/event/Webhooks/index.tsx:31
#~ msgid "What is a webhook?"
#~ msgstr "Qu'est-ce qu'un webhook ?"

#: src/components/forms/QuestionForm/index.tsx:159
msgid "What products does this code apply to?"
msgstr "À quels produits ce code s'applique-t-il ?"

#: src/components/forms/PromoCodeForm/index.tsx:68
msgid "What products does this code apply to? (Applies to all by default)"
msgstr "À quels produits ce code s'applique-t-il ? (S'applique à tous par défaut)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:48
msgid "What products should this capacity apply to?"
msgstr "À quels produits cette capacité doit-elle s'appliquer ?"

#: src/components/forms/PromoCodeForm/index.tsx:68
#~ msgid "What tickets does this code apply to? (Applies to all by default)"
#~ msgstr "À quels billets ce code s'applique-t-il ? (S'applique à tous par défaut)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:47
#: src/components/forms/QuestionForm/index.tsx:159
#~ msgid "What tickets should this question be apply to?"
#~ msgstr "À quels billets cette question doit-elle s'appliquer ?"

#: src/components/forms/QuestionForm/index.tsx:179
msgid "What time will you be arriving?"
msgstr "A quelle heure arriverez-vous ?"

#: src/components/forms/QuestionForm/index.tsx:170
msgid "What type of question is this?"
msgstr "De quel type de question s'agit-il ?"

#: src/components/forms/WebhookForm/index.tsx:108
msgid "When a check-in is deleted"
msgstr "Lorsqu'un enregistrement est supprimé"

#: src/components/forms/WebhookForm/index.tsx:84
msgid "When a new attendee is created"
msgstr "Lorsqu'un nouveau participant est créé"

#: src/components/forms/WebhookForm/index.tsx:54
msgid "When a new order is created"
msgstr "Lorsqu'une nouvelle commande est créée"

#: src/components/forms/WebhookForm/index.tsx:36
msgid "When a new product is created"
msgstr "Lorsqu'un nouveau produit est créé"

#: src/components/forms/WebhookForm/index.tsx:48
msgid "When a product is deleted"
msgstr "Lorsqu'un produit est supprimé"

#: src/components/forms/WebhookForm/index.tsx:42
msgid "When a product is updated"
msgstr "Lorsqu'un produit est mis à jour"

#: src/components/forms/WebhookForm/index.tsx:96
msgid "When an attendee is cancelled"
msgstr "Lorsqu'un participant est annulé"

#: src/components/forms/WebhookForm/index.tsx:102
msgid "When an attendee is checked in"
msgstr "Lorsqu'un participant est enregistré"

#: src/components/forms/WebhookForm/index.tsx:90
msgid "When an attendee is updated"
msgstr "Lorsqu'un participant est mis à jour"

#: src/components/forms/WebhookForm/index.tsx:78
msgid "When an order is cancelled"
msgstr "Lorsqu'une commande est annulée"

#: src/components/forms/WebhookForm/index.tsx:66
msgid "When an order is marked as paid"
msgstr "Lorsqu'une commande est marquée comme payée"

#: src/components/forms/WebhookForm/index.tsx:72
msgid "When an order is refunded"
msgstr "Lorsqu'une commande est remboursée"

#: src/components/forms/WebhookForm/index.tsx:60
msgid "When an order is updated"
msgstr "Lorsqu'une commande est mise à jour"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:182
msgid "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."
msgstr "Lorsque activé, des factures seront générées pour les commandes de billets. Les factures seront envoyées avec l'e-mail de confirmation de commande. Les participants peuvent également télécharger leurs factures depuis la page de confirmation de commande."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:141
msgid "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."
msgstr "Lorsque les paiements hors ligne sont activés, les utilisateurs pourront finaliser leurs commandes et recevoir leurs billets. Leurs billets indiqueront clairement que la commande n'est pas payée, et l'outil d'enregistrement informera le personnel si une commande nécessite un paiement."

#: src/components/forms/CheckInListForm/index.tsx:59
msgid "When should this check-in list expire?"
msgstr "Quand cette liste de pointage doit-elle expirer ?"

#: src/components/forms/CheckInListForm/index.tsx:25
msgid "Which tickets should be associated with this check-in list?"
msgstr "Quels billets doivent être associés à cette liste de pointage ?"

#: src/components/modals/CreateEventModal/index.tsx:107
msgid "Who is organizing this event?"
msgstr "Qui organise cet événement ?"

#: src/components/modals/SendMessageModal/index.tsx:194
msgid "Who is this message to?"
msgstr "A qui s'adresse ce message ?"

#: src/components/forms/QuestionForm/index.tsx:151
msgid "Who should be asked this question?"
msgstr "À qui faut-il poser cette question ?"

#: src/components/layouts/Event/index.tsx:110
msgid "Widget Embed"
msgstr "Intégrer le widget"

#: src/components/common/WidgetEditor/index.tsx:162
msgid "Widget Settings"
msgstr "Paramètres des widgets"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Working"
msgstr "Fonctionnement"

#: src/components/modals/CreateProductCategoryModal/index.tsx:60
#: src/components/modals/CreateProductModal/index.tsx:88
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
#: src/components/modals/CreateQuestionModal/index.tsx:74
#: src/components/modals/DuplicateEventModal/index.tsx:142
#: src/components/modals/DuplicateProductModal/index.tsx:113
#: src/components/modals/EditProductCategoryModal/index.tsx:70
#: src/components/modals/EditProductModal/index.tsx:108
#: src/components/modals/EditPromoCodeModal/index.tsx:84
#: src/components/modals/EditQuestionModal/index.tsx:101
#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/Register/index.tsx:129
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Working..."
msgstr "Fonctionnement..."

#: src/components/common/ReportTable/index.tsx:51
msgid "Year to date"
msgstr "Année à ce jour"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "Yes"
msgstr "Oui"

#: src/components/forms/ProductForm/index.tsx:376
msgid "Yes, remove them"
msgstr "Oui, supprime-les"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:77
msgid "You already scanned this ticket"
msgstr "Vous avez déjà scanné ce billet"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:53
msgid "You are changing your email to <0>{0}</0>."
msgstr "Vous changez votre adresse e-mail en <0>{0}</0>."

#: src/components/layouts/CheckIn/index.tsx:88
#: src/components/layouts/CheckIn/index.tsx:110
msgid "You are offline"
msgstr "Vous êtes hors ligne"

#: src/components/forms/ProductForm/index.tsx:407
msgid "You can create a promo code which targets this product on the"
msgstr "Vous pouvez créer un code promo qui cible ce produit sur le"

#: src/components/forms/TicketForm/index.tsx:352
#~ msgid "You can create a promo code which targets this ticket on the"
#~ msgstr "Vous pouvez créer un code promo qui cible ce billet sur le"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:31
#~ msgid "You can now start receiving payments through Stripe."
#~ msgstr "Vous pouvez désormais commencer à recevoir des paiements via Stripe."

#: src/components/forms/ProductForm/index.tsx:208
msgid "You cannot change the product type as there are attendees associated with this product."
msgstr "Vous ne pouvez pas changer le type de produit car des invités y sont associés."

#: src/components/forms/TicketForm/index.tsx:184
#~ msgid "You cannot change the ticket type as there are attendees associated with this ticket."
#~ msgstr "Vous ne pouvez pas modifier le type de ticket car des participants sont associés à ce ticket."

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "You cannot check in attendees with unpaid orders."
#~ msgstr "Vous ne pouvez pas enregistrer des participants avec des commandes impayées."

#: src/components/layouts/CheckIn/index.tsx:129
#: src/components/layouts/CheckIn/index.tsx:164
msgid "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."
msgstr "Vous ne pouvez pas enregistrer des participants avec des commandes impayées. Ce paramètre peut être modifié dans les paramètres de l'événement."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:40
msgid "You cannot delete the last category."
msgstr "Vous ne pouvez pas supprimer la dernière catégorie."

#: src/components/forms/ProductForm/index.tsx:57
msgid "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."
msgstr "Vous ne pouvez pas supprimer ce niveau de prix car des produits ont déjà été vendus pour ce niveau. Vous pouvez le masquer à la place."

#: src/components/forms/TicketForm/index.tsx:54
#~ msgid "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."
#~ msgstr "Vous ne pouvez pas supprimer ce niveau tarifaire car des billets sont déjà vendus pour ce niveau. Vous pouvez le cacher à la place."

#: src/components/modals/EditUserModal/index.tsx:88
msgid "You cannot edit the role or status of the account owner."
msgstr "Vous ne pouvez pas modifier le rôle ou le statut du propriétaire du compte."

#: src/components/modals/RefundOrderModal/index.tsx:132
msgid "You cannot refund a manually created order."
msgstr "Vous ne pouvez pas rembourser une commande créée manuellement."

#: src/components/common/QuestionsTable/index.tsx:252
msgid "You created a hidden question but disabled the option to show hidden questions. It has been enabled."
msgstr "Vous avez créé une question masquée mais avez désactivé l'option permettant d'afficher les questions masquées. Il a été activé."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:77
#~ msgid "You do not have permission to access this page"
#~ msgstr "Vous n'avez pas la permission d'accéder à cette page"

#: src/components/modals/ChooseAccountModal/index.tsx:14
msgid "You have access to multiple accounts. Please choose one to continue."
msgstr "Vous avez accès à plusieurs comptes. Veuillez en choisir un pour continuer."

#: src/components/routes/auth/AcceptInvitation/index.tsx:57
msgid "You have already accepted this invitation. Please login to continue."
msgstr "Vous avez déjà accepté cette invitation. Merci de vous connecter pour continuer."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:29
#~ msgid "You have connected your Stripe account"
#~ msgstr "Vous avez connecté votre compte Stripe"

#: src/components/common/QuestionsTable/index.tsx:338
msgid "You have no attendee questions."
msgstr "Vous n'avez aucune question de participant."

#: src/components/common/QuestionsTable/index.tsx:323
msgid "You have no order questions."
msgstr "Vous n'avez aucune question de commande."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:41
msgid "You have no pending email change."
msgstr "Vous n’avez aucun changement d’e-mail en attente."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:39
#~ msgid "You have not completed your Stripe Connect setup"
#~ msgstr "Vous n'avez pas terminé votre configuration Stripe Connect"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:38
#~ msgid "You have not connected your Stripe account"
#~ msgstr "Vous n'avez pas connecté votre compte Stripe"

#: src/components/layouts/Checkout/index.tsx:156
msgid "You have run out of time to complete your order."
msgstr "Vous avez manqué de temps pour compléter votre commande."

#: src/components/forms/ProductForm/index.tsx:374
#~ msgid "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"
#~ msgstr "Vous avez ajouté des taxes et des frais à un produit gratuit. Souhaitez-vous les supprimer ou les masquer ?"

#: src/components/forms/ProductForm/index.tsx:374
msgid "You have taxes and fees added to a Free Product. Would you like to remove them?"
msgstr "Vous avez ajouté des taxes et des frais à un produit gratuit. Voulez-vous les supprimer ?"

#: src/components/forms/TicketForm/index.tsx:319
#~ msgid "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"
#~ msgstr "Des taxes et des frais sont ajoutés à un billet gratuit. Souhaitez-vous les supprimer ou les masquer ?"

#: src/components/common/MessageList/index.tsx:74
msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."
msgstr "Vous n'avez encore envoyé aucun message. Vous pouvez envoyer des messages à tous les invités ou à des détenteurs de produits spécifiques."

#: src/components/common/MessageList/index.tsx:64
#~ msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."
#~ msgstr "Vous n'avez pas encore envoyé de messages. Vous pouvez envoyer des messages à tous les participants ou à des détenteurs de billets spécifiques."

#: src/components/modals/SendMessageModal/index.tsx:108
msgid "You must acknowledge that this email is not promotional"
msgstr "Vous devez reconnaître que cet e-mail n'est pas promotionnel"

#: src/components/routes/auth/AcceptInvitation/index.tsx:33
msgid "You must agree to the terms and conditions"
msgstr "Vous devez accepter les termes et conditions"

#: src/components/routes/event/GettingStarted/index.tsx:193
msgid "You must confirm your email address before your event can go live."
msgstr "Vous devez confirmer votre adresse e-mail avant que votre événement puisse être mis en ligne."

#: src/components/modals/CreateAttendeeModal/index.tsx:98
msgid "You must create a ticket before you can manually add an attendee."
msgstr "Vous devez créer un ticket avant de pouvoir ajouter manuellement un participant."

#: src/components/forms/ProductForm/index.tsx:60
msgid "You must have at least one price tier"
msgstr "Vous devez avoir au moins un niveau de prix"

#: src/components/modals/SendMessageModal/index.tsx:136
#~ msgid "You need to verify your account before you can send messages."
#~ msgstr "Vous devez vérifier votre compte avant de pouvoir envoyer des messages."

#: src/components/modals/SendMessageModal/index.tsx:151
msgid "You need to verify your account email before you can send messages."
msgstr "Vous devez vérifier l'adresse e-mail de votre compte avant de pouvoir envoyer des messages."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:143
msgid "You will have to mark an order as paid manually. This can be done on the manage order page."
msgstr "Vous devrez marquer une commande comme payée manuellement. Cela peut être fait sur la page de gestion des commandes."

#: src/components/modals/CreateCheckInListModal/index.tsx:55
msgid "You'll need a ticket before you can create a check-in list."
msgstr "Vous aurez besoin d'un billet avant de pouvoir créer une liste de pointage."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
msgid "You'll need at a product before you can create a capacity assignment."
msgstr "Vous aurez besoin d'un produit avant de pouvoir créer une affectation de capacité."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
#~ msgid "You'll need at a ticket before you can create a capacity assignment."
#~ msgstr "Vous aurez besoin d'un billet avant de pouvoir créer une affectation de capacité."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:43
msgid "You'll need at least one product to get started. Free, paid or let the user decide what to pay."
msgstr "Vous aurez besoin d'au moins un produit pour commencer. Gratuit, payant ou laissez l'utilisateur décider du montant à payer."

#: src/components/common/TicketsTable/index.tsx:69
#~ msgid "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."
#~ msgstr "Vous aurez besoin d'au moins un ticket pour commencer. Gratuit, payant ou laissez l'utilisateur décider quoi payer."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:93
msgid "You're going to {0}! 🎉"
msgstr "Vous allez à {0}! 🎉"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:64
msgid "Your account name is used on event pages and in emails."
msgstr "Le nom de votre compte est utilisé sur les pages d'événements et dans les e-mails."

#: src/components/routes/event/attendees.tsx:44
msgid "Your attendees have been exported successfully."
msgstr "Vos participants ont été exportés avec succès."

#: src/components/common/AttendeeTable/index.tsx:61
msgid "Your attendees will appear here once they have registered for your event. You can also manually add attendees."
msgstr "Vos participants apparaîtront ici une fois qu’ils se seront inscrits à votre événement. Vous pouvez également ajouter manuellement des participants."

#: src/components/common/WidgetEditor/index.tsx:329
msgid "Your awesome website 🎉"
msgstr "Votre superbe site internet 🎉"

#: src/components/routes/product-widget/CollectInformation/index.tsx:276
msgid "Your Details"
msgstr "Vos détails"

#: src/components/routes/auth/ForgotPassword/index.tsx:52
msgid "Your Email"
msgstr "Votre e-mail"

#: src/components/routes/profile/ManageProfile/index.tsx:118
msgid "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"
msgstr "Votre demande de modification par e-mail en <0>{0}</0> est en attente. S'il vous plaît vérifier votre e-mail pour confirmer"

#: src/components/routes/event/EventDashboard/index.tsx:150
msgid "Your event must be live before you can sell tickets to attendees."
msgstr "Votre événement doit être en ligne avant que vous puissiez vendre des billets aux participants."

#: src/components/routes/event/EventDashboard/index.tsx:164
#~ msgid "Your event must be live before you can sell tickets."
#~ msgstr "Votre événement doit être en ligne avant que vous puissiez vendre des billets."

#: src/components/common/OrdersTable/index.tsx:88
msgid "Your message has been sent"
msgstr "Votre message a été envoyé"

#: src/components/layouts/Checkout/index.tsx:84
msgid "Your Order"
msgstr "Votre commande"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:94
msgid "Your order has been cancelled"
msgstr "Votre commande a été annulée"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:96
msgid "Your order is awaiting payment 🏦"
msgstr "Votre commande est en attente de paiement 🏦"

#: src/components/routes/event/orders.tsx:95
msgid "Your orders have been exported successfully."
msgstr "Vos commandes ont été exportées avec succès."

#: src/components/common/OrdersTable/index.tsx:66
msgid "Your orders will appear here once they start rolling in."
msgstr "Vos commandes apparaîtront ici une fois qu’elles commenceront à arriver."

#: src/components/routes/auth/Login/index.tsx:74
#: src/components/routes/auth/Register/index.tsx:107
msgid "Your password"
msgstr "Votre mot de passe"

#: src/components/forms/StripeCheckoutForm/index.tsx:63
msgid "Your payment is processing."
msgstr "Votre paiement est en cours de traitement."

#: src/components/forms/StripeCheckoutForm/index.tsx:66
msgid "Your payment was not successful, please try again."
msgstr "Votre paiement n'a pas abouti, veuillez réessayer."

#: src/components/forms/StripeCheckoutForm/index.tsx:126
msgid "Your payment was unsuccessful. Please try again."
msgstr "Votre paiement a échoué. Veuillez réessayer."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#~ msgid "Your product for"
#~ msgstr "Votre produit pour"

#: src/components/modals/RefundOrderModal/index.tsx:58
msgid "Your refund is processing."
msgstr "Votre remboursement est en cours de traitement."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:153
msgid "Your Stripe account is connected and ready to process payments."
msgstr "Votre compte Stripe est connecté et prêt à traiter les paiements."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#: src/components/routes/product-widget/PrintProduct/index.tsx:42
msgid "Your ticket for"
msgstr "Votre billet pour"

#: src/components/routes/product-widget/CollectInformation/index.tsx:348
msgid "ZIP / Postal Code"
msgstr "Code postal"

#: src/components/common/CheckoutQuestion/index.tsx:170
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:146
msgid "Zip or Postal Code"
msgstr "Code Postal"

#: src/components/routes/product-widget/CollectInformation/index.tsx:349
msgid "ZIP or Postal Code"
msgstr "Code postal"
