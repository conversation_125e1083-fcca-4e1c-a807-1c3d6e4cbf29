@use "../../../styles/mixins";

.container {
  display: flex;
  background-color: #3c2b5c05 !important;
  height: 100vh;

  .sidebar {
    @include mixins.respond-below(md) {
      display: none;
    }
  }

  .countdown {
    color: #0ca678;
  }

  .countdownCloseToExpiry {
    color: #ad2f26;
  }

  .subTitle {
    font-size: 0.95em;
    margin-top: 10px;
  }

  .mainContent {
    flex: 1;
    border-right: 1px solid #e0e0e0;
    //height: 100vh;

    .header {
      height: 60px;
      background-color: #ffffff;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      box-shadow: 0 5px 5px 0 #0000000a;
      text-align: center;

      .actionBar {
        padding: var(--tk-spacing-md);
        width: 100%;

        .title {
          font-size: 1.2em;
          font-weight: 500;
        }
      }

      h1 {
        font-size: 1.3em;
        width: calc(100vw - 350px);
        padding: 0 15px;

        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        @include mixins.respond-below(md) {
          width: calc(100vw - 60px);
          font-size: 1em;
        }
      }
    }

    .main {
      position: relative;
      height: calc(100vh - 90px);
      overflow-y: auto;
      padding: 20px 60px;
      @include mixins.scrollbar();

      @include mixins.respond-below(md) {
        padding: 20px;
      }

      .innerContainer {
        max-width: 700px;
        margin: 0 auto;
      }
    }

    .footer {
      height: 100px;
      background-color: #ffffff;
    }
  }
}
