msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: Hi.Events\n"
"Language: zh-Hans\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/components/layouts/Event/index.tsx:189
msgid "- Click to Publish"
msgstr "- 点击发布"

#: src/components/layouts/Event/index.tsx:191
msgid "- Click to Unpublish"
msgstr "- 点击取消发布"

#: src/components/common/NoResultsSplash/index.tsx:14
msgid "'There\\'s nothing to show yet'"
msgstr "There's nothing to show yet"

#: src/components/modals/SendMessageModal/index.tsx:159
#~ msgid ""
#~ "\"\"Due to the high risk of spam, we require manual verification before you can send messages.\n"
#~ "\"\"Please contact us to request access."
#~ msgstr ""
#~ "由于垃圾邮件风险较高，我们要求手动验证后才能发送消息。\n"
#~ "请联系我们以申请访问权限。"

#: src/components/routes/auth/ForgotPassword/index.tsx:40
#~ msgid ""
#~ "\"\"If you have an account with us, you will receive an email with instructions on how to reset your\n"
#~ "\"\"password."
#~ msgstr "如果您拥有我们的账户，您将收到一封包含密码重置说明的电子邮件。"

#: src/components/common/ReportTable/index.tsx:303
#: src/locales.ts:37
msgid "{0}"
msgstr "{0}"

#: src/components/layouts/CheckIn/index.tsx:82
msgid "{0} <0>checked in</0> successfully"
msgstr "{0} <0>签到</0>成功"

#: src/components/layouts/CheckIn/index.tsx:106
msgid "{0} <0>checked out</0> successfully"
msgstr "{0} <0>签退</0>成功"

#: src/components/routes/event/Webhooks/index.tsx:24
msgid "{0} Active Webhooks"
msgstr "{0} 个活动的 Webhook"

#: src/components/routes/product-widget/SelectProducts/index.tsx:412
msgid "{0} available"
msgstr "{0}可用"

#: src/components/common/AttendeeCheckInTable/index.tsx:155
#~ msgid "{0} checked in"
#~ msgstr "{0}签到"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
msgid "{0} created successfully"
msgstr "成功创建 {0}"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
msgid "{0} updated successfully"
msgstr "{0}更新成功"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:57
msgid "{0}'s Events"
msgstr "{0}的活动"

#: src/components/layouts/CheckIn/index.tsx:449
msgid "{0}/{1} checked in"
msgstr "{0}/{1} 已签到"

#: src/components/common/Countdown/index.tsx:68
msgid "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{days} 天, {hours} 小时, {minutes} 分钟, 和 {seconds} 秒"

#: src/components/common/WebhookTable/index.tsx:79
msgid "{eventCount} events"
msgstr "{eventCount} 个事件"

#: src/components/common/Countdown/index.tsx:70
msgid "{hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{hours} 小时, {minutes} 分钟, 和 {seconds} 秒"

#: src/components/common/Countdown/index.tsx:72
msgid "{minutes} minutes and {seconds} seconds"
msgstr "{分}分钟和{秒}秒钟"

#: src/components/routes/welcome/index.tsx:59
msgid "{organizerName}'s first event"
msgstr "{组织者名称}的首次活动"

#: src/components/common/ReportTable/index.tsx:256
#~ msgid "{title}"
#~ msgstr ""

#: src/components/common/CapacityAssignmentList/index.tsx:47
msgid "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"
msgstr "<0>容量分配让你可以管理票务或整个活动的容量。非常适合多日活动、研讨会等，需要控制出席人数的场合。</0><1>例如，你可以将容量分配与<2>第一天</2>和<3>所有天数</3>的票关联起来。一旦达到容量，这两种票将自动停止销售。</1>"

#: src/components/common/CheckInListList/index.tsx:49
msgid "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"
msgstr "<0>签到列表帮助管理活动的参与者入场。您可以将多个票与一个签到列表关联，并确保只有持有效票的人员才能入场。</0>"

#: src/components/common/WidgetEditor/index.tsx:324
msgid "<0>https://</0>your-website.com"
msgstr "<0>https://</0>your-website.com"

#: src/components/forms/ProductForm/index.tsx:287
msgid "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"
msgstr "<0>请输入不含税费的价格。</0><1>税费可以在下方添加。</1>"

#: src/components/forms/ProductForm/index.tsx:304
msgid "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"
msgstr "<0>该产品的可用数量</0><1>如果该产品有相关的<2>容量限制</2>，此值可以被覆盖。</1>"

#: src/components/forms/TicketForm/index.tsx:249
#~ msgid "<0>The number of tickets available for this ticket</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this ticket.</1>"
#~ msgstr "<0>此票的可用票数</0><1>如果此票有<2>容量限制</2>，此值可以被覆盖。</1>"

#: src/components/common/WebhookTable/index.tsx:205
msgid "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"
msgstr "<0>Webhooks 可在事件发生时立即通知外部服务，例如，在注册时将新与会者添加到您的 CRM 或邮件列表，确保无缝自动化。</0><1>使用第三方服务，如 <2>Zapier</2>、<3>IFTTT</3> 或 <4>Make</4> 来创建自定义工作流并自动化任务。</1>"

#: src/components/routes/event/GettingStarted/index.tsx:134
msgid "⚡️ Set up your event"
msgstr "⚡️ 设置您的活动"

#: src/components/routes/event/GettingStarted/index.tsx:190
msgid "✉️ Confirm your email address"
msgstr "✉️ 确认您的电子邮件地址"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "🎉 Congratulations on creating an event!"
#~ msgstr "恭喜您创建了一个活动！"

#: src/components/routes/event/GettingStarted/index.tsx:67
#~ msgid "🎟️ Add products"
#~ msgstr "🎟️ 添加产品"

#: src/components/routes/event/GettingStarted/index.tsx:119
msgid "🎟️ Add tickets"
msgstr "🎟️ 添加门票"

#: src/components/routes/event/GettingStarted/index.tsx:162
msgid "🎨 Customize your event page"
msgstr "🎨 自定义活动页面"

#: src/components/routes/event/GettingStarted/index.tsx:147
msgid "💳 Connect with Stripe"
msgstr "与 Stripe 连接"

#: src/components/routes/event/GettingStarted/index.tsx:176
msgid "🚀 Set your event live"
msgstr "🚀 实时设置您的活动"

#: src/components/common/Countdown/index.tsx:34
msgid "0 minutes and 0 seconds"
msgstr "0 分 0 秒"

#: src/components/routes/event/Webhooks/index.tsx:23
msgid "1 Active Webhook"
msgstr "1 个活动的 Webhook"

#: src/components/modals/RefundOrderModal/index.tsx:105
msgid "10.00"
msgstr "10.00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:123
msgid "123 Main Street"
msgstr "缅因街 123 号"

#: src/components/common/WidgetEditor/index.tsx:226
msgid "20"
msgstr "20"

#: src/components/routes/welcome/index.tsx:91
msgid "2024-01-01 10:00"
msgstr "2024-01-01 10:00"

#: src/components/routes/welcome/index.tsx:97
msgid "2024-01-01 18:00"
msgstr "2024-01-01 18:00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:147
msgid "94103"
msgstr "94103"

#: src/components/forms/QuestionForm/index.tsx:138
msgid "A date input. Perfect for asking for a date of birth etc."
msgstr "日期输入字段。非常适合询问出生日期等。"

#: src/components/forms/TaxAndFeeForm/index.tsx:88
msgid "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."
msgstr "默认的{type}会自动应用于所有新产品。您可以为每个产品单独覆盖此设置。"

#: src/components/forms/TaxAndFeeForm/index.tsx:88
#~ msgid "A default {type} is automaticaly applied to all new tickets. You can override this on a per ticket basis."
#~ msgstr "默认 {type} 会自动应用于所有新票单。您可以根据每张票单的具体情况覆盖该默认值。"

#: src/components/forms/QuestionForm/index.tsx:126
msgid "A Dropdown input allows only one selection"
msgstr "下拉式输入法只允许一个选择"

#: src/components/forms/TaxAndFeeForm/index.tsx:20
msgid "A fee, like a booking fee or a service fee"
msgstr "费用，如预订费或服务费"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
msgid "A fixed amount per product. E.g, $0.50 per product"
msgstr "每个产品的固定金额。例如，每个产品$0.50"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
#~ msgid "A fixed amount per ticket. E.g, $0.50 per ticket"
#~ msgstr "每张票的固定金额。例如，每张票 0.50 美元"

#: src/components/forms/QuestionForm/index.tsx:108
msgid "A multi line text input"
msgstr "多行文本输入"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
msgid "A percentage of the product price. E.g., 3.5% of the product price"
msgstr "产品价格的百分比。例如，3.5%的产品价格"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
#~ msgid "A percentage of the ticket price. E.g., 3.5% of the ticket price"
#~ msgstr "票价的百分比。例如，票价的 3.5%"

#: src/components/forms/PromoCodeForm/index.tsx:37
msgid "A promo code with no discount can be used to reveal hidden products."
msgstr "无折扣的促销代码可以用来显示隐藏的产品。"

#: src/components/forms/PromoCodeForm/index.tsx:36
#~ msgid "A promo code with no discount can be used to reveal hidden tickets."
#~ msgstr "使用无折扣的促销代码可显示隐藏的门票。"

#: src/components/forms/QuestionForm/index.tsx:120
msgid "A Radio option has multiple options but only one can be selected."
msgstr "单选题有多个选项，但只能选择一个。"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:69
msgid "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"
msgstr "活动的简短描述，将显示在搜索引擎结果中，并在社交媒体上分享时显示。默认情况下，将使用活动描述"

#: src/components/forms/QuestionForm/index.tsx:102
msgid "A single line text input"
msgstr "单行文本输入"

#: src/components/forms/QuestionForm/index.tsx:92
#~ msgid "A single question per attendee. E.g, What is your preferred meal?"
#~ msgstr "每位与会者只需回答一个问题。例如：您喜欢吃什么？"

#: src/components/forms/QuestionForm/index.tsx:86
#~ msgid "A single question per order. E.g, What is your company name?"
#~ msgstr "每份订单只有一个问题。例如：贵公司的名称是什么？"

#: src/components/forms/QuestionForm/index.tsx:87
msgid "A single question per order. E.g, What is your shipping address?"
msgstr "每个订单一个问题。例如，您的送货地址是什么？"

#: src/components/forms/QuestionForm/index.tsx:93
msgid "A single question per product. E.g, What is your t-shirt size?"
msgstr "每个产品一个问题。例如，您的T恤尺码是多少？"

#: src/components/forms/TaxAndFeeForm/index.tsx:14
msgid "A standard tax, like VAT or GST"
msgstr "标准税，如增值税或消费税"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:76
msgid "About"
msgstr "关于"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:211
msgid "About Stripe Connect"
msgstr "关于 Stripe Connect"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:79
#~ msgid "About the event"
#~ msgstr "关于活动"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:91
msgid "Accept bank transfers, checks, or other offline payment methods"
msgstr "接受银行转账、支票或其他线下支付方式"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:86
msgid "Accept credit card payments with Stripe"
msgstr "通过 Stripe 接受信用卡支付"

#: src/components/routes/auth/AcceptInvitation/index.tsx:126
msgid "Accept Invitation"
msgstr "接受邀请"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:125
msgid "Access Denied"
msgstr "访问被拒绝"

#: src/components/routes/account/ManageAccount/index.tsx:24
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:53
msgid "Account"
msgstr "账户"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:62
msgid "Account Name"
msgstr "账户名称"

#: src/components/common/GlobalMenu/index.tsx:32
#: src/components/routes/account/ManageAccount/index.tsx:19
msgid "Account Settings"
msgstr "账户设置"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:42
msgid "Account updated successfully"
msgstr "账户更新成功"

#: src/components/common/AttendeeTable/index.tsx:158
#: src/components/common/ProductsTable/SortableProduct/index.tsx:267
#: src/components/common/QuestionsTable/index.tsx:108
msgid "Actions"
msgstr "行动"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "Activate"
msgstr "激活"

#: src/components/forms/CheckInListForm/index.tsx:47
msgid "Activation date"
msgstr "激活日期"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:19
#: src/components/modals/EditUserModal/index.tsx:110
msgid "Active"
msgstr "活跃"

#: src/components/routes/event/attendees.tsx:59
#~ msgid "Add"
#~ msgstr "添加"

#: src/components/forms/CheckInListForm/index.tsx:39
msgid "Add a description for this check-in list"
msgstr "为此签到列表添加描述"

#: src/components/modals/ManageAttendeeModal/index.tsx:131
msgid "Add any notes about the attendee. These will not be visible to the attendee."
msgstr "添加有关与会者的任何备注。这些将不会对与会者可见。"

#: src/components/modals/ManageAttendeeModal/index.tsx:133
msgid "Add any notes about the attendee..."
msgstr "添加有关与会者的任何备注..."

#: src/components/modals/ManageOrderModal/index.tsx:165
msgid "Add any notes about the order. These will not be visible to the customer."
msgstr "添加关于订单的备注。这些信息不会对客户可见。"

#: src/components/modals/ManageOrderModal/index.tsx:169
msgid "Add any notes about the order..."
msgstr "添加关于订单的备注..."

#: src/components/forms/QuestionForm/index.tsx:202
msgid "Add description"
msgstr "添加描述"

#: src/components/routes/event/GettingStarted/index.tsx:85
#~ msgid "Add event details and and manage event settings."
#~ msgstr "添加活动详情并管理活动设置。"

#: src/components/routes/event/GettingStarted/index.tsx:137
msgid "Add event details and manage event settings."
msgstr "添加活动详情并管理活动设置。"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:153
msgid "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"
msgstr "添加线下支付的说明（例如，银行转账详情、支票寄送地址、付款截止日期）"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add More products"
#~ msgstr "添加更多产品"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add More tickets"
msgstr "添加更多门票"

#: src/components/common/HeadingCard/index.tsx:27
msgid "Add New"
msgstr "添加新内容"

#: src/components/forms/QuestionForm/index.tsx:68
msgid "Add Option"
msgstr "添加选项"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:68
msgid "Add Product"
msgstr "添加产品"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:51
msgid "Add Product to Category"
msgstr "将产品添加到类别"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add products"
#~ msgstr "添加产品"

#: src/components/common/QuestionsTable/index.tsx:281
msgid "Add question"
msgstr "添加问题"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:18
msgid "Add Tax or Fee"
msgstr "加税或费用"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add tickets"
msgstr "添加机票"

#: src/components/forms/ProductForm/index.tsx:337
msgid "Add tier"
msgstr "增加层级"

#: src/components/common/AddEventToCalendarButton/index.tsx:112
#: src/components/common/AddEventToCalendarButton/index.tsx:120
msgid "Add to Calendar"
msgstr "添加到日历"

#: src/components/common/WebhookTable/index.tsx:223
#: src/components/routes/event/Webhooks/index.tsx:35
msgid "Add Webhook"
msgstr "添加 Webhook"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:219
msgid "Additional Information"
msgstr "附加信息"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/forms/ProductForm/index.tsx:348
msgid "Additional Options"
msgstr "附加选项"

#: src/components/common/OrderDetails/index.tsx:96
#: src/components/forms/QuestionForm/index.tsx:130
msgid "Address"
msgstr "地址"

#: src/components/common/CheckoutQuestion/index.tsx:156
msgid "Address line 1"
msgstr "地址第 1 行"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:122
#: src/components/routes/product-widget/CollectInformation/index.tsx:319
#: src/components/routes/product-widget/CollectInformation/index.tsx:320
msgid "Address Line 1"
msgstr "地址 1"

#: src/components/common/CheckoutQuestion/index.tsx:159
msgid "Address line 2"
msgstr "地址第 2 行"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:127
#: src/components/routes/product-widget/CollectInformation/index.tsx:324
#: src/components/routes/product-widget/CollectInformation/index.tsx:325
msgid "Address Line 2"
msgstr "地址第 2 行"

#: src/components/modals/EditUserModal/index.tsx:49
#: src/components/modals/InviteUserModal/index.tsx:41
msgid "Admin"
msgstr "管理员"

#: src/components/modals/EditUserModal/index.tsx:51
#: src/components/modals/InviteUserModal/index.tsx:43
msgid "Admin users have full access to events and account settings."
msgstr "管理员用户可以完全访问事件和账户设置。"

#: src/components/layouts/Event/index.tsx:53
#~ msgid "Affiliates"
#~ msgstr "附属机构"

#: src/components/common/MessageList/index.tsx:21
msgid "All attendees"
msgstr "所有与会者"

#: src/components/modals/SendMessageModal/index.tsx:187
msgid "All attendees of this event"
msgstr "本次活动的所有与会者"

#: src/components/routes/events/Dashboard/index.tsx:52
#~ msgid "All Events"
#~ msgstr "所有活动"

#: src/components/common/PromoCodeTable/index.tsx:131
msgid "All Products"
msgstr "所有产品"

#: src/components/common/PromoCodeTable/index.tsx:130
#: src/components/forms/PromoCodeForm/index.tsx:67
#~ msgid "All Tickets"
#~ msgstr "所有门票"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:157
msgid "Allow attendees associated with unpaid orders to check in"
msgstr "允许与未支付订单关联的参与者签到"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:82
msgid "Allow search engine indexing"
msgstr "允许搜索引擎索引"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:81
msgid "Allow search engines to index this event"
msgstr "允许搜索引擎索引此事件"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:78
msgid "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."
msgstr "快到了！我们正在等待处理您的付款。只需几秒钟。"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:77
msgid "Amazing, Event, Keywords..."
msgstr "令人惊叹, 活动, 关键词..."

#: src/components/common/OrdersTable/index.tsx:207
#: src/components/forms/TaxAndFeeForm/index.tsx:71
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:36
msgid "Amount"
msgstr "金额"

#: src/components/modals/CreateAttendeeModal/index.tsx:165
msgid "Amount paid ({0})"
msgstr "支付金额 ({0})"

#: src/mutations/useExportAnswers.ts:45
msgid "An error occurred while checking export status."
msgstr "检查导出状态时发生错误。"

#: src/components/common/ErrorDisplay/index.tsx:18
msgid "An error occurred while loading the page"
msgstr "加载页面时出现错误"

#: src/components/common/QuestionsTable/index.tsx:148
msgid "An error occurred while sorting the questions. Please try again or refresh the page"
msgstr "问题排序时发生错误。请重试或刷新页面"

#: src/components/common/TicketsTable/index.tsx:47
#~ msgid "An error occurred while sorting the tickets. Please try again or refresh the page"
#~ msgstr "在整理门票时发生错误。请重试或刷新页面"

#: src/components/routes/welcome/index.tsx:75
#~ msgid "An event is the actual event you are hosting. You can add more details later."
#~ msgstr "事件是您要举办的实际活动。您可以稍后添加更多细节。"

#: src/components/routes/welcome/index.tsx:75
msgid "An event is the gathering or occasion you’re organizing. You can add more details later."
msgstr "活动是您正在组织的聚会或场合。您可以稍后添加更多详细信息。"

#: src/components/routes/welcome/index.tsx:23
msgid "An organizer is the company or person who is hosting the event"
msgstr "主办方是指举办活动的公司或个人"

#: src/components/forms/StripeCheckoutForm/index.tsx:40
msgid "An unexpected error occurred."
msgstr "出现意外错误。"

#: src/hooks/useFormErrorResponseHandler.tsx:48
msgid "An unexpected error occurred. Please try again."
msgstr "出现意外错误。请重试。"

#: src/components/common/QuestionAndAnswerList/index.tsx:97
msgid "Answer updated successfully."
msgstr "答案更新成功。"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
msgid "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
msgstr "产品持有者的任何查询都将发送到此电子邮件地址。此地址还将用作从此活动发送的所有电子邮件的“回复至”地址"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
#~ msgid "Any queries from ticket holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
#~ msgstr "持票人的任何询问都将发送到此电子邮件地址。该地址也将作为本次活动发送的所有电子邮件的 \"回复地址\"。"

#: src/components/common/WidgetEditor/index.tsx:213
msgid "Appearance"
msgstr "外观"

#: src/components/routes/product-widget/SelectProducts/index.tsx:500
msgid "applied"
msgstr "应用"

#: src/components/common/CapacityAssignmentList/index.tsx:97
msgid "Applies to {0} products"
msgstr "适用于{0}个产品"

#: src/components/common/CapacityAssignmentList/index.tsx:97
#~ msgid "Applies to {0} tickets"
#~ msgstr "适用于{0}张票"

#: src/components/common/CapacityAssignmentList/index.tsx:98
msgid "Applies to 1 product"
msgstr "适用于1个产品"

#: src/components/common/CapacityAssignmentList/index.tsx:98
#~ msgid "Applies to 1 ticket"
#~ msgstr "适用于1张票"

#: src/components/common/FilterModal/index.tsx:253
msgid "Apply"
msgstr "应用"

#: src/components/routes/product-widget/SelectProducts/index.tsx:528
msgid "Apply Promo Code"
msgstr "应用促销代码"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
msgid "Apply this {type} to all new products"
msgstr "将此{type}应用于所有新产品"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
#~ msgid "Apply this {type} to all new tickets"
#~ msgstr "将此 {类型}应用于所有新票"

#: src/components/common/EventCard/index.tsx:167
msgid "Archive event"
msgstr "归档活动"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:32
msgid "Archived"
msgstr "已归档"

#: src/components/routes/events/Dashboard/index.tsx:59
msgid "Archived Events"
msgstr "已归档的活动"

#: src/components/common/AttendeeTable/index.tsx:76
msgid "Are you sure you want to activate this attendee?"
msgstr "您确定要激活该与会者吗？"

#: src/components/common/EventCard/index.tsx:58
msgid "Are you sure you want to archive this event?"
msgstr "您确定要归档此活动吗?"

#: src/components/common/AttendeeTable/index.tsx:78
#~ msgid "Are you sure you want to cancel this attendee? This will void their product"
#~ msgstr "您确定要取消该参会者吗？这将使他们的产品无效"

#: src/components/common/AttendeeTable/index.tsx:77
msgid "Are you sure you want to cancel this attendee? This will void their ticket"
msgstr "您确定要取消该与会者吗？这将使其门票作废"

#: src/components/common/PromoCodeTable/index.tsx:37
msgid "Are you sure you want to delete this promo code?"
msgstr "您确定要删除此促销代码吗？"

#: src/components/common/QuestionsTable/index.tsx:164
msgid "Are you sure you want to delete this question?"
msgstr "您确定要删除这个问题吗？"

#: src/components/common/WebhookTable/index.tsx:122
msgid "Are you sure you want to delete this webhook?"
msgstr "您确定要删除此 Webhook 吗？"

#: src/components/layouts/Event/index.tsx:68
#: src/components/routes/event/EventDashboard/index.tsx:64
msgid "Are you sure you want to make this event draft? This will make the event invisible to the public"
msgstr "您确定要将此活动设为草稿吗？这将使公众无法看到该活动"

#: src/components/layouts/Event/index.tsx:69
#: src/components/routes/event/EventDashboard/index.tsx:65
msgid "Are you sure you want to make this event public? This will make the event visible to the public"
msgstr "您确定要将此事件公开吗？这将使事件对公众可见"

#: src/components/common/EventCard/index.tsx:59
msgid "Are you sure you want to restore this event? It will be restored as a draft event."
msgstr "您确定要恢复此活动吗？它将作为草稿恢复。"

#: src/components/common/CapacityAssignmentList/index.tsx:163
msgid "Are you sure you would like to delete this Capacity Assignment?"
msgstr "您确定要删除此容量分配吗？"

#: src/components/common/CheckInListList/index.tsx:183
msgid "Are you sure you would like to delete this Check-In List?"
msgstr "您确定要删除此签到列表吗？"

#: src/components/forms/QuestionForm/index.tsx:90
#~ msgid "Ask once per attendee"
#~ msgstr "每位与会者提问一次"

#: src/components/forms/QuestionForm/index.tsx:85
msgid "Ask once per order"
msgstr "每份订单询问一次"

#: src/components/forms/QuestionForm/index.tsx:91
msgid "Ask once per product"
msgstr "每个产品询问一次"

#: src/components/modals/CreateWebhookModal/index.tsx:33
msgid "At least one event type must be selected"
msgstr "必须选择至少一种事件类型"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Attendee"
msgstr "与会者"

#: src/components/forms/WebhookForm/index.tsx:94
msgid "Attendee Cancelled"
msgstr "与会者已取消"

#: src/components/forms/WebhookForm/index.tsx:82
msgid "Attendee Created"
msgstr "与会者已创建"

#: src/components/modals/ManageAttendeeModal/index.tsx:147
msgid "Attendee Details"
msgstr "与会者详情"

#: src/components/layouts/AuthLayout/index.tsx:73
msgid "Attendee Management"
msgstr "与会者管理"

#: src/components/layouts/CheckIn/index.tsx:150
msgid "Attendee not found"
msgstr "未找到参与者"

#: src/components/modals/ManageAttendeeModal/index.tsx:153
msgid "Attendee Notes"
msgstr "与会者备注"

#: src/components/common/QuestionsTable/index.tsx:369
msgid "Attendee questions"
msgstr "与会者提问"

#: src/components/modals/ManageAttendeeModal/index.tsx:172
msgid "Attendee Ticket"
msgstr "参会者票"

#: src/components/forms/WebhookForm/index.tsx:88
msgid "Attendee Updated"
msgstr "与会者已更新"

#: src/components/common/OrdersTable/index.tsx:206
#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
#: src/components/common/StatBoxes/index.tsx:27
#: src/components/layouts/Event/index.tsx:99
#: src/components/modals/ManageOrderModal/index.tsx:126
#: src/components/routes/event/attendees.tsx:59
msgid "Attendees"
msgstr "与会者"

#: src/components/routes/event/attendees.tsx:43
msgid "Attendees Exported"
msgstr "与会者已导出"

#: src/components/routes/event/EventDashboard/index.tsx:239
msgid "Attendees Registered"
msgstr "注册的参会者"

#: src/components/modals/SendMessageModal/index.tsx:146
#~ msgid "Attendees with a specific product"
#~ msgstr "具有特定产品的参会者"

#: src/components/modals/SendMessageModal/index.tsx:183
msgid "Attendees with a specific ticket"
msgstr "持有特定门票的与会者"

#: src/components/common/WidgetEditor/index.tsx:233
msgid "Auto Resize"
msgstr "自动调整大小"

#: src/components/layouts/AuthLayout/index.tsx:88
#~ msgid "Auto Workflow"
#~ msgstr "自动化工作流"

#: src/components/layouts/AuthLayout/index.tsx:79
msgid "Automated entry management with multiple check-in lists and real-time validation"
msgstr "使用多个签到列表和实时验证的自动化入场管理"

#: src/components/common/WidgetEditor/index.tsx:236
msgid "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."
msgstr "根据内容自动调整 widget 高度。禁用时，窗口小部件将填充容器的高度。"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
#~ msgid "Avg Discount/Order"
#~ msgstr "平均折扣/订单"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
#~ msgid "Avg Order Value"
#~ msgstr "平均订单价值"

#: src/components/common/OrderStatusBadge/index.tsx:15
#: src/components/modals/SendMessageModal/index.tsx:231
msgid "Awaiting offline payment"
msgstr "等待线下付款"

#: src/components/routes/event/orders.tsx:25
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:43
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:66
msgid "Awaiting Offline Payment"
msgstr "等待线下付款"

#: src/components/layouts/CheckIn/index.tsx:263
msgid "Awaiting payment"
msgstr "等待付款"

#: src/components/common/AttendeeTicket/index.tsx:67
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:40
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:65
msgid "Awaiting Payment"
msgstr "等待付款"

#: src/components/routes/welcome/index.tsx:84
msgid "Awesome Event"
msgstr "精彩活动"

#: src/components/forms/OrganizerForm/index.tsx:28
msgid "Awesome Organizer Ltd."
msgstr "Awesome Organizer Ltd."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:42
msgid "Back to all events"
msgstr "返回所有活动"

#: src/components/layouts/Checkout/index.tsx:73
#: src/components/routes/product-widget/CollectInformation/index.tsx:249
#: src/components/routes/product-widget/CollectInformation/index.tsx:261
msgid "Back to event page"
msgstr "返回活动页面"

#: src/components/routes/auth/ForgotPassword/index.tsx:59
#: src/components/routes/auth/ResetPassword/index.tsx:66
msgid "Back to login"
msgstr "返回登录"

#: src/components/common/WidgetEditor/index.tsx:175
msgid "Background Color"
msgstr "背景颜色"

#: src/components/routes/event/HomepageDesigner/index.tsx:144
msgid "Background Type"
msgstr "背景类型"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#~ msgid "Basic Details"
#~ msgstr "基本详情"

#: src/components/modals/SendMessageModal/index.tsx:278
msgid "Before you send!"
msgstr "发送之前"

#: src/components/routes/event/GettingStarted/index.tsx:60
#~ msgid "Before your event can go live, there are a few things you need to do."
#~ msgstr "在活动上线之前，您需要做几件事。"

#: src/components/routes/event/GettingStarted/index.tsx:92
msgid "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."
msgstr "在您的活动可以上线之前，您需要完成以下几项任务。请完成以下所有步骤以开始。"

#: src/components/routes/auth/Register/index.tsx:66
#~ msgid "Begin selling products in minutes"
#~ msgstr "几分钟内开始销售产品"

#: src/components/routes/auth/Register/index.tsx:49
#~ msgid "Begin selling tickets in minutes"
#~ msgstr "几分钟内开始售票"

#: src/components/routes/product-widget/CollectInformation/index.tsx:313
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:142
msgid "Billing Address"
msgstr "账单地址"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:167
msgid "Billing Settings"
msgstr "账单设置"

#: src/components/layouts/AuthLayout/index.tsx:83
#~ msgid "Brand Control"
#~ msgstr "品牌控制"

#: src/components/common/LanguageSwitcher/index.tsx:28
msgid "Brazilian Portuguese"
msgstr "巴西葡萄牙语"

#: src/components/routes/auth/Register/index.tsx:133
msgid "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."
msgstr "注册即表示您同意我们的<0>服务条款</0>和<1>隐私政策</1>。"

#: src/components/forms/TaxAndFeeForm/index.tsx:52
msgid "Calculation Type"
msgstr "计算类型"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:140
msgid "California"
msgstr "加利福尼亚州"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:175
msgid "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."
msgstr "相机权限被拒绝。<0>再次请求权限</0>，如果还不行，则需要在浏览器设置中<1>授予此页面</1>访问相机的权限。"

#: src/components/common/AttendeeTable/index.tsx:182
#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/QuestionAndAnswerList/index.tsx:149
#: src/components/layouts/CheckIn/index.tsx:225
#: src/utilites/confirmationDialog.tsx:11
msgid "Cancel"
msgstr "取消"

#: src/components/routes/profile/ManageProfile/index.tsx:126
msgid "Cancel email change"
msgstr "取消更改电子邮件"

#: src/components/common/OrdersTable/index.tsx:190
#: src/components/modals/RefundOrderModal/index.tsx:112
msgid "Cancel order"
msgstr "取消订单"

#: src/components/modals/CancelOrderModal/index.tsx:59
msgid "Cancel Order"
msgstr "取消订单"

#: src/components/modals/CancelOrderModal/index.tsx:44
msgid "Cancel Order {0}"
msgstr "取消订单 {0}"

#: src/components/common/AttendeeDetails/index.tsx:32
#~ msgid "Canceled"
#~ msgstr "已取消"

#: src/components/modals/CancelOrderModal/index.tsx:54
msgid "Canceling will cancel all products associated with this order, and release the products back into the available pool."
msgstr "取消将会取消与此订单关联的所有产品，并将产品释放回可用库存。"

#: src/components/modals/CancelOrderModal/index.tsx:54
#~ msgid "Canceling will cancel all tickets associated with this order, and release the tickets back into the available pool."
#~ msgstr "取消将取消与此订单相关的所有机票，并将机票放回可用票池。"

#: src/components/common/AttendeeTicket/index.tsx:61
#: src/components/common/OrderStatusBadge/index.tsx:12
#: src/components/routes/event/orders.tsx:24
msgid "Cancelled"
msgstr "已取消"

#: src/components/layouts/CheckIn/index.tsx:173
msgid "Cannot Check In"
msgstr "无法签到"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:42
#: src/components/layouts/Event/index.tsx:103
msgid "Capacity"
msgstr "容量"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:36
msgid "Capacity Assignment created successfully"
msgstr "容量分配创建成功"

#: src/components/common/CapacityAssignmentList/index.tsx:31
msgid "Capacity Assignment deleted successfully"
msgstr "容量分配删除成功"

#: src/components/routes/event/CapacityAssignments/index.tsx:32
msgid "Capacity Management"
msgstr "容量管理"

#: src/components/modals/CreateProductCategoryModal/index.tsx:56
msgid "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."
msgstr "类别允许您将产品分组。例如，您可以有一个“门票”类别和另一个“商品”类别。"

#: src/components/forms/ProductForm/index.tsx:267
msgid "Categories help you organize your products. This title will be displayed on the public event page."
msgstr "类别帮助您组织产品。此标题将在公共活动页面上显示。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:91
msgid "Categories reordered successfully."
msgstr "类别重新排序成功。"

#: src/components/routes/event/products.tsx:96
msgid "Category"
msgstr "类别"

#: src/components/modals/CreateProductCategoryModal/index.tsx:37
msgid "Category Created Successfully"
msgstr "类别创建成功"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Change Cover"
msgstr "更改封面"

#: src/components/routes/profile/ManageProfile/index.tsx:201
msgid "Change password"
msgstr "更改密码"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check in"
#~ msgstr "报到"

#: src/components/layouts/CheckIn/index.tsx:180
msgid "Check In"
msgstr "报到"

#: src/components/layouts/CheckIn/index.tsx:193
msgid "Check in {0} {1}"
msgstr "签到 {0} {1}"

#: src/components/layouts/CheckIn/index.tsx:218
msgid "Check in and mark order as paid"
msgstr "签到并将订单标记为已支付"

#: src/components/layouts/CheckIn/index.tsx:209
msgid "Check in only"
msgstr "仅签到"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check out"
#~ msgstr "查看"

#: src/components/layouts/CheckIn/index.tsx:177
msgid "Check Out"
msgstr "签退"

#: src/components/layouts/Checkout/index.tsx:106
msgid "Check out this event!"
msgstr "看看这个活动吧！"

#: src/components/common/EventCard/index.tsx:156
msgid "Check-in"
msgstr "办理登机手续"

#: src/components/layouts/Event/index.tsx:52
#~ msgid "Check-In"
#~ msgstr "办理登机手续"

#: src/components/forms/WebhookForm/index.tsx:100
msgid "Check-in Created"
msgstr "签到已创建"

#: src/components/forms/WebhookForm/index.tsx:106
msgid "Check-in Deleted"
msgstr "签到已删除"

#: src/components/modals/CreateCheckInListModal/index.tsx:40
msgid "Check-In List created successfully"
msgstr "签到列表创建成功"

#: src/components/common/CheckInListList/index.tsx:33
msgid "Check-In List deleted successfully"
msgstr "签到列表删除成功"

#: src/components/layouts/CheckIn/index.tsx:326
msgid "Check-in list has expired"
msgstr "签到列表已过期"

#: src/components/layouts/CheckIn/index.tsx:343
msgid "Check-in list is not active"
msgstr "签到列表未激活"

#: src/components/layouts/CheckIn/index.tsx:311
msgid "Check-in list not found"
msgstr "未找到签到列表"

#: src/components/layouts/Event/index.tsx:104
#: src/components/routes/event/CheckInLists/index.tsx:32
msgid "Check-In Lists"
msgstr "签到列表"

#: src/components/common/CheckInListList/index.tsx:162
msgid "Check-In URL copied to clipboard"
msgstr "签到链接已复制到剪贴板"

#: src/components/forms/QuestionForm/index.tsx:114
msgid "Checkbox options allow multiple selections"
msgstr "复选框选项允许多重选择"

#: src/components/forms/QuestionForm/index.tsx:112
msgid "Checkboxes"
msgstr "复选框"

#: src/components/common/AttendeeDetails/index.tsx:29
msgid "Checked In"
msgstr "登记入住"

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "Checked in successfully"
#~ msgstr "签到成功"

#: src/components/layouts/Checkout/index.tsx:83
#: src/components/routes/event/Settings/index.tsx:40
msgid "Checkout"
msgstr "结账"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:61
msgid "Checkout Settings"
msgstr "结账设置"

#: src/locales.ts:46
#~ msgid "Chinese"
#~ msgstr "中文"

#: src/components/common/LanguageSwitcher/index.tsx:30
msgid "Chinese (Simplified)"
msgstr "简体中文"

#: src/components/common/LanguageSwitcher/index.tsx:32
msgid "Chinese (Traditional)"
msgstr "中文（繁体）"

#: src/components/routes/event/HomepageDesigner/index.tsx:133
msgid "Choose a color for your background"
msgstr "选择背景颜色"

#: src/components/modals/ChooseAccountModal/index.tsx:13
msgid "Choose an account"
msgstr "选择账户"

#: src/components/common/CheckoutQuestion/index.tsx:162
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:134
#: src/components/routes/product-widget/CollectInformation/index.tsx:333
#: src/components/routes/product-widget/CollectInformation/index.tsx:334
msgid "City"
msgstr "城市"

#: src/components/common/SearchBar/index.tsx:75
msgid "Clear Search Text"
msgstr "清除搜索文本"

#: src/components/routes/product-widget/SelectProducts/index.tsx:288
#~ msgid "click here"
#~ msgstr "点击此处"

#: src/components/common/PromoCodeTable/index.tsx:98
msgid "Click to copy"
msgstr "点击复制"

#: src/components/routes/product-widget/SelectProducts/index.tsx:533
msgid "close"
msgstr "关闭"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:186
#: src/components/modals/RefundOrderModal/index.tsx:125
#: src/components/routes/product-widget/SelectProducts/index.tsx:534
msgid "Close"
msgstr "关闭"

#: src/components/layouts/Event/index.tsx:281
msgid "Close sidebar"
msgstr "关闭侧边栏"

#: src/components/common/PromoCodeTable/index.tsx:69
#: src/components/forms/PromoCodeForm/index.tsx:34
msgid "Code"
msgstr "代码"

#: src/components/modals/CreatePromoCodeModal/index.tsx:26
#: src/components/modals/EditPromoCodeModal/index.tsx:38
msgid "Code must be between 3 and 50 characters long"
msgstr "代码长度必须在 3 至 50 个字符之间"

#: src/components/forms/ProductForm/index.tsx:401
msgid "Collapse this product when the event page is initially loaded"
msgstr "当活动页面初始加载时折叠此产品"

#: src/components/forms/TicketForm/index.tsx:346
#~ msgid "Collapse this ticket when the event page is initially loaded"
#~ msgstr "加载活动页面时折叠此票"

#: src/components/routes/event/HomepageDesigner/index.tsx:131
msgid "Color"
msgstr "颜色"

#: src/components/common/WidgetEditor/index.tsx:21
msgid "Color must be a valid hex color code. Example: #ffffff"
msgstr "颜色必须是有效的十六进制颜色代码。例如#ffffff"

#: src/components/common/WidgetEditor/index.tsx:166
#: src/components/routes/event/HomepageDesigner/index.tsx:124
msgid "Colors"
msgstr "颜色"

#: src/components/layouts/Event/index.tsx:158
msgid "Coming Soon"
msgstr "即将推出"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
msgid "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"
msgstr "以逗号分隔的描述活动的关键字。搜索引擎将使用这些关键字来帮助对活动进行分类和索引"

#: src/components/routes/product-widget/CollectInformation/index.tsx:453
msgid "Complete Order"
msgstr "完整订单"

#: src/components/routes/product-widget/CollectInformation/index.tsx:223
msgid "Complete payment"
msgstr "完成付款"

#: src/components/routes/product-widget/Payment/index.tsx:131
msgid "Complete Payment"
msgstr "完成付款"

#: src/components/layouts/AuthLayout/index.tsx:68
#~ msgid "Complete Store"
#~ msgstr "完整商店"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:202
#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Complete Stripe Setup"
msgstr "完成 Stripe 设置"

#: src/components/routes/event/EventDashboard/index.tsx:127
msgid "Complete these steps to start selling tickets for your event."
msgstr "完成以下步骤即可开始销售您的活动门票。"

#: src/components/modals/SendMessageModal/index.tsx:230
#: src/components/routes/event/GettingStarted/index.tsx:70
#: src/components/routes/event/orders.tsx:23
msgid "Completed"
msgstr "已完成"

#: src/components/common/StatBoxes/index.tsx:51
msgid "Completed orders"
msgstr "已完成订单"

#: src/components/routes/event/EventDashboard/index.tsx:237
msgid "Completed Orders"
msgstr "已完成订单"

#: src/components/common/WidgetEditor/index.tsx:287
msgid "Component Code"
msgstr "组件代码"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:35
msgid "Configured Discount"
msgstr "已配置折扣"

#: src/utilites/confirmationDialog.tsx:11
msgid "Confirm"
msgstr "确认"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:51
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:56
msgid "Confirm Email Change"
msgstr "确认电子邮件更改"

#: src/components/routes/profile/ManageProfile/index.tsx:199
msgid "Confirm New Password"
msgstr "确认新密码"

#: src/components/routes/auth/Register/index.tsx:115
msgid "Confirm password"
msgstr "确认密码"

#: src/components/routes/auth/AcceptInvitation/index.tsx:112
#: src/components/routes/auth/Register/index.tsx:114
#: src/components/routes/auth/ResetPassword/index.tsx:60
msgid "Confirm Password"
msgstr "确认密码"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:36
msgid "Confirming email address..."
msgstr "确认电子邮件地址..."

#: src/components/routes/event/GettingStarted/index.tsx:88
msgid "Congratulations on creating an event!"
msgstr "恭喜你创建了一个活动！"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:173
msgid "Connect Documentation"
msgstr "连接文档"

#: src/components/routes/event/EventDashboard/index.tsx:192
msgid "Connect payment processing"
msgstr "连接支付处理"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:52
#~ msgid "Connect Stripe"
#~ msgstr "连接条纹"

#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Connect to Stripe"
msgstr "连接到 Stripe"

#: src/components/layouts/AuthLayout/index.tsx:89
msgid "Connect with CRM and automate tasks using webhooks and integrations"
msgstr "连接 CRM 并使用 Webhook 和集成自动化任务"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:201
#: src/components/routes/event/GettingStarted/index.tsx:154
msgid "Connect with Stripe"
msgstr "与 Stripe 连接"

#: src/components/routes/event/GettingStarted/index.tsx:150
msgid "Connect your Stripe account to start receiving payments."
msgstr "连接 Stripe 账户，开始接收付款。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:148
msgid "Connected to Stripe"
msgstr "已连接到 Stripe"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:98
msgid "Connection Details"
msgstr "连接详情"

#: src/components/modals/SendMessageModal/index.tsx:167
msgid "Contact Support"
msgstr "联系支持"

#: src/components/modals/SendMessageModal/index.tsx:158
msgid "Contact us to enable messaging"
msgstr "联系我们以启用消息功能"

#: src/components/routes/event/HomepageDesigner/index.tsx:155
msgid "Content background color"
msgstr "内容背景颜色"

#: src/components/common/WidgetEditor/index.tsx:31
#: src/components/common/WidgetEditor/index.tsx:217
#: src/components/layouts/Checkout/CheckoutFooter/index.tsx:38
#: src/components/routes/product-widget/CollectInformation/index.tsx:444
#: src/components/routes/product-widget/SelectProducts/index.tsx:486
msgid "Continue"
msgstr "继续"

#: src/components/routes/event/HomepageDesigner/index.tsx:164
msgid "Continue button text"
msgstr "继续按钮文本"

#: src/components/common/WidgetEditor/index.tsx:216
msgid "Continue Button Text"
msgstr "继续按钮文本"

#: src/components/modals/CreateEventModal/index.tsx:153
msgid "Continue Event Setup"
msgstr "继续活动设置"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Continue set up"
msgstr "继续设置"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
#~ msgid "Continue Stripe Connect Setup"
#~ msgstr "继续 Stripe 连接设置"

#: src/components/routes/product-widget/SelectProducts/index.tsx:337
msgid "Continue to Checkout"
msgstr "继续结账"

#: src/components/routes/product-widget/CollectInformation/index.tsx:380
#~ msgid "Continue To Payment"
#~ msgstr "继续付款"

#: src/components/common/AttendeeTicket/index.tsx:91
#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:243
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copied"
msgstr "复制的"

#: src/components/common/PromoCodeTable/index.tsx:106
msgid "copied to clipboard"
msgstr "复制到剪贴板"

#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copy"
msgstr "复制"

#: src/components/common/CheckInListList/index.tsx:156
msgid "Copy Check-In URL"
msgstr "复制签到链接"

#: src/components/routes/product-widget/CollectInformation/index.tsx:306
msgid "Copy details to all attendees"
msgstr "将详细信息抄送给所有与会者"

#: src/components/common/AttendeeTicket/index.tsx:91
msgid "Copy Link"
msgstr "复制链接"

#: src/components/common/PromoCodeTable/index.tsx:177
#: src/components/modals/ShareModal/index.tsx:243
msgid "Copy URL"
msgstr "复制 URL"

#: src/components/common/CheckoutQuestion/index.tsx:174
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:152
#: src/components/routes/product-widget/CollectInformation/index.tsx:354
msgid "Country"
msgstr "国家"

#: src/components/routes/event/HomepageDesigner/index.tsx:117
msgid "Cover"
msgstr "封面"

#: src/components/routes/event/attendees.tsx:71
#: src/components/routes/event/products.tsx:74
msgid "Create"
msgstr "创建"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
msgid "Create {0}"
msgstr "创建 {0}"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:59
msgid "Create a Product"
msgstr "创建一个产品"

#: src/components/common/PromoCodeTable/index.tsx:57
msgid "Create a Promo Code"
msgstr "创建促销代码"

#: src/components/modals/CreateCheckInListModal/index.tsx:63
msgid "Create a Ticket"
msgstr "创建票单"

#: src/components/routes/auth/Register/index.tsx:69
msgid "Create an account or <0>{0}</0> to get started"
msgstr "创建账户或 <0>{0}</0> 开始使用"

#: src/components/modals/CreateEventModal/index.tsx:120
msgid "create an organizer"
msgstr "创建一个组织者"

#: src/components/layouts/AuthLayout/index.tsx:27
msgid "Create and customize your event page instantly"
msgstr "立即创建和自定义您的活动页面"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Create Attendee"
msgstr "创建与会者"

#: src/components/common/CapacityAssignmentList/index.tsx:63
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:68
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:79
#: src/components/routes/event/CapacityAssignments/index.tsx:46
msgid "Create Capacity Assignment"
msgstr "创建容量分配"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:128
#: src/components/common/ProductsTable/SortableCategory/index.tsx:129
msgid "Create category"
msgstr "创建类别"

#: src/components/modals/CreateProductCategoryModal/index.tsx:45
#: src/components/modals/CreateProductCategoryModal/index.tsx:60
msgid "Create Category"
msgstr "创建类别"

#: src/components/common/CheckInListList/index.tsx:61
#: src/components/modals/CreateCheckInListModal/index.tsx:72
#: src/components/modals/CreateCheckInListModal/index.tsx:82
#: src/components/routes/event/CheckInLists/index.tsx:46
msgid "Create Check-In List"
msgstr "创建签到列表"

#: src/components/common/NoEventsBlankSlate/index.tsx:27
#: src/components/modals/CreateEventModal/index.tsx:85
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:81
#: src/components/routes/welcome/index.tsx:108
msgid "Create Event"
msgstr "创建活动"

#: src/components/routes/events/Dashboard/index.tsx:91
msgid "Create new"
msgstr "创建新的"

#: src/components/forms/OrganizerForm/index.tsx:111
#: src/components/modals/CreateEventModal/index.tsx:93
#: src/components/modals/CreateOrganizerModal/index.tsx:16
msgid "Create Organizer"
msgstr "创建组织器"

#: src/components/modals/CreateProductModal/index.tsx:80
#: src/components/modals/CreateProductModal/index.tsx:88
msgid "Create Product"
msgstr "创建产品"

#: src/components/routes/event/GettingStarted/index.tsx:70
#~ msgid "Create products for your event, set prices, and manage available quantity."
#~ msgstr "为您的活动创建产品，设置价格，并管理可用数量。"

#: src/components/modals/CreatePromoCodeModal/index.tsx:51
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
msgid "Create Promo Code"
msgstr "创建促销代码"

#: src/components/modals/CreateQuestionModal/index.tsx:69
#: src/components/modals/CreateQuestionModal/index.tsx:74
msgid "Create Question"
msgstr "创建问题"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:41
msgid "Create Tax or Fee"
msgstr "创建税费"

#: src/components/modals/CreateTicketModal/index.tsx:83
#: src/components/modals/CreateTicketModal/index.tsx:91
#: src/components/routes/event/tickets.tsx:50
#~ msgid "Create Ticket"
#~ msgstr "创建机票"

#: src/components/routes/event/GettingStarted/index.tsx:122
msgid "Create tickets for your event, set prices, and manage available quantity."
msgstr "为您的活动创建门票、设定价格并管理可用数量。"

#: src/components/modals/CreateWebhookModal/index.tsx:57
#: src/components/modals/CreateWebhookModal/index.tsx:67
msgid "Create Webhook"
msgstr "创建 Webhook"

#: src/components/common/OrdersTable/index.tsx:208
#: src/components/common/OrdersTable/index.tsx:281
msgid "Created"
msgstr "创建"

#: src/components/forms/OrganizerForm/index.tsx:45
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:73
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:100
msgid "Currency"
msgstr "货币"

#: src/components/routes/profile/ManageProfile/index.tsx:191
msgid "Current Password"
msgstr "当前密码"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:159
msgid "Custom Maps URL"
msgstr "自定义地图 URL"

#: src/components/common/ReportTable/index.tsx:53
msgid "Custom Range"
msgstr "自定义范围"

#: src/components/common/OrdersTable/index.tsx:205
msgid "Customer"
msgstr "客户"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:55
msgid "Customize the email and notification settings for this event"
msgstr "自定义此事件的电子邮件和通知设置"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:62
msgid "Customize the event homepage and checkout messaging"
msgstr "定制活动主页和结账信息"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:70
msgid "Customize the miscellaneous settings for this event"
msgstr "自定义此事件的其他设置"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:57
msgid "Customize the SEO settings for this event"
msgstr "自定义此事件的搜索引擎优化设置"

#: src/components/routes/event/GettingStarted/index.tsx:169
msgid "Customize your event page"
msgstr "定制您的活动页面"

#: src/components/layouts/AuthLayout/index.tsx:84
msgid "Customize your event page and widget design to match your brand perfectly"
msgstr "自定义您的活动页面和小部件设计，以完美匹配您的品牌"

#: src/components/routes/event/GettingStarted/index.tsx:165
msgid "Customize your event page to match your brand and style."
msgstr "定制您的活动页面，以符合您的品牌和风格。"

#: src/components/routes/event/Reports/index.tsx:23
msgid "Daily Sales Report"
msgstr "每日销售报告"

#: src/components/routes/event/Reports/index.tsx:24
msgid "Daily sales, tax, and fee breakdown"
msgstr "每日销售、税费和费用明细"

#: src/components/common/CapacityAssignmentList/index.tsx:156
#: src/components/common/CheckInListList/index.tsx:176
#: src/components/common/OrdersTable/index.tsx:186
#: src/components/common/ProductsTable/SortableProduct/index.tsx:287
#: src/components/common/PromoCodeTable/index.tsx:181
#: src/components/common/QuestionsTable/index.tsx:115
#: src/components/common/WebhookTable/index.tsx:116
msgid "Danger zone"
msgstr "危险区"

#: src/components/common/AttendeeTable/index.tsx:179
msgid "Danger Zone"
msgstr "危险区"

#: src/components/layouts/Event/index.tsx:89
msgid "Dashboard"
msgstr "仪表板"

#: src/components/common/OrderDetails/index.tsx:39
#: src/components/forms/QuestionForm/index.tsx:136
msgid "Date"
msgstr "日期"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:40
#~ msgid "Date & Time"
#~ msgstr "日期和时间"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:38
msgid "Day one capacity"
msgstr "第一天容量"

#: src/components/forms/CheckInListForm/index.tsx:21
#~ msgid "Day one check-in list"
#~ msgstr "第一天签到列表"

#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/TaxAndFeeList/index.tsx:73
msgid "Delete"
msgstr "删除"

#: src/components/common/CapacityAssignmentList/index.tsx:159
msgid "Delete Capacity"
msgstr "删除容量"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:146
#: src/components/common/ProductsTable/SortableCategory/index.tsx:147
msgid "Delete category"
msgstr "删除类别"

#: src/components/common/CheckInListList/index.tsx:179
msgid "Delete Check-In List"
msgstr "删除签到列表"

#: src/components/common/PromoCodeTable/index.tsx:185
msgid "Delete code"
msgstr "删除代码"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:113
msgid "Delete Cover"
msgstr "删除封面"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:98
msgid "Delete Image"
msgstr "删除图像"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:292
msgid "Delete product"
msgstr "删除产品"

#: src/components/common/QuestionsTable/index.tsx:120
msgid "Delete question"
msgstr "删除问题"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:199
#~ msgid "Delete ticket"
#~ msgstr "删除机票"

#: src/components/common/WebhookTable/index.tsx:127
msgid "Delete webhook"
msgstr "删除 Webhook"

#: src/components/forms/ProductForm/index.tsx:258
#: src/components/forms/TaxAndFeeForm/index.tsx:81
#: src/components/modals/CreateEventModal/index.tsx:135
#: src/components/modals/DuplicateEventModal/index.tsx:84
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:78
msgid "Description"
msgstr "说明"

#: src/components/forms/CheckInListForm/index.tsx:36
msgid "Description for check-in staff"
msgstr "签到工作人员的描述"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Details"
msgstr "详细信息"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
#~ msgid "Disable this capacity track capacity without stopping product sales"
#~ msgstr "禁用此容量以跟踪容量，而不停止产品销售"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:26
#~ msgid "Disable this capacity track capacity without stopping ticket sales"
#~ msgstr "禁用此容量以追踪容量而不停止售票"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
msgid "Disabling this capacity will track sales but not stop them when the limit is reached"
msgstr "禁用此容量将跟踪销售情况，但不会在达到限制时停止销售"

#: src/components/common/PromoCodeTable/index.tsx:70
msgid "Discount"
msgstr "折扣"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount %"
msgstr "折扣率"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount in {0}"
msgstr "{0}中的折扣"

#: src/components/forms/PromoCodeForm/index.tsx:43
msgid "Discount Type"
msgstr "折扣类型"

#: src/components/routes/product-widget/SelectProducts/index.tsx:297
#~ msgid "Dismiss"
#~ msgstr "解散"

#: src/components/routes/product-widget/SelectProducts/index.tsx:354
msgid "Dismiss this message"
msgstr "关闭此消息"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:190
msgid "Document Label"
msgstr "文档标签"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:222
msgid "Documentation"
msgstr "文档"

#: src/components/layouts/AuthLayout/index.tsx:19
#~ msgid "Does not exist"
#~ msgstr "不存在"

#: src/components/routes/auth/Login/index.tsx:57
msgid "Don't have an account?   <0>Sign up</0>"
msgstr "还没有账户？   <0>注册</0>"

#: src/components/routes/auth/Login/index.tsx:72
#~ msgid "Don't have an account?   <0>Sign Up</0>"
#~ msgstr "没有帐户？   <0>注册</0>"

#: src/components/forms/ProductForm/index.tsx:157
msgid "Donation / Pay what you'd like product"
msgstr "捐赠 / 自由定价产品"

#: src/components/forms/TicketForm/index.tsx:139
#~ msgid "Donation / Pay what you'd like ticket"
#~ msgstr "捐款/自费门票"

#: src/components/common/AddEventToCalendarButton/index.tsx:137
msgid "Download .ics"
msgstr "下载 .ics"

#: src/components/common/DownloadCsvButton/index.tsx:48
msgid "Download CSV"
msgstr "下载 CSV"

#: src/components/common/OrdersTable/index.tsx:162
msgid "Download invoice"
msgstr "下载发票"

#: src/components/layouts/Checkout/index.tsx:126
msgid "Download Invoice"
msgstr "下载发票"

#: src/components/modals/ShareModal/index.tsx:224
msgid "Download QR Code"
msgstr "下载二维码"

#: src/components/common/OrdersTable/index.tsx:111
#: src/components/layouts/Checkout/index.tsx:49
msgid "Downloading Invoice"
msgstr "正在下载发票"

#: src/components/layouts/Event/index.tsx:188
msgid "Draft"
msgstr "草稿"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:83
msgid "Drag and drop or click"
msgstr "拖放或点击"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Drag to sort"
#~ msgstr "拖动排序"

#: src/components/forms/QuestionForm/index.tsx:124
msgid "Dropdown selection"
msgstr "下拉选择"

#: src/components/modals/SendMessageModal/index.tsx:159
msgid ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."
msgstr ""
"由于存在较高的垃圾邮件风险，我们要求进行人工验证后才能发送消息。\n"
"请联系我们以请求访问权限。"

#: src/components/modals/DuplicateEventModal/index.tsx:124
msgid "Duplicate Capacity Assignments"
msgstr "复制容量分配"

#: src/components/modals/DuplicateEventModal/index.tsx:128
msgid "Duplicate Check-In Lists"
msgstr "复制签到列表"

#: src/components/common/EventCard/index.tsx:162
msgid "Duplicate event"
msgstr "复制活动"

#: src/components/modals/DuplicateEventModal/index.tsx:69
#: src/components/modals/DuplicateEventModal/index.tsx:142
msgid "Duplicate Event"
msgstr "复制活动"

#: src/components/modals/DuplicateEventModal/index.tsx:132
msgid "Duplicate Event Cover Image"
msgstr "复制事件封面图像"

#: src/components/modals/DuplicateEventModal/index.tsx:103
msgid "Duplicate Options"
msgstr "复制选项"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:285
#: src/components/modals/DuplicateProductModal/index.tsx:109
#: src/components/modals/DuplicateProductModal/index.tsx:113
msgid "Duplicate Product"
msgstr "复制产品"

#: src/components/modals/DuplicateEventModal/index.tsx:108
msgid "Duplicate Products"
msgstr "复制产品"

#: src/components/modals/DuplicateEventModal/index.tsx:120
msgid "Duplicate Promo Codes"
msgstr "复制促销代码"

#: src/components/modals/DuplicateEventModal/index.tsx:112
msgid "Duplicate Questions"
msgstr "复制问题"

#: src/components/modals/DuplicateEventModal/index.tsx:116
msgid "Duplicate Settings"
msgstr "复制设置"

#: src/components/modals/DuplicateEventModal/index.tsx:107
#~ msgid "Duplicate Tickets"
#~ msgstr "复制票"

#: src/components/modals/DuplicateEventModal/index.tsx:137
msgid "Duplicate Webhooks"
msgstr "复制 Webhooks"

#: src/components/common/LanguageSwitcher/index.tsx:24
msgid "Dutch"
msgstr "荷兰语"

#: src/components/forms/ProductForm/index.tsx:78
msgid "Early bird"
msgstr "早起的鸟儿"

#: src/components/common/TaxAndFeeList/index.tsx:65
#: src/components/modals/ManageAttendeeModal/index.tsx:221
#: src/components/modals/ManageOrderModal/index.tsx:203
msgid "Edit"
msgstr "编辑"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
msgid "Edit {0}"
msgstr "编辑 {0}"

#: src/components/common/QuestionAndAnswerList/index.tsx:159
msgid "Edit Answer"
msgstr "编辑答案"

#: src/components/common/AttendeeTable/index.tsx:174
#~ msgid "Edit attendee"
#~ msgstr "编辑与会者"

#: src/components/modals/EditAttendeeModal/index.tsx:72
#: src/components/modals/EditAttendeeModal/index.tsx:110
#~ msgid "Edit Attendee"
#~ msgstr "编辑参会者"

#: src/components/common/CapacityAssignmentList/index.tsx:146
msgid "Edit Capacity"
msgstr "编辑容量"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:66
#: src/components/modals/EditCapacityAssignmentModal/index.tsx:74
msgid "Edit Capacity Assignment"
msgstr "编辑容量分配"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:137
#: src/components/common/ProductsTable/SortableCategory/index.tsx:138
msgid "Edit category"
msgstr "编辑类别"

#: src/components/common/CheckInListList/index.tsx:148
#: src/components/modals/EditCheckInListModal/index.tsx:69
#: src/components/modals/EditCheckInListModal/index.tsx:91
msgid "Edit Check-In List"
msgstr "编辑签到列表"

#: src/components/common/PromoCodeTable/index.tsx:170
msgid "Edit Code"
msgstr "编辑代码"

#: src/components/modals/EditOrganizerModal/index.tsx:63
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:51
msgid "Edit Organizer"
msgstr "编辑组织器"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:280
#: src/components/modals/EditProductModal/index.tsx:100
#: src/components/modals/EditProductModal/index.tsx:108
msgid "Edit Product"
msgstr "编辑产品"

#: src/components/modals/EditProductCategoryModal/index.tsx:62
#: src/components/modals/EditProductCategoryModal/index.tsx:70
msgid "Edit Product Category"
msgstr "编辑产品类别"

#: src/components/modals/EditPromoCodeModal/index.tsx:79
#: src/components/modals/EditPromoCodeModal/index.tsx:84
msgid "Edit Promo Code"
msgstr "编辑促销代码"

#: src/components/common/QuestionsTable/index.tsx:112
msgid "Edit question"
msgstr "编辑问题"

#: src/components/modals/EditQuestionModal/index.tsx:95
#: src/components/modals/EditQuestionModal/index.tsx:101
msgid "Edit Question"
msgstr "编辑问题"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:191
#: src/components/modals/EditTicketModal/index.tsx:96
#: src/components/modals/EditTicketModal/index.tsx:104
#~ msgid "Edit Ticket"
#~ msgstr "编辑机票"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Edit user"
msgstr "编辑用户"

#: src/components/modals/EditUserModal/index.tsx:62
#: src/components/modals/EditUserModal/index.tsx:120
msgid "Edit User"
msgstr "编辑用户"

#: src/components/common/WebhookTable/index.tsx:104
msgid "Edit webhook"
msgstr "编辑 Webhook"

#: src/components/modals/EditWebhookModal/index.tsx:66
#: src/components/modals/EditWebhookModal/index.tsx:87
msgid "Edit Webhook"
msgstr "编辑 Webhook"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 2.50 for $2.50"
msgstr "例如2.50 换 2.50"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 23.5 for 23.5%"
msgstr "例如23.5 表示 23.5%"

#: src/components/common/AttendeeDetails/index.tsx:21
#: src/components/common/AttendeeTable/index.tsx:109
#: src/components/common/OrderDetails/index.tsx:31
#: src/components/forms/OrganizerForm/index.tsx:32
#: src/components/modals/EditUserModal/index.tsx:80
#: src/components/modals/InviteUserModal/index.tsx:61
#: src/components/routes/auth/AcceptInvitation/index.tsx:98
#: src/components/routes/auth/Login/index.tsx:68
#: src/components/routes/auth/Register/index.tsx:97
#: src/components/routes/event/Settings/index.tsx:52
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:117
#: src/components/routes/profile/ManageProfile/index.tsx:138
msgid "Email"
msgstr "电子邮件"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
msgid "Email & Notification Settings"
msgstr "电子邮件和通知设置"

#: src/components/modals/CreateAttendeeModal/index.tsx:132
#: src/components/modals/ManageAttendeeModal/index.tsx:114
#: src/components/modals/ManageOrderModal/index.tsx:157
msgid "Email address"
msgstr "电子邮件地址"

#: src/components/common/QuestionsTable/index.tsx:220
#: src/components/common/QuestionsTable/index.tsx:221
#: src/components/routes/product-widget/CollectInformation/index.tsx:298
#: src/components/routes/product-widget/CollectInformation/index.tsx:299
#: src/components/routes/product-widget/CollectInformation/index.tsx:408
#: src/components/routes/product-widget/CollectInformation/index.tsx:409
msgid "Email Address"
msgstr "电子邮件地址"

#: src/components/routes/profile/ManageProfile/index.tsx:77
msgid "Email change cancelled successfully"
msgstr "电子邮件更改已成功取消"

#: src/components/routes/profile/ManageProfile/index.tsx:116
msgid "Email change pending"
msgstr "电子邮件更改待定"

#: src/components/routes/profile/ManageProfile/index.tsx:151
msgid "Email confirmation resent"
msgstr "重新发送电子邮件确认"

#: src/components/routes/profile/ManageProfile/index.tsx:90
msgid "Email confirmation resent successfully"
msgstr "成功重新发送电子邮件确认"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:66
msgid "Email footer message"
msgstr "电子邮件页脚信息"

#: src/components/routes/profile/ManageProfile/index.tsx:141
msgid "Email not verified"
msgstr "电子邮件未经验证"

#: src/components/common/WidgetEditor/index.tsx:272
msgid "Embed Code"
msgstr "嵌入代码"

#: src/components/common/WidgetEditor/index.tsx:260
msgid "Embed Script"
msgstr "嵌入脚本"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:181
msgid "Enable Invoicing"
msgstr "启用发票功能"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:21
msgid "Enable this capacity to stop product sales when the limit is reached"
msgstr "启用此容量以在达到限制时停止产品销售"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:20
#~ msgid "Enable this capacity to stop ticket sales when the limit is reached"
#~ msgstr "启用此容量以在达到限制时停止售票"

#: src/components/forms/WebhookForm/index.tsx:19
msgid "Enabled"
msgstr "已启用"

#: src/components/modals/CreateEventModal/index.tsx:149
#: src/components/modals/DuplicateEventModal/index.tsx:98
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:92
#: src/components/routes/welcome/index.tsx:96
msgid "End Date"
msgstr "结束日期"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:26
#: src/components/common/EventStatusBadge/index.tsx:14
msgid "Ended"
msgstr "完工"

#: src/components/routes/events/Dashboard/index.tsx:57
msgid "Ended Events"
msgstr "已结束的活动"

#: src/components/common/LanguageSwitcher/index.tsx:16
#: src/components/common/LanguageSwitcher/index.tsx:50
#: src/components/modals/CreateAttendeeModal/index.tsx:145
#: src/components/routes/profile/ManageProfile/index.tsx:173
msgid "English"
msgstr "英语"

#: src/components/modals/CreateAttendeeModal/index.tsx:170
msgid "Enter an amount excluding taxes and fees."
msgstr "输入不含税费的金额。"

#: src/components/common/OrdersTable/index.tsx:119
#: src/components/layouts/Checkout/index.tsx:57
msgid "Error"
msgstr "错误"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:27
msgid "Error confirming email address"
msgstr "确认电子邮件地址出错"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:35
msgid "Error confirming email change"
msgstr "确认更改电子邮件时出错"

#: src/components/modals/WebhookLogsModal/index.tsx:166
msgid "Error loading logs"
msgstr "加载日志时出错"

#: src/components/forms/OrganizerForm/index.tsx:46
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:74
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:101
msgid "EUR"
msgstr "欧元"

#: src/components/routes/events/Dashboard/index.tsx:103
msgid "Event"
msgstr "活动"

#: src/components/modals/CreateEventModal/index.tsx:76
#~ msgid "Event created successfully 🎉"
#~ msgstr "成功创建事件 🎉"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:161
msgid "Event Date"
msgstr "活动日期"

#: src/components/routes/account/ManageAccount/sections/EventDefaultsSettings/index.tsx:5
msgid "Event Defaults"
msgstr "事件默认值"

#: src/components/routes/event/Settings/index.tsx:28
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:276
msgid "Event Details"
msgstr "活动详情"

#: src/components/modals/DuplicateEventModal/index.tsx:58
msgid "Event duplicated successfully"
msgstr "事件成功复制"

#: src/components/layouts/Checkout/index.tsx:79
msgid "Event Homepage"
msgstr "活动主页"

#: src/components/layouts/Event/index.tsx:166
#~ msgid "Event is not visible to the public"
#~ msgstr "公众无法看到活动"

#: src/components/layouts/Event/index.tsx:165
#~ msgid "Event is visible to the public"
#~ msgstr "活动对公众可见"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:85
msgid "Event location & venue details"
msgstr "活动地点和场地详情"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:12
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
msgid "Event Not Available"
msgstr "活动不可用"

#: src/components/layouts/Event/index.tsx:187
#~ msgid "Event page"
#~ msgstr "活动页面"

#: src/components/layouts/Event/index.tsx:212
msgid "Event Page"
msgstr "活动页面"

#: src/components/common/EventCard/index.tsx:70
#: src/components/layouts/Event/index.tsx:80
#: src/components/routes/event/EventDashboard/index.tsx:76
#: src/components/routes/event/GettingStarted/index.tsx:63
msgid "Event status update failed. Please try again later"
msgstr "活动状态更新失败。请稍后再试"

#: src/components/common/EventCard/index.tsx:67
#: src/components/layouts/Event/index.tsx:77
#: src/components/routes/event/EventDashboard/index.tsx:73
#: src/components/routes/event/GettingStarted/index.tsx:60
msgid "Event status updated"
msgstr "事件状态已更新"

#: src/components/common/WebhookTable/index.tsx:237
#: src/components/forms/WebhookForm/index.tsx:122
msgid "Event Types"
msgstr "事件类型"

#: src/components/modals/ShareModal/index.tsx:237
msgid "Event URL"
msgstr "活动链接"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
#~ msgid "Event wdwdeNot Available"
#~ msgstr ""

#: src/components/layouts/Event/index.tsx:226
msgid "Events"
msgstr "活动"

#: src/components/forms/CheckInListForm/index.tsx:56
msgid "Expiration date"
msgstr "过期日期"

#: src/components/common/PromoCodeTable/index.tsx:73
msgid "Expires"
msgstr "到期"

#: src/components/forms/PromoCodeForm/index.tsx:79
msgid "Expiry Date"
msgstr "有效期"

#: src/components/routes/event/attendees.tsx:80
#: src/components/routes/event/orders.tsx:140
msgid "Export"
msgstr "出口"

#: src/components/common/QuestionsTable/index.tsx:267
msgid "Export answers"
msgstr "导出答案"

#: src/mutations/useExportAnswers.ts:39
msgid "Export failed. Please try again."
msgstr "导出失败。请重试。"

#: src/mutations/useExportAnswers.ts:17
msgid "Export started. Preparing file..."
msgstr "导出已开始。正在准备文件..."

#: src/components/routes/event/attendees.tsx:39
msgid "Exporting Attendees"
msgstr "正在导出与会者"

#: src/mutations/useExportAnswers.ts:33
msgid "Exporting complete. Downloading file..."
msgstr "导出完成。正在下载文件..."

#: src/components/routes/event/orders.tsx:90
msgid "Exporting Orders"
msgstr "正在导出订单"

#: src/components/common/AttendeeTable/index.tsx:97
msgid "Failed to cancel attendee"
msgstr "取消与会者失败"

#: src/components/modals/CancelOrderModal/index.tsx:33
msgid "Failed to cancel order"
msgstr "取消订单失败"

#: src/components/common/QuestionsTable/index.tsx:175
msgid "Failed to delete message. Please try again."
msgstr "删除信息失败。请重试。"

#: src/components/common/OrdersTable/index.tsx:120
#: src/components/layouts/Checkout/index.tsx:58
msgid "Failed to download invoice. Please try again."
msgstr "下载发票失败。请重试。"

#: src/components/routes/event/attendees.tsx:48
msgid "Failed to export attendees"
msgstr "导出与会者失败"

#: src/components/routes/event/attendees.tsx:39
#~ msgid "Failed to export attendees. Please try again."
#~ msgstr "导出与会者失败。请重试。"

#: src/components/routes/event/orders.tsx:99
msgid "Failed to export orders"
msgstr "导出订单失败"

#: src/components/routes/event/orders.tsx:88
#~ msgid "Failed to export orders. Please try again."
#~ msgstr "导出订单失败。请重试。"

#: src/components/modals/EditCheckInListModal/index.tsx:79
msgid "Failed to load Check-In List"
msgstr "加载签到列表失败"

#: src/components/modals/EditWebhookModal/index.tsx:75
msgid "Failed to load Webhook"
msgstr "加载 Webhook 失败"

#: src/components/common/AttendeeTable/index.tsx:51
#~ msgid "Failed to resend product email"
#~ msgstr "重新发送产品电子邮件失败"

#: src/components/common/AttendeeTable/index.tsx:50
msgid "Failed to resend ticket email"
msgstr "重新发送票据电子邮件失败"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:179
msgid "Failed to sort products"
msgstr "产品排序失败"

#: src/mutations/useExportAnswers.ts:15
msgid "Failed to start export job"
msgstr "无法启动导出任务"

#: src/components/common/QuestionAndAnswerList/index.tsx:102
msgid "Failed to update answer."
msgstr "更新答案失败。"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:64
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:69
msgid "Failed to upload image."
msgstr "上传图片失败。"

#: src/components/forms/TaxAndFeeForm/index.tsx:18
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Fee"
msgstr "费用"

#: src/components/common/GlobalMenu/index.tsx:30
#~ msgid "Feedback"
#~ msgstr "反馈意见"

#: src/components/forms/ProductForm/index.tsx:366
msgid "Fees"
msgstr "费用"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:80
msgid "Fees are subject to change. You will be notified of any changes to your fee structure."
msgstr "费用可能会变更。如有变更，您将收到通知。"

#: src/components/routes/event/orders.tsx:121
msgid "Filter Orders"
msgstr "筛选订单"

#: src/components/common/FilterModal/index.tsx:75
#: src/components/common/FilterModal/index.tsx:223
msgid "Filters"
msgstr "筛选器"

#: src/components/common/FilterModal/index.tsx:223
msgid "Filters ({activeFilterCount})"
msgstr "筛选器 ({activeFilterCount})"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:206
msgid "First Invoice Number"
msgstr "第一张发票号码"

#: src/components/common/QuestionsTable/index.tsx:208
#: src/components/modals/CreateAttendeeModal/index.tsx:118
#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:144
#: src/components/routes/product-widget/CollectInformation/index.tsx:284
#: src/components/routes/product-widget/CollectInformation/index.tsx:395
msgid "First name"
msgstr "姓名"

#: src/components/common/QuestionsTable/index.tsx:207
#: src/components/modals/EditUserModal/index.tsx:71
#: src/components/modals/InviteUserModal/index.tsx:57
#: src/components/routes/auth/AcceptInvitation/index.tsx:92
#: src/components/routes/auth/Register/index.tsx:83
#: src/components/routes/product-widget/CollectInformation/index.tsx:283
#: src/components/routes/product-widget/CollectInformation/index.tsx:394
#: src/components/routes/profile/ManageProfile/index.tsx:135
msgid "First Name"
msgstr "姓名"

#: src/components/routes/auth/AcceptInvitation/index.tsx:29
msgid "First name must be between 1 and 50 characters"
msgstr "名字必须在 1 至 50 个字符之间"

#: src/components/common/QuestionsTable/index.tsx:349
msgid "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."
msgstr "名字、姓氏和电子邮件地址为默认问题，在结账过程中始终包含。"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
msgid "First Used"
msgstr "首次使用"

#: src/components/forms/TaxAndFeeForm/index.tsx:33
msgid "Fixed"
msgstr "固定式"

#: src/components/forms/PromoCodeForm/index.tsx:55
msgid "Fixed amount"
msgstr "固定金额"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:65
msgid "Fixed Fee:"
msgstr "固定费用："

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:123
msgid "Flash is not available on this device"
msgstr "该设备不支持闪存"

#: src/components/layouts/AuthLayout/index.tsx:58
msgid "Flexible Ticketing"
msgstr "灵活的票务系统"

#: src/components/routes/auth/Login/index.tsx:80
msgid "Forgot password?"
msgstr "忘记密码？"

#: src/components/common/AttendeeTicket/index.tsx:40
#: src/components/common/Currency/index.tsx:90
#: src/components/common/OrderSummary/index.tsx:46
#: src/components/common/OrderSummary/index.tsx:99
#: src/components/common/ProductsTable/SortableProduct/index.tsx:93
#: src/components/common/ProductsTable/SortableProduct/index.tsx:103
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:53
msgid "Free"
msgstr "免费"

#: src/components/forms/ProductForm/index.tsx:151
msgid "Free Product"
msgstr "免费产品"

#: src/components/forms/ProductForm/index.tsx:153
msgid "Free product, no payment information required"
msgstr "免费产品，无需付款信息"

#: src/components/forms/TicketForm/index.tsx:133
#~ msgid "Free Ticket"
#~ msgstr "免费门票"

#: src/components/forms/TicketForm/index.tsx:135
#~ msgid "Free ticket, no payment information required"
#~ msgstr "免费门票，无需支付信息"

#: src/components/common/LanguageSwitcher/index.tsx:20
msgid "French"
msgstr "法语"

#: src/components/layouts/AuthLayout/index.tsx:88
msgid "Fully Integrated"
msgstr "全面集成"

#: src/components/forms/ProductForm/index.tsx:136
msgid "General"
msgstr "常规"

#: src/components/common/LanguageSwitcher/index.tsx:14
msgid "German"
msgstr "德国"

#: src/components/layouts/AuthLayout/index.tsx:35
msgid "Get started for free, no subscription fees"
msgstr "免费开始，无订阅费用"

#: src/components/routes/event/EventDashboard/index.tsx:125
msgid "Get your event ready"
msgstr "准备好您的活动"

#: src/components/layouts/Event/index.tsx:88
msgid "Getting Started"
msgstr "入门"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:23
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:42
msgid "Go back to profile"
msgstr "返回个人资料"

#: src/components/routes/product-widget/CollectInformation/index.tsx:239
msgid "Go to event homepage"
msgstr "前往活动主页"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:53
msgid "Go to Hi.Events"
msgstr "前往 Hi.Events"

#: src/components/common/ErrorDisplay/index.tsx:64
msgid "Go to home page"
msgstr "返回主页"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:162
msgid "Go to Stripe Dashboard"
msgstr "前往 Stripe 仪表板"

#: src/components/common/AddEventToCalendarButton/index.tsx:128
msgid "Google Calendar"
msgstr "谷歌日历"

#: src/components/common/EventCard/index.tsx:133
msgid "gross sales"
msgstr "总销售额"

#: src/components/common/StatBoxes/index.tsx:39
msgid "Gross sales"
msgstr "销售总额"

#: src/components/routes/event/EventDashboard/index.tsx:274
msgid "Gross Sales"
msgstr "销售总额"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:281
msgid "Guests"
msgstr "宾客"

#: src/components/routes/product-widget/SelectProducts/index.tsx:495
msgid "Have a promo code?"
msgstr "有促销代码吗？"

#: src/components/forms/OrganizerForm/index.tsx:33
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/GlobalMenu/index.tsx:24
#~ msgid "Help & Support"
#~ msgstr "帮助与支持"

#: src/components/common/WidgetEditor/index.tsx:295
msgid "Here is an example of how you can use the component in your application."
msgstr "下面举例说明如何在应用程序中使用该组件。"

#: src/components/common/WidgetEditor/index.tsx:284
msgid "Here is the React component you can use to embed the widget in your application."
msgstr "下面是 React 组件，您可以用它在应用程序中嵌入 widget。"

#: src/components/routes/event/EventDashboard/index.tsx:101
msgid "Hi {0} 👋"
msgstr "你好 {0} 👋"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:43
msgid "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."
msgstr "Hi.Events 收取平台费用以维护和改进我们的服务。这些费用会自动从每笔交易中扣除。"

#: src/components/modals/CreateEventModal/index.tsx:130
#: src/components/modals/DuplicateEventModal/index.tsx:79
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:73
msgid "Hi.Events Conference {0}"
msgstr "Hi.Events Conference {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:117
msgid "Hi.Events Conference Center"
msgstr "Hi.Events 会议中心"

#: src/components/layouts/AuthLayout/index.tsx:131
msgid "hi.events logo"
msgstr "hi.events 徽标"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:79
msgid "Hidden from public view"
msgstr "隐藏于公众视线之外"

#: src/components/common/QuestionsTable/index.tsx:290
msgid "hidden question"
msgstr "隐藏问题"

#: src/components/common/QuestionsTable/index.tsx:291
msgid "hidden questions"
msgstr "隐藏问题"

#: src/components/forms/QuestionForm/index.tsx:218
msgid "Hidden questions are only visible to the event organizer and not to the customer."
msgstr "隐藏问题只有活动组织者可以看到，客户看不到。"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/routes/product-widget/SelectProducts/index.tsx:460
msgid "Hide"
msgstr "隐藏"

#: src/components/common/AttendeeList/index.tsx:84
msgid "Hide Answers"
msgstr "隐藏答案"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:89
msgid "Hide getting started page"
msgstr "隐藏入门页面"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Hide hidden questions"
msgstr "隐藏隐藏问题"

#: src/components/forms/ProductForm/index.tsx:399
msgid "Hide product after sale end date"
msgstr "在销售结束日期后隐藏产品"

#: src/components/forms/ProductForm/index.tsx:397
msgid "Hide product before sale start date"
msgstr "在销售开始日期前隐藏产品"

#: src/components/forms/ProductForm/index.tsx:412
msgid "Hide product unless user has applicable promo code"
msgstr "除非用户有适用的促销代码，否则隐藏产品"

#: src/components/forms/ProductForm/index.tsx:405
msgid "Hide product when sold out"
msgstr "售罄时隐藏产品"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:90
msgid "Hide the getting started page from the sidebar"
msgstr "隐藏侧边栏中的入门页面"

#: src/components/forms/ProductForm/index.tsx:417
msgid "Hide this product from customers"
msgstr "对客户隐藏此产品"

#: src/components/forms/QuestionForm/index.tsx:219
msgid "Hide this question"
msgstr "隐藏此问题"

#: src/components/forms/TicketForm/index.tsx:362
#~ msgid "Hide this ticket from customers"
#~ msgstr "向客户隐藏此票据"

#: src/components/forms/ProductForm/index.tsx:103
msgid "Hide this tier from users"
msgstr "向用户隐藏此层级"

#: src/components/forms/TicketForm/index.tsx:344
#~ msgid "Hide ticket after sale end date"
#~ msgstr "隐藏销售截止日期后的机票"

#: src/components/forms/TicketForm/index.tsx:342
#~ msgid "Hide ticket before sale start date"
#~ msgstr "在开售日期前隐藏机票"

#: src/components/forms/TicketForm/index.tsx:357
#~ msgid "Hide ticket unless user has applicable promo code"
#~ msgstr "隐藏机票，除非用户有适用的促销代码"

#: src/components/forms/TicketForm/index.tsx:350
#~ msgid "Hide ticket when sold out"
#~ msgstr "售罄后隐藏门票"

#: src/components/forms/ProductForm/index.tsx:101
msgid "Hiding a product will prevent users from seeing it on the event page."
msgstr "隐藏产品将防止用户在活动页面上看到它。"

#: src/components/forms/TicketForm/index.tsx:98
#~ msgid "Hiding a ticket will prevent users from seeing it on the event page."
#~ msgstr "隐藏票单将阻止用户在活动页面上看到该票单。"

#: src/components/routes/event/HomepageDesigner/index.tsx:115
msgid "Homepage Design"
msgstr "主页设计"

#: src/components/layouts/Event/index.tsx:109
msgid "Homepage Designer"
msgstr "主页设计师"

#: src/components/routes/event/HomepageDesigner/index.tsx:174
msgid "Homepage Preview"
msgstr "主页预览"

#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:145
msgid "Homer"
msgstr "荷马"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
msgid "How many minutes the customer has to complete their order. We recommend at least 15 minutes"
msgstr "客户有多少分钟来完成订单。我们建议至少 15 分钟"

#: src/components/forms/PromoCodeForm/index.tsx:84
msgid "How many times can this code be used?"
msgstr "这个代码可以使用多少次？"

#: src/components/common/Editor/index.tsx:75
msgid "HTML character limit exceeded: {htmlLength}/{maxLength}"
msgstr "HTML字符限制已超出：{htmlLength}/{maxLength}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:160
msgid "https://example-maps-service.com/..."
msgstr "https://example-maps-service.com/..."

#: src/components/forms/WebhookForm/index.tsx:118
msgid "https://webhook-domain.com/webhook"
msgstr "https://webhook-domain.com/webhook"

#: src/components/routes/auth/AcceptInvitation/index.tsx:118
msgid "I agree to the <0>terms and conditions</0>"
msgstr "我同意<0>条款和条件</0>。"

#: src/components/routes/product-widget/Payment/index.tsx:106
msgid "I would like to pay using an offline method"
msgstr "我想使用线下支付方式付款"

#: src/components/routes/product-widget/Payment/index.tsx:107
msgid "I would like to pay using an online method (credit card etc.)"
msgstr "我想使用在线支付方式（如信用卡）付款"

#: src/components/routes/product-widget/SelectProducts/index.tsx:315
msgid "If a new tab did not open automatically, please click the button below to continue to checkout."
msgstr "如果没有自动打开新标签页，请点击下方按钮继续结账。"

#: src/components/routes/product-widget/SelectProducts/index.tsx:284
#~ msgid "If a new tab did not open, please  <0><1>{0}</1>.</0>"
#~ msgstr "如果新标签页没有打开，请<0><1>{0}</1>.</0>。"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:149
#~ msgid "If blank, the address will be used to generate a Google map link"
#~ msgstr "如果为空，该地址将用于生成 Google 地图链接"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:158
msgid "If blank, the address will be used to generate a Google Mapa link"
msgstr "如果为空，将使用地址生成 Google 地图链接"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:158
msgid "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."
msgstr "如果启用，登记工作人员可以将与会者标记为已登记或将订单标记为已支付并登记与会者。如果禁用，关联未支付订单的与会者无法登记。"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:76
msgid "If enabled, the organizer will receive an email notification when a new order is placed"
msgstr "如果启用，当有新订单时，组织者将收到电子邮件通知"

#: src/components/routes/profile/ManageProfile/index.tsx:122
msgid "If you did not request this change, please immediately change your password."
msgstr "如果您没有要求更改密码，请立即更改密码。"

#: src/components/routes/auth/ForgotPassword/index.tsx:40
msgid ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."
msgstr "如果您在我们这里有账户，您将收到一封电子邮件，内含重置密码的说明。"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:108
msgid "Image deleted successfully"
msgstr "图像已成功删除"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
#~ msgid "Image dimensions must be between 3000px by 2000px. With a max height of 2000px and max width of 3000px"
#~ msgstr "图片尺寸必须在 3000px x 2000px 之间。最大高度为 2000px，最大宽度为 3000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
msgid "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"
msgstr "图像尺寸必须在4000px和4000px之间。最大高度为4000px，最大宽度为4000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:49
msgid "Image must be less than 5MB"
msgstr "图片必须小于 5MB"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:63
msgid "Image uploaded successfully"
msgstr "图片上传成功"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:111
msgid "Image URL"
msgstr "图片网址"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
msgid "Image width must be at least 900px and height at least 50px"
msgstr "图片宽度必须至少为 900px，高度必须至少为 50px"

#: src/components/layouts/AuthLayout/index.tsx:53
msgid "In-depth Analytics"
msgstr "深入分析"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:25
#: src/components/modals/EditUserModal/index.tsx:111
msgid "Inactive"
msgstr "不活动"

#: src/components/modals/EditUserModal/index.tsx:107
msgid "Inactive users cannot log in."
msgstr "非活动用户无法登录。"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee product page"
#~ msgstr "包括您的在线活动的连接详细信息。这些详细信息将显示在订单摘要页面和参会者产品页面上"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page"
#~ msgstr "包括在线活动的连接详情。这些详细信息将显示在订单摘要页面和与会者门票页面上"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:102
msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."
msgstr "包含您的在线活动的连接详细信息。这些信息将在订单摘要页面和参会者门票页面显示。"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:54
msgid "Include tax and fees in the price"
msgstr "价格中包含税费"

#: src/components/common/CheckInListList/index.tsx:103
msgid "Includes {0} products"
msgstr "包括{0}个产品"

#: src/components/common/CheckInListList/index.tsx:112
#~ msgid "Includes {0} tickets"
#~ msgstr "包括 {0} 张票"

#: src/components/common/CheckInListList/index.tsx:104
msgid "Includes 1 product"
msgstr "包括1个产品"

#: src/components/common/CheckInListList/index.tsx:113
#~ msgid "Includes 1 ticket"
#~ msgstr "包括 1 张票"

#: src/components/common/MessageList/index.tsx:20
msgid "Individual attendees"
msgstr "个人与会者"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:100
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:121
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:137
msgid "Insert Image"
msgstr "插入图片"

#: src/components/layouts/Event/index.tsx:54
#~ msgid "Integrations"
#~ msgstr "集成"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation resent!"
msgstr "再次发出邀请！"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation revoked!"
msgstr "撤销邀请！"

#: src/components/modals/InviteUserModal/index.tsx:54
#: src/components/modals/InviteUserModal/index.tsx:74
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invite User"
msgstr "邀请用户"

#: src/components/common/OrdersTable/index.tsx:116
#: src/components/layouts/Checkout/index.tsx:54
msgid "Invoice downloaded successfully"
msgstr "发票下载成功"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:252
msgid "Invoice Notes"
msgstr "发票备注"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:197
msgid "Invoice Numbering"
msgstr "发票编号"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:177
msgid "Invoice Settings"
msgstr "发票设置"

#: src/components/modals/RefundOrderModal/index.tsx:115
msgid "Issue refund"
msgstr "退款问题"

#: src/components/common/LanguageSwitcher/index.tsx:22
msgid "Italian"
msgstr "意大利语"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Item"
msgstr "项目"

#: src/components/routes/auth/Register/index.tsx:84
msgid "John"
msgstr "约翰"

#: src/components/modals/CreateAttendeeModal/index.tsx:126
msgid "Johnson"
msgstr "约翰逊"

#: src/components/forms/ProductForm/index.tsx:77
msgid "Label"
msgstr "标签"

#: src/components/common/AttendeeDetails/index.tsx:45
#: src/components/modals/CreateAttendeeModal/index.tsx:144
#: src/components/routes/profile/ManageProfile/index.tsx:172
msgid "Language"
msgstr "语言"

#: src/components/common/ReportTable/index.tsx:52
msgid "Last 12 months"
msgstr "过去 12 个月"

#: src/components/common/ReportTable/index.tsx:47
msgid "Last 14 days"
msgstr "过去 14 天"

#: src/components/common/ReportTable/index.tsx:44
msgid "Last 24 hours"
msgstr "过去 24 小时"

#: src/components/common/ReportTable/index.tsx:48
msgid "Last 30 days"
msgstr "过去 30 天"

#: src/components/common/ReportTable/index.tsx:45
msgid "Last 48 hours"
msgstr "过去 48 小时"

#: src/components/common/ReportTable/index.tsx:50
msgid "Last 6 months"
msgstr "过去 6 个月"

#: src/components/common/ReportTable/index.tsx:46
msgid "Last 7 days"
msgstr "过去 7 天"

#: src/components/common/ReportTable/index.tsx:49
msgid "Last 90 days"
msgstr "过去 90 天"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Last login"
msgstr "最后登录"

#: src/components/modals/CreateAttendeeModal/index.tsx:125
#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:150
msgid "Last name"
msgstr "姓氏"

#: src/components/common/QuestionsTable/index.tsx:212
#: src/components/common/QuestionsTable/index.tsx:213
#: src/components/modals/EditUserModal/index.tsx:72
#: src/components/modals/InviteUserModal/index.tsx:58
#: src/components/routes/auth/AcceptInvitation/index.tsx:94
#: src/components/routes/auth/Register/index.tsx:89
#: src/components/routes/product-widget/CollectInformation/index.tsx:289
#: src/components/routes/product-widget/CollectInformation/index.tsx:290
#: src/components/routes/product-widget/CollectInformation/index.tsx:400
#: src/components/routes/product-widget/CollectInformation/index.tsx:401
#: src/components/routes/profile/ManageProfile/index.tsx:137
msgid "Last Name"
msgstr "姓氏"

#: src/components/common/WebhookTable/index.tsx:239
msgid "Last Response"
msgstr "最新响应"

#: src/components/common/WebhookTable/index.tsx:240
msgid "Last Triggered"
msgstr "最近触发"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
msgid "Last Used"
msgstr "最近一次使用"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:56
#~ msgid "Learn more about Stripe"
#~ msgstr "进一步了解 Stripe"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:191
msgid "Leave blank to use the default word \"Invoice\""
msgstr "留空以使用默认词“发票”"

#: src/components/routes/welcome/index.tsx:20
msgid "Let's get started by creating your first organizer"
msgstr "让我们从创建第一个组织者开始吧"

#: src/components/routes/event/EventDashboard/index.tsx:194
msgid "Link your Stripe account to receive funds from ticket sales."
msgstr "链接您的 Stripe 账户以接收售票资金。"

#: src/components/layouts/Event/index.tsx:190
msgid "Live"
msgstr "上线"

#: src/components/routes/event/Webhooks/index.tsx:21
msgid "Loading Webhooks"
msgstr "正在加载 Webhook"

#: src/components/common/ReportTable/index.tsx:210
msgid "Loading..."
msgstr "加载中..."

#: src/components/routes/event/Settings/index.tsx:34
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:84
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:167
msgid "Location"
msgstr "地点"

#: src/components/routes/auth/Login/index.tsx:84
#: src/components/routes/auth/Register/index.tsx:71
msgid "Log in"
msgstr "登录"

#: src/components/routes/auth/Login/index.tsx:84
msgid "Logging in"
msgstr "登录"

#: src/components/routes/auth/Register/index.tsx:70
#~ msgid "Login"
#~ msgstr "登录"

#: src/components/common/GlobalMenu/index.tsx:47
msgid "Logout"
msgstr "注销"

#: src/components/common/WidgetEditor/index.tsx:331
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit.Nam placerat elementum..."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:170
msgid "Make billing address mandatory during checkout"
msgstr "在结账时强制要求填写账单地址"

#: src/components/routes/event/EventDashboard/index.tsx:172
#~ msgid "Make Event Live"
#~ msgstr "使活动上线"

#: src/components/forms/QuestionForm/index.tsx:212
msgid "Make this question mandatory"
msgstr "将此问题作为必答题"

#: src/components/routes/event/EventDashboard/index.tsx:148
msgid "Make your event live"
msgstr "让您的活动上线"

#: src/components/common/CapacityAssignmentList/index.tsx:143
#: src/components/common/CheckInListList/index.tsx:145
#: src/components/common/EventCard/index.tsx:180
#: src/components/common/OrdersTable/index.tsx:139
#: src/components/common/OrdersTable/index.tsx:154
#: src/components/common/ProductsTable/SortableProduct/index.tsx:256
#: src/components/common/PromoCodeTable/index.tsx:167
#: src/components/layouts/Event/index.tsx:97
msgid "Manage"
msgstr "管理"

#: src/components/common/AttendeeTable/index.tsx:161
msgid "Manage attendee"
msgstr "管理与会者"

#: src/components/common/EventCard/index.tsx:150
msgid "Manage event"
msgstr "管理活动"

#: src/components/common/OrdersTable/index.tsx:156
msgid "Manage order"
msgstr "管理订单"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:99
msgid "Manage payment and invoicing settings for this event."
msgstr "管理此活动的支付和发票设置。"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
#~ msgid "Manage products"
#~ msgstr "管理产品"

#: src/components/routes/profile/ManageProfile/index.tsx:101
msgid "Manage Profile"
msgstr "管理简介"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
msgid "Manage taxes and fees which can be applied to your products"
msgstr "管理可以应用于您的产品的税费"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
#~ msgid "Manage taxes and fees which can be applied to your tickets"
#~ msgstr "管理适用于机票的税费"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
msgid "Manage tickets"
msgstr "管理机票"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:54
msgid "Manage your account details and default settings"
msgstr "管理账户详情和默认设置"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:241
msgid "Manage your payment processing and view platform fees"
msgstr "管理您的支付处理并查看平台费用"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:91
#~ msgid "Manage your Stripe payment details"
#~ msgstr "管理 Stripe 付款详情"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Manage your users and their permissions"
msgstr "管理用户及其权限"

#: src/components/forms/QuestionForm/index.tsx:211
msgid "Mandatory questions must be answered before the customer can checkout."
msgstr "在顾客结账前，必须回答必填问题。"

#: src/components/common/AttendeeTable/index.tsx:67
msgid "Manually add an Attendee"
msgstr "手动添加与会者"

#: src/components/modals/CreateAttendeeModal/index.tsx:97
#: src/components/modals/CreateAttendeeModal/index.tsx:113
msgid "Manually Add Attendee"
msgstr "手动添加与会者"

#: src/components/modals/CreateAttendeeModal/index.tsx:148
#~ msgid "Manually adding an attendee will adjust ticket quantity."
#~ msgstr "手动添加与会者将调整门票数量。"

#: src/components/common/OrdersTable/index.tsx:167
msgid "Mark as paid"
msgstr "标记为已支付"

#: src/components/layouts/AuthLayout/index.tsx:83
msgid "Match Your Brand"
msgstr "匹配您的品牌"

#: src/components/forms/ProductForm/index.tsx:383
msgid "Maximum Per Order"
msgstr "每份订单的最高限额"

#: src/components/common/AttendeeTable/index.tsx:166
msgid "Message attendee"
msgstr "与会者留言"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:273
msgid "Message Attendees"
msgstr "留言参与者"

#: src/components/modals/SendMessageModal/index.tsx:165
#~ msgid "Message attendees with specific products"
#~ msgstr "向有特定产品的参会者发送消息"

#: src/components/modals/SendMessageModal/index.tsx:206
msgid "Message attendees with specific tickets"
msgstr "向与会者发送特定门票的信息"

#: src/components/layouts/AuthLayout/index.tsx:74
msgid "Message attendees, manage orders, and handle refunds all in one place"
msgstr "在一个地方向与会者发送消息、管理订单并处理退款"

#: src/components/common/OrdersTable/index.tsx:158
msgid "Message buyer"
msgstr "留言买家"

#: src/components/modals/SendMessageModal/index.tsx:250
msgid "Message Content"
msgstr "留言内容"

#: src/components/modals/SendMessageModal/index.tsx:71
msgid "Message individual attendees"
msgstr "给个别与会者留言"

#: src/components/modals/SendMessageModal/index.tsx:218
msgid "Message order owners with specific products"
msgstr "向具有特定产品的订单所有者发送消息"

#: src/components/modals/SendMessageModal/index.tsx:118
msgid "Message Sent"
msgstr "发送的信息"

#: src/components/layouts/Event/index.tsx:105
#: src/components/routes/event/messages.tsx:31
msgid "Messages"
msgstr "信息"

#: src/components/forms/ProductForm/index.tsx:381
msgid "Minimum Per Order"
msgstr "每次订购的最低数量"

#: src/components/forms/ProductForm/index.tsx:285
msgid "Minimum Price"
msgstr "最低价格"

#: src/components/routes/event/Settings/index.tsx:58
msgid "Miscellaneous"
msgstr "其他"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:69
msgid "Miscellaneous Settings"
msgstr "杂项设置"

#: src/components/layouts/AuthLayout/index.tsx:63
msgid "Mobile Check-in"
msgstr "移动签到"

#: src/components/forms/QuestionForm/index.tsx:106
msgid "Multi line text box"
msgstr "多行文本框"

#: src/components/forms/ProductForm/index.tsx:165
msgid "Multiple price options. Perfect for early bird products etc."
msgstr "多种价格选项。非常适合早鸟产品等。"

#: src/components/forms/TicketForm/index.tsx:147
#~ msgid "Multiple price options. Perfect for early bird tickets etc."
#~ msgstr "多种价格选择。非常适合早鸟票等。"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:71
msgid "My amazing event description..."
msgstr "我的精彩活动描述"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:65
msgid "My amazing event title..."
msgstr "我的精彩活动标题..."

#: src/components/common/GlobalMenu/index.tsx:27
msgid "My Profile"
msgstr "我的简介"

#: src/components/common/QuestionAndAnswerList/index.tsx:178
msgid "N/A"
msgstr "不适用"

#: src/components/common/WidgetEditor/index.tsx:354
msgid "Nam placerat elementum..."
msgstr "Nam placerat elementum..."

#: src/components/common/AttendeeDetails/index.tsx:13
#: src/components/common/AttendeeTable/index.tsx:108
#: src/components/common/OrderDetails/index.tsx:23
#: src/components/forms/CapaciyAssigmentForm/index.tsx:37
#: src/components/forms/CheckInListForm/index.tsx:20
#: src/components/forms/ProductForm/index.tsx:253
#: src/components/forms/TaxAndFeeForm/index.tsx:61
#: src/components/modals/CreateEventModal/index.tsx:129
#: src/components/modals/DuplicateEventModal/index.tsx:78
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:63
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:72
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:107
#: src/components/routes/welcome/index.tsx:83
msgid "Name"
msgstr "名称"

#: src/components/modals/CreateEventModal/index.tsx:38
msgid "Name should be less than 150 characters"
msgstr "名称应少于 150 个字符"

#: src/components/common/QuestionAndAnswerList/index.tsx:181
#: src/components/common/QuestionAndAnswerList/index.tsx:289
msgid "Navigate to Attendee"
msgstr "导航至与会者"

#: src/components/common/PromoCodeTable/index.tsx:158
#: src/components/common/WebhookTable/index.tsx:266
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Never"
msgstr "从不"

#: src/components/routes/auth/AcceptInvitation/index.tsx:111
#: src/components/routes/auth/ResetPassword/index.tsx:59
#: src/components/routes/profile/ManageProfile/index.tsx:195
msgid "New Password"
msgstr "新密码"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "No"
msgstr "没有"

#: src/components/common/QuestionAndAnswerList/index.tsx:104
#~ msgid "No {0} available."
#~ msgstr "没有可用的{0}。"

#: src/components/routes/event/Webhooks/index.tsx:22
msgid "No Active Webhooks"
msgstr "没有活动的 Webhook"

#: src/components/common/NoEventsBlankSlate/index.tsx:21
msgid "No archived events to show."
msgstr "没有可显示的已归档活动。"

#: src/components/common/AttendeeList/index.tsx:23
msgid "No attendees found for this order."
msgstr "未找到此订单的参会者。"

#: src/components/modals/ManageOrderModal/index.tsx:132
msgid "No attendees have been added to this order."
msgstr "尚未向此订单添加参会者。"

#: src/components/common/AttendeeTable/index.tsx:56
msgid "No Attendees to show"
msgstr "无与会者"

#: src/components/forms/CheckInListForm/index.tsx:48
msgid "No attendees will be able to check in before this date using this list"
msgstr "在此日期前，使用此列表的参与者将无法签到"

#: src/components/common/CapacityAssignmentList/index.tsx:42
msgid "No Capacity Assignments"
msgstr "没有容量分配"

#: src/components/common/CheckInListList/index.tsx:44
msgid "No Check-In Lists"
msgstr "没有签到列表"

#: src/components/layouts/AuthLayout/index.tsx:34
msgid "No Credit Card Required"
msgstr "无需信用卡"

#: src/components/common/ReportTable/index.tsx:218
msgid "No data available"
msgstr "无可用数据"

#: src/components/common/ReportTable/index.tsx:214
msgid "No data to show. Please select a date range"
msgstr "无数据显示。请选择日期范围"

#: src/components/forms/PromoCodeForm/index.tsx:47
msgid "No Discount"
msgstr "无折扣"

#: src/components/common/NoEventsBlankSlate/index.tsx:20
msgid "No ended events to show."
msgstr "没有可显示的已结束活动。"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:88
#~ msgid "No events for this organizer"
#~ msgstr "没有该组织者的活动"

#: src/components/common/NoEventsBlankSlate/index.tsx:14
msgid "No events to show"
msgstr "无事件显示"

#: src/components/common/FilterModal/index.tsx:230
msgid "No filters available"
msgstr "没有可用筛选器"

#: src/components/modals/WebhookLogsModal/index.tsx:177
msgid "No logs found"
msgstr "未找到日志"

#: src/components/common/MessageList/index.tsx:69
msgid "No messages to show"
msgstr "无信息显示"

#: src/components/common/OrdersTable/index.tsx:63
msgid "No orders to show"
msgstr "无订单显示"

#: src/components/routes/product-widget/Payment/index.tsx:76
msgid "No payment methods are currently available. Please contact the event organizer for assistance."
msgstr "当前没有可用的支付方式。请联系活动组织者以获取帮助。"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:39
msgid "No Payment Required"
msgstr "无需支付"

#: src/components/modals/ManageAttendeeModal/index.tsx:177
msgid "No product associated with this attendee."
msgstr "此参会者没有关联的产品。"

#: src/components/common/ProductSelector/index.tsx:33
msgid "No products available for selection"
msgstr "没有可供选择的产品"

#: src/components/modals/CreateProductCategoryModal/index.tsx:22
msgid "No products available in this category."
msgstr "此类别中没有可用的产品。"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:39
msgid "No Products Yet"
msgstr "尚无产品"

#: src/components/common/PromoCodeTable/index.tsx:46
msgid "No Promo Codes to show"
msgstr "无促销代码显示"

#: src/components/modals/ManageAttendeeModal/index.tsx:193
msgid "No questions answered by this attendee."
msgstr "此与会者未回答任何问题。"

#: src/components/modals/ViewAttendeeModal/index.tsx:75
#~ msgid "No questions have been answered by this attendee."
#~ msgstr "此参会者尚未回答任何问题。"

#: src/components/modals/ManageOrderModal/index.tsx:119
msgid "No questions have been asked for this order."
msgstr "此订单尚未提出任何问题。"

#: src/components/common/WebhookTable/index.tsx:174
msgid "No response"
msgstr "无响应"

#: src/components/common/WebhookTable/index.tsx:141
msgid "No responses yet"
msgstr "尚无响应"

#: src/components/common/NoResultsSplash/index.tsx:24
msgid "No results"
msgstr "无结果"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:20
msgid "No Search Results"
msgstr "没有搜索结果"

#: src/components/common/NoResultsSplash/index.tsx:29
msgid "No search results."
msgstr "没有搜索结果。"

#: src/components/common/TaxAndFeeList/index.tsx:101
msgid "No Taxes or Fees have been added."
msgstr "未加收任何税费。"

#: src/components/common/TicketsTable/index.tsx:65
#~ msgid "No tickets to show"
#~ msgstr "没有演出门票"

#: src/components/modals/WebhookLogsModal/index.tsx:180
msgid "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."
msgstr "此端点尚未记录任何 Webhook 事件。事件触发后将显示在此处。"

#: src/components/common/WebhookTable/index.tsx:201
msgid "No Webhooks"
msgstr "没有 Webhooks"

#: src/components/common/PromoCodeTable/index.tsx:82
msgid "None"
msgstr "无"

#: src/components/common/ProductPriceAvailability/index.tsx:27
#: src/components/common/ProductPriceAvailability/index.tsx:48
msgid "Not available"
msgstr "不详"

#: src/components/common/AttendeeCheckInTable/index.tsx:125
#~ msgid "Not Checked In"
#~ msgstr "未办理登机手续"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "Not On Sale"
msgstr "非卖品"

#: src/components/modals/ManageAttendeeModal/index.tsx:130
#: src/components/modals/ManageOrderModal/index.tsx:164
msgid "Notes"
msgstr "备注"

#: src/components/common/ReportTable/index.tsx:240
msgid "Nothing to show yet"
msgstr "暂无显示内容"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:72
msgid "Notification Settings"
msgstr "通知设置"

#: src/components/modals/RefundOrderModal/index.tsx:108
msgid "Notify buyer of refund"
msgstr "通知买方退款"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:75
msgid "Notify organizer of new orders"
msgstr "将新订单通知组织者"

#: src/components/routes/welcome/index.tsx:72
msgid "Now let's create your first event"
msgstr "现在，让我们创建第一个事件"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:217
msgid "Number of days allowed for payment (leave blank to omit payment terms from invoices)"
msgstr "允许支付的天数（留空以从发票中省略付款条款）"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:199
msgid "Number Prefix"
msgstr "号码前缀"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:145
msgid "Offline orders are not reflected in event statistics until the order is marked as paid."
msgstr "线下订单在标记为已支付之前不会反映在活动统计中。"

#: src/components/routes/product-widget/Payment/index.tsx:66
msgid "Offline payment failed. Please try again or contact the event organizer."
msgstr "线下支付失败。请重试或联系活动组织者。"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:151
msgid "Offline Payment Instructions"
msgstr "线下支付说明"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:90
msgid "Offline Payments"
msgstr "线下支付"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:139
msgid "Offline Payments Information"
msgstr "线下支付信息"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:134
msgid "Offline Payments Settings"
msgstr "线下支付设置"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:71
msgid "On sale"
msgstr "销售中"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "On Sale"
msgstr "销售中"

#: src/components/common/NoEventsBlankSlate/index.tsx:19
msgid "Once you create an event, you'll see it here."
msgstr "创建事件后，您就可以在这里看到它。"

#: src/components/common/ReportTable/index.tsx:245
msgid "Once you start collecting data, you'll see it here."
msgstr "一旦开始收集数据，您将在这里看到。"

#: src/components/routes/event/GettingStarted/index.tsx:179
msgid "Once you're ready, set your event live and start selling products."
msgstr "一旦准备就绪，将您的活动上线并开始销售产品。"

#: src/components/routes/event/GettingStarted/index.tsx:108
#~ msgid "Once you're ready, set your event live and start selling tickets."
#~ msgstr "准备就绪后，设置您的活动并开始售票。"

#: src/components/common/EventStatusBadge/index.tsx:18
msgid "Ongoing"
msgstr "持续进行"

#: src/components/common/EventCard/index.tsx:118
msgid "Online event"
msgstr "在线活动"

#: src/components/common/OnlineEventDetails/index.tsx:9
msgid "Online Event Details"
msgstr "在线活动详情"

#: src/components/modals/SendMessageModal/index.tsx:279
msgid ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."
msgstr ""
"只有与本次活动直接相关的重要邮件才能使用此表单发送。\n"
"任何滥用行为，包括发送促销邮件，都将导致账户立即被封禁。"

#: src/components/modals/SendMessageModal/index.tsx:226
msgid "Only send to orders with these statuses"
msgstr "仅发送给具有这些状态的订单"

#: src/components/common/CheckInListList/index.tsx:167
msgid "Open Check-In Page"
msgstr "打开签到页面"

#: src/components/layouts/Event/index.tsx:288
msgid "Open sidebar"
msgstr "打开侧边栏"

#: src/components/forms/QuestionForm/index.tsx:45
msgid "Option {i}"
msgstr "方案 {i}"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:253
msgid "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"
msgstr "所有发票上显示的可选附加信息（例如，付款条款、逾期付款费用、退货政策）"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:200
msgid "Optional prefix for invoice numbers (e.g., INV-)"
msgstr "发票编号的可选前缀（例如，INV-）"

#: src/components/forms/QuestionForm/index.tsx:28
msgid "Options"
msgstr "选项"

#: src/components/modals/CreateEventModal/index.tsx:118
msgid "or"
msgstr "或"

#: src/components/common/AttendeeTable/index.tsx:110
msgid "Order"
msgstr "订购"

#: src/components/common/OrdersTable/index.tsx:212
#~ msgid "Order #"
#~ msgstr "订单号"

#: src/components/forms/WebhookForm/index.tsx:76
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:63
msgid "Order Cancelled"
msgstr "取消订单"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:62
msgid "Order Completed"
msgstr "订单已完成"

#: src/components/forms/WebhookForm/index.tsx:52
msgid "Order Created"
msgstr "订单已创建"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:122
msgid "Order Date"
msgstr "订购日期"

#: src/components/modals/ManageAttendeeModal/index.tsx:166
#: src/components/modals/ManageOrderModal/index.tsx:86
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:266
msgid "Order Details"
msgstr "订购详情"

#: src/components/modals/ViewOrderModal/index.tsx:30
#~ msgid "Order Details {0}"
#~ msgstr "订单详情 {0}"

#: src/components/modals/CancelOrderModal/index.tsx:29
msgid "Order has been canceled and the order owner has been notified."
msgstr "订单已取消，并已通知订单所有者。"

#: src/components/common/OrdersTable/index.tsx:79
msgid "Order marked as paid"
msgstr "订单已标记为已支付"

#: src/components/forms/WebhookForm/index.tsx:64
msgid "Order Marked as Paid"
msgstr "订单标记为已支付"

#: src/components/modals/ManageOrderModal/index.tsx:92
msgid "Order Notes"
msgstr "订单备注"

#: src/components/common/MessageList/index.tsx:23
msgid "Order owner"
msgstr "订单所有者"

#: src/components/modals/SendMessageModal/index.tsx:191
msgid "Order owners with a specific product"
msgstr "具有特定产品的订单所有者"

#: src/components/common/MessageList/index.tsx:19
msgid "Order owners with products"
msgstr "具有产品的订单所有者"

#: src/components/common/QuestionsTable/index.tsx:313
#: src/components/common/QuestionsTable/index.tsx:355
msgid "Order questions"
msgstr "订购问题"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:112
msgid "Order Reference"
msgstr "订购参考"

#: src/components/forms/WebhookForm/index.tsx:70
msgid "Order Refunded"
msgstr "订单已退款"

#: src/components/routes/event/orders.tsx:45
msgid "Order Status"
msgstr "订单状态"

#: src/components/modals/SendMessageModal/index.tsx:228
msgid "Order statuses"
msgstr "订单状态"

#: src/components/layouts/Checkout/CheckoutSidebar/index.tsx:28
#: src/components/modals/ManageOrderModal/index.tsx:105
msgid "Order Summary"
msgstr "订单摘要"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:90
msgid "Order timeout"
msgstr "订单超时"

#: src/components/forms/WebhookForm/index.tsx:58
msgid "Order Updated"
msgstr "订单已更新"

#: src/components/layouts/Event/index.tsx:100
#: src/components/routes/event/orders.tsx:113
msgid "Orders"
msgstr "订单"

#: src/components/common/StatBoxes/index.tsx:51
#: src/components/routes/event/EventDashboard/index.tsx:105
#~ msgid "Orders Created"
#~ msgstr "创建的订单"

#: src/components/routes/event/orders.tsx:94
msgid "Orders Exported"
msgstr "订单已导出"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:235
msgid "Organization Address"
msgstr "组织地址"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:226
msgid "Organization Details"
msgstr "组织详细信息"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:228
msgid "Organization Name"
msgstr "组织名称"

#: src/components/modals/EditUserModal/index.tsx:55
#: src/components/modals/InviteUserModal/index.tsx:47
#: src/components/routes/events/Dashboard/index.tsx:113
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:186
msgid "Organizer"
msgstr "组织者"

#: src/components/modals/CreateEventModal/index.tsx:41
msgid "Organizer is required"
msgstr "需要组织者"

#: src/components/forms/OrganizerForm/index.tsx:27
msgid "Organizer Name"
msgstr "组织者姓名"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
msgid "Organizers can only manage events and products. They cannot manage users, account settings or billing information."
msgstr "组织者只能管理活动和产品。他们无法管理用户、账户设置或账单信息。"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
#~ msgid "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."
#~ msgstr "组织者只能管理活动和门票。他们不能管理用户、账户设置或账单信息。"

#: src/components/layouts/Event/index.tsx:87
msgid "Overview"
msgstr "概览"

#: src/components/common/WidgetEditor/index.tsx:223
msgid "Padding"
msgstr "衬垫"

#: src/components/routes/event/HomepageDesigner/index.tsx:150
msgid "Page background color"
msgstr "页面背景颜色"

#: src/components/common/ErrorDisplay/index.tsx:13
msgid "Page not found"
msgstr "页面未找到"

#: src/components/common/StatBoxes/index.tsx:45
msgid "Page views"
msgstr "页面浏览量"

#: src/components/forms/ProductForm/index.tsx:358
msgid "page."
msgstr "page."

#: src/components/modals/CreateAttendeeModal/index.tsx:180
msgid "paid"
msgstr "付讫"

#: src/components/forms/ProductForm/index.tsx:145
msgid "Paid Product"
msgstr "付费产品"

#: src/components/forms/TicketForm/index.tsx:127
#~ msgid "Paid Ticket"
#~ msgstr "付费机票"

#: src/components/routes/event/orders.tsx:30
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:54
msgid "Partially Refunded"
msgstr "部分退款"

#: src/components/routes/auth/Login/index.tsx:73
#: src/components/routes/auth/Register/index.tsx:106
#: src/components/routes/profile/ManageProfile/index.tsx:109
msgid "Password"
msgstr "密码"

#: src/components/routes/auth/AcceptInvitation/index.tsx:30
msgid "Password must be a minimum  of 8 characters"
msgstr "密码必须至少包含 8 个字符"

#: src/components/routes/auth/Register/index.tsx:36
msgid "Password must be at least 8 characters"
msgstr "密码必须至少包含 8 个字符"

#: src/components/routes/auth/ResetPassword/index.tsx:46
msgid "Password reset successfully. Please login with your new password."
msgstr "密码重置成功。请使用新密码登录。"

#: src/components/routes/auth/AcceptInvitation/index.tsx:31
#: src/components/routes/auth/Register/index.tsx:37
msgid "Passwords are not the same"
msgstr "密码不一样"

#: src/components/common/WidgetEditor/index.tsx:269
msgid "Paste this where you want the widget to appear."
msgstr "将其粘贴到希望小部件出现的位置。"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:104
msgid "Paste URL"
msgstr "粘贴链接"

#: src/components/modals/CreateAttendeeModal/index.tsx:119
msgid "Patrick"
msgstr "帕特里克"

#: src/components/modals/CreateAttendeeModal/index.tsx:133
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/forms/WebhookForm/index.tsx:25
msgid "Paused"
msgstr "已暂停"

#: src/components/forms/StripeCheckoutForm/index.tsx:123
msgid "Payment"
msgstr "付款方式"

#: src/components/routes/event/Settings/index.tsx:64
msgid "Payment & Invoicing"
msgstr "支付和发票"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:98
msgid "Payment & Invoicing Settings"
msgstr "支付和发票设置"

#: src/components/routes/account/ManageAccount/index.tsx:38
msgid "Payment & Plan"
msgstr "付款与计划"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:216
msgid "Payment Due Period"
msgstr "付款期限"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:41
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:64
msgid "Payment Failed"
msgstr "付款失败"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:228
#: src/components/routes/product-widget/Payment/PaymentMethods/Offline/index.tsx:14
msgid "Payment Instructions"
msgstr "支付说明"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:105
msgid "Payment Methods"
msgstr "支付方式"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:138
msgid "Payment Processing"
msgstr "支付处理"

#: src/components/common/OrderDetails/index.tsx:76
msgid "Payment provider"
msgstr "支付提供商"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:42
msgid "Payment Received"
msgstr "已收到付款"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:240
msgid "Payment Settings"
msgstr "支付设置"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:135
msgid "Payment Status"
msgstr "支付状态"

#: src/components/forms/StripeCheckoutForm/index.tsx:60
msgid "Payment succeeded!"
msgstr "付款成功！"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:214
msgid "Payment Terms"
msgstr "付款条款"

#: src/components/forms/PromoCodeForm/index.tsx:51
#: src/components/forms/TaxAndFeeForm/index.tsx:27
msgid "Percentage"
msgstr "百分比"

#: src/components/forms/TaxAndFeeForm/index.tsx:71
msgid "Percentage Amount"
msgstr "百分比 金额"

#: src/components/routes/product-widget/Payment/index.tsx:122
msgid "Place Order"
msgstr "下单"

#: src/components/common/WidgetEditor/index.tsx:257
msgid "Place this in the <head> of your website."
msgstr "将其放在网站的 <head> 中。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:40
msgid "Platform Fees"
msgstr "平台费用"

#: src/components/forms/QuestionForm/index.tsx:31
msgid "Please add at least one option"
msgstr "请至少添加一个选项"

#: src/hooks/useFormErrorResponseHandler.tsx:18
msgid "Please check the provided information is correct"
msgstr "请检查所提供的信息是否正确"

#: src/components/routes/auth/Login/index.tsx:41
msgid "Please check your email and password and try again"
msgstr "请检查您的电子邮件和密码并重试"

#: src/components/routes/auth/AcceptInvitation/index.tsx:32
#: src/components/routes/auth/Register/index.tsx:38
msgid "Please check your email is valid"
msgstr "请检查您的电子邮件是否有效"

#: src/components/routes/profile/ManageProfile/index.tsx:152
msgid "Please check your email to confirm your email address"
msgstr "请检查您的电子邮件以确认您的电子邮件地址"

#: src/components/routes/auth/AcceptInvitation/index.tsx:86
msgid "Please complete the form below to accept your invitation"
msgstr "请填写下表接受邀请"

#: src/components/routes/product-widget/SelectProducts/index.tsx:306
msgid "Please continue in the new tab"
msgstr "请在新标签页中继续"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:47
msgid "Please create a product"
msgstr "请创建一个产品"

#: src/components/modals/CreateCheckInListModal/index.tsx:51
msgid "Please create a ticket"
msgstr "请创建一张票"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:45
msgid "Please enter a valid image URL that points to an image."
msgstr "请输入指向图像的有效图片网址。"

#: src/components/modals/CreateWebhookModal/index.tsx:30
msgid "Please enter a valid URL"
msgstr "请输入有效的 URL"

#: src/components/routes/auth/ResetPassword/index.tsx:55
msgid "Please enter your new password"
msgstr "请输入新密码"

#: src/components/modals/CancelOrderModal/index.tsx:52
msgid "Please Note"
msgstr "请注意"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:34
msgid "Please provide an image."
msgstr "请提供一张图片。"

#: src/components/common/TicketsTable/index.tsx:84
#~ msgid "Please remove filters and set sorting to \"Homepage order\" to enable sorting"
#~ msgstr "请移除筛选器，并将排序设置为 \"主页顺序\"，以启用排序功能"

#: src/components/layouts/Checkout/index.tsx:159
msgid "Please return to the event page to start over."
msgstr "请返回活动页面重新开始。"

#: src/components/modals/SendMessageModal/index.tsx:195
msgid "Please select"
msgstr "请选择"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:53
msgid "Please select an image."
msgstr "请选择一张图片。"

#: src/components/routes/product-widget/SelectProducts/index.tsx:237
msgid "Please select at least one product"
msgstr "请选择至少一个产品"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:227
#~ msgid "Please select at least one ticket"
#~ msgstr "请至少选择一张票"

#: src/components/routes/event/attendees.tsx:49
#: src/components/routes/event/orders.tsx:100
msgid "Please try again."
msgstr "请再试一次。"

#: src/components/routes/profile/ManageProfile/index.tsx:142
msgid "Please verify your email address to access all features"
msgstr "请验证您的电子邮件地址，以访问所有功能"

#: src/components/routes/event/attendees.tsx:40
msgid "Please wait while we prepare your attendees for export..."
msgstr "请稍候，我们正在准备导出您的与会者..."

#: src/components/common/OrdersTable/index.tsx:112
#: src/components/layouts/Checkout/index.tsx:50
msgid "Please wait while we prepare your invoice..."
msgstr "请稍候，我们正在准备您的发票..."

#: src/components/routes/event/orders.tsx:91
msgid "Please wait while we prepare your orders for export..."
msgstr "请稍候，我们正在准备导出您的订单..."

#: src/components/common/LanguageSwitcher/index.tsx:26
msgid "Portuguese"
msgstr "葡萄牙语"

#: src/locales.ts:47
#~ msgid "Portuguese (Brazil)"
#~ msgstr "葡萄牙语（巴西）"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:74
msgid "Post Checkout message"
msgstr "结账后信息"


#: src/components/forms/StripeCheckoutForm/index.tsx:137
#~ msgid "Powered by Stripe"
#~ msgstr "由 Stripe 提供支持"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:67
msgid "Pre Checkout message"
msgstr "结账前信息"

#: src/components/common/QuestionsTable/index.tsx:347
msgid "Preview"
msgstr "预览"

#: src/components/layouts/Event/index.tsx:205
#: src/components/layouts/Event/index.tsx:209
msgid "Preview Event page"
msgstr "预览活动页面"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:236
#: src/components/forms/ProductForm/index.tsx:73
#: src/components/forms/ProductForm/index.tsx:285
msgid "Price"
msgstr "价格"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:78
msgid "Price display mode"
msgstr "价格显示模式"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:88
msgid "Price not set"
msgstr "未设置价格"

#: src/components/forms/ProductForm/index.tsx:322
msgid "Price tiers"
msgstr "价格等级"

#: src/components/forms/ProductForm/index.tsx:228
msgid "Price Type"
msgstr "价格类型"

#: src/components/common/WidgetEditor/index.tsx:182
msgid "Primary Color"
msgstr "原色"

#: src/components/routes/event/HomepageDesigner/index.tsx:156
msgid "Primary Colour"
msgstr "原色"

#: src/components/common/WidgetEditor/index.tsx:190
#: src/components/routes/event/HomepageDesigner/index.tsx:158
msgid "Primary Text Color"
msgstr "主要文字颜色"

#: src/components/common/AttendeeTicket/index.tsx:81
msgid "Print"
msgstr "打印"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:288
msgid "Print All Tickets"
msgstr "打印所有门票"

#: src/components/layouts/Checkout/index.tsx:114
msgid "Print Tickets"
msgstr "打印票"

#: src/components/common/AttendeeTable/index.tsx:190
#~ msgid "product"
#~ msgstr "产品"

#: src/components/common/AttendeeDetails/index.tsx:37
#: src/components/modals/ManageAttendeeModal/index.tsx:119
msgid "Product"
msgstr "产品"

#: src/components/forms/ProductForm/index.tsx:266
msgid "Product Category"
msgstr "产品类别"

#: src/components/modals/EditProductCategoryModal/index.tsx:53
msgid "Product category updated successfully."
msgstr "产品类别更新成功。"

#: src/components/forms/WebhookForm/index.tsx:34
msgid "Product Created"
msgstr "产品已创建"

#: src/components/forms/WebhookForm/index.tsx:46
msgid "Product Deleted"
msgstr "产品已删除"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:55
msgid "Product deleted successfully"
msgstr "产品删除成功"

#: src/components/common/AttendeeTable/index.tsx:50
#~ msgid "Product email has been resent to attendee"
#~ msgstr "产品电子邮件已重新发送给参会者"

#: src/components/forms/ProductForm/index.tsx:236
msgid "Product Price Type"
msgstr "产品价格类型"

#: src/components/common/QuestionsTable/index.tsx:328
msgid "Product questions"
msgstr "产品问题"

#: src/components/routes/event/EventDashboard/index.tsx:217
#: src/components/routes/event/Reports/index.tsx:17
msgid "Product Sales"
msgstr "产品销售"

#: src/components/routes/event/Reports/index.tsx:18
msgid "Product sales, revenue, and tax breakdown"
msgstr "产品销售、收入和税费明细"

#: src/components/common/ProductSelector/index.tsx:63
msgid "Product Tier"
msgstr "产品等级"

#: src/components/forms/ProductForm/index.tsx:214
#: src/components/forms/ProductForm/index.tsx:221
msgid "Product Type"
msgstr "产品类型"

#: src/components/forms/WebhookForm/index.tsx:40
msgid "Product Updated"
msgstr "产品已更新"

#: src/components/common/WidgetEditor/index.tsx:313
msgid "Product Widget Preview"
msgstr "产品小部件预览"

#: src/components/common/PromoCodeTable/index.tsx:148
msgid "Product(s)"
msgstr "产品"

#: src/components/common/PromoCodeTable/index.tsx:72
msgid "Products"
msgstr "产品"

#: src/components/common/EventCard/index.tsx:126
msgid "products sold"
msgstr "已售产品"

#: src/components/common/StatBoxes/index.tsx:21
msgid "Products sold"
msgstr "已售产品"

#: src/components/routes/event/EventDashboard/index.tsx:238
msgid "Products Sold"
msgstr "已售产品"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:178
msgid "Products sorted successfully"
msgstr "产品排序成功"

#: src/components/layouts/AuthLayout/index.tsx:43
msgid "Products, merchandise, and flexible pricing options"
msgstr "产品、商品和灵活的定价选项"

#: src/components/routes/profile/ManageProfile/index.tsx:106
msgid "Profile"
msgstr "简介"

#: src/components/routes/profile/ManageProfile/index.tsx:59
msgid "Profile updated successfully"
msgstr "成功更新个人资料"

#: src/components/routes/product-widget/SelectProducts/index.tsx:178
msgid "Promo {promo_code} code applied"
msgstr "已使用促销 {promo_code} 代码"

#: src/components/common/OrderDetails/index.tsx:86
msgid "Promo code"
msgstr "优惠码"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:20
msgid "Promo Code"
msgstr "促销代码"

#: src/components/forms/ProductForm/index.tsx:409
msgid "Promo Code page"
msgstr "促销代码页面"

#: src/components/routes/event/Reports/index.tsx:30
msgid "Promo code usage and discount breakdown"
msgstr "促销码使用情况及折扣明细"

#: src/components/layouts/Event/index.tsx:106
#: src/components/routes/event/promo-codes.tsx:31
msgid "Promo Codes"
msgstr "促销代码"

#: src/components/common/PromoCodeTable/index.tsx:51
msgid "Promo codes can be used to offer discounts, presale access, or provide special access to your event."
msgstr "促销代码可用于提供折扣、预售权限或为您的活动提供特殊权限。"

#: src/components/routes/event/Reports/index.tsx:29
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:95
msgid "Promo Codes Report"
msgstr "促销代码报告"

#: src/components/forms/QuestionForm/index.tsx:189
msgid ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."
msgstr "提供此问题的附加背景或说明。使用此字段添加条款和条件、指南或参与者在回答前需要知道的任何重要信息。"

#: src/components/routes/event/EventDashboard/index.tsx:159
msgid "Publish Event"
msgstr "发布活动"

#: src/components/common/GlobalMenu/index.tsx:25
#~ msgid "Purchase License"
#~ msgstr "购买许可证"

#: src/components/modals/ShareModal/index.tsx:126
msgid "QR Code"
msgstr "二维码"

#: src/components/layouts/AuthLayout/index.tsx:64
msgid "QR code scanning with instant feedback and secure sharing for staff access"
msgstr "二维码扫描，提供即时反馈和安全共享，供工作人员使用"

#: src/components/forms/ProductForm/index.tsx:85
#: src/components/forms/ProductForm/index.tsx:302
msgid "Quantity Available"
msgstr "可用数量"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
msgid "Quantity Sold"
msgstr "销售数量"

#: src/components/common/QuestionsTable/index.tsx:170
msgid "Question deleted"
msgstr "问题已删除"

#: src/components/forms/QuestionForm/index.tsx:188
msgid "Question Description"
msgstr "问题描述"

#: src/components/forms/QuestionForm/index.tsx:178
msgid "Question Title"
msgstr "问题标题"

#: src/components/common/QuestionsTable/index.tsx:275
#: src/components/layouts/Event/index.tsx:102
msgid "Questions"
msgstr "问题"

#: src/components/modals/ManageAttendeeModal/index.tsx:184
#: src/components/modals/ManageOrderModal/index.tsx:111
msgid "Questions & Answers"
msgstr "问与答"

#: src/components/common/QuestionsTable/index.tsx:145
msgid "Questions sorted successfully"
msgstr "问题已成功分类"

#: src/components/forms/QuestionForm/index.tsx:118
msgid "Radio Option"
msgstr "无线电选项"

#: src/components/common/MessageList/index.tsx:59
msgid "Read less"
msgstr "更多信息"

#: src/components/modals/SendMessageModal/index.tsx:36
msgid "Recipient"
msgstr "受援国"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:110
#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:195
msgid "Redirecting to Stripe..."
msgstr "正在重定向到 Stripe..."

#: src/components/common/OrdersTable/index.tsx:204
#: src/components/common/OrdersTable/index.tsx:274
msgid "Reference"
msgstr "参考资料"

#: src/components/modals/RefundOrderModal/index.tsx:104
msgid "Refund amount ({0})"
msgstr "退款金额 ({0})"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:52
msgid "Refund Failed"
msgstr "退款失败"

#: src/components/common/OrdersTable/index.tsx:172
msgid "Refund order"
msgstr "退款订单"

#: src/components/modals/RefundOrderModal/index.tsx:151
msgid "Refund Order"
msgstr "退款订单"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:51
msgid "Refund Pending"
msgstr "退款处理中"

#: src/components/routes/event/orders.tsx:51
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:128
msgid "Refund Status"
msgstr "退款状态"

#: src/components/common/OrderSummary/index.tsx:80
#: src/components/common/StatBoxes/index.tsx:33
#: src/components/routes/event/orders.tsx:29
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:53
msgid "Refunded"
msgstr "退款"

#: src/components/routes/auth/Register/index.tsx:129
msgid "Register"
msgstr "注册"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:82
msgid "Remaining Uses"
msgstr "剩余使用次数"

#: src/components/routes/product-widget/SelectProducts/index.tsx:504
msgid "remove"
msgstr "去除"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:140
#: src/components/routes/product-widget/SelectProducts/index.tsx:505
msgid "Remove"
msgstr "移除"

#: src/components/layouts/Event/index.tsx:92
#: src/components/routes/event/Reports/index.tsx:39
msgid "Reports"
msgstr "报告"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:169
msgid "Require Billing Address"
msgstr "要求账单地址"

#: src/components/routes/event/GettingStarted/index.tsx:197
msgid "Resend confirmation email"
msgstr "重新发送确认电子邮件"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resend email confirmation"
msgstr "重新发送电子邮件确认"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Resend invitation"
msgstr "重新发送邀请"

#: src/components/common/OrdersTable/index.tsx:179
msgid "Resend order email"
msgstr "重新发送订单电子邮件"

#: src/components/common/AttendeeTable/index.tsx:179
#~ msgid "Resend product email"
#~ msgstr "重新发送产品电子邮件"

#: src/components/common/AttendeeTable/index.tsx:171
msgid "Resend ticket email"
msgstr "重新发送票务电子邮件"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resending..."
msgstr "重新发送..."

#: src/components/common/FilterModal/index.tsx:248
msgid "Reset"
msgstr "重置"

#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Reset password"
msgstr "重置密码"

#: src/components/routes/auth/ResetPassword/index.tsx:54
msgid "Reset Password"
msgstr "重置密码"

#: src/components/routes/auth/ForgotPassword/index.tsx:50
msgid "Reset your password"
msgstr "重置您的密码"

#: src/components/common/EventCard/index.tsx:167
msgid "Restore event"
msgstr "恢复活动"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:50
#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:63
msgid "Return to event page"
msgstr "返回活动页面"

#: src/components/layouts/Checkout/index.tsx:165
msgid "Return to Event Page"
msgstr "返回活动页面"

#: src/components/routes/event/EventDashboard/index.tsx:249
msgid "Revenue"
msgstr "收入"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Revoke invitation"
msgstr "撤销邀请"

#: src/components/modals/EditUserModal/index.tsx:93
#: src/components/modals/InviteUserModal/index.tsx:64
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Role"
msgstr "角色"

#: src/components/forms/ProductForm/index.tsx:96
#: src/components/forms/ProductForm/index.tsx:391
msgid "Sale End Date"
msgstr "销售结束日期"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:75
msgid "Sale ended"
msgstr "销售结束"

#: src/components/forms/ProductForm/index.tsx:91
#: src/components/forms/ProductForm/index.tsx:389
msgid "Sale Start Date"
msgstr "销售开始日期"

#: src/components/common/ProductPriceAvailability/index.tsx:13
#: src/components/common/ProductPriceAvailability/index.tsx:35
msgid "Sales ended"
msgstr "销售结束"

#: src/components/common/ProductPriceAvailability/index.tsx:19
#: src/components/common/ProductPriceAvailability/index.tsx:40
msgid "Sales start"
msgstr "销售开始"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:135
msgid "San Francisco"
msgstr "旧金山"

#: src/components/common/QuestionAndAnswerList/index.tsx:142
#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:80
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:114
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:96
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:166
#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:94
#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:265
#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:85
msgid "Save"
msgstr "节省"

#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/event/HomepageDesigner/index.tsx:166
msgid "Save Changes"
msgstr "保存更改"

#: src/components/modals/EditOrganizerModal/index.tsx:70
msgid "Save Organizer"
msgstr "保存组织器"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:90
msgid "Save Settings"
msgstr "保存设置"

#: src/components/layouts/CheckIn/index.tsx:398
#: src/components/layouts/CheckIn/index.tsx:400
msgid "Scan QR Code"
msgstr "扫描 QR 码"

#: src/components/modals/ShareModal/index.tsx:229
msgid "Scan this QR code to access the event page or share it with others"
msgstr "扫描此二维码以访问活动页面或与他人分享"

#: src/components/layouts/CheckIn/index.tsx:288
#~ msgid "Seach by name, order #, attendee # or email..."
#~ msgstr "按姓名、订单号、参会者编号或电子邮件搜索..."

#: src/components/routes/event/attendees.tsx:64
msgid "Search by attendee name, email or order #..."
msgstr "按与会者姓名、电子邮件或订单号搜索..."

#: src/components/routes/events/Dashboard/index.tsx:69
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:66
msgid "Search by event name..."
msgstr "按活动名称搜索..."

#: src/components/routes/event/orders.tsx:126
msgid "Search by name, email, or order #..."
msgstr "按姓名、电子邮件或订单号搜索..."

#: src/components/layouts/CheckIn/index.tsx:394
msgid "Search by name, order #, attendee # or email..."
msgstr "按姓名、订单号、参与者号或电子邮件搜索..."

#: src/components/routes/event/promo-codes.tsx:34
msgid "Search by name..."
msgstr "按名称搜索..."

#: src/components/routes/event/products.tsx:43
#~ msgid "Search by product name..."
#~ msgstr ""

#: src/components/routes/event/messages.tsx:34
msgid "Search by subject or content..."
msgstr "按主题或内容搜索..."

#: src/components/routes/event/tickets.tsx:43
#~ msgid "Search by ticket name..."
#~ msgstr "按票务名称搜索..."

#: src/components/routes/event/CapacityAssignments/index.tsx:37
msgid "Search capacity assignments..."
msgstr "搜索容量分配..."

#: src/components/routes/event/CheckInLists/index.tsx:37
msgid "Search check-in lists..."
msgstr "搜索签到列表..."

#: src/components/routes/event/products.tsx:52
msgid "Search products"
msgstr "搜索产品"

#: src/components/common/SearchBar/index.tsx:36
msgid "Search..."
msgstr "搜索..."

#: src/components/routes/event/HomepageDesigner/index.tsx:160
msgid "Secondary color"
msgstr "辅助色"

#: src/components/common/WidgetEditor/index.tsx:198
msgid "Secondary Color"
msgstr "辅助色"

#: src/components/routes/event/HomepageDesigner/index.tsx:162
msgid "Secondary text color"
msgstr "辅助文字颜色"

#: src/components/common/WidgetEditor/index.tsx:206
msgid "Secondary Text Color"
msgstr "辅助文字颜色"

#: src/components/common/FilterModal/index.tsx:150
msgid "Select {0}"
msgstr "选择 {0}"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:214
msgid "Select Camera"
msgstr "选择相机"

#: src/components/forms/ProductForm/index.tsx:269
msgid "Select category..."
msgstr "选择类别..."

#: src/components/forms/WebhookForm/index.tsx:124
msgid "Select event types"
msgstr "选择事件类型"

#: src/components/modals/CreateEventModal/index.tsx:110
msgid "Select organizer"
msgstr "选择组织者"

#: src/components/modals/ManageAttendeeModal/index.tsx:118
msgid "Select Product"
msgstr "选择产品"

#: src/components/common/ProductSelector/index.tsx:65
msgid "Select Product Tier"
msgstr "选择产品等级"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:49
#: src/components/modals/SendMessageModal/index.tsx:219
msgid "Select products"
msgstr "选择产品"

#: src/components/modals/EditUserModal/index.tsx:104
msgid "Select status"
msgstr "选择状态"

#: src/components/modals/CreateAttendeeModal/index.tsx:150
msgid "Select Ticket"
msgstr "选择机票"

#: src/components/modals/CreateAttendeeModal/index.tsx:163
#: src/components/modals/EditAttendeeModal/index.tsx:117
#~ msgid "Select Ticket Tier"
#~ msgstr "选择票价等级"

#: src/components/forms/CheckInListForm/index.tsx:26
#: src/components/modals/SendMessageModal/index.tsx:207
msgid "Select tickets"
msgstr "选择票"

#: src/components/common/ReportTable/index.tsx:261
msgid "Select time period"
msgstr "选择时间段"

#: src/components/forms/WebhookForm/index.tsx:123
msgid "Select which events will trigger this webhook"
msgstr "选择哪些事件将触发此 Webhook"

#: src/components/forms/ProductForm/index.tsx:352
msgid "Select..."
msgstr "选择..."

#: src/components/layouts/AuthLayout/index.tsx:68
msgid "Sell Anything"
msgstr "销售任何产品"

#: src/components/layouts/AuthLayout/index.tsx:69
msgid "Sell merchandise alongside tickets with integrated tax and promo code support"
msgstr "在销售门票的同时销售商品，并支持集成税务和促销代码"

#: src/components/layouts/AuthLayout/index.tsx:42
msgid "Sell More Than Tickets"
msgstr "销售不仅仅是门票"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send"
msgstr "发送"

#: src/components/modals/SendMessageModal/index.tsx:259
msgid "Send a copy to <0>{0}</0>"
msgstr "将副本发送至 <0>{0}</0>"

#: src/components/modals/SendMessageModal/index.tsx:139
msgid "Send a message"
msgstr "发送信息"

#: src/components/modals/SendMessageModal/index.tsx:269
msgid "Send as a test. This will send the message to your email address instead of the recipients."
msgstr "作为测试发送。这将把信息发送到您的电子邮件地址，而不是收件人的电子邮件地址。"

#: src/components/routes/event/messages.tsx:41
msgid "Send Message"
msgstr "发送消息"

#: src/components/modals/CreateAttendeeModal/index.tsx:191
#~ msgid "Send order confirmation and product email"
#~ msgstr "发送订单确认和产品电子邮件"

#: src/components/modals/CreateAttendeeModal/index.tsx:192
msgid "Send order confirmation and ticket email"
msgstr "发送订单确认和票务电子邮件"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send Test"
msgstr "发送测试"

#: src/components/routes/event/Settings/index.tsx:46
msgid "SEO"
msgstr "SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:70
msgid "SEO Description"
msgstr "搜索引擎优化说明"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:76
msgid "SEO Keywords"
msgstr "搜索引擎优化关键词"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:56
msgid "SEO Settings"
msgstr "搜索引擎优化设置"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:64
msgid "SEO Title"
msgstr "搜索引擎优化标题"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "Service Fee"
msgstr "服务费"

#: src/components/forms/ProductForm/index.tsx:159
msgid "Set a minimum price and let users pay more if they choose"
msgstr "设定最低价格，用户可选择支付更高的价格"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:207
msgid "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."
msgstr "设置发票编号的起始编号。一旦发票生成，就无法更改。"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Set up your event"
msgstr "准备活动"

#: src/components/routes/event/EventDashboard/index.tsx:190
#~ msgid "Set up your payment processing to receive funds from ticket sales."
#~ msgstr "设置您的支付处理，以接收门票销售的资金。"

#: src/components/routes/event/GettingStarted/index.tsx:183
msgid "Set your event live"
msgstr "实时设置您的活动"

#: src/components/layouts/Event/index.tsx:98
#: src/components/routes/event/Settings/index.tsx:103
msgid "Settings"
msgstr "设置"

#: src/components/layouts/AuthLayout/index.tsx:26
msgid "Setup in Minutes"
msgstr "几分钟内完成设置"

#: src/components/common/ShareIcon/index.tsx:28
#: src/components/modals/ShareModal/index.tsx:123
#: src/components/modals/ShareModal/index.tsx:141
msgid "Share"
msgstr "分享"

#: src/components/layouts/Event/index.tsx:251
#: src/components/modals/ShareModal/index.tsx:116
msgid "Share Event"
msgstr "分享活动"

#: src/components/modals/ShareModal/index.tsx:169
msgid "Share to Facebook"
msgstr "分享到 Facebook"

#: src/components/modals/ShareModal/index.tsx:163
msgid "Share to LinkedIn"
msgstr "分享到 LinkedIn"

#: src/components/modals/ShareModal/index.tsx:193
msgid "Share to Pinterest"
msgstr "分享到 Pinterest"

#: src/components/modals/ShareModal/index.tsx:187
msgid "Share to Reddit"
msgstr "分享到 Reddit"

#: src/components/modals/ShareModal/index.tsx:148
msgid "Share to Social"
msgstr "分享到社交网络"

#: src/components/modals/ShareModal/index.tsx:181
msgid "Share to Telegram"
msgstr "分享到 Telegram"

#: src/components/modals/ShareModal/index.tsx:175
msgid "Share to WhatsApp"
msgstr "分享到 WhatsApp"

#: src/components/modals/ShareModal/index.tsx:157
msgid "Share to X"
msgstr "分享到 X"

#: src/components/modals/ShareModal/index.tsx:199
msgid "Share via Email"
msgstr "通过电子邮件分享"

#: src/components/forms/ProductForm/index.tsx:344
msgid "Show"
msgstr "显示"

#: src/components/forms/ProductForm/index.tsx:403
msgid "Show available product quantity"
msgstr "显示可用产品数量"

#: src/components/forms/TicketForm/index.tsx:348
#~ msgid "Show available ticket quantity"
#~ msgstr "显示可用门票数量"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Show hidden questions"
msgstr "显示隐藏问题"

#: src/components/routes/product-widget/SelectProducts/index.tsx:459
msgid "Show more"
msgstr "显示更多"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:60
msgid "Show tax and fees separately"
msgstr "单独显示税费"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:76
#~ msgid "Shown to the customer after they checkout, on the order summary page"
#~ msgstr "在客户结账后，在订单摘要页面上显示给客户"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
msgid "Shown to the customer after they checkout, on the order summary page."
msgstr "结账后显示给客户，在订单摘要页面。"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:69
msgid "Shown to the customer before they checkout"
msgstr "在顾客结账前向他们展示"

#: src/components/forms/QuestionForm/index.tsx:132
msgid "Shows common address fields, including country"
msgstr "显示常用地址字段，包括国家"

#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:151
msgid "Simpson"
msgstr "辛普森"

#: src/components/forms/QuestionForm/index.tsx:100
msgid "Single line text box"
msgstr "单行文本框"

#: src/components/routes/welcome/index.tsx:137
msgid "Skip this step"
msgstr "跳过此步骤"

#: src/components/layouts/AuthLayout/index.tsx:78
msgid "Smart Check-in"
msgstr "智能签到"

#: src/components/layouts/AuthLayout/index.tsx:53
#~ msgid "Smart Dashboard"
#~ msgstr "智能仪表板"

#: src/components/routes/auth/Register/index.tsx:90
msgid "Smith"
msgstr "史密斯"

#: src/components/common/ProductPriceAvailability/index.tsx:9
#: src/components/common/ProductPriceAvailability/index.tsx:32
msgid "Sold out"
msgstr "售罄"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:67
msgid "Sold Out"
msgstr "售罄"

#: src/components/common/ErrorDisplay/index.tsx:14
#: src/components/routes/auth/AcceptInvitation/index.tsx:47
msgid "Something went wrong"
msgstr "出了问题"

#: src/components/common/TaxAndFeeList/index.tsx:39
msgid "Something went wrong while deleting the Tax or Fee"
msgstr "删除税费时出了问题"

#: src/components/routes/auth/ForgotPassword/index.tsx:31
msgid "Something went wrong, please try again, or contact support if the problem persists"
msgstr "出现问题，请重试，或在问题持续时联系客服"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Something went wrong! Please try again"
msgstr "出错了！请重试"

#: src/components/forms/StripeCheckoutForm/index.tsx:69
msgid "Something went wrong."
msgstr "出问题了"

#: src/components/routes/profile/ManageProfile/index.tsx:80
#: src/components/routes/profile/ManageProfile/index.tsx:94
msgid "Something went wrong. Please try again."
msgstr "出错了。请重试。"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:61
msgid "Sorry, something has gone wrong. Please restart the checkout process."
msgstr "对不起，出了点问题。请重新开始结账。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:259
msgid "Sorry, something went wrong loading this page."
msgstr "抱歉，在加载此页面时出了点问题。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:247
msgid "Sorry, this order no longer exists."
msgstr "抱歉，此订单不再存在。"

#: src/components/routes/product-widget/SelectProducts/index.tsx:246
msgid "Sorry, this promo code is not recognized"
msgstr "对不起，此优惠代码不可用"

#: src/components/layouts/Checkout/index.tsx:26
#~ msgid "Sorry, your order has expired. Please start a new order."
#~ msgstr "抱歉，您的订单已过期。请重新订购。"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Sorting is disabled while filters and sorting are applied"
#~ msgstr "应用筛选器和排序时禁用排序"

#: src/components/common/LanguageSwitcher/index.tsx:18
msgid "Spanish"
msgstr "西班牙语"

#: src/components/forms/ProductForm/index.tsx:147
msgid "Standard product with a fixed price"
msgstr "固定价格的标准产品"

#: src/components/forms/TicketForm/index.tsx:129
#~ msgid "Standard ticket with a fixed price"
#~ msgstr "固定价格标准票"

#: src/components/modals/CreateEventModal/index.tsx:144
#: src/components/modals/DuplicateEventModal/index.tsx:93
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:87
#: src/components/routes/welcome/index.tsx:90
msgid "Start Date"
msgstr "开始日期"

#: src/components/common/CheckoutQuestion/index.tsx:165
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:139
#: src/components/routes/product-widget/CollectInformation/index.tsx:339
#: src/components/routes/product-widget/CollectInformation/index.tsx:340
msgid "State or Region"
msgstr "州或地区"

#: src/components/common/AttendeeTable/index.tsx:112
#: src/components/common/OrderDetails/index.tsx:51
#: src/components/common/OrdersTable/index.tsx:209
#: src/components/common/ProductsTable/SortableProduct/index.tsx:222
#: src/components/common/WebhookTable/index.tsx:238
#: src/components/forms/CapaciyAssigmentForm/index.tsx:56
#: src/components/forms/WebhookForm/index.tsx:133
#: src/components/modals/EditUserModal/index.tsx:103
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:88
msgid "Status"
msgstr "现状"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:85
msgid "Stripe"
msgstr "Stripe"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:48
msgid "Stripe payments are not enabled for this event."
msgstr "此活动未启用 Stripe 支付。"

#: src/components/modals/SendMessageModal/index.tsx:245
msgid "Subject"
msgstr "主题"

#: src/components/common/OrderSummary/index.tsx:41
msgid "Subtotal"
msgstr "小计"

#: src/components/common/OrdersTable/index.tsx:115
#: src/components/layouts/Checkout/index.tsx:53
msgid "Success"
msgstr "成功"

#: src/components/modals/EditUserModal/index.tsx:40
#: src/components/modals/InviteUserModal/index.tsx:32
msgid "Success! {0} will receive an email shortly."
msgstr "成功！{0} 将很快收到一封电子邮件。"

#: src/components/common/AttendeeTable/index.tsx:90
msgid "Successfully {0} attendee"
msgstr "成功 {0} 参会者"

#: src/components/common/AttendeeCheckInTable/index.tsx:47
#: src/components/common/AttendeeCheckInTable/index.tsx:71
#~ msgid "Successfully checked <0>{0} {1}</0> {2}"
#~ msgstr "成功检查 <0>{0} {1}</0> {2}"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:23
msgid "Successfully confirmed email address"
msgstr "成功确认电子邮件地址"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:31
msgid "Successfully confirmed email change"
msgstr "成功确认电子邮件更改"

#: src/components/modals/CreateAttendeeModal/index.tsx:82
msgid "Successfully created attendee"
msgstr "成功创建与会者"

#: src/components/modals/CreateProductModal/index.tsx:54
msgid "Successfully Created Product"
msgstr "产品创建成功"

#: src/components/modals/CreatePromoCodeModal/index.tsx:39
msgid "Successfully Created Promo Code"
msgstr "成功创建促销代码"

#: src/components/modals/CreateQuestionModal/index.tsx:45
#: src/components/modals/EditQuestionModal/index.tsx:66
msgid "Successfully Created Question"
msgstr "成功创建问题"

#: src/components/modals/CreateTicketModal/index.tsx:50
#~ msgid "Successfully Created Ticket"
#~ msgstr "成功创建票单"

#: src/components/modals/DuplicateProductModal/index.tsx:95
msgid "Successfully Duplicated Product"
msgstr "产品复制成功"

#: src/components/modals/ManageAttendeeModal/index.tsx:96
msgid "Successfully updated attendee"
msgstr "成功更新与会者"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:47
msgid "Successfully updated Capacity Assignment"
msgstr "容量分配更新成功"

#: src/components/modals/EditCheckInListModal/index.tsx:49
msgid "Successfully updated Check-In List"
msgstr "签到列表更新成功"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:43
msgid "Successfully Updated Email Settings"
msgstr "成功更新电子邮件设置"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:54
msgid "Successfully Updated Event"
msgstr "成功更新活动"

#: src/components/routes/event/HomepageDesigner/index.tsx:84
msgid "Successfully Updated Homepage Design"
msgstr "成功更新主页设计"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:50
msgid "Successfully Updated Homepage Settings"
msgstr "成功更新主页设置"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:73
msgid "Successfully Updated Location"
msgstr "成功更新位置"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:43
msgid "Successfully Updated Misc Settings"
msgstr "成功更新杂项设置"

#: src/components/modals/ManageOrderModal/index.tsx:74
msgid "Successfully updated order"
msgstr "订单更新成功"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:74
msgid "Successfully Updated Payment & Invoicing Settings"
msgstr "支付和发票设置已成功更新"

#: src/components/modals/EditProductModal/index.tsx:89
msgid "Successfully updated product"
msgstr "产品更新成功"

#: src/components/modals/EditPromoCodeModal/index.tsx:52
msgid "Successfully Updated Promo Code"
msgstr "成功更新促销代码"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:45
msgid "Successfully Updated Seo Settings"
msgstr "成功更新搜索引擎设置"

#: src/components/modals/EditTicketModal/index.tsx:85
#~ msgid "Successfully updated ticket"
#~ msgstr "成功更新票单"

#: src/components/modals/EditWebhookModal/index.tsx:44
msgid "Successfully updated Webhook"
msgstr "Webhook 更新成功"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:128
msgid "Suite 100"
msgstr "100 号套房"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
msgid "Support Email"
msgstr "支持电子邮件"

#: src/components/layouts/AuthLayout/index.tsx:59
msgid "Support for tiered, donation-based, and product sales with customizable pricing and capacity"
msgstr "支持分级、基于捐赠和产品销售，并提供可自定义的定价和容量"

#: src/components/forms/ProductForm/index.tsx:254
msgid "T-shirt"
msgstr "T恤衫"

#: src/components/forms/TaxAndFeeForm/index.tsx:12
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Tax"
msgstr "税收"

#: src/components/routes/account/ManageAccount/index.tsx:27
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:16
msgid "Tax & Fees"
msgstr "税费"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:243
msgid "Tax Details"
msgstr "税务详情"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:244
msgid "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"
msgstr "所有发票底部显示的税务信息（例如，增值税号、税务注册号）"

#: src/components/common/TaxAndFeeList/index.tsx:36
msgid "Tax or Fee deleted successfully"
msgstr "成功删除税费"

#: src/components/forms/ProductForm/index.tsx:363
msgid "Taxes"
msgstr "税收"

#: src/components/forms/ProductForm/index.tsx:351
#: src/components/forms/ProductForm/index.tsx:358
msgid "Taxes and Fees"
msgstr "税费"

#: src/components/routes/product-widget/SelectProducts/index.tsx:138
#: src/components/routes/product-widget/SelectProducts/index.tsx:186
msgid "That promo code is invalid"
msgstr "促销代码无效"

#: src/components/layouts/CheckIn/index.tsx:316
msgid "The check-in list you are looking for does not exist."
msgstr "您查找的签到列表不存在。"

#: src/components/forms/OrganizerForm/index.tsx:47
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:75
msgid "The default currency for your events."
msgstr "事件的默认货币。"

#: src/components/forms/OrganizerForm/index.tsx:56
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:84
msgid "The default timezone for your events."
msgstr "事件的默认时区。"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:16
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:43
msgid "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."
msgstr "您查找的活动目前不可用。它可能已被删除、过期或 URL 不正确。"

#: src/components/modals/CreateAttendeeModal/index.tsx:146
msgid "The language the attendee will receive emails in."
msgstr "与会者接收电子邮件的语言。"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:22
msgid "The link you clicked is invalid."
msgstr "您点击的链接无效。"

#: src/components/routes/product-widget/SelectProducts/index.tsx:444
msgid "The maximum number of products for {0}is {1}"
msgstr "{0}的最大产品数量是{1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:351
#~ msgid "The maximum numbers number of tickets for {0}is {1}"
#~ msgstr "{0}的最大票数是{1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:318
#~ msgid "The maximum numbers number of tickets for Generals is {0}"
#~ msgstr "将军票的最大票数为{0}。"

#: src/components/common/ErrorDisplay/index.tsx:17
msgid "The page you are looking for does not exist"
msgstr "您要查找的页面不存在"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:56
msgid "The price displayed to the customer will include taxes and fees."
msgstr "显示给客户的价格将包括税费。"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:62
msgid "The price displayed to the customer will not include taxes and fees. They will be shown separately"
msgstr "显示给客户的价格不包括税费。税费将单独显示"

#: src/components/common/WidgetEditor/index.tsx:169
msgid "The styling settings you choose apply only to copied HTML and won't be stored."
msgstr "您选择的样式设置只适用于复制的 HTML，不会被保存。"

#: src/components/forms/ProductForm/index.tsx:355
msgid "The taxes and fees to apply to this product. You can create new taxes and fees on the"
msgstr "应用于此产品的税费。您可以在此创建新的税费"

#: src/components/forms/TicketForm/index.tsx:300
#~ msgid "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "适用于该票据的税费。您可以在"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:63
msgid "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"
msgstr "活动标题，将显示在搜索引擎结果中，并在社交媒体上分享时显示。默认情况下，将使用事件标题"

#: src/components/routes/product-widget/SelectProducts/index.tsx:272
msgid "There are no products available for this event"
msgstr "此活动没有可用产品"

#: src/components/routes/product-widget/SelectProducts/index.tsx:375
msgid "There are no products available in this category"
msgstr "此类别中没有可用产品"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:260
#~ msgid "There are no tickets available for this event"
#~ msgstr "本次活动没有门票"

#: src/components/modals/RefundOrderModal/index.tsx:137
msgid "There is a refund pending. Please wait for it to complete before requesting another refund."
msgstr "退款正在处理中。请等待退款完成后再申请退款。"

#: src/components/common/OrdersTable/index.tsx:80
msgid "There was an error marking the order as paid"
msgstr "标记订单为已支付时出错"

#: src/hooks/useFormErrorResponseHandler.tsx:28
msgid "There was an error processing your request. Please try again."
msgstr "处理您的请求时出现错误。请重试。"

#: src/components/common/OrdersTable/index.tsx:95
msgid "There was an error sending your message"
msgstr "发送信息时出现错误"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:105
msgid "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."
msgstr "这些详细信息仅在订单成功完成后显示。等待付款的订单不会显示此消息。"

#: src/components/layouts/CheckIn/index.tsx:201
msgid "This attendee has an unpaid order."
msgstr "此与会者有未支付的订单。"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:61
msgid "This category doesn't have any products yet."
msgstr "此类别尚无任何产品。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:112
msgid "This category is hidden from public view"
msgstr "此类别对公众隐藏"

#: src/components/common/CheckInListList/index.tsx:75
msgid "This check-in list has expired"
msgstr "此签到列表已过期"

#: src/components/layouts/CheckIn/index.tsx:331
msgid "This check-in list has expired and is no longer available for check-ins."
msgstr "此签到列表已过期，不再可用于签到。"

#: src/components/common/CheckInListList/index.tsx:82
msgid "This check-in list is active"
msgstr "此签到列表已激活"

#: src/components/common/CheckInListList/index.tsx:79
msgid "This check-in list is not active yet"
msgstr "此签到列表尚未激活"

#: src/components/layouts/CheckIn/index.tsx:348
msgid "This check-in list is not yet active and is not available for check-ins."
msgstr "此签到列表尚未激活，不能用于签到。"

#: src/components/forms/CheckInListForm/index.tsx:37
msgid "This description will be shown to the check-in staff"
msgstr "此描述将显示给签到工作人员"

#: src/components/modals/SendMessageModal/index.tsx:285
msgid "This email is not promotional and is directly related to the event."
msgstr "此电子邮件并非促销邮件，与活动直接相关。"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:8
#~ msgid "This event is not available at the moment. Please check back later."
#~ msgstr "此活动暂时不可用。请稍后再查看。"

#: src/components/layouts/EventHomepage/index.tsx:49
#~ msgid "This event is not available."
#~ msgstr "此活动不可用。"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
msgid "This information will be shown on the payment page, order summary page, and order confirmation email."
msgstr "这些信息将显示在支付页面、订单摘要页面和订单确认电子邮件中。"

#: src/components/forms/ProductForm/index.tsx:138
msgid "This is a general product, like a t-shirt or a mug. No ticket will be issued"
msgstr "这是一种常规产品，例如T恤或杯子。不发行门票"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:91
msgid "This is an online event"
msgstr "这是一项在线活动"

#: src/components/forms/CheckInListForm/index.tsx:57
msgid "This list will no longer be available for check-ins after this date"
msgstr "此日期后此列表将不再可用于签到"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:68
msgid "This message will be included in the footer of all emails sent from this event"
msgstr "此信息将包含在本次活动发送的所有电子邮件的页脚中"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
msgid "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"
msgstr "此消息仅在订单成功完成后显示。等待付款的订单不会显示此消息。"

#: src/components/forms/StripeCheckoutForm/index.tsx:93
msgid "This order has already been paid."
msgstr "此订单已付款。"

#: src/components/modals/RefundOrderModal/index.tsx:141
msgid "This order has already been refunded."
msgstr "此订单已退款。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:237
msgid "This order has been cancelled"
msgstr "此订单已取消"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:209
msgid "This order has been cancelled."
msgstr "此订单已取消。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:210
msgid "This order has expired. Please start again."
msgstr "此订单已过期。请重新开始。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:221
msgid "This order is awaiting payment"
msgstr "此订单正在等待付款"

#: src/components/routes/product-widget/CollectInformation/index.tsx:229
msgid "This order is complete"
msgstr "此订单已完成"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:211
msgid "This order is complete."
msgstr "此订单已完成。"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:204
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:207
msgid "This order is processing."
msgstr "此订单正在处理中。"

#: src/components/forms/StripeCheckoutForm/index.tsx:103
msgid "This order page is no longer available."
msgstr "此订购页面已不可用。"

#: src/components/forms/ProductForm/index.tsx:415
msgid "This overrides all visibility settings and will hide the product from all customers."
msgstr "这将覆盖所有可见性设置，并将该产品对所有客户隐藏。"

#: src/components/forms/TicketForm/index.tsx:360
#~ msgid "This overrides all visibility settings and will hide the ticket from all customers."
#~ msgstr "这会覆盖所有可见性设置，并对所有客户隐藏票单。"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:59
msgid "This product cannot be deleted because it is associated with an order. You can hide it instead."
msgstr "此产品无法删除，因为它与订单关联。您可以将其隐藏。"

#: src/components/forms/ProductForm/index.tsx:132
msgid "This product is a ticket. Buyers will be issued a ticket upon purchase"
msgstr "此产品为门票。购买后买家将收到门票"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:215
msgid "This product is hidden from public view"
msgstr "此产品对公众隐藏"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:216
msgid "This product is hidden unless targeted by a Promo Code"
msgstr "除非由促销代码指定，否则此产品是隐藏的"

#: src/components/common/QuestionsTable/index.tsx:90
msgid "This question is only visible to the event organizer"
msgstr "此问题只有活动组织者可见"

#: src/components/routes/auth/ResetPassword/index.tsx:29
msgid "This reset password link is invalid or expired."
msgstr "此重置密码链接无效或已过期。"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:56
#~ msgid ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."
#~ msgstr ""
#~ "不能删除此票单，因为它\n"
#~ "与订单相关联。您可以将其隐藏。"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:131
#~ msgid "This ticket is hidden from public view"
#~ msgstr "此票不对外开放"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:132
#~ msgid "This ticket is hidden unless targeted by a Promo Code"
#~ msgstr "除非使用促销代码，否则此票为隐藏票"

#: src/components/modals/EditUserModal/index.tsx:65
msgid "This user is not active, as they have not accepted their invitation."
msgstr "该用户未激活，因为他们没有接受邀请。"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "ticket"
msgstr "入场券"

#: src/components/common/AttendeeTable/index.tsx:111
#: src/components/forms/ProductForm/index.tsx:130
#: src/components/modals/CreateAttendeeModal/index.tsx:151
msgid "Ticket"
msgstr "门票"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:52
#~ msgid "Ticket deleted successfully"
#~ msgstr "成功删除票单"

#: src/components/common/AttendeeTable/index.tsx:49
msgid "Ticket email has been resent to attendee"
msgstr "门票电子邮件已重新发送给与会者"

#: src/components/common/MessageList/index.tsx:22
msgid "Ticket holders"
msgstr "票务持有人"

#: src/components/routes/event/products.tsx:86
msgid "Ticket or Product"
msgstr "票或商品"

#: src/components/routes/event/EventDashboard/index.tsx:53
#~ msgid "Ticket Sales"
#~ msgstr "门票销售"

#: src/components/modals/CreateAttendeeModal/index.tsx:161
#: src/components/modals/EditAttendeeModal/index.tsx:115
#~ msgid "Ticket Tier"
#~ msgstr "门票等级"

#: src/components/forms/TicketForm/index.tsx:189
#: src/components/forms/TicketForm/index.tsx:196
#~ msgid "Ticket Type"
#~ msgstr "机票类型"

#: src/components/common/WidgetEditor/index.tsx:311
#~ msgid "Ticket Widget Preview"
#~ msgstr "票务小工具预览"

#: src/components/common/PromoCodeTable/index.tsx:147
#~ msgid "Ticket(s)"
#~ msgstr "机票"

#: src/components/common/PromoCodeTable/index.tsx:71
#: src/components/layouts/Event/index.tsx:40
#: src/components/layouts/EventHomepage/index.tsx:80
#: src/components/routes/event/tickets.tsx:39
#~ msgid "Tickets"
#~ msgstr "门票"

#: src/components/layouts/Event/index.tsx:101
#: src/components/routes/event/products.tsx:46
msgid "Tickets & Products"
msgstr "票务与商品"

#: src/components/routes/product-widget/PrintOrder/index.tsx:43
msgid "Tickets for"
msgstr "门票"

#: src/components/common/EventCard/index.tsx:126
#~ msgid "tickets sold"
#~ msgstr "已售票"

#: src/components/common/StatBoxes/index.tsx:21
#~ msgid "Tickets sold"
#~ msgstr "已售出的门票"

#: src/components/routes/event/EventDashboard/index.tsx:73
#~ msgid "Tickets Sold"
#~ msgstr "已售出的门票"

#: src/components/common/TicketsTable/index.tsx:44
#~ msgid "Tickets sorted successfully"
#~ msgstr "成功分类的门票"

#: src/components/forms/ProductForm/index.tsx:67
msgid "Tier {0}"
msgstr "{0}级"

#: src/components/forms/ProductForm/index.tsx:163
msgid "Tiered Product"
msgstr "分层产品"

#: src/components/forms/ProductForm/index.tsx:240
#~ msgid ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""

#: src/components/forms/ProductForm/index.tsx:243
msgid "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."
msgstr "分层产品允许您为同一产品提供多种价格选项。这非常适合早鸟产品，或为不同人群提供不同的价格选项。\" # zh-cn"

#: src/components/forms/TicketForm/index.tsx:145
#~ msgid "Tiered Ticket"
#~ msgstr "分层票"

#: src/components/forms/TicketForm/index.tsx:203
#~ msgid ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""
#~ "分层门票允许您为同一张门票提供多种价格选择。\n"
#~ "这非常适合早鸟票，或为不同人群提供不同价\n"
#~ "选择。"

#: src/components/layouts/Checkout/index.tsx:90
msgid "Time left:"
msgstr "剩余时间："

#: src/components/common/PromoCodeTable/index.tsx:71
msgid "Times used"
msgstr "使用次数"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:25
msgid "Times Used"
msgstr "使用次数"

#: src/components/forms/OrganizerForm/index.tsx:54
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:82
#: src/components/routes/auth/AcceptInvitation/index.tsx:105
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:109
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:181
#: src/components/routes/profile/ManageProfile/index.tsx:161
msgid "Timezone"
msgstr "时区"

#: src/components/forms/PromoCodeForm/index.tsx:36
msgid "TIP"
msgstr "TIP"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:206
msgid "Title"
msgstr "标题"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:182
msgid "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."
msgstr "要接受信用卡付款，您需要连接您的 Stripe 账户。Stripe 是我们的支付处理合作伙伴，确保安全交易和及时付款。"

#: src/components/layouts/Event/index.tsx:108
msgid "Tools"
msgstr "工具"

#: src/components/common/OrderSummary/index.tsx:92
msgid "Total"
msgstr "总计"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:52
msgid "Total Before Discounts"
msgstr "折扣前总计"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:58
msgid "Total Discount Amount"
msgstr "折扣总金额"

#: src/components/routes/event/EventDashboard/index.tsx:273
msgid "Total Fees"
msgstr "总费用"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:46
msgid "Total Gross Sales"
msgstr "总销售额"

#: src/components/common/OrderDetails/index.tsx:59
#: src/components/modals/RefundOrderModal/index.tsx:74
msgid "Total order amount"
msgstr "订单总额"

#: src/components/common/OrderDetails/index.tsx:67
#: src/components/modals/RefundOrderModal/index.tsx:82
msgid "Total refunded"
msgstr "退款总额"

#: src/components/routes/event/EventDashboard/index.tsx:276
msgid "Total Refunded"
msgstr "退款总额"

#: src/components/modals/RefundOrderModal/index.tsx:90
msgid "Total remaining"
msgstr "剩余总数"

#: src/components/routes/event/EventDashboard/index.tsx:275
msgid "Total Tax"
msgstr "总税额"

#: src/components/layouts/AuthLayout/index.tsx:54
msgid "Track revenue, page views, and sales with detailed analytics and exportable reports"
msgstr "通过详细的分析和可导出的报告跟踪收入、页面浏览量和销售情况"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
msgid "Transaction Fee:"
msgstr "交易手续费："

#: src/components/forms/TaxAndFeeForm/index.tsx:44
msgid "Type"
msgstr "类型"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "Unable to {0} attendee"
#~ msgstr "无法{0}与会者"

#: src/components/layouts/CheckIn/index.tsx:93
msgid "Unable to check in attendee"
msgstr "无法签到参与者"

#: src/components/layouts/CheckIn/index.tsx:114
msgid "Unable to check out attendee"
msgstr "无法签退参与者"

#: src/components/modals/CreateProductModal/index.tsx:63
msgid "Unable to create product. Please check the your details"
msgstr "无法创建产品。请检查您的详细信息"

#: src/components/routes/product-widget/SelectProducts/index.tsx:123
msgid "Unable to create product. Please check your details"
msgstr "无法创建产品。请检查您的详细信息"

#: src/components/modals/CreateQuestionModal/index.tsx:60
msgid "Unable to create question. Please check the your details"
msgstr "无法创建问题。请检查您的详细信息"

#: src/components/modals/CreateTicketModal/index.tsx:65
#: src/components/routes/ticket-widget/SelectTickets/index.tsx:117
#~ msgid "Unable to create ticket. Please check the your details"
#~ msgstr "无法创建票单。请检查您的详细信息"

#: src/components/modals/DuplicateProductModal/index.tsx:103
msgid "Unable to duplicate product. Please check the your details"
msgstr "无法复制产品。请检查您的详细信息"

#: src/components/layouts/CheckIn/index.tsx:145
msgid "Unable to fetch attendee"
msgstr "无法获取参与者"

#: src/components/modals/EditQuestionModal/index.tsx:84
msgid "Unable to update question. Please check the your details"
msgstr "无法更新问题。请检查您的详细信息"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:30
msgid "Unique Customers"
msgstr "独立客户"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:153
msgid "United States"
msgstr "美国"

#: src/components/common/QuestionAndAnswerList/index.tsx:284
msgid "Unknown Attendee"
msgstr "未知参会者"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:43
#: src/components/forms/ProductForm/index.tsx:83
#: src/components/forms/ProductForm/index.tsx:299
#: src/components/forms/PromoCodeForm/index.tsx:82
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:78
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:84
msgid "Unlimited"
msgstr "无限制"

#: src/components/routes/product-widget/SelectProducts/index.tsx:407
msgid "Unlimited available"
msgstr "无限供应"

#: src/components/common/PromoCodeTable/index.tsx:126
msgid "Unlimited usages allowed"
msgstr "允许无限次使用"

#: src/components/layouts/CheckIn/index.tsx:200
msgid "Unpaid Order"
msgstr "未支付订单"

#: src/components/routes/event/EventDashboard/index.tsx:170
msgid "Unpublish Event"
msgstr "取消发布活动"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:20
#: src/components/common/EventStatusBadge/index.tsx:16
msgid "Upcoming"
msgstr "即将推出"

#: src/components/routes/events/Dashboard/index.tsx:55
msgid "Upcoming Events"
msgstr "即将举行的活动"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Update {0}"
msgstr "更新 {0}"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:66
msgid "Update event name, description and dates"
msgstr "更新活动名称、说明和日期"

#: src/components/routes/profile/ManageProfile/index.tsx:177
msgid "Update profile"
msgstr "更新个人资料"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Upload Cover"
msgstr "上传封面"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:105
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:152
msgid "Upload Image"
msgstr "上传图片"

#: src/components/common/WebhookTable/index.tsx:236
#: src/components/modals/ShareModal/index.tsx:129
msgid "URL"
msgstr "链接"

#: src/components/common/PromoCodeTable/index.tsx:175
msgid "URL copied to clipboard"
msgstr "复制到剪贴板的 URL"

#: src/components/modals/CreateWebhookModal/index.tsx:25
msgid "URL is required"
msgstr "URL 是必填项"

#: src/components/common/WidgetEditor/index.tsx:298
msgid "Usage Example"
msgstr "使用示例"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:76
msgid "Usage Limit"
msgstr "使用限制"

#: src/components/routes/event/HomepageDesigner/index.tsx:139
msgid "Use a blurred version of the cover image as the background"
msgstr "使用封面图片的模糊版本作为背景"

#: src/components/routes/event/HomepageDesigner/index.tsx:137
msgid "Use cover image"
msgstr "使用封面图片"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User"
msgstr "用户"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User Management"
msgstr "用户管理"

#: src/components/routes/account/ManageAccount/index.tsx:32
msgid "Users"
msgstr "用户"

#: src/components/modals/EditUserModal/index.tsx:81
msgid "Users can change their email in <0>Profile Settings</0>"
msgstr "用户可在 <0>\"配置文件设置\"</0> 中更改自己的电子邮件"

#: src/components/forms/OrganizerForm/index.tsx:55
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:83
#: src/components/routes/auth/AcceptInvitation/index.tsx:106
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:110
#: src/components/routes/profile/ManageProfile/index.tsx:162
msgid "UTC"
msgstr "世界协调时"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "VAT"
msgstr "增值税"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:116
msgid "Venue Name"
msgstr "地点名称"

#: src/components/common/LanguageSwitcher/index.tsx:34
msgid "Vietnamese"
msgstr "越南语"

#: src/components/modals/ManageAttendeeModal/index.tsx:220
#: src/components/modals/ManageOrderModal/index.tsx:200
msgid "View"
msgstr "查看"

#: src/components/routes/event/Reports/index.tsx:38
msgid "View and download reports for your event. Please note, only completed orders are included in these reports."
msgstr "查看并下载您的活动报告。请注意，报告中仅包含已完成的订单。"

#: src/components/common/AttendeeList/index.tsx:84
msgid "View Answers"
msgstr "查看答案"

#: src/components/common/AttendeeTable/index.tsx:164
#~ msgid "View attendee"
#~ msgstr "查看与会者"

#: src/components/common/AttendeeList/index.tsx:103
msgid "View Attendee Details"
msgstr "查看参会者详情"

#: src/components/layouts/Checkout/index.tsx:55
#~ msgid "View event homepage"
#~ msgstr "查看活动主页"

#: src/components/common/EventCard/index.tsx:145
msgid "View event page"
msgstr "查看活动页面"

#: src/components/common/MessageList/index.tsx:59
msgid "View full message"
msgstr "查看完整信息"

#: src/components/common/WebhookTable/index.tsx:113
msgid "View logs"
msgstr "查看日志"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View map"
msgstr "查看地图"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View on Google Maps"
msgstr "在谷歌地图上查看"

#: src/components/common/OrdersTable/index.tsx:111
#~ msgid "View order"
#~ msgstr "查看订单"

#: src/components/forms/StripeCheckoutForm/index.tsx:94
#: src/components/forms/StripeCheckoutForm/index.tsx:104
#: src/components/routes/product-widget/CollectInformation/index.tsx:231
msgid "View order details"
msgstr "查看订单详情"

#: src/components/forms/CheckInListForm/index.tsx:21
msgid "VIP check-in list"
msgstr "VIP签到列表"

#: src/components/forms/ProductForm/index.tsx:254
#~ msgid "VIP Product"
#~ msgstr "VIP产品"

#: src/components/forms/ProductForm/index.tsx:254
msgid "VIP Ticket"
msgstr "贵宾票"

#: src/components/forms/ProductForm/index.tsx:394
msgid "Visibility"
msgstr "可见性"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:77
msgid "We could not process your payment. Please try again or contact support."
msgstr "我们无法处理您的付款。请重试或联系技术支持。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:53
msgid "We couldn't delete the category. Please try again."
msgstr "我们无法删除该类别。请再试一次。"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:24
msgid "We couldn't find any tickets matching {0}"
msgstr "我们找不到与{0}匹配的任何门票"

#: src/components/common/ErrorLoadingMessage/index.tsx:13
msgid "We couldn't load the data. Please try again."
msgstr "我们无法加载数据。请重试。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:94
msgid "We couldn't reorder the categories. Please try again."
msgstr "我们无法重新排序类别。请再试一次。"

#: src/components/routes/event/HomepageDesigner/index.tsx:118
msgid "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"
msgstr "我们建议尺寸为 2160px x 1080px，文件大小不超过 5MB"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:42
#~ msgid "We use Stripe to process payments. Connect your Stripe account to start receiving payments."
#~ msgstr "我们使用 Stripe 处理付款。连接您的 Stripe 账户即可开始接收付款。"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:84
msgid "We were unable to confirm your payment. Please try again or contact support."
msgstr "我们无法确认您的付款。请重试或联系技术支持。"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:79
msgid "We're processing your order. Please wait..."
msgstr "我们正在处理您的订单。请稍候..."

#: src/components/modals/CreateWebhookModal/index.tsx:46
msgid "Webhook created successfully"
msgstr "Webhook 创建成功"

#: src/components/common/WebhookTable/index.tsx:53
msgid "Webhook deleted successfully"
msgstr "Webhook 删除成功"

#: src/components/modals/WebhookLogsModal/index.tsx:151
msgid "Webhook Logs"
msgstr "Webhook 日志"

#: src/components/forms/WebhookForm/index.tsx:117
msgid "Webhook URL"
msgstr "Webhook URL"

#: src/components/forms/WebhookForm/index.tsx:27
msgid "Webhook will not send notifications"
msgstr "Webhook 不会发送通知"

#: src/components/forms/WebhookForm/index.tsx:21
msgid "Webhook will send notifications"
msgstr "Webhook 将发送通知"

#: src/components/layouts/Event/index.tsx:111
#: src/components/routes/event/Webhooks/index.tsx:30
msgid "Webhooks"
msgstr "Webhooks"

#: src/components/routes/auth/AcceptInvitation/index.tsx:76
msgid "Welcome aboard! Please login to continue."
msgstr "欢迎加入！请登录以继续。"

#: src/components/routes/auth/Login/index.tsx:55
msgid "Welcome back 👋"
msgstr "欢迎回来 👋"

#: src/components/routes/event/EventDashboard/index.tsx:95
msgid "Welcome back{0} 👋"
msgstr "欢迎回来{0} 👋"

#: src/components/routes/auth/Register/index.tsx:67
msgid "Welcome to Hi.Events 👋"
msgstr "欢迎来到 Hi.Events 👋"

#: src/components/routes/welcome/index.tsx:125
msgid "Welcome to Hi.Events, {0} 👋"
msgstr "欢迎来到 Hi.Events, {0} 👋"

#: src/components/forms/ProductForm/index.tsx:242
msgid "What are Tiered Products?"
msgstr "什么是分层产品？"

#: src/components/forms/TicketForm/index.tsx:202
#~ msgid "What are Tiered Tickets?"
#~ msgstr "什么是分层门票？"

#: src/components/forms/CheckInListForm/index.tsx:50
msgid "What date should this check-in list become active?"
msgstr "此签到列表应在何时激活？"

#: src/components/modals/CreateProductCategoryModal/index.tsx:53
msgid "What is a Category?"
msgstr "什么是类别？"

#: src/components/routes/event/Webhooks/index.tsx:31
#~ msgid "What is a webhook?"
#~ msgstr "什么是 Webhook？"

#: src/components/forms/QuestionForm/index.tsx:159
msgid "What products does this code apply to?"
msgstr "此代码适用于哪些产品？"

#: src/components/forms/PromoCodeForm/index.tsx:68
msgid "What products does this code apply to? (Applies to all by default)"
msgstr "此代码适用于哪些产品？（默认适用于所有产品）"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:48
msgid "What products should this capacity apply to?"
msgstr "此容量应适用于哪些产品？"

#: src/components/forms/PromoCodeForm/index.tsx:68
#~ msgid "What tickets does this code apply to? (Applies to all by default)"
#~ msgstr "此代码适用于哪些机票？(默认适用于所有）"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:47
#: src/components/forms/QuestionForm/index.tsx:159
#~ msgid "What tickets should this question be apply to?"
#~ msgstr "该问题应适用于哪些机票？"

#: src/components/forms/QuestionForm/index.tsx:179
msgid "What time will you be arriving?"
msgstr "您什么时候抵达？"

#: src/components/forms/QuestionForm/index.tsx:170
msgid "What type of question is this?"
msgstr "这是什么类型的问题？"

#: src/components/forms/WebhookForm/index.tsx:108
msgid "When a check-in is deleted"
msgstr "当签到被删除时"

#: src/components/forms/WebhookForm/index.tsx:84
msgid "When a new attendee is created"
msgstr "当新与会者被创建时"

#: src/components/forms/WebhookForm/index.tsx:54
msgid "When a new order is created"
msgstr "当新订单被创建时"

#: src/components/forms/WebhookForm/index.tsx:36
msgid "When a new product is created"
msgstr "当新产品被创建时"

#: src/components/forms/WebhookForm/index.tsx:48
msgid "When a product is deleted"
msgstr "当产品被删除时"

#: src/components/forms/WebhookForm/index.tsx:42
msgid "When a product is updated"
msgstr "当产品被更新时"

#: src/components/forms/WebhookForm/index.tsx:96
msgid "When an attendee is cancelled"
msgstr "当与会者被取消时"

#: src/components/forms/WebhookForm/index.tsx:102
msgid "When an attendee is checked in"
msgstr "当与会者签到时"

#: src/components/forms/WebhookForm/index.tsx:90
msgid "When an attendee is updated"
msgstr "当与会者被更新时"

#: src/components/forms/WebhookForm/index.tsx:78
msgid "When an order is cancelled"
msgstr "当订单被取消时"

#: src/components/forms/WebhookForm/index.tsx:66
msgid "When an order is marked as paid"
msgstr "当订单被标记为已支付时"

#: src/components/forms/WebhookForm/index.tsx:72
msgid "When an order is refunded"
msgstr "当订单被退款时"

#: src/components/forms/WebhookForm/index.tsx:60
msgid "When an order is updated"
msgstr "当订单被更新时"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:182
msgid "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."
msgstr "启用后，将为票务订单生成发票。发票将随订单确认邮件一起发送。参与"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:141
msgid "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."
msgstr "启用线下支付后，用户可以完成订单并收到门票。他们的门票将清楚地显示订单未支付，签到工具会通知签到工作人员订单是否需要支付。"

#: src/components/forms/CheckInListForm/index.tsx:59
msgid "When should this check-in list expire?"
msgstr "此签到列表应在何时过期？"

#: src/components/forms/CheckInListForm/index.tsx:25
msgid "Which tickets should be associated with this check-in list?"
msgstr "哪些票应与此签到列表关联？"

#: src/components/modals/CreateEventModal/index.tsx:107
msgid "Who is organizing this event?"
msgstr "这项活动由谁组织？"

#: src/components/modals/SendMessageModal/index.tsx:194
msgid "Who is this message to?"
msgstr "这条信息是发给谁的？"

#: src/components/forms/QuestionForm/index.tsx:151
msgid "Who should be asked this question?"
msgstr "这个问题应该问谁？"

#: src/components/layouts/Event/index.tsx:110
msgid "Widget Embed"
msgstr "嵌入小部件"

#: src/components/common/WidgetEditor/index.tsx:162
msgid "Widget Settings"
msgstr "小部件设置"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Working"
msgstr "工作"

#: src/components/modals/CreateProductCategoryModal/index.tsx:60
#: src/components/modals/CreateProductModal/index.tsx:88
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
#: src/components/modals/CreateQuestionModal/index.tsx:74
#: src/components/modals/DuplicateEventModal/index.tsx:142
#: src/components/modals/DuplicateProductModal/index.tsx:113
#: src/components/modals/EditProductCategoryModal/index.tsx:70
#: src/components/modals/EditProductModal/index.tsx:108
#: src/components/modals/EditPromoCodeModal/index.tsx:84
#: src/components/modals/EditQuestionModal/index.tsx:101
#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/Register/index.tsx:129
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Working..."
msgstr "工作..."

#: src/components/common/ReportTable/index.tsx:51
msgid "Year to date"
msgstr "年度至今"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "Yes"
msgstr "是"

#: src/components/forms/ProductForm/index.tsx:376
msgid "Yes, remove them"
msgstr "是的，移除它们"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:77
msgid "You already scanned this ticket"
msgstr "您已经扫描过此票"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:53
msgid "You are changing your email to <0>{0}</0>."
msgstr "您正在将电子邮件更改为 <0>{0}</0>。"

#: src/components/layouts/CheckIn/index.tsx:88
#: src/components/layouts/CheckIn/index.tsx:110
msgid "You are offline"
msgstr "您处于离线状态"

#: src/components/forms/ProductForm/index.tsx:407
msgid "You can create a promo code which targets this product on the"
msgstr "您可以创建一个促销代码，针对该产品"

#: src/components/forms/TicketForm/index.tsx:352
#~ msgid "You can create a promo code which targets this ticket on the"
#~ msgstr "您可以在"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:31
#~ msgid "You can now start receiving payments through Stripe."
#~ msgstr "现在您可以开始通过 Stripe 接收付款。"

#: src/components/forms/ProductForm/index.tsx:208
msgid "You cannot change the product type as there are attendees associated with this product."
msgstr "您无法更改产品类型，因为有与该产品关联的参会者。"

#: src/components/forms/TicketForm/index.tsx:184
#~ msgid "You cannot change the ticket type as there are attendees associated with this ticket."
#~ msgstr "您不能更改票务类型，因为该票务已关联了与会者。"

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "You cannot check in attendees with unpaid orders."
#~ msgstr "您无法为未支付订单的与会者签到。"

#: src/components/layouts/CheckIn/index.tsx:129
#: src/components/layouts/CheckIn/index.tsx:164
msgid "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."
msgstr "您无法为未支付订单的与会者签到。此设置可在活动设置中更改。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:40
msgid "You cannot delete the last category."
msgstr "您不能删除最后一个类别。"

#: src/components/forms/ProductForm/index.tsx:57
msgid "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."
msgstr "您无法删除此价格层，因为此层已有售出的产品。您可以将其隐藏。"

#: src/components/forms/TicketForm/index.tsx:54
#~ msgid "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."
#~ msgstr "您不能删除此价格等级，因为此等级已售出门票。您可以将其隐藏。"

#: src/components/modals/EditUserModal/index.tsx:88
msgid "You cannot edit the role or status of the account owner."
msgstr "不能编辑账户所有者的角色或状态。"

#: src/components/modals/RefundOrderModal/index.tsx:132
msgid "You cannot refund a manually created order."
msgstr "您不能退还手动创建的订单。"

#: src/components/common/QuestionsTable/index.tsx:252
msgid "You created a hidden question but disabled the option to show hidden questions. It has been enabled."
msgstr "您创建了一个隐藏问题，但禁用了显示隐藏问题的选项。该选项已启用。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:77
#~ msgid "You do not have permission to access this page"
#~ msgstr "您没有访问该页面的权限"

#: src/components/modals/ChooseAccountModal/index.tsx:14
msgid "You have access to multiple accounts. Please choose one to continue."
msgstr "您可以访问多个账户。请选择一个继续。"

#: src/components/routes/auth/AcceptInvitation/index.tsx:57
msgid "You have already accepted this invitation. Please login to continue."
msgstr "您已接受此邀请。请登录以继续。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:29
#~ msgid "You have connected your Stripe account"
#~ msgstr "您已连接 Stripe 账户"

#: src/components/common/QuestionsTable/index.tsx:338
msgid "You have no attendee questions."
msgstr "没有与会者提问。"

#: src/components/common/QuestionsTable/index.tsx:323
msgid "You have no order questions."
msgstr "您没有订单问题。"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:41
msgid "You have no pending email change."
msgstr "您没有待处理的电子邮件更改。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:39
#~ msgid "You have not completed your Stripe Connect setup"
#~ msgstr "您尚未完成 Stripe Connect 设置"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:38
#~ msgid "You have not connected your Stripe account"
#~ msgstr "您尚未连接 Stripe 账户"

#: src/components/layouts/Checkout/index.tsx:156
msgid "You have run out of time to complete your order."
msgstr "您已超时，未能完成订单。"

#: src/components/forms/ProductForm/index.tsx:374
#~ msgid "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"
#~ msgstr "您已为免费产品添加了税费。您想删除或隐藏它们吗？"

#: src/components/forms/ProductForm/index.tsx:374
msgid "You have taxes and fees added to a Free Product. Would you like to remove them?"
msgstr "您已向免费产品添加了税费。您想要删除它们吗？"

#: src/components/forms/TicketForm/index.tsx:319
#~ msgid "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"
#~ msgstr "您在免费机票上添加了税费。您想删除或隐藏它们吗？"

#: src/components/common/MessageList/index.tsx:74
msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."
msgstr "您尚未发送任何消息。您可以向所有参会者发送消息，或向特定产品持有者发送消息。"

#: src/components/common/MessageList/index.tsx:64
#~ msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."
#~ msgstr "您尚未发送任何信息。您可以向所有与会者或特定持票人发送信息。"

#: src/components/modals/SendMessageModal/index.tsx:108
msgid "You must acknowledge that this email is not promotional"
msgstr "您必须确认此电子邮件并非促销邮件"

#: src/components/routes/auth/AcceptInvitation/index.tsx:33
msgid "You must agree to the terms and conditions"
msgstr "您必须同意条款和条件"

#: src/components/routes/event/GettingStarted/index.tsx:193
msgid "You must confirm your email address before your event can go live."
msgstr "您必须在活动上线前确认您的电子邮件地址。"

#: src/components/modals/CreateAttendeeModal/index.tsx:98
msgid "You must create a ticket before you can manually add an attendee."
msgstr "必须先创建机票，然后才能手动添加与会者。"

#: src/components/forms/ProductForm/index.tsx:60
msgid "You must have at least one price tier"
msgstr "您必须至少有一个价格等级"

#: src/components/modals/SendMessageModal/index.tsx:136
#~ msgid "You need to verify your account before you can send messages."
#~ msgstr "发送信息前，您需要验证您的账户。"

#: src/components/modals/SendMessageModal/index.tsx:151
msgid "You need to verify your account email before you can send messages."
msgstr "您需要验证您的帐户电子邮件才能发送消息。"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:143
msgid "You will have to mark an order as paid manually. This can be done on the manage order page."
msgstr "您必须手动将订单标记为已支付。这可以在订单管理页面上完成。"

#: src/components/modals/CreateCheckInListModal/index.tsx:55
msgid "You'll need a ticket before you can create a check-in list."
msgstr "在创建签到列表之前，您需要先获得票。"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
msgid "You'll need at a product before you can create a capacity assignment."
msgstr "在您创建容量分配之前，您需要一个产品。"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
#~ msgid "You'll need at a ticket before you can create a capacity assignment."
#~ msgstr "在创建容量分配之前，您需要一张票。"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:43
msgid "You'll need at least one product to get started. Free, paid or let the user decide what to pay."
msgstr "您需要至少一个产品才能开始。免费、付费或让用户决定支付金额。"

#: src/components/common/TicketsTable/index.tsx:69
#~ msgid "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."
#~ msgstr "您至少需要一张票才能开始。免费、付费或让用户决定支付方式。"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:93
msgid "You're going to {0}! 🎉"
msgstr "你要去 {0}! 🎉"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:64
msgid "Your account name is used on event pages and in emails."
msgstr "您的账户名称会在活动页面和电子邮件中使用。"

#: src/components/routes/event/attendees.tsx:44
msgid "Your attendees have been exported successfully."
msgstr "您的与会者已成功导出。"

#: src/components/common/AttendeeTable/index.tsx:61
msgid "Your attendees will appear here once they have registered for your event. You can also manually add attendees."
msgstr "与会者注册参加活动后，就会出现在这里。您也可以手动添加与会者。"

#: src/components/common/WidgetEditor/index.tsx:329
msgid "Your awesome website 🎉"
msgstr "您的网站真棒 🎉"

#: src/components/routes/product-widget/CollectInformation/index.tsx:276
msgid "Your Details"
msgstr "您的详细信息"

#: src/components/routes/auth/ForgotPassword/index.tsx:52
msgid "Your Email"
msgstr "您的电子邮件"

#: src/components/routes/profile/ManageProfile/index.tsx:118
msgid "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"
msgstr "您要求将电子邮件更改为<0>{0}</0>的申请正在处理中。请检查您的电子邮件以确认"

#: src/components/routes/event/EventDashboard/index.tsx:150
msgid "Your event must be live before you can sell tickets to attendees."
msgstr "在向与会者销售门票之前，您的活动必须处于上线状态。"

#: src/components/routes/event/EventDashboard/index.tsx:164
#~ msgid "Your event must be live before you can sell tickets."
#~ msgstr "您的活动必须上线后才能销售门票。"

#: src/components/common/OrdersTable/index.tsx:88
msgid "Your message has been sent"
msgstr "您的信息已发送"

#: src/components/layouts/Checkout/index.tsx:84
msgid "Your Order"
msgstr "您的订单"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:94
msgid "Your order has been cancelled"
msgstr "您的订单已被取消"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:96
msgid "Your order is awaiting payment 🏦"
msgstr "您的订单正在等待支付 🏦"

#: src/components/routes/event/orders.tsx:95
msgid "Your orders have been exported successfully."
msgstr "您的订单已成功导出。"

#: src/components/common/OrdersTable/index.tsx:66
msgid "Your orders will appear here once they start rolling in."
msgstr "您的订单一旦开始滚动，就会出现在这里。"

#: src/components/routes/auth/Login/index.tsx:74
#: src/components/routes/auth/Register/index.tsx:107
msgid "Your password"
msgstr "您的密码"

#: src/components/forms/StripeCheckoutForm/index.tsx:63
msgid "Your payment is processing."
msgstr "您的付款正在处理中。"

#: src/components/forms/StripeCheckoutForm/index.tsx:66
msgid "Your payment was not successful, please try again."
msgstr "您的付款未成功，请重试。"

#: src/components/forms/StripeCheckoutForm/index.tsx:126
msgid "Your payment was unsuccessful. Please try again."
msgstr "您的付款未成功。请重试。"

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#~ msgid "Your product for"
#~ msgstr "您的产品为"

#: src/components/modals/RefundOrderModal/index.tsx:58
msgid "Your refund is processing."
msgstr "您的退款正在处理中。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:153
msgid "Your Stripe account is connected and ready to process payments."
msgstr "您的 Stripe 账户已连接并准备好处理支付。"

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#: src/components/routes/product-widget/PrintProduct/index.tsx:42
msgid "Your ticket for"
msgstr "您的入场券"

#: src/components/routes/product-widget/CollectInformation/index.tsx:348
msgid "ZIP / Postal Code"
msgstr "邮政编码"

#: src/components/common/CheckoutQuestion/index.tsx:170
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:146
msgid "Zip or Postal Code"
msgstr "邮政编码"

#: src/components/routes/product-widget/CollectInformation/index.tsx:349
msgid "ZIP or Postal Code"
msgstr "邮政编码"
