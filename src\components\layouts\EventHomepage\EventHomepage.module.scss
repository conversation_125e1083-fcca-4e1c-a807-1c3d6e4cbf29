@use "../../../styles/mixins.scss";

.background {
  position: fixed;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  filter: blur(10px);
  z-index: -1;
  transform: scale(2);
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;

  @include mixins.respond-below(sm) {
    display: none;
  }
}

.styleContainer {
  display: flex;
  justify-content: center;
}

.container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  max-width: 1000px;
  border-radius: 5px;
  margin: 30px;
  overflow: hidden;
  box-shadow: 0px 0px 6px 0px #00000025;

  @include mixins.respond-above(md) {
    min-width: 730px;
  }

  @include mixins.respond-below(md) {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    width: 100%;
  }

  @include mixins.respond-below(sm) {
    border-radius: 0;
    margin: 0;
  }

  .innerContainer {
    display: flex;
    flex-direction: column;
    max-width: 1000px;
    place-self: center;
    background-color: var(--homepage-background-color, var(--tk-color-white));
    color: var(--homepage-primary-text-color, var(--tk-primary));
    padding: 35px;
    width: 100%;

    @include mixins.respond-below(md) {
      min-height: 100vh;
      justify-content: space-between;
      padding: 20px;
    }

    a {
      color: var(--homepage-primary-text-color, var(--tk-primary)) !important;
    }

    .eventInfo {
      width: 100%;
    }

    .productSelection {
      .buyProductsBox {
        border-radius: 10px;
        border: 1px solid #ddd;
        position: sticky;
        top: 20px;
        width: 300px;

        .priceRange {
          text-align: center;
          font-weight: 600;
          padding: 0 10px 10px;
        }
      }
    }
  }
}
