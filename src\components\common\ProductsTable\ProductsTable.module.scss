@use "../../../styles/mixins.scss";

.sortableCategory {
  margin-bottom: 20px;
  transition: transform 250ms ease;
  border-radius: var(--tk-radius-sm);
  padding: var(--tk-spacing-lg);
  box-shadow: 0 3px 0 #dddddd;
  border: 1px solid #e3e3e3;
  background-color: #ffffff;
}

.categoryHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  gap: 10px;
}

.categoryActions {
  display: flex;
  gap: 5px;
}

.categoryAction {
  margin-left: auto;
}

.categoryTitle {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.categoryDragHandle {
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

.dragHandle {
  touch-action: none;
  margin-top: 5px;
}

.dragHandleDisabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.categoryContent {
  min-height: 50px;
}

.isOver {
  background-color: #efefef;
}

.isDragging {
  opacity: 0.5;
  z-index: 1000;
}

.dragOverlay {
  .sortableCategory,
  .productCard {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  }
}

.cards {
  display: flex;
  flex-direction: column;
}

.productCard {
  box-sizing: border-box;
  border-radius: var(--tk-radius-sm);
  border: 1px solid #e3e3e3;
  background-color: #ffffff;
  display: grid;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
  gap: 10px;
  transition: transform 250ms ease;
  grid-template-areas: "dragHanlde productInfo action";
  grid-template-columns: 40px 1fr 40px;

  @include mixins.respond-below(lg) {
    grid-template-areas:
      "dragHanlde productInfo"
      "dragHanlde action";
  }

  .halfCircle {
    width: 20px;
    height: 10px;
    background-color: #fff;
    border-top-left-radius: 110px;
    border-top-right-radius: 110px;
    border: 1px solid #ddd;
    border-bottom: 0;
    transform: rotate(90deg);
    position: absolute;
    left: -6px;
    top: 44%;
  }

  .halfCircle.right {
    left: auto;
    right: -6px;
    transform: rotate(270deg);
  }

  .dragHandle {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: move;
    grid-area: dragHanlde;
    touch-action: none;
  }

  .dragHandleDisabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .productInfo {
    grid-area: productInfo;
    display: flex;

    .productDetails {
      display: grid;
      width: 100%;
      align-items: center;
      gap: 15px;
      flex-wrap: wrap;
      grid-template-columns: 1fr 1fr 1fr 1fr;

      @include mixins.respond-below(lg) {
        flex-direction: column;
        align-items: flex-start;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }

      @include mixins.respond-below(sm) {
        gap: 10px;
      }

      @include mixins.respond-below(xs) {
        gap: 20px;
        grid-template-columns: 1fr;
      }

      > div {
        flex: 1;
        min-width: 125px;

        @include mixins.respond-below(sm) {
          min-width: 100px;
        }
      }

      .heading {
        text-transform: uppercase;
        color: #9ca3af;
        font-size: 0.8em;
      }

      .status {
        max-width: 120px;
        cursor: pointer;
      }

      .title {
        text-wrap: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .price {
        color: var(--tk-color-money-green);

        .priceAmount {
          font-weight: 600;
          text-wrap: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .availability {
      }
    }
  }

  .action {
    display: flex;
    grid-area: action;

    @include mixins.respond-below(lg) {
      margin-top: 10px;
    }

    .desktopAction {
      @include mixins.respond-below(lg) {
        display: none;
      }
    }

    .mobileAction {
      display: none;
      @include mixins.respond-below(lg) {
        display: block;
      }
    }
  }
}

.dragPreview {
  background-color: #fff;
  border: 1px solid #e3e3e3;
  border-radius: var(--tk-radius-sm);
  padding: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 5px;
  }

  p {
    margin: 0;
    color: #666;
  }
}

.moreProducts {
  background-color: #f0f0f0;
  border-radius: var(--tk-radius-sm);
  padding: 10px;
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  text-align: center;
}

.isDragging {
  opacity: 0.6;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
