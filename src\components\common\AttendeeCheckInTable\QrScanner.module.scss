@use "../../../styles/mixins";

@keyframes colorfulBorder {
  0% {
    border-color: #ffffff50;
  }
  50% {
    border-color: #00000050;
  }
  100% {
    border-color: #ffffff50;
  }
}

.videoContainer {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  .permissionMessage {
    position: absolute;
    width: 100vw;
    padding: 20px;
    text-align: center;
    background-color: #000000;
    color: #fff;
    z-index: 3;

    a {
      color: #dddddd;
      text-decoration: underline;
    }
  }

  .flashToggle {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 2;
  }

  .soundToggle {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 2;
  }

  .closeButton {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 2;
  }

  .switchCameraButton {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 2;
  }

  //scanner overlay is a square div that scales as the browser window scales
  .scannerOverlay {
    width: 60vw;
    height: 60vw;
    border: 5px solid #ffffff50;
    position: absolute;
    animation: colorfulBorder 10s infinite;
    border-radius: 10px;
    outline: solid 50vmax rgb(71 46 120 / 50%);
    transition: outline-color 0.2s ease-out;
    min-width: 200px;
    min-height: 200px;

    @include mixins.respond-above(md) {
      width: 40vw;
      height: 40vw;
    }
  }

  .scannerOverlay.success {
    outline: solid 50vmax rgb(80 148 80 / 75%);
  }

  .scannerOverlay.failure {
    outline: solid 50vmax rgb(193 72 72 / 75%);
  }

  .scannerOverlay.checkingIn {
    outline: solid 50vmax rgb(172 158 85 / 60%);
  }

  video {
    width: 100vw !important;
    height: 100vh !important;
    object-fit: cover;
  }
}
