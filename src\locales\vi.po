msgid ""
msgstr ""
"POT-Creation-Date: 2025-02-25 16:30+0700\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: vi\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/components/layouts/Event/index.tsx:189
msgid "- Click to Publish"
msgstr "- Nhấp để xuất bản"

#: src/components/layouts/Event/index.tsx:191
msgid "- Click to Unpublish"
msgstr "- Nhấp để gỡ bỏ"

#: src/components/common/NoResultsSplash/index.tsx:14
msgid "'There\\'s nothing to show yet'"
msgstr "'không có gì để hiển thị'"

#: src/components/modals/SendMessageModal/index.tsx:159
#~ msgid ""
#~ "\"\"Due to the high risk of spam, we require manual verification before you can send messages.\n"
#~ "\"\"Please contact us to request access."
#~ msgstr ""
#~ "Do rủi ro cao về spam, chúng tôi yêu cầu xác minh thủ công trước khi bạn có thể gửi tin nhắn.\n"
#~ "Vui lòng liên hệ với chúng tôi để yêu cầu quyền truy cập."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
#~ msgid ""
#~ "\"\"If you have an account with us, you will receive an email with instructions on how to reset your\n"
#~ "\"\"password."
#~ msgstr ""
#~ "Nếu bạn có tài khoản với chúng tôi, bạn sẽ nhận được email hướng dẫn cách đặt lại\n"
#~ "mật khẩu."

#: src/components/forms/QuestionForm/index.tsx:189
#~ msgid ""
#~ "\"\"Provide additional context or instructions for this question. Use this field to add terms\n"
#~ "\"\"and conditions, guidelines, or any important information that attendees need to know before answering."
#~ msgstr ""
#~ "Cung cấp ngữ cảnh hoặc hướng dẫn bổ sung cho câu hỏi này. Sử dụng trường này để thêm điều khoản\n"
#~ "và điều kiện, hướng dẫn hoặc bất kỳ thông tin quan trọng nào mà người tham dự cần biết trước khi trả lời."

#: src/components/common/ReportTable/index.tsx:303
#: src/locales.ts:37
msgid "{0}"
msgstr "{0}"

#: src/components/layouts/CheckIn/index.tsx:82
msgid "{0} <0>checked in</0> successfully"
msgstr "{0} <0>checked in</0> thành công"

#: src/components/layouts/CheckIn/index.tsx:106
msgid "{0} <0>checked out</0> successfully"
msgstr "{0} <0>checked out</0> thành công"

#: src/components/routes/event/Webhooks/index.tsx:24
msgid "{0} Active Webhooks"
msgstr "{0} Webhook đang hoạt động"

#: src/components/routes/product-widget/SelectProducts/index.tsx:412
msgid "{0} available"
msgstr "{0} Có sẵn"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
msgid "{0} created successfully"
msgstr "{0} đã tạo thành công"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
msgid "{0} updated successfully"
msgstr "{0} cập nhật thành công"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:57
msgid "{0}'s Events"
msgstr "{0} sự kiện của"

#: src/components/layouts/CheckIn/index.tsx:449
msgid "{0}/{1} checked in"
msgstr "{0}/{1} đã checked in"

#: src/components/common/Countdown/index.tsx:68
msgid "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{ngày} ngày, {giờ} giờ, {phút} phút và {giây} giây"

#: src/components/common/WebhookTable/index.tsx:79
msgid "{eventCount} events"
msgstr "{EventCount} Sự kiện"

#: src/components/common/Countdown/index.tsx:70
msgid "{hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{giờ} giờ, {phút} phút và {giây} giây"

#: src/components/common/Countdown/index.tsx:72
msgid "{minutes} minutes and {seconds} seconds"
msgstr "{phút} phút và {giây} giây"

#: src/components/routes/welcome/index.tsx:59
msgid "{organizerName}'s first event"
msgstr "Sự kiện đầu tiên của {organizerName}"

#: src/components/common/CapacityAssignmentList/index.tsx:47
msgid "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"
msgstr "<0>Phân bổ sức chứa cho phép bạn quản lý sức chứa trên các vé hoặc toàn bộ sự kiện. Điều này lý tưởng cho các sự kiện nhiều ngày, hội thảo và nhiều hoạt động khác, nơi việc kiểm soát số lượng người tham gia là rất quan trọng.</0><1>Ví dụ: bạn có thể liên kết một phân bổ sức chứa với vé <2>Ngày đầu tiên</2> và <3>Tất cả các ngày</3>. Khi đạt đến giới hạn, cả hai vé sẽ tự động ngừng bán.</1>"

#: src/components/common/CheckInListList/index.tsx:49
msgid "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"
msgstr "<0>Danh sách check-in giúp quản lý việc vào cửa của người tham dự sự kiện của bạn. Bạn có thể liên kết nhiều vé với danh sách check-in và đảm bảo chỉ những người có vé hợp lệ mới được vào.</0>"

#: src/components/common/WidgetEditor/index.tsx:324
msgid "<0>https://</0>your-website.com"
msgstr "<0>https://</0>your-website.com"

#: src/components/forms/ProductForm/index.tsx:287
msgid "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"
msgstr "<0> Vui lòng nhập giá không bao gồm thuế và phí. </0> <1> Thuế và phí có thể được thêm vào bên dưới. </1>"

#: src/components/forms/ProductForm/index.tsx:304
msgid "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"
msgstr "<0> Số lượng sản phẩm có sẵn cho sản phẩm này </0> <1> Giá trị này có thể được ghi đè nếu có giới hạn công suất <2>"

#: src/components/common/WebhookTable/index.tsx:205
msgid "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"
msgstr "<0>Webhooks thông báo ngay lập tức cho các dịch vụ bên ngoài khi sự kiện diễn ra, chẳng hạn như thêm người tham dự mới vào CRM hoặc danh sách email khi đăng ký, đảm bảo tự động hóa mượt mà.</0><1>Sử dụng các dịch vụ bên thứ ba như <2>Zapier</2>, <3>IFTTT</3> hoặc <4>Make</4> để tạo quy trình làm việc tùy chỉnh và tự động hóa công việc.</1>"

#: src/components/routes/event/GettingStarted/index.tsx:134
msgid "⚡️ Set up your event"
msgstr "Thiết lập sự kiện của bạn"

#: src/components/routes/event/GettingStarted/index.tsx:190
msgid "✉️ Confirm your email address"
msgstr "Xác nhận địa chỉ email của bạn"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "🎉 Congratulations on creating an event!"
#~ msgstr "🎉 Xin chúc mừng đã tạo ra một sự kiện!"

#: src/components/routes/event/GettingStarted/index.tsx:67
#~ msgid "🎟️ Add products"
#~ msgstr "🎟️Thêm sản phẩm"

#: src/components/routes/event/GettingStarted/index.tsx:119
msgid "🎟️ Add tickets"
msgstr ""

#: src/components/routes/event/GettingStarted/index.tsx:162
msgid "🎨 Customize your event page"
msgstr "🎟️ Tùy chỉnh trang sự kiện của bạn"

#: src/components/routes/event/GettingStarted/index.tsx:147
msgid "💳 Connect with Stripe"
msgstr "💳 Kết nối với Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:176
msgid "🚀 Set your event live"
msgstr "🚀 Đặt sự kiện của bạn công khai"

#: src/components/common/Countdown/index.tsx:34
msgid "0 minutes and 0 seconds"
msgstr "0 phút và 0 giây"

#: src/components/routes/event/Webhooks/index.tsx:23
msgid "1 Active Webhook"
msgstr "1 Webhook đang hoạt động"

#: src/components/modals/RefundOrderModal/index.tsx:105
msgid "10.00"
msgstr "10.00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:123
msgid "123 Main Street"
msgstr "123 Phố chính"

#: src/components/common/WidgetEditor/index.tsx:226
msgid "20"
msgstr "20"

#: src/components/routes/welcome/index.tsx:91
msgid "2024-01-01 10:00"
msgstr "2024-01-01 10:00"

#: src/components/routes/welcome/index.tsx:97
msgid "2024-01-01 18:00"
msgstr "2024-01-01 18:00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:147
msgid "94103"
msgstr "94103"

#: src/components/forms/QuestionForm/index.tsx:138
msgid "A date input. Perfect for asking for a date of birth etc."
msgstr "Trường nhập ngày. Hoàn hảo để hỏi ngày sinh, v.v."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
msgid "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."
msgstr "Một mặc định {type} là tự động được áp dụng cho tất cả các sản phẩm mới. "

#: src/components/forms/QuestionForm/index.tsx:126
msgid "A Dropdown input allows only one selection"
msgstr "Đầu vào thả xuống chỉ cho phép một lựa chọn"

#: src/components/forms/TaxAndFeeForm/index.tsx:20
msgid "A fee, like a booking fee or a service fee"
msgstr "Một khoản phí, như phí đặt phòng hoặc phí dịch vụ"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
msgid "A fixed amount per product. E.g, $0.50 per product"
msgstr "Một lượng cố định cho mỗi sản phẩm. Vd, $0.5 cho mỗi sản phẩm "

#: src/components/forms/QuestionForm/index.tsx:108
msgid "A multi line text input"
msgstr "Đầu vào văn bản nhiều dòng"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
msgid "A percentage of the product price. E.g., 3.5% of the product price"
msgstr "Một tỷ lệ phần trăm của giá sản phẩm. "

#: src/components/forms/PromoCodeForm/index.tsx:37
msgid "A promo code with no discount can be used to reveal hidden products."
msgstr "Mã khuyến mãi không có giảm giá có thể được sử dụng để tiết lộ các sản phẩm ẩn."

#: src/components/forms/QuestionForm/index.tsx:120
msgid "A Radio option has multiple options but only one can be selected."
msgstr "Tùy chọn radio có nhiều tùy chọn nhưng chỉ có thể chọn một tùy chọn."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:69
msgid "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"
msgstr "Mô tả ngắn về sự kiện sẽ được hiển thị trong kết quả tìm kiếm và khi chia sẻ trên mạng xã hội. Theo mặc định, mô tả sự kiện sẽ được sử dụng."

#: src/components/forms/QuestionForm/index.tsx:102
msgid "A single line text input"
msgstr "Một đầu vào văn bản dòng duy nhất"

#: src/components/forms/QuestionForm/index.tsx:87
msgid "A single question per order. E.g, What is your shipping address?"
msgstr "Một câu hỏi duy nhất cho mỗi đơn hàng. Ví dụ: Địa chỉ giao hàng của bạn là gì?"

#: src/components/forms/QuestionForm/index.tsx:93
msgid "A single question per product. E.g, What is your t-shirt size?"
msgstr "Một câu hỏi duy nhất cho mỗi sản phẩm. Ví dụ: Kích thước áo thun của bạn là gì?"

#: src/components/forms/TaxAndFeeForm/index.tsx:14
msgid "A standard tax, like VAT or GST"
msgstr "Thuế tiêu chuẩn, như VAT hoặc GST"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:76
msgid "About"
msgstr "Thông tin sự kiện"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:211
msgid "About Stripe Connect"
msgstr "Giới thiệu về Stripe Connect"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:91
msgid "Accept bank transfers, checks, or other offline payment methods"
msgstr "Chấp nhận chuyển khoản ngân hàng, séc hoặc các phương thức thanh toán offline khác"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:86
msgid "Accept credit card payments with Stripe"
msgstr "Chấp nhận thanh toán thẻ tín dụng với Stripe"

#: src/components/routes/auth/AcceptInvitation/index.tsx:126
msgid "Accept Invitation"
msgstr "Chấp nhận lời mời"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:125
msgid "Access Denied"
msgstr "Truy cập bị từ chối"

#: src/components/routes/account/ManageAccount/index.tsx:24
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:53
msgid "Account"
msgstr "Tài khoản"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:62
msgid "Account Name"
msgstr "Tên tài khoản"

#: src/components/common/GlobalMenu/index.tsx:32
#: src/components/routes/account/ManageAccount/index.tsx:19
msgid "Account Settings"
msgstr "Cài đặt tài khoản"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:42
msgid "Account updated successfully"
msgstr "Tài khoản được cập nhật thành công"

#: src/components/common/AttendeeTable/index.tsx:158
#: src/components/common/ProductsTable/SortableProduct/index.tsx:267
#: src/components/common/QuestionsTable/index.tsx:108
msgid "Actions"
msgstr "Hành động"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "Activate"
msgstr "Kích hoạt"

#: src/components/forms/CheckInListForm/index.tsx:47
msgid "Activation date"
msgstr "Ngày kích hoạt"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:19
#: src/components/modals/EditUserModal/index.tsx:110
msgid "Active"
msgstr "Hoạt động"

#: src/components/forms/CheckInListForm/index.tsx:39
msgid "Add a description for this check-in list"
msgstr "Thêm mô tả cho danh sách check-in này"

#: src/components/modals/ManageAttendeeModal/index.tsx:131
msgid "Add any notes about the attendee. These will not be visible to the attendee."
msgstr "Thêm bất kỳ ghi chú nào về người tham dự. Những ghi chú này sẽ không hiển thị cho người tham dự."

#: src/components/modals/ManageAttendeeModal/index.tsx:133
msgid "Add any notes about the attendee..."
msgstr "Thêm bất kỳ ghi chú nào về người tham dự ..."

#: src/components/modals/ManageOrderModal/index.tsx:165
msgid "Add any notes about the order. These will not be visible to the customer."
msgstr "Thêm bất kỳ ghi chú nào về đơn hàng. Những ghi chú này sẽ không hiển thị cho khách hàng."

#: src/components/modals/ManageOrderModal/index.tsx:169
msgid "Add any notes about the order..."
msgstr "Thêm bất kỳ ghi chú nào về đơn hàng ..."

#: src/components/forms/QuestionForm/index.tsx:202
msgid "Add description"
msgstr "Thêm mô tả"

#: src/components/routes/event/GettingStarted/index.tsx:85
#~ msgid "Add event details and and manage event settings."
#~ msgstr "Thêm chi tiết sự kiện và quản lý cài đặt sự kiện."

#: src/components/routes/event/GettingStarted/index.tsx:137
msgid "Add event details and manage event settings."
msgstr "Thêm chi tiết sự kiện và quản lý cài đặt sự kiện."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:153
msgid "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"
msgstr "Thêm hướng dẫn thanh toán offline (ví dụ: chi tiết chuyển khoản ngân hàng, nơi gửi séc, thời hạn thanh toán)"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add More products"
#~ msgstr "Thêm nhiều sản phẩm"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add More tickets"
msgstr ""

#: src/components/common/HeadingCard/index.tsx:27
msgid "Add New"
msgstr "Thêm mới"

#: src/components/forms/QuestionForm/index.tsx:68
msgid "Add Option"
msgstr "Thêm tùy chọn"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:68
msgid "Add Product"
msgstr "Thêm sản phẩm"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:51
msgid "Add Product to Category"
msgstr "Thêm sản phẩm vào danh mục"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add products"
#~ msgstr "Thêm sản phẩm"

#: src/components/common/QuestionsTable/index.tsx:281
msgid "Add question"
msgstr "Thêm câu hỏi"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:18
msgid "Add Tax or Fee"
msgstr "Thêm thuế hoặc phí"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add tickets"
msgstr ""

#: src/components/forms/ProductForm/index.tsx:337
msgid "Add tier"
msgstr "Thêm tầng"

#: src/components/common/AddEventToCalendarButton/index.tsx:112
#: src/components/common/AddEventToCalendarButton/index.tsx:120
msgid "Add to Calendar"
msgstr "Thêm vào lịch"

#: src/components/common/WebhookTable/index.tsx:223
#: src/components/routes/event/Webhooks/index.tsx:35
msgid "Add Webhook"
msgstr "Thêm Webhook"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:219
msgid "Additional Information"
msgstr "Thông tin bổ sung"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/forms/ProductForm/index.tsx:348
msgid "Additional Options"
msgstr "Tùy chọn bổ sung"

#: src/components/common/OrderDetails/index.tsx:96
#: src/components/forms/QuestionForm/index.tsx:130
msgid "Address"
msgstr "Địa chỉ"

#: src/components/common/CheckoutQuestion/index.tsx:156
msgid "Address line 1"
msgstr "Dòng địa chỉ 1"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:122
#: src/components/routes/product-widget/CollectInformation/index.tsx:319
#: src/components/routes/product-widget/CollectInformation/index.tsx:320
msgid "Address Line 1"
msgstr "Dòng địa chỉ 1"

#: src/components/common/CheckoutQuestion/index.tsx:159
msgid "Address line 2"
msgstr "Dòng địa chỉ 2"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:127
#: src/components/routes/product-widget/CollectInformation/index.tsx:324
#: src/components/routes/product-widget/CollectInformation/index.tsx:325
msgid "Address Line 2"
msgstr "Dòng địa chỉ 2"

#: src/components/modals/EditUserModal/index.tsx:49
#: src/components/modals/InviteUserModal/index.tsx:41
msgid "Admin"
msgstr "Quản trị viên"

#: src/components/modals/EditUserModal/index.tsx:51
#: src/components/modals/InviteUserModal/index.tsx:43
msgid "Admin users have full access to events and account settings."
msgstr "Người dùng quản trị có quyền truy cập đầy đủ vào các sự kiện và cài đặt tài khoản."

#: src/components/common/MessageList/index.tsx:21
msgid "All attendees"
msgstr "Tất cả người tham dự"

#: src/components/modals/SendMessageModal/index.tsx:187
msgid "All attendees of this event"
msgstr "Tất cả những người tham dự sự kiện này"

#: src/components/common/PromoCodeTable/index.tsx:131
msgid "All Products"
msgstr "Tất cả các sản phẩm"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:157
msgid "Allow attendees associated with unpaid orders to check in"
msgstr "Cho phép người tham dự liên kết với đơn hàng chưa thanh toán được check-in"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:82
msgid "Allow search engine indexing"
msgstr "Cho phép công cụ tìm kiếm lập chỉ mục"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:81
msgid "Allow search engines to index this event"
msgstr "Cho phép các công cụ tìm kiếm lập chỉ mục cho sự kiện này"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:78
msgid "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."
msgstr "Sắp xong rồi! Chúng tôi chỉ đang đợi xử lý thanh toán của bạn. Điều này chỉ tốn vài giây.. "

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:77
msgid "Amazing, Event, Keywords..."
msgstr "Tuyệt vời, sự kiện, từ khóa ..."

#: src/components/common/OrdersTable/index.tsx:207
#: src/components/forms/TaxAndFeeForm/index.tsx:71
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:36
msgid "Amount"
msgstr "Số tiền"

#: src/components/modals/CreateAttendeeModal/index.tsx:165
msgid "Amount paid ({0})"
msgstr "Số tiền đã trả ({0})"

#: src/mutations/useExportAnswers.ts:45
msgid "An error occurred while checking export status."
msgstr "Đã xảy ra lỗi khi kiểm tra trạng thái xuất."

#: src/components/common/ErrorDisplay/index.tsx:18
msgid "An error occurred while loading the page"
msgstr "Đã xảy ra lỗi trong khi tải trang"

#: src/components/common/QuestionsTable/index.tsx:148
msgid "An error occurred while sorting the questions. Please try again or refresh the page"
msgstr "Vui lòng thử lại hoặc tải lại trang này"

#: src/components/routes/welcome/index.tsx:75
#~ msgid "An event is the actual event you are hosting. You can add more details later."
#~ msgstr "Một sự kiện là sự kiện thực tế bạn đang tổ chức. "

#: src/components/routes/welcome/index.tsx:75
msgid "An event is the gathering or occasion you’re organizing. You can add more details later."
msgstr "Sự kiện là buổi tụ họp hoặc dịp mà bạn đang tổ chức. Bạn có thể thêm thông tin chi tiết sau."

#: src/components/routes/welcome/index.tsx:23
msgid "An organizer is the company or person who is hosting the event"
msgstr "Một người tổ chức là công ty hoặc người đang tổ chức sự kiện"

#: src/components/forms/StripeCheckoutForm/index.tsx:40
msgid "An unexpected error occurred."
msgstr "Đã xảy ra lỗi không mong muốn."

#: src/hooks/useFormErrorResponseHandler.tsx:48
msgid "An unexpected error occurred. Please try again."
msgstr "Đã xảy ra lỗi không mong muốn. Vui lòng thử lại."

#: src/components/common/QuestionAndAnswerList/index.tsx:97
msgid "Answer updated successfully."
msgstr "Câu trả lời đã được cập nhật thành công."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
msgid "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
msgstr "Mọi thắc mắc từ chủ sở hữu sản phẩm sẽ được gửi đến địa chỉ email này. Địa chỉ này cũng sẽ được sử dụng làm địa chỉ \"trả lời\" cho tất cả email gửi từ sự kiện này."

#: src/components/common/WidgetEditor/index.tsx:213
msgid "Appearance"
msgstr "Giao diện"

#: src/components/routes/product-widget/SelectProducts/index.tsx:500
msgid "applied"
msgstr "đã được áp dụng"

#: src/components/common/CapacityAssignmentList/index.tsx:97
msgid "Applies to {0} products"
msgstr "Áp dụng cho các sản phẩm {0}"

#: src/components/common/CapacityAssignmentList/index.tsx:98
msgid "Applies to 1 product"
msgstr "Áp dụng cho 1 sản phẩm"

#: src/components/common/FilterModal/index.tsx:253
msgid "Apply"
msgstr "Áp dụng"

#: src/components/routes/product-widget/SelectProducts/index.tsx:528
msgid "Apply Promo Code"
msgstr "Áp dụng mã khuyến mãi"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
msgid "Apply this {type} to all new products"
msgstr "Áp dụng {type} này cho tất cả các sản phẩm mới"

#: src/components/common/EventCard/index.tsx:167
msgid "Archive event"
msgstr "Lưu trữ sự kiện"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:32
msgid "Archived"
msgstr "Lưu trữ"

#: src/components/routes/events/Dashboard/index.tsx:59
msgid "Archived Events"
msgstr "Các sự kiện lưu trữ"

#: src/components/common/AttendeeTable/index.tsx:76
msgid "Are you sure you want to activate this attendee?"
msgstr "Bạn có chắc mình muốn kích hoạt người tham dự này không?"

#: src/components/common/EventCard/index.tsx:58
msgid "Are you sure you want to archive this event?"
msgstr "Bạn có chắc mình muốn lưu trữ sự kiện này không?"

#: src/components/common/AttendeeTable/index.tsx:77
msgid "Are you sure you want to cancel this attendee? This will void their ticket"
msgstr "Bạn có chắc mình muốn hủy người tham dự này không? Điều này sẽ làm mất hiệu lực vé của họ"

#: src/components/common/PromoCodeTable/index.tsx:37
msgid "Are you sure you want to delete this promo code?"
msgstr "Bạn có chắc là bạn muốn xóa mã khuyến mãi này không?"

#: src/components/common/QuestionsTable/index.tsx:164
msgid "Are you sure you want to delete this question?"
msgstr "Bạn có chắc là bạn muốn xóa câu hỏi này không?"

#: src/components/common/WebhookTable/index.tsx:122
msgid "Are you sure you want to delete this webhook?"
msgstr "Bạn có chắc là bạn muốn xóa webhook này không?"

#: src/components/layouts/Event/index.tsx:68
#: src/components/routes/event/EventDashboard/index.tsx:64
msgid "Are you sure you want to make this event draft? This will make the event invisible to the public"
msgstr "Bạn có chắc chắn muốn chuyển sự kiện này thành bản nháp không? Điều này sẽ làm cho sự kiện không hiển thị với công chúng."

#: src/components/layouts/Event/index.tsx:69
#: src/components/routes/event/EventDashboard/index.tsx:65
msgid "Are you sure you want to make this event public? This will make the event visible to the public"
msgstr "Bạn có chắc mình muốn công khai sự kiện này không? "

#: src/components/common/EventCard/index.tsx:59
msgid "Are you sure you want to restore this event? It will be restored as a draft event."
msgstr "Bạn có chắc chắn muốn khôi phục sự kiện này không? Sự kiện sẽ được khôi phục dưới dạng bản nháp."

#: src/components/common/CapacityAssignmentList/index.tsx:163
msgid "Are you sure you would like to delete this Capacity Assignment?"
msgstr "Bạn có chắc chắn muốn xóa phân bổ sức chứa này không?"

#: src/components/common/CheckInListList/index.tsx:183
msgid "Are you sure you would like to delete this Check-In List?"
msgstr "Bạn có chắc là bạn muốn xóa danh sách tham dự này không?"

#: src/components/forms/QuestionForm/index.tsx:85
msgid "Ask once per order"
msgstr "Hỏi một lần cho mỗi đơn hàng"

#: src/components/forms/QuestionForm/index.tsx:91
msgid "Ask once per product"
msgstr "Hỏi một lần cho mỗi sản phẩm"

#: src/components/modals/CreateWebhookModal/index.tsx:33
msgid "At least one event type must be selected"
msgstr "Ít nhất một loại sự kiện phải được chọn"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Attendee"
msgstr "Người tham dự"

#: src/components/forms/WebhookForm/index.tsx:94
msgid "Attendee Cancelled"
msgstr "Người tham dự đã hủy bỏ"

#: src/components/forms/WebhookForm/index.tsx:82
msgid "Attendee Created"
msgstr "Người tham dự đã tạo ra"

#: src/components/modals/ManageAttendeeModal/index.tsx:147
msgid "Attendee Details"
msgstr "Chi tiết người tham dự"

#: src/components/layouts/AuthLayout/index.tsx:73
msgid "Attendee Management"
msgstr "Quản lý người tham dự"

#: src/components/layouts/CheckIn/index.tsx:150
msgid "Attendee not found"
msgstr "Không tìm thấy người tham dự"

#: src/components/modals/ManageAttendeeModal/index.tsx:153
msgid "Attendee Notes"
msgstr "Ghi chú của người tham dự"

#: src/components/common/QuestionsTable/index.tsx:369
msgid "Attendee questions"
msgstr "Câu hỏi của người tham dự"

#: src/components/modals/ManageAttendeeModal/index.tsx:172
msgid "Attendee Ticket"
msgstr "Vé tham dự"

#: src/components/forms/WebhookForm/index.tsx:88
msgid "Attendee Updated"
msgstr "Người tham dự cập nhật"

#: src/components/common/OrdersTable/index.tsx:206
#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
#: src/components/common/StatBoxes/index.tsx:27
#: src/components/layouts/Event/index.tsx:99
#: src/components/modals/ManageOrderModal/index.tsx:126
#: src/components/routes/event/attendees.tsx:59
msgid "Attendees"
msgstr "Người tham dự"

#: src/components/routes/event/attendees.tsx:43
msgid "Attendees Exported"
msgstr "Danh sách người tham dự đã được xuất"

#: src/components/routes/event/EventDashboard/index.tsx:239
msgid "Attendees Registered"
msgstr "Người tham dự đã đăng ký"

#: src/components/modals/SendMessageModal/index.tsx:146
#~ msgid "Attendees with a specific product"
#~ msgstr "Người tham dự với một sản phẩm cụ thể"

#: src/components/modals/SendMessageModal/index.tsx:183
msgid "Attendees with a specific ticket"
msgstr "Người tham dự có vé cụ thể"

#: src/components/common/WidgetEditor/index.tsx:233
msgid "Auto Resize"
msgstr "Tự động thay đổi kích thước"

#: src/components/layouts/AuthLayout/index.tsx:88
#~ msgid "Auto Workflow"
#~ msgstr "Quy trình tự động"

#: src/components/layouts/AuthLayout/index.tsx:79
msgid "Automated entry management with multiple check-in lists and real-time validation"
msgstr "Quản lý vào cổng tự động với nhiều danh sách check-in và xác thực theo thời gian thực"

#: src/components/common/WidgetEditor/index.tsx:236
msgid "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."
msgstr "Tự động điều chỉnh chiều cao của widget dựa trên nội dung. Khi tắt, widget sẽ lấp đầy chiều cao của vùng chứa."

#: src/components/common/OrderStatusBadge/index.tsx:15
#: src/components/modals/SendMessageModal/index.tsx:231
msgid "Awaiting offline payment"
msgstr "Đang chờ thanh toán offline"

#: src/components/routes/event/orders.tsx:25
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:43
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:66
msgid "Awaiting Offline Payment"
msgstr "Đang chờ thanh toán offline"

#: src/components/layouts/CheckIn/index.tsx:263
msgid "Awaiting payment"
msgstr "đang chờ thanh toán"

#: src/components/common/AttendeeTicket/index.tsx:67
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:40
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:65
msgid "Awaiting Payment"
msgstr "Đang chờ thanh toán"

#: src/components/routes/welcome/index.tsx:84
msgid "Awesome Event"
msgstr "Sự kiện tuyệt vời"

#: src/components/forms/OrganizerForm/index.tsx:28
msgid "Awesome Organizer Ltd."
msgstr "Nhà tổ chức tuyệt vời Ltd."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:42
msgid "Back to all events"
msgstr "Quay lại tất cả các sự kiện"

#: src/components/layouts/Checkout/index.tsx:73
#: src/components/routes/product-widget/CollectInformation/index.tsx:249
#: src/components/routes/product-widget/CollectInformation/index.tsx:261
msgid "Back to event page"
msgstr "Trở lại trang sự kiện"

#: src/components/routes/auth/ForgotPassword/index.tsx:59
#: src/components/routes/auth/ResetPassword/index.tsx:66
msgid "Back to login"
msgstr "Quay lại đăng nhập"

#: src/components/common/WidgetEditor/index.tsx:175
msgid "Background Color"
msgstr "Màu nền"

#: src/components/routes/event/HomepageDesigner/index.tsx:144
msgid "Background Type"
msgstr "Loại nền"

#: src/components/modals/SendMessageModal/index.tsx:278
msgid "Before you send!"
msgstr "Trước khi bạn gửi!"

#: src/components/routes/event/GettingStarted/index.tsx:60
#~ msgid "Before your event can go live, there are a few things you need to do."
#~ msgstr "Trước khi sự kiện của bạn có thể phát hành, có một vài điều bạn cần làm."

#: src/components/routes/event/GettingStarted/index.tsx:92
msgid "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."
msgstr "Trước khi sự kiện của bạn có thể phát trực tiếp, bạn cần thực hiện một vài việc. Hoàn thành tất cả các bước dưới đây để bắt đầu."

#: src/components/routes/product-widget/CollectInformation/index.tsx:313
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:142
msgid "Billing Address"
msgstr "Địa chỉ thanh toán"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:167
msgid "Billing Settings"
msgstr "Cài đặt thanh toán"

#: src/components/layouts/AuthLayout/index.tsx:83
#~ msgid "Brand Control"
#~ msgstr "Kiểm soát thương hiệu"

#: src/components/common/LanguageSwitcher/index.tsx:28
msgid "Brazilian Portuguese"
msgstr "Tiếng Bồ Đào Nha Brazil"

#: src/components/routes/auth/Register/index.tsx:133
msgid "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."
msgstr "Bằng cách đăng ký, bạn đồng ý với các <0>Điều khoản dịch vụ của chúng tôi</0> và <1>Chính sách bảo mật</1>."

#: src/components/forms/TaxAndFeeForm/index.tsx:52
msgid "Calculation Type"
msgstr "Loại tính toán"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:140
msgid "California"
msgstr "California"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:175
msgid "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."
msgstr "Quyền sử dụng máy ảnh đã bị từ chối. Hãy <0>Yêu cầu cấp quyền</0> lần nữa hoặc nếu không được, bạn sẽ cần <1>cấp cho trang này</1> quyền truy cập vào máy ảnh trong cài đặt trình duyệt của bạn."

#: src/components/common/AttendeeTable/index.tsx:182
#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/QuestionAndAnswerList/index.tsx:149
#: src/components/layouts/CheckIn/index.tsx:225
#: src/utilites/confirmationDialog.tsx:11
msgid "Cancel"
msgstr "Hủy"

#: src/components/routes/profile/ManageProfile/index.tsx:126
msgid "Cancel email change"
msgstr "Hủy thay đổi email"

#: src/components/common/OrdersTable/index.tsx:190
#: src/components/modals/RefundOrderModal/index.tsx:112
msgid "Cancel order"
msgstr "Hủy đơn hàng"

#: src/components/modals/CancelOrderModal/index.tsx:59
msgid "Cancel Order"
msgstr "Hủy đơn hàng"

#: src/components/modals/CancelOrderModal/index.tsx:44
msgid "Cancel Order {0}"
msgstr "Hủy đơn hàng {0}"

#: src/components/modals/CancelOrderModal/index.tsx:54
msgid "Canceling will cancel all products associated with this order, and release the products back into the available pool."
msgstr "Hủy đơn hàng sẽ hủy tất cả các sản phẩm liên quan và đưa chúng trở lại kho hàng có sẵn."

#: src/components/common/AttendeeTicket/index.tsx:61
#: src/components/common/OrderStatusBadge/index.tsx:12
#: src/components/routes/event/orders.tsx:24
msgid "Cancelled"
msgstr "Hủy bỏ"

#: src/components/layouts/CheckIn/index.tsx:173
msgid "Cannot Check In"
msgstr "Không thể xác nhận tham dự"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:42
#: src/components/layouts/Event/index.tsx:103
msgid "Capacity"
msgstr "Công suất"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:36
msgid "Capacity Assignment created successfully"
msgstr "Phân bổ sức chứa được tạo thành công"

#: src/components/common/CapacityAssignmentList/index.tsx:31
msgid "Capacity Assignment deleted successfully"
msgstr "Phân bổ sức chứa đã được xóa thành công"

#: src/components/routes/event/CapacityAssignments/index.tsx:32
msgid "Capacity Management"
msgstr "Quản lý sức chứa"

#: src/components/modals/CreateProductCategoryModal/index.tsx:56
msgid "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."
msgstr "Danh mục giúp bạn nhóm các sản phẩm lại với nhau. Ví dụ, bạn có thể có một danh mục cho \"Vé\" và một danh mục khác cho \"Hàng hóa\"."

#: src/components/forms/ProductForm/index.tsx:267
msgid "Categories help you organize your products. This title will be displayed on the public event page."
msgstr "Danh mục giúp bạn sắp xếp sản phẩm của mình. Tiêu đề này sẽ được hiển thị trên trang sự kiện công khai."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:91
msgid "Categories reordered successfully."
msgstr "Danh mục đã được sắp xếp lại thành công."

#: src/components/routes/event/products.tsx:96
msgid "Category"
msgstr "Danh mục"

#: src/components/modals/CreateProductCategoryModal/index.tsx:37
msgid "Category Created Successfully"
msgstr "Danh mục được tạo thành công"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Change Cover"
msgstr "Thay đổi bìa"

#: src/components/routes/profile/ManageProfile/index.tsx:201
msgid "Change password"
msgstr "Thay đổi mật khẩu"

#: src/components/layouts/CheckIn/index.tsx:180
msgid "Check In"
msgstr "Check-in"

#: src/components/layouts/CheckIn/index.tsx:193
msgid "Check in {0} {1}"
msgstr "Check-in {0} {1}"

#: src/components/layouts/CheckIn/index.tsx:218
msgid "Check in and mark order as paid"
msgstr "Check-in và đánh dấu đơn hàng đã thanh toán"

#: src/components/layouts/CheckIn/index.tsx:209
msgid "Check in only"
msgstr "Chỉ check-in"

#: src/components/layouts/CheckIn/index.tsx:177
msgid "Check Out"
msgstr "Check-out"

#: src/components/layouts/Checkout/index.tsx:106
msgid "Check out this event!"
msgstr "Xác nhận rời khỏi sự kiện này!"

#: src/components/common/EventCard/index.tsx:156
msgid "Check-in"
msgstr "Check-in"

#: src/components/forms/WebhookForm/index.tsx:100
msgid "Check-in Created"
msgstr "Check-in đã được tạo"

#: src/components/forms/WebhookForm/index.tsx:106
msgid "Check-in Deleted"
msgstr "Check-in đã bị xóa"

#: src/components/modals/CreateCheckInListModal/index.tsx:40
msgid "Check-In List created successfully"
msgstr "Danh sách check-in được tạo thành công"

#: src/components/common/CheckInListList/index.tsx:33
msgid "Check-In List deleted successfully"
msgstr "Danh sách check-in đã bị xóa thành công"

#: src/components/layouts/CheckIn/index.tsx:326
msgid "Check-in list has expired"
msgstr "Danh sách check-in đã hết hạn"

#: src/components/layouts/CheckIn/index.tsx:343
msgid "Check-in list is not active"
msgstr "Danh sách check-in không hoạt động"

#: src/components/layouts/CheckIn/index.tsx:311
msgid "Check-in list not found"
msgstr "Danh sách Check-In không tồn tại"

#: src/components/layouts/Event/index.tsx:104
#: src/components/routes/event/CheckInLists/index.tsx:32
msgid "Check-In Lists"
msgstr "Danh sách Check-In"

#: src/components/common/CheckInListList/index.tsx:162
msgid "Check-In URL copied to clipboard"
msgstr "URL check-in đã được sao chép vào clipboard"

#: src/components/forms/QuestionForm/index.tsx:114
msgid "Checkbox options allow multiple selections"
msgstr "Tùy chọn checkbox cho phép chọn nhiều mục"

#: src/components/forms/QuestionForm/index.tsx:112
msgid "Checkboxes"
msgstr "Checkbox"

#: src/components/common/AttendeeDetails/index.tsx:29
msgid "Checked In"
msgstr "Đã xác nhận tham dự"

#: src/components/layouts/Checkout/index.tsx:83
#: src/components/routes/event/Settings/index.tsx:40
msgid "Checkout"
msgstr "Thanh toán"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:61
msgid "Checkout Settings"
msgstr "Cài đặt thanh toán"

#: src/components/common/LanguageSwitcher/index.tsx:30
msgid "Chinese (Simplified)"
msgstr "Trung Quốc (đơn giản hóa)"

#: src/components/common/LanguageSwitcher/index.tsx:32
msgid "Chinese (Traditional)"
msgstr "Tiếng Trung (Phồn thể)"

#: src/components/routes/event/HomepageDesigner/index.tsx:133
msgid "Choose a color for your background"
msgstr "Chọn một màu cho nền của bạn"

#: src/components/modals/ChooseAccountModal/index.tsx:13
msgid "Choose an account"
msgstr "Chọn một tài khoản"

#: src/components/common/CheckoutQuestion/index.tsx:162
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:134
#: src/components/routes/product-widget/CollectInformation/index.tsx:333
#: src/components/routes/product-widget/CollectInformation/index.tsx:334
msgid "City"
msgstr "Thành phố"

#: src/components/common/SearchBar/index.tsx:75
msgid "Clear Search Text"
msgstr "Xoá văn bản tìm kiếm"

#: src/components/routes/product-widget/SelectProducts/index.tsx:288
#~ msgid "click here"
#~ msgstr "Bấm vào đây"

#: src/components/common/PromoCodeTable/index.tsx:98
msgid "Click to copy"
msgstr "Bấm để sao chép"

#: src/components/routes/product-widget/SelectProducts/index.tsx:533
msgid "close"
msgstr "đóng"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:186
#: src/components/modals/RefundOrderModal/index.tsx:125
#: src/components/routes/product-widget/SelectProducts/index.tsx:534
msgid "Close"
msgstr "Đóng"

#: src/components/layouts/Event/index.tsx:281
msgid "Close sidebar"
msgstr "Đóng thanh bên"

#: src/components/common/PromoCodeTable/index.tsx:69
#: src/components/forms/PromoCodeForm/index.tsx:34
msgid "Code"
msgstr "Mã"

#: src/components/modals/CreatePromoCodeModal/index.tsx:26
#: src/components/modals/EditPromoCodeModal/index.tsx:38
msgid "Code must be between 3 and 50 characters long"
msgstr "Mã phải dài từ 3 đến 50 ký tự"

#: src/components/forms/ProductForm/index.tsx:401
msgid "Collapse this product when the event page is initially loaded"
msgstr "Thu gọn sản phẩm này khi trang sự kiện ban đầu được tải"

#: src/components/routes/event/HomepageDesigner/index.tsx:131
msgid "Color"
msgstr "Màu sắc"

#: src/components/common/WidgetEditor/index.tsx:21
msgid "Color must be a valid hex color code. Example: #ffffff"
msgstr "Màu sắc phải là mã màu hex hợp lệ. Ví dụ: #ffffff"

#: src/components/common/WidgetEditor/index.tsx:166
#: src/components/routes/event/HomepageDesigner/index.tsx:124
msgid "Colors"
msgstr "Màu sắc"

#: src/components/layouts/Event/index.tsx:158
msgid "Coming Soon"
msgstr "Sắp ra mắt"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
#~ msgid "Comma separated keywords that describe the event. These will be used by search engines to help categorize and index the event"
#~ msgstr "Các từ khóa mô tả sự kiện, cách nhau bằng dấu phẩy. Chúng sẽ được công cụ tìm kiếm sử dụng để phân loại và lập chỉ mục sự kiện"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
msgid "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"
msgstr "Các từ khóa mô tả sự kiện, được phân tách bằng dấu phẩy. Chúng sẽ được công cụ tìm kiếm sử dụng để phân loại và lập chỉ mục sự kiện."

#: src/components/routes/product-widget/CollectInformation/index.tsx:453
msgid "Complete Order"
msgstr "Hoàn tất đơn hàng"

#: src/components/routes/product-widget/CollectInformation/index.tsx:223
msgid "Complete payment"
msgstr "Hoàn tất thanh toán"

#: src/components/routes/product-widget/Payment/index.tsx:131
msgid "Complete Payment"
msgstr "Hoàn tất thanh toán"

#: src/components/layouts/AuthLayout/index.tsx:68
#~ msgid "Complete Store"
#~ msgstr "Cửa hàng hoàn chỉnh"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:202
#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Complete Stripe Setup"
msgstr "Hoàn tất thiết lập Stripe"

#: src/components/routes/event/EventDashboard/index.tsx:127
msgid "Complete these steps to start selling tickets for your event."
msgstr "Hoàn thành các bước này để bắt đầu bán vé cho sự kiện của bạn."

#: src/components/modals/SendMessageModal/index.tsx:230
#: src/components/routes/event/GettingStarted/index.tsx:70
#: src/components/routes/event/orders.tsx:23
msgid "Completed"
msgstr "Hoàn thành"

#: src/components/common/StatBoxes/index.tsx:51
msgid "Completed orders"
msgstr "Đơn hàng đã hoàn thành"

#: src/components/routes/event/EventDashboard/index.tsx:237
msgid "Completed Orders"
msgstr "Đơn hàng đã hoàn thành"

#: src/components/common/WidgetEditor/index.tsx:287
msgid "Component Code"
msgstr "Mã thành phần"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:35
msgid "Configured Discount"
msgstr "Giảm giá đã cấu hình"

#: src/utilites/confirmationDialog.tsx:11
msgid "Confirm"
msgstr "Xác nhận"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:51
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:56
msgid "Confirm Email Change"
msgstr "Xác nhận thay đổi email"

#: src/components/routes/profile/ManageProfile/index.tsx:199
msgid "Confirm New Password"
msgstr "Xác nhận mật khẩu mới"

#: src/components/routes/auth/Register/index.tsx:115
msgid "Confirm password"
msgstr "Xác nhận mật khẩu"

#: src/components/routes/auth/AcceptInvitation/index.tsx:112
#: src/components/routes/auth/Register/index.tsx:114
#: src/components/routes/auth/ResetPassword/index.tsx:60
msgid "Confirm Password"
msgstr "Xác nhận mật khẩu"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:36
msgid "Confirming email address..."
msgstr "Đang xác nhận địa chỉ email..."

#: src/components/routes/event/GettingStarted/index.tsx:88
msgid "Congratulations on creating an event!"
msgstr "Chúc mừng bạn đã tạo sự kiện thành công!"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:173
msgid "Connect Documentation"
msgstr "Tài liệu kết nối"

#: src/components/routes/event/EventDashboard/index.tsx:192
msgid "Connect payment processing"
msgstr "Kết nối xử lý thanh toán"

#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Connect to Stripe"
msgstr "Kết nối với Stripe"

#: src/components/layouts/AuthLayout/index.tsx:89
msgid "Connect with CRM and automate tasks using webhooks and integrations"
msgstr "Kết nối với CRM và tự động hóa các tác vụ bằng webhooks và tích hợp"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:201
#: src/components/routes/event/GettingStarted/index.tsx:154
msgid "Connect with Stripe"
msgstr "Kết nối với Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:150
msgid "Connect your Stripe account to start receiving payments."
msgstr "Kết nối tài khoản Stripe của bạn để bắt đầu nhận thanh toán."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:148
msgid "Connected to Stripe"
msgstr "Đã kết nối với Stripe"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:98
msgid "Connection Details"
msgstr "Chi tiết kết nối"

#: src/components/modals/SendMessageModal/index.tsx:167
msgid "Contact Support"
msgstr "Liên hệ hỗ trợ"

#: src/components/modals/SendMessageModal/index.tsx:158
msgid "Contact us to enable messaging"
msgstr "Liên hệ với chúng tôi để bật tính năng nhắn tin"

#: src/components/routes/event/HomepageDesigner/index.tsx:155
msgid "Content background color"
msgstr "Màu nền nội dung"

#: src/components/common/WidgetEditor/index.tsx:31
#: src/components/common/WidgetEditor/index.tsx:217
#: src/components/layouts/Checkout/CheckoutFooter/index.tsx:38
#: src/components/routes/product-widget/CollectInformation/index.tsx:444
#: src/components/routes/product-widget/SelectProducts/index.tsx:486
msgid "Continue"
msgstr "Tiếp tục"

#: src/components/routes/event/HomepageDesigner/index.tsx:164
msgid "Continue button text"
msgstr "Văn bản nút tiếp tục"

#: src/components/common/WidgetEditor/index.tsx:216
msgid "Continue Button Text"
msgstr "Văn bản nút tiếp tục"

#: src/components/modals/CreateEventModal/index.tsx:153
msgid "Continue Event Setup"
msgstr "Tiếp tục thiết lập sự kiện"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Continue set up"
msgstr "Tiếp tục thiết lập"

#: src/components/routes/product-widget/SelectProducts/index.tsx:337
msgid "Continue to Checkout"
msgstr "Tiếp tục đến thanh toán"

#: src/components/common/AttendeeTicket/index.tsx:91
#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:243
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copied"
msgstr "Đã Sao chép"

#: src/components/common/PromoCodeTable/index.tsx:106
msgid "copied to clipboard"
msgstr "Sao chép vào bộ nhớ tạm"

#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copy"
msgstr "Sao chép"

#: src/components/common/CheckInListList/index.tsx:156
msgid "Copy Check-In URL"
msgstr "Sao chép URL check-in"

#: src/components/routes/product-widget/CollectInformation/index.tsx:306
msgid "Copy details to all attendees"
msgstr "Sử dụng thông tin này cho tất cả người tham dự"

#: src/components/common/AttendeeTicket/index.tsx:91
msgid "Copy Link"
msgstr "Sao chép Link"

#: src/components/common/PromoCodeTable/index.tsx:177
#: src/components/modals/ShareModal/index.tsx:243
msgid "Copy URL"
msgstr "Sao chép URL"

#: src/components/common/CheckoutQuestion/index.tsx:174
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:152
#: src/components/routes/product-widget/CollectInformation/index.tsx:354
msgid "Country"
msgstr "Quốc gia"

#: src/components/routes/event/HomepageDesigner/index.tsx:117
msgid "Cover"
msgstr "Bìa"

#: src/components/routes/event/attendees.tsx:71
#: src/components/routes/event/products.tsx:74
msgid "Create"
msgstr "Tạo"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
msgid "Create {0}"
msgstr "Tạo {0}"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:59
msgid "Create a Product"
msgstr "Tạo một sản phẩm"

#: src/components/common/PromoCodeTable/index.tsx:57
msgid "Create a Promo Code"
msgstr "Tạo mã khuyến mãi"

#: src/components/modals/CreateCheckInListModal/index.tsx:63
msgid "Create a Ticket"
msgstr "Tạo một vé"

#: src/components/routes/auth/Register/index.tsx:69
msgid "Create an account or <0>{0}</0> to get started"
msgstr "Tạo tài khoản hoặc <0>{0}</0> để bắt đầu"

#: src/components/modals/CreateEventModal/index.tsx:120
msgid "create an organizer"
msgstr "Tạo một tổ chức"

#: src/components/layouts/AuthLayout/index.tsx:27
msgid "Create and customize your event page instantly"
msgstr "Tạo và tùy chỉnh trang sự kiện của bạn ngay lập tức"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Create Attendee"
msgstr "Tạo người tham dự"

#: src/components/common/CapacityAssignmentList/index.tsx:63
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:68
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:79
#: src/components/routes/event/CapacityAssignments/index.tsx:46
msgid "Create Capacity Assignment"
msgstr "Tạo phân bổ sức chứa"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:128
#: src/components/common/ProductsTable/SortableCategory/index.tsx:129
msgid "Create category"
msgstr "Tạo thể loại"

#: src/components/modals/CreateProductCategoryModal/index.tsx:45
#: src/components/modals/CreateProductCategoryModal/index.tsx:60
msgid "Create Category"
msgstr "Tạo thể loại"

#: src/components/common/CheckInListList/index.tsx:61
#: src/components/modals/CreateCheckInListModal/index.tsx:72
#: src/components/modals/CreateCheckInListModal/index.tsx:82
#: src/components/routes/event/CheckInLists/index.tsx:46
msgid "Create Check-In List"
msgstr "Tạo danh sách check-in"

#: src/components/common/NoEventsBlankSlate/index.tsx:27
#: src/components/modals/CreateEventModal/index.tsx:85
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:81
#: src/components/routes/welcome/index.tsx:108
msgid "Create Event"
msgstr "Tạo sự kiện"

#: src/components/routes/events/Dashboard/index.tsx:91
msgid "Create new"
msgstr "Tạo mới"

#: src/components/forms/OrganizerForm/index.tsx:111
#: src/components/modals/CreateEventModal/index.tsx:93
#: src/components/modals/CreateOrganizerModal/index.tsx:16
msgid "Create Organizer"
msgstr "Tạo tổ chức"

#: src/components/modals/CreateProductModal/index.tsx:80
#: src/components/modals/CreateProductModal/index.tsx:88
msgid "Create Product"
msgstr "Tạo sản phẩm"

#: src/components/routes/event/GettingStarted/index.tsx:70
#~ msgid "Create products for your event, set prices, and manage available quantity."
#~ msgstr "Tạo sản phẩm cho sự kiện của bạn, đặt giá và quản lý số lượng có sẵn."

#: src/components/modals/CreatePromoCodeModal/index.tsx:51
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
msgid "Create Promo Code"
msgstr "Tạo mã khuyến mãi"

#: src/components/modals/CreateQuestionModal/index.tsx:69
#: src/components/modals/CreateQuestionModal/index.tsx:74
msgid "Create Question"
msgstr "Tạo câu hỏi"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:41
msgid "Create Tax or Fee"
msgstr "Tạo thuế hoặc phí"

#: src/components/routes/event/GettingStarted/index.tsx:122
msgid "Create tickets for your event, set prices, and manage available quantity."
msgstr ""

#: src/components/modals/CreateWebhookModal/index.tsx:57
#: src/components/modals/CreateWebhookModal/index.tsx:67
msgid "Create Webhook"
msgstr "Tạo webhook"

#: src/components/common/OrdersTable/index.tsx:208
#: src/components/common/OrdersTable/index.tsx:281
msgid "Created"
msgstr "Được tạo"

#: src/components/forms/OrganizerForm/index.tsx:45
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:73
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:100
msgid "Currency"
msgstr "Tiền tệ"

#: src/components/routes/profile/ManageProfile/index.tsx:191
msgid "Current Password"
msgstr "Mật khẩu hiện tại"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:159
msgid "Custom Maps URL"
msgstr "URL bản đồ tùy chỉnh"

#: src/components/common/ReportTable/index.tsx:53
msgid "Custom Range"
msgstr "Phạm vi tùy chỉnh"

#: src/components/common/OrdersTable/index.tsx:205
msgid "Customer"
msgstr "Khách hàng"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:55
msgid "Customize the email and notification settings for this event"
msgstr "Tùy chỉnh cài đặt email và thông báo cho sự kiện này"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:62
msgid "Customize the event homepage and checkout messaging"
msgstr "Tùy chỉnh trang chủ sự kiện và tin nhắn thanh toán"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:70
msgid "Customize the miscellaneous settings for this event"
msgstr "Tùy chỉnh các cài đặt linh tinh cho sự kiện này"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:57
msgid "Customize the SEO settings for this event"
msgstr "Tùy chỉnh cài đặt SEO cho sự kiện này"

#: src/components/routes/event/GettingStarted/index.tsx:169
msgid "Customize your event page"
msgstr "Tùy chỉnh trang sự kiện của bạn"

#: src/components/layouts/AuthLayout/index.tsx:84
msgid "Customize your event page and widget design to match your brand perfectly"
msgstr "Tùy chỉnh trang sự kiện và thiết kế widget của bạn để phù hợp với thương hiệu của bạn một cách hoàn hảo"

#: src/components/routes/event/GettingStarted/index.tsx:165
msgid "Customize your event page to match your brand and style."
msgstr "Tùy chỉnh trang sự kiện của bạn để phù hợp với thương hiệu và phong cách của bạn."

#: src/components/routes/event/Reports/index.tsx:23
msgid "Daily Sales Report"
msgstr "Báo cáo doanh số hàng ngày"

#: src/components/routes/event/Reports/index.tsx:24
msgid "Daily sales, tax, and fee breakdown"
msgstr "Chi tiết doanh số hàng ngày, thuế và phí"

#: src/components/common/CapacityAssignmentList/index.tsx:156
#: src/components/common/CheckInListList/index.tsx:176
#: src/components/common/OrdersTable/index.tsx:186
#: src/components/common/ProductsTable/SortableProduct/index.tsx:287
#: src/components/common/PromoCodeTable/index.tsx:181
#: src/components/common/QuestionsTable/index.tsx:115
#: src/components/common/WebhookTable/index.tsx:116
msgid "Danger zone"
msgstr "Vùng nguy hiểm"

#: src/components/common/AttendeeTable/index.tsx:179
msgid "Danger Zone"
msgstr "Vùng nguy hiểm"

#: src/components/layouts/Event/index.tsx:89
msgid "Dashboard"
msgstr "Bảng điều khiển"

#: src/components/common/OrderDetails/index.tsx:39
#: src/components/forms/QuestionForm/index.tsx:136
msgid "Date"
msgstr "Ngày"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:40
#~ msgid "Date & Time"
#~ msgstr "Ngày và Thời gian"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:38
msgid "Day one capacity"
msgstr "Sức chứa ngày đầu tiên"

#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/TaxAndFeeList/index.tsx:73
msgid "Delete"
msgstr "Xóa"

#: src/components/common/CapacityAssignmentList/index.tsx:159
msgid "Delete Capacity"
msgstr "Xóa sức chứa"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:146
#: src/components/common/ProductsTable/SortableCategory/index.tsx:147
msgid "Delete category"
msgstr "Xóa danh mục"

#: src/components/common/CheckInListList/index.tsx:179
msgid "Delete Check-In List"
msgstr "Xóa danh sách check-in"

#: src/components/common/PromoCodeTable/index.tsx:185
msgid "Delete code"
msgstr "Xóa mã"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:113
msgid "Delete Cover"
msgstr "Xóa bìa"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:98
msgid "Delete Image"
msgstr "Xóa hình ảnh"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:292
msgid "Delete product"
msgstr "Xóa sản phẩm"

#: src/components/common/QuestionsTable/index.tsx:120
msgid "Delete question"
msgstr "Xóa câu hỏi"

#: src/components/common/WebhookTable/index.tsx:127
msgid "Delete webhook"
msgstr "Xóa webhook"

#: src/components/forms/ProductForm/index.tsx:258
#: src/components/forms/TaxAndFeeForm/index.tsx:81
#: src/components/modals/CreateEventModal/index.tsx:135
#: src/components/modals/DuplicateEventModal/index.tsx:84
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:78
msgid "Description"
msgstr "Mô tả"

#: src/components/forms/CheckInListForm/index.tsx:36
msgid "Description for check-in staff"
msgstr "Mô tả cho nhân viên làm thủ tục check-in"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Details"
msgstr "Chi tiết"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
msgid "Disabling this capacity will track sales but not stop them when the limit is reached"
msgstr "Vô hiệu hóa sức chứa này sẽ theo dõi doanh số nhưng không dừng bán khi đạt giới hạn"

#: src/components/common/PromoCodeTable/index.tsx:70
msgid "Discount"
msgstr "Giảm giá"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount %"
msgstr "Giảm giá %"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount in {0}"
msgstr "Giảm giá trong {0}"

#: src/components/forms/PromoCodeForm/index.tsx:43
msgid "Discount Type"
msgstr "Loại giảm giá"

#: src/components/routes/product-widget/SelectProducts/index.tsx:297
#~ msgid "Dismiss"
#~ msgstr "Bỏ qua"

#: src/components/routes/product-widget/SelectProducts/index.tsx:354
msgid "Dismiss this message"
msgstr "Bỏ qua thông báo này"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:190
msgid "Document Label"
msgstr "Nhãn tài liệu"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:222
msgid "Documentation"
msgstr "Tài liệu"

#: src/components/routes/auth/Login/index.tsx:57
msgid "Don't have an account?   <0>Sign up</0>"
msgstr "Chưa có tài khoản? <0>Đăng ký</0>"

#: src/components/forms/ProductForm/index.tsx:157
msgid "Donation / Pay what you'd like product"
msgstr "Sản phẩm quyên góp / Trả số tiền bạn muốn"

#: src/components/common/AddEventToCalendarButton/index.tsx:137
msgid "Download .ics"
msgstr "Tải xuống .ics"

#: src/components/common/DownloadCsvButton/index.tsx:48
msgid "Download CSV"
msgstr "Tải xuống CSV"

#: src/components/common/OrdersTable/index.tsx:162
msgid "Download invoice"
msgstr "Tải xuống hóa đơn"

#: src/components/layouts/Checkout/index.tsx:126
msgid "Download Invoice"
msgstr "Tải xuống hóa đơn"

#: src/components/modals/ShareModal/index.tsx:224
msgid "Download QR Code"
msgstr "Tải về mã QR"

#: src/components/common/OrdersTable/index.tsx:111
#: src/components/layouts/Checkout/index.tsx:49
msgid "Downloading Invoice"
msgstr "Tải xuống hóa đơn"

#: src/components/layouts/Event/index.tsx:188
msgid "Draft"
msgstr "Bản nháp"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:83
msgid "Drag and drop or click"
msgstr "kéo và thả hoặc nhấp vào"

#: src/components/forms/QuestionForm/index.tsx:124
msgid "Dropdown selection"
msgstr "Lựa chọn thả xuống"

#: src/components/modals/SendMessageModal/index.tsx:159
msgid ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."
msgstr ""
"Do nguy cơ spam cao, chúng tôi yêu cầu xác minh thủ công trước khi bạn có thể gửi tin nhắn.\n"
"Vui lòng liên hệ với chúng tôi để yêu cầu quyền truy cập."

#: src/components/modals/DuplicateEventModal/index.tsx:124
msgid "Duplicate Capacity Assignments"
msgstr "Nhân bản phân bổ sức chứa"

#: src/components/modals/DuplicateEventModal/index.tsx:128
msgid "Duplicate Check-In Lists"
msgstr "Nhân bản danh sách check-in"

#: src/components/common/EventCard/index.tsx:162
msgid "Duplicate event"
msgstr "Nhân bản sự kiện"

#: src/components/modals/DuplicateEventModal/index.tsx:69
#: src/components/modals/DuplicateEventModal/index.tsx:142
msgid "Duplicate Event"
msgstr "Nhân bản sự kiện"

#: src/components/modals/DuplicateEventModal/index.tsx:132
msgid "Duplicate Event Cover Image"
msgstr "Nhân bản ảnh bìa sự kiện"

#: src/components/modals/DuplicateEventModal/index.tsx:103
msgid "Duplicate Options"
msgstr "Tùy chọn nhân bản"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:285
#: src/components/modals/DuplicateProductModal/index.tsx:109
#: src/components/modals/DuplicateProductModal/index.tsx:113
msgid "Duplicate Product"
msgstr "Nhân bản sản phẩm"

#: src/components/modals/DuplicateEventModal/index.tsx:108
msgid "Duplicate Products"
msgstr "Nhân bản sản phẩm"

#: src/components/modals/DuplicateEventModal/index.tsx:120
msgid "Duplicate Promo Codes"
msgstr "Nhân bản mã khuyến mãi"

#: src/components/modals/DuplicateEventModal/index.tsx:112
msgid "Duplicate Questions"
msgstr "Nhân bản câu hỏi"

#: src/components/modals/DuplicateEventModal/index.tsx:116
msgid "Duplicate Settings"
msgstr "Nhân bản cài đặt"

#: src/components/modals/DuplicateEventModal/index.tsx:137
msgid "Duplicate Webhooks"
msgstr "Sao chép webhooks"

#: src/components/common/LanguageSwitcher/index.tsx:24
msgid "Dutch"
msgstr "Tiếng Hà Lan"

#: src/components/forms/ProductForm/index.tsx:78
msgid "Early bird"
msgstr "Ưu đãi sớm"

#: src/components/common/TaxAndFeeList/index.tsx:65
#: src/components/modals/ManageAttendeeModal/index.tsx:221
#: src/components/modals/ManageOrderModal/index.tsx:203
msgid "Edit"
msgstr "Chỉnh sửa"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
msgid "Edit {0}"
msgstr "chỉnh sửa {0}"

#: src/components/common/QuestionAndAnswerList/index.tsx:159
msgid "Edit Answer"
msgstr "Chỉnh sửa câu trả lời"

#: src/components/common/CapacityAssignmentList/index.tsx:146
msgid "Edit Capacity"
msgstr "Chỉnh sửa công suất"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:66
#: src/components/modals/EditCapacityAssignmentModal/index.tsx:74
msgid "Edit Capacity Assignment"
msgstr "Chỉnh sửa phân bổ sức chứa"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:137
#: src/components/common/ProductsTable/SortableCategory/index.tsx:138
msgid "Edit category"
msgstr "Chỉnh sửa danh mục"

#: src/components/common/CheckInListList/index.tsx:148
#: src/components/modals/EditCheckInListModal/index.tsx:69
#: src/components/modals/EditCheckInListModal/index.tsx:91
msgid "Edit Check-In List"
msgstr "Chỉnh sửa danh sách check-in"

#: src/components/common/PromoCodeTable/index.tsx:170
msgid "Edit Code"
msgstr "Chỉnh sửa mã"

#: src/components/modals/EditOrganizerModal/index.tsx:63
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:51
msgid "Edit Organizer"
msgstr "Chỉnh sửa tổ chức"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:280
#: src/components/modals/EditProductModal/index.tsx:100
#: src/components/modals/EditProductModal/index.tsx:108
msgid "Edit Product"
msgstr "Chỉnh sửa sản phẩm"

#: src/components/modals/EditProductCategoryModal/index.tsx:62
#: src/components/modals/EditProductCategoryModal/index.tsx:70
msgid "Edit Product Category"
msgstr "Chỉnh sửa danh mục sản phẩm"

#: src/components/modals/EditPromoCodeModal/index.tsx:79
#: src/components/modals/EditPromoCodeModal/index.tsx:84
msgid "Edit Promo Code"
msgstr "Chỉnh sửa mã khuyến mãi"

#: src/components/common/QuestionsTable/index.tsx:112
msgid "Edit question"
msgstr "Chỉnh sửa câu hỏi"

#: src/components/modals/EditQuestionModal/index.tsx:95
#: src/components/modals/EditQuestionModal/index.tsx:101
msgid "Edit Question"
msgstr "Chỉnh sửa câu hỏi"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Edit user"
msgstr "Chỉnh sửa người dùng"

#: src/components/modals/EditUserModal/index.tsx:62
#: src/components/modals/EditUserModal/index.tsx:120
msgid "Edit User"
msgstr "Chỉnh sửa người dùng"

#: src/components/common/WebhookTable/index.tsx:104
msgid "Edit webhook"
msgstr "Chỉnh sửa webhook"

#: src/components/modals/EditWebhookModal/index.tsx:66
#: src/components/modals/EditWebhookModal/index.tsx:87
msgid "Edit Webhook"
msgstr "Chỉnh sửa webhook"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 2.50 for $2.50"
msgstr "ví dụ: 2.50 cho $2.50"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 23.5 for 23.5%"
msgstr "ví dụ: 23.5 cho 23.5%"

#: src/components/common/AttendeeDetails/index.tsx:21
#: src/components/common/AttendeeTable/index.tsx:109
#: src/components/common/OrderDetails/index.tsx:31
#: src/components/forms/OrganizerForm/index.tsx:32
#: src/components/modals/EditUserModal/index.tsx:80
#: src/components/modals/InviteUserModal/index.tsx:61
#: src/components/routes/auth/AcceptInvitation/index.tsx:98
#: src/components/routes/auth/Login/index.tsx:68
#: src/components/routes/auth/Register/index.tsx:97
#: src/components/routes/event/Settings/index.tsx:52
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:117
#: src/components/routes/profile/ManageProfile/index.tsx:138
msgid "Email"
msgstr "Email"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
msgid "Email & Notification Settings"
msgstr "Cài đặt email & thông báo"

#: src/components/modals/CreateAttendeeModal/index.tsx:132
#: src/components/modals/ManageAttendeeModal/index.tsx:114
#: src/components/modals/ManageOrderModal/index.tsx:157
msgid "Email address"
msgstr "Địa chỉ email"

#: src/components/common/QuestionsTable/index.tsx:220
#: src/components/common/QuestionsTable/index.tsx:221
#: src/components/routes/product-widget/CollectInformation/index.tsx:298
#: src/components/routes/product-widget/CollectInformation/index.tsx:299
#: src/components/routes/product-widget/CollectInformation/index.tsx:408
#: src/components/routes/product-widget/CollectInformation/index.tsx:409
msgid "Email Address"
msgstr "Địa chỉ Email"

#: src/components/routes/profile/ManageProfile/index.tsx:77
msgid "Email change cancelled successfully"
msgstr "Hủy thay đổi email thành công"

#: src/components/routes/profile/ManageProfile/index.tsx:116
msgid "Email change pending"
msgstr "Thay đổi email đang chờ xử lý"

#: src/components/routes/profile/ManageProfile/index.tsx:151
msgid "Email confirmation resent"
msgstr "Xác nhận email đã được gửi lại"

#: src/components/routes/profile/ManageProfile/index.tsx:90
msgid "Email confirmation resent successfully"
msgstr "Xác nhận email đã được gửi lại thành công"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:66
msgid "Email footer message"
msgstr "Thông điệp chân trang email"

#: src/components/routes/profile/ManageProfile/index.tsx:141
msgid "Email not verified"
msgstr "Email không được xác minh"

#: src/components/common/WidgetEditor/index.tsx:272
msgid "Embed Code"
msgstr "Mã nhúng"

#: src/components/common/WidgetEditor/index.tsx:260
msgid "Embed Script"
msgstr "Tập lệnh nhúng"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:181
msgid "Enable Invoicing"
msgstr "Bật hóa đơn"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:21
msgid "Enable this capacity to stop product sales when the limit is reached"
msgstr "Cho phép khả năng này dừng bán sản phẩm khi đạt đến giới hạn"

#: src/components/forms/WebhookForm/index.tsx:19
msgid "Enabled"
msgstr "Đã bật"

#: src/components/modals/CreateEventModal/index.tsx:149
#: src/components/modals/DuplicateEventModal/index.tsx:98
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:92
#: src/components/routes/welcome/index.tsx:96
msgid "End Date"
msgstr "Ngày kết thúc"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:26
#: src/components/common/EventStatusBadge/index.tsx:14
msgid "Ended"
msgstr "Kết thúc"

#: src/components/routes/events/Dashboard/index.tsx:57
msgid "Ended Events"
msgstr "Sự kiện kết thúc"

#: src/components/common/LanguageSwitcher/index.tsx:16
#: src/components/common/LanguageSwitcher/index.tsx:50
#: src/components/modals/CreateAttendeeModal/index.tsx:145
#: src/components/routes/profile/ManageProfile/index.tsx:173
msgid "English"
msgstr "Tiếng Anh"

#: src/components/modals/CreateAttendeeModal/index.tsx:170
msgid "Enter an amount excluding taxes and fees."
msgstr "Nhập một số tiền không bao gồm thuế và phí."

#: src/components/common/OrdersTable/index.tsx:119
#: src/components/layouts/Checkout/index.tsx:57
msgid "Error"
msgstr "Lỗi"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:27
msgid "Error confirming email address"
msgstr "Lỗi xác nhận địa chỉ email"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:35
msgid "Error confirming email change"
msgstr "Lỗi xác nhận thay đổi email"

#: src/components/modals/WebhookLogsModal/index.tsx:166
msgid "Error loading logs"
msgstr "Lỗi khi tải nhật ký"

#: src/components/forms/OrganizerForm/index.tsx:46
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:74
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:101
msgid "EUR"
msgstr "EUR"

#: src/components/routes/events/Dashboard/index.tsx:103
msgid "Event"
msgstr "Sự kiện"

#: src/components/modals/CreateEventModal/index.tsx:76
#~ msgid "Event created successfully 🎉"
#~ msgstr "Sự kiện được tạo thành công 🎉"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:161
msgid "Event Date"
msgstr "Ngày sự kiện"

#: src/components/routes/account/ManageAccount/sections/EventDefaultsSettings/index.tsx:5
msgid "Event Defaults"
msgstr "Mặc định sự kiện"

#: src/components/routes/event/Settings/index.tsx:28
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:276
msgid "Event Details"
msgstr "Chi tiết sự kiện"

#: src/components/modals/DuplicateEventModal/index.tsx:58
msgid "Event duplicated successfully"
msgstr "Sự kiện nhân đôi thành công"

#: src/components/layouts/Checkout/index.tsx:79
msgid "Event Homepage"
msgstr "Trang chủ sự kiện"

#: src/components/layouts/Event/index.tsx:166
#~ msgid "Event is not visible to the public"
#~ msgstr "Sự kiện không thể nhìn thấy đối với công chúng"

#: src/components/layouts/Event/index.tsx:165
#~ msgid "Event is visible to the public"
#~ msgstr "Sự kiện có thể nhìn thấy cho công chúng"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:85
msgid "Event location & venue details"
msgstr "Vị trí sự kiện & địa điểm tổ chức"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:12
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
msgid "Event Not Available"
msgstr "Sự kiện không có sẵn"

#: src/components/layouts/Event/index.tsx:187
#~ msgid "Event page"
#~ msgstr "Trang sự kiện"

#: src/components/layouts/Event/index.tsx:212
msgid "Event Page"
msgstr "Trang sự kiện"

#: src/components/common/EventCard/index.tsx:70
#: src/components/layouts/Event/index.tsx:80
#: src/components/routes/event/EventDashboard/index.tsx:76
#: src/components/routes/event/GettingStarted/index.tsx:63
msgid "Event status update failed. Please try again later"
msgstr "Cập nhật trạng thái sự kiện thất bại. Vui lòng thử lại sau"

#: src/components/common/EventCard/index.tsx:67
#: src/components/layouts/Event/index.tsx:77
#: src/components/routes/event/EventDashboard/index.tsx:73
#: src/components/routes/event/GettingStarted/index.tsx:60
msgid "Event status updated"
msgstr "Trạng thái sự kiện đã được cập nhật"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Event Ticketing by"
msgstr "Phát hành vé bởi"

#: src/components/common/WebhookTable/index.tsx:237
#: src/components/forms/WebhookForm/index.tsx:122
msgid "Event Types"
msgstr "Loại sự kiện"

#: src/components/modals/ShareModal/index.tsx:237
msgid "Event URL"
msgstr "URL sự kiện"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
#~ msgid "Event wdwdeNot Available"
#~ msgstr ""

#: src/components/layouts/Event/index.tsx:226
msgid "Events"
msgstr "Sự kiện"

#: src/components/forms/CheckInListForm/index.tsx:56
msgid "Expiration date"
msgstr "Ngày hết hạn"

#: src/components/common/PromoCodeTable/index.tsx:73
msgid "Expires"
msgstr "Hết hạn"

#: src/components/forms/PromoCodeForm/index.tsx:79
msgid "Expiry Date"
msgstr "Ngày hết hạn"

#: src/components/routes/event/attendees.tsx:80
#: src/components/routes/event/orders.tsx:140
msgid "Export"
msgstr "Xuất"

#: src/components/common/QuestionsTable/index.tsx:267
msgid "Export answers"
msgstr "Xuất câu trả lời"

#: src/mutations/useExportAnswers.ts:39
msgid "Export failed. Please try again."
msgstr "Xuất thất bại. Vui lòng thử lại."

#: src/mutations/useExportAnswers.ts:17
msgid "Export started. Preparing file..."
msgstr "Đã bắt đầu xuất. Đang chuẩn bị tệp..."

#: src/components/routes/event/attendees.tsx:39
msgid "Exporting Attendees"
msgstr "Đang xuất danh sách người tham dự"

#: src/mutations/useExportAnswers.ts:33
msgid "Exporting complete. Downloading file..."
msgstr "Xuất hoàn tất. Đang tải xuống tệp..."

#: src/components/routes/event/orders.tsx:90
msgid "Exporting Orders"
msgstr "Đang xuất đơn hàng"

#: src/components/common/AttendeeTable/index.tsx:97
msgid "Failed to cancel attendee"
msgstr "Không thể hủy người tham dự"

#: src/components/modals/CancelOrderModal/index.tsx:33
msgid "Failed to cancel order"
msgstr "Không thể hủy đơn hàng"

#: src/components/common/QuestionsTable/index.tsx:175
msgid "Failed to delete message. Please try again."
msgstr "Không thể xóa tin nhắn. Vui lòng thử lại."

#: src/components/common/OrdersTable/index.tsx:120
#: src/components/layouts/Checkout/index.tsx:58
msgid "Failed to download invoice. Please try again."
msgstr "Không thể tải hóa đơn. Vui lòng thử lại."

#: src/components/routes/event/attendees.tsx:48
msgid "Failed to export attendees"
msgstr "Không thể xuất danh sách người tham dự"

#: src/components/routes/event/orders.tsx:99
msgid "Failed to export orders"
msgstr "Không thể xuất đơn hàng"

#: src/components/modals/EditCheckInListModal/index.tsx:79
msgid "Failed to load Check-In List"
msgstr "Không thể tải danh sách check-in"

#: src/components/modals/EditWebhookModal/index.tsx:75
msgid "Failed to load Webhook"
msgstr "Không thể tải Webhook"

#: src/components/common/AttendeeTable/index.tsx:50
msgid "Failed to resend ticket email"
msgstr "Không thể gửi lại email vé"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:179
msgid "Failed to sort products"
msgstr "Không thể sắp xếp sản phẩm"

#: src/mutations/useExportAnswers.ts:15
msgid "Failed to start export job"
msgstr "Không thể bắt đầu quá trình xuất"

#: src/components/common/QuestionAndAnswerList/index.tsx:102
msgid "Failed to update answer."
msgstr "Không thể cập nhật câu trả lời."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:64
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:69
msgid "Failed to upload image."
msgstr "Không thể tải ảnh lên."

#: src/components/forms/TaxAndFeeForm/index.tsx:18
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Fee"
msgstr "Phí"

#: src/components/forms/ProductForm/index.tsx:366
msgid "Fees"
msgstr "Các khoản phí"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:80
msgid "Fees are subject to change. You will be notified of any changes to your fee structure."
msgstr "Các khoản phí có thể thay đổi. Bạn sẽ được thông báo về bất kỳ thay đổi nào trong cấu trúc phí của mình."

#: src/components/routes/event/orders.tsx:121
msgid "Filter Orders"
msgstr "Lọc đơn hàng"

#: src/components/common/FilterModal/index.tsx:75
#: src/components/common/FilterModal/index.tsx:223
msgid "Filters"
msgstr "Bộ lọc"

#: src/components/common/FilterModal/index.tsx:223
msgid "Filters ({activeFilterCount})"
msgstr "Bộ lọc ({activeFilterCount})"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:206
msgid "First Invoice Number"
msgstr "Số hóa đơn đầu tiên"

#: src/components/common/QuestionsTable/index.tsx:208
#: src/components/modals/CreateAttendeeModal/index.tsx:118
#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:144
#: src/components/routes/product-widget/CollectInformation/index.tsx:284
#: src/components/routes/product-widget/CollectInformation/index.tsx:395
msgid "First name"
msgstr "Tên"

#: src/components/common/QuestionsTable/index.tsx:207
#: src/components/modals/EditUserModal/index.tsx:71
#: src/components/modals/InviteUserModal/index.tsx:57
#: src/components/routes/auth/AcceptInvitation/index.tsx:92
#: src/components/routes/auth/Register/index.tsx:83
#: src/components/routes/product-widget/CollectInformation/index.tsx:283
#: src/components/routes/product-widget/CollectInformation/index.tsx:394
#: src/components/routes/profile/ManageProfile/index.tsx:135
msgid "First Name"
msgstr "Tên"

#: src/components/routes/auth/AcceptInvitation/index.tsx:29
msgid "First name must be between 1 and 50 characters"
msgstr "Tên của bạn phải nằm trong khoảng từ 1 đến 50 ký tự"

#: src/components/common/QuestionsTable/index.tsx:349
msgid "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."
msgstr "Tên, họ và địa chỉ email là các câu hỏi mặc định và luôn được đưa vào quy trình thanh toán."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
msgid "First Used"
msgstr "Được sử dụng lần đầu tiên"

#: src/components/forms/TaxAndFeeForm/index.tsx:33
msgid "Fixed"
msgstr "Đã sửa"

#: src/components/forms/PromoCodeForm/index.tsx:55
msgid "Fixed amount"
msgstr "Số tiền cố định"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:65
msgid "Fixed Fee:"
msgstr "Phí cố định:"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:123
msgid "Flash is not available on this device"
msgstr "đèn Flash không khả dụng trên thiết bị này"

#: src/components/layouts/AuthLayout/index.tsx:58
msgid "Flexible Ticketing"
msgstr "Vé linh hoạt"

#: src/components/routes/auth/Login/index.tsx:80
msgid "Forgot password?"
msgstr "Quên mật khẩu?"

#: src/components/common/AttendeeTicket/index.tsx:40
#: src/components/common/Currency/index.tsx:90
#: src/components/common/OrderSummary/index.tsx:46
#: src/components/common/OrderSummary/index.tsx:99
#: src/components/common/ProductsTable/SortableProduct/index.tsx:93
#: src/components/common/ProductsTable/SortableProduct/index.tsx:103
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:53
msgid "Free"
msgstr "Miễn phí"

#: src/components/forms/ProductForm/index.tsx:151
msgid "Free Product"
msgstr "Sản phẩm miễn phí"

#: src/components/forms/ProductForm/index.tsx:153
msgid "Free product, no payment information required"
msgstr "Sản phẩm miễn phí, không cần thông tin thanh toán"

#: src/components/common/LanguageSwitcher/index.tsx:20
msgid "French"
msgstr "Tiếng Pháp"

#: src/components/layouts/AuthLayout/index.tsx:88
msgid "Fully Integrated"
msgstr "Tích hợp hoàn toàn"

#: src/components/forms/ProductForm/index.tsx:136
msgid "General"
msgstr "Chung"

#: src/components/common/LanguageSwitcher/index.tsx:14
msgid "German"
msgstr "Tiếng Đức"

#: src/components/layouts/AuthLayout/index.tsx:35
msgid "Get started for free, no subscription fees"
msgstr "Bắt đầu miễn phí, không có phí đăng ký"

#: src/components/routes/event/EventDashboard/index.tsx:125
msgid "Get your event ready"
msgstr "Chuẩn bị sự kiện của bạn"

#: src/components/layouts/Event/index.tsx:88
msgid "Getting Started"
msgstr "Bắt đầu"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:23
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:42
msgid "Go back to profile"
msgstr "Quay trở lại hồ sơ"

#: src/components/routes/product-widget/CollectInformation/index.tsx:239
msgid "Go to event homepage"
msgstr "Đi đến trang chủ sự kiện"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:53
msgid "Go to Hi.Events"
msgstr "Truy cập Hi.Events"

#: src/components/common/ErrorDisplay/index.tsx:64
msgid "Go to home page"
msgstr "Đi đến trang chủ"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:162
msgid "Go to Stripe Dashboard"
msgstr "Đi đến Bảng điều khiển Stripe"

#: src/components/common/AddEventToCalendarButton/index.tsx:128
msgid "Google Calendar"
msgstr "Lịch Google"

#: src/components/common/EventCard/index.tsx:133
msgid "gross sales"
msgstr "Tổng doanh số"

#: src/components/common/StatBoxes/index.tsx:39
msgid "Gross sales"
msgstr "Tổng doanh số"

#: src/components/routes/event/EventDashboard/index.tsx:274
msgid "Gross Sales"
msgstr "Tổng doanh số"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:281
msgid "Guests"
msgstr "Người được mời"

#: src/components/routes/product-widget/SelectProducts/index.tsx:495
msgid "Have a promo code?"
msgstr "Nhập mã khuyến mãi?"

#: src/components/forms/OrganizerForm/index.tsx:33
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/WidgetEditor/index.tsx:295
msgid "Here is an example of how you can use the component in your application."
msgstr "Đây là một ví dụ về cách bạn có thể sử dụng thành phần trong ứng dụng của mình."

#: src/components/common/WidgetEditor/index.tsx:284
msgid "Here is the React component you can use to embed the widget in your application."
msgstr "Đây là thành phần React bạn có thể sử dụng để nhúng tiện ích vào ứng dụng của mình."

#: src/components/routes/event/EventDashboard/index.tsx:101
msgid "Hi {0} 👋"
msgstr "Chào {0} 👋"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:43
msgid "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."
msgstr "Hi.Events tính phí nền tảng để duy trì và cải thiện dịch vụ của chúng tôi. Các khoản phí này sẽ được khấu trừ tự động từ mỗi giao dịch."

#: src/components/modals/CreateEventModal/index.tsx:130
#: src/components/modals/DuplicateEventModal/index.tsx:79
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:73
msgid "Hi.Events Conference {0}"
msgstr "Hội nghị Hi.events {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:117
msgid "Hi.Events Conference Center"
msgstr "Trung tâm hội nghị HI.Events"

#: src/components/layouts/AuthLayout/index.tsx:131
msgid "hi.events logo"
msgstr "Logo hi.events"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:79
msgid "Hidden from public view"
msgstr "Ẩn khỏi chế độ xem công khai"

#: src/components/common/QuestionsTable/index.tsx:290
msgid "hidden question"
msgstr "Câu hỏi ẩn"

#: src/components/common/QuestionsTable/index.tsx:291
msgid "hidden questions"
msgstr "Câu hỏi ẩn"

#: src/components/forms/QuestionForm/index.tsx:218
msgid "Hidden questions are only visible to the event organizer and not to the customer."
msgstr "Các câu hỏi ẩn chỉ hiển thị cho người tổ chức sự kiện chứ không phải cho khách hàng."

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/routes/product-widget/SelectProducts/index.tsx:460
msgid "Hide"
msgstr "Ẩn"

#: src/components/common/AttendeeList/index.tsx:84
msgid "Hide Answers"
msgstr "Ẩn câu trả lời"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:89
msgid "Hide getting started page"
msgstr "ẩn trang bắt đầu"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Hide hidden questions"
msgstr "ẩn các câu hỏi ẩn"

#: src/components/forms/ProductForm/index.tsx:399
msgid "Hide product after sale end date"
msgstr "ẩn sản phẩm sau ngày kết thúc bán"

#: src/components/forms/ProductForm/index.tsx:397
msgid "Hide product before sale start date"
msgstr "ẩn sản phẩm trước ngày bắt đầu bán"

#: src/components/forms/ProductForm/index.tsx:412
msgid "Hide product unless user has applicable promo code"
msgstr "ẩn sản phẩm trừ khi người dùng có mã khuyến mãi áp dụng"

#: src/components/forms/ProductForm/index.tsx:405
msgid "Hide product when sold out"
msgstr "Ẩn sản phẩm khi bán hết"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:90
msgid "Hide the getting started page from the sidebar"
msgstr "ẩn trang bắt đầu từ thanh bên"

#: src/components/forms/ProductForm/index.tsx:417
msgid "Hide this product from customers"
msgstr "Ẩn sản phẩm này khỏi khách hàng"

#: src/components/forms/QuestionForm/index.tsx:219
msgid "Hide this question"
msgstr "Ẩn câu hỏi này"

#: src/components/forms/ProductForm/index.tsx:103
msgid "Hide this tier from users"
msgstr "Ẩn tầng này khỏi người dùng"

#: src/components/forms/ProductForm/index.tsx:101
msgid "Hiding a product will prevent users from seeing it on the event page."
msgstr "Việc ẩn một sản phẩm sẽ ngăn người dùng xem nó trên trang sự kiện."

#: src/components/routes/event/HomepageDesigner/index.tsx:115
msgid "Homepage Design"
msgstr "Thiết kế trang sự kiện"

#: src/components/layouts/Event/index.tsx:109
msgid "Homepage Designer"
msgstr "Thiết kế trang sự kiện"

#: src/components/routes/event/HomepageDesigner/index.tsx:174
msgid "Homepage Preview"
msgstr "Xem trước trang chủ"

#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:145
msgid "Homer"
msgstr "Homer"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
msgid "How many minutes the customer has to complete their order. We recommend at least 15 minutes"
msgstr "Khách hàng phải hoàn thành đơn đơn hàng bao nhiêu phút. "

#: src/components/forms/PromoCodeForm/index.tsx:84
msgid "How many times can this code be used?"
msgstr "Mã này có thể được sử dụng bao nhiêu lần?"

#: src/components/common/Editor/index.tsx:75
msgid "HTML character limit exceeded: {htmlLength}/{maxLength}"
msgstr "Vượt quá giới hạn ký tự HTML: {htmllesth}/{maxlength}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:160
msgid "https://example-maps-service.com/..."
msgstr "https://example-maps-service.com/..."

#: src/components/forms/WebhookForm/index.tsx:118
msgid "https://webhook-domain.com/webhook"
msgstr "https://webhook-domain.com/webhook"

#: src/components/routes/auth/AcceptInvitation/index.tsx:118
msgid "I agree to the <0>terms and conditions</0>"
msgstr "Tôi đồng ý với <0>các điều khoản và điều kiện</0>"

#: src/components/routes/product-widget/Payment/index.tsx:106
msgid "I would like to pay using an offline method"
msgstr "Tôi muốn thanh toán bằng phương thức ngoại tuyến"

#: src/components/routes/product-widget/Payment/index.tsx:107
msgid "I would like to pay using an online method (credit card etc.)"
msgstr "Tôi muốn thanh toán bằng phương thức trực tuyến (thẻ tín dụng, v.v.)"

#: src/components/routes/product-widget/SelectProducts/index.tsx:315
msgid "If a new tab did not open automatically, please click the button below to continue to checkout."
msgstr "Nếu tab mới không tự động mở, vui lòng nhấn nút bên dưới để tiếp tục thanh toán."

#: src/components/routes/product-widget/SelectProducts/index.tsx:284
#~ msgid "If a new tab did not open, please  <0><1>{0}</1>.</0>"
#~ msgstr "Nếu một tab mới không mở, vui lòng <0> <1>{0}</1>.</0>"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:158
msgid "If blank, the address will be used to generate a Google Mapa link"
msgstr "Nếu trống, địa chỉ sẽ được sử dụng để tạo liên kết Google Mapa link"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:158
msgid "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."
msgstr "Nếu được bật, nhân viên check-in có thể đánh dấu người tham dự đã check-in hoặc đánh dấu đơn hàng đã thanh toán và check-in người tham dự. Nếu tắt, những người tham dự liên kết với đơn hàng chưa thanh toán sẽ không thể check-in."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:76
msgid "If enabled, the organizer will receive an email notification when a new order is placed"
msgstr "Nếu được bật, người tổ chức sẽ nhận được thông báo email khi có đơn hàng mới"

#: src/components/routes/profile/ManageProfile/index.tsx:122
msgid "If you did not request this change, please immediately change your password."
msgstr "Nếu bạn không yêu cầu thay đổi này, vui lòng thay đổi ngay mật khẩu của bạn."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
msgid ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."
msgstr "Nếu bạn có tài khoản với chúng tôi, bạn sẽ nhận được email với hướng dẫn cách đặt lại mật khẩu."

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:108
msgid "Image deleted successfully"
msgstr "Hình ảnh đã xóa thành công"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
msgid "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"
msgstr "Kích thước hình ảnh phải trong khoảng từ 4000px x 4000px. Chiều cao tối đa 4000px và chiều rộng tối đa 4000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:49
msgid "Image must be less than 5MB"
msgstr "Hình ảnh phải nhỏ hơn 5MB"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:63
msgid "Image uploaded successfully"
msgstr "Hình ảnh được tải lên thành công"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:111
msgid "Image URL"
msgstr "URL hình ảnh"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
msgid "Image width must be at least 900px and height at least 50px"
msgstr "Chiều rộng hình ảnh phải ít nhất 900px và chiều cao ít nhất là 50px"

#: src/components/layouts/AuthLayout/index.tsx:53
msgid "In-depth Analytics"
msgstr "Phân tích chuyên sâu"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:25
#: src/components/modals/EditUserModal/index.tsx:111
msgid "Inactive"
msgstr "Không hoạt động"

#: src/components/modals/EditUserModal/index.tsx:107
msgid "Inactive users cannot log in."
msgstr "Người dùng không hoạt động không thể đăng nhập."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:102
msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."
msgstr "Bao gồm thông tin kết nối cho sự kiện trực tuyến của bạn. Những chi tiết này sẽ được hiển thị trên trang tóm tắt đơn hàng và trang vé của người tham dự."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:54
msgid "Include tax and fees in the price"
msgstr "Bao gồm thuế và phí trong giá"

#: src/components/common/CheckInListList/index.tsx:103
msgid "Includes {0} products"
msgstr "Bao gồm các sản phẩm {0}"

#: src/components/common/CheckInListList/index.tsx:104
msgid "Includes 1 product"
msgstr "Bao gồm 1 sản phẩm"

#: src/components/common/MessageList/index.tsx:20
msgid "Individual attendees"
msgstr "Người tham dự riêng lẻ"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:100
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:121
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:137
msgid "Insert Image"
msgstr "Chèn hình ảnh"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation resent!"
msgstr "Lời mời đã được gửi lại!"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation revoked!"
msgstr "Lời mời bị thu hồi!"

#: src/components/modals/InviteUserModal/index.tsx:54
#: src/components/modals/InviteUserModal/index.tsx:74
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invite User"
msgstr "Mời người dùng"

#: src/components/common/OrdersTable/index.tsx:116
#: src/components/layouts/Checkout/index.tsx:54
msgid "Invoice downloaded successfully"
msgstr "Hóa đơn được tải xuống thành công"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:252
msgid "Invoice Notes"
msgstr "Ghi chú hóa đơn"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:197
msgid "Invoice Numbering"
msgstr "Đánh số hóa đơn"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:177
msgid "Invoice Settings"
msgstr "Cài đặt hóa đơn"

#: src/components/modals/RefundOrderModal/index.tsx:115
msgid "Issue refund"
msgstr "Thực hiện hoàn tiền"

#: src/components/common/LanguageSwitcher/index.tsx:22
msgid "Italian"
msgstr "Tiếng Ý"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Item"
msgstr "Mục"

#: src/components/routes/auth/Register/index.tsx:84
msgid "John"
msgstr "John"

#: src/components/modals/CreateAttendeeModal/index.tsx:126
msgid "Johnson"
msgstr "Johnson"

#: src/components/forms/ProductForm/index.tsx:77
msgid "Label"
msgstr "Nhãn"

#: src/components/common/AttendeeDetails/index.tsx:45
#: src/components/modals/CreateAttendeeModal/index.tsx:144
#: src/components/routes/profile/ManageProfile/index.tsx:172
msgid "Language"
msgstr "Ngôn ngữ"

#: src/components/common/ReportTable/index.tsx:52
msgid "Last 12 months"
msgstr "12 tháng qua"

#: src/components/common/ReportTable/index.tsx:47
msgid "Last 14 days"
msgstr "14 ngày qua"

#: src/components/common/ReportTable/index.tsx:44
msgid "Last 24 hours"
msgstr "24 giờ qua"

#: src/components/common/ReportTable/index.tsx:48
msgid "Last 30 days"
msgstr "30 ngày qua"

#: src/components/common/ReportTable/index.tsx:45
msgid "Last 48 hours"
msgstr "48 giờ qua"

#: src/components/common/ReportTable/index.tsx:50
msgid "Last 6 months"
msgstr "6 tháng qua"

#: src/components/common/ReportTable/index.tsx:46
msgid "Last 7 days"
msgstr "7 ngày qua"

#: src/components/common/ReportTable/index.tsx:49
msgid "Last 90 days"
msgstr "90 ngày qua"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Last login"
msgstr "Đăng nhập cuối cùng"

#: src/components/modals/CreateAttendeeModal/index.tsx:125
#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:150
msgid "Last name"
msgstr "Họ"

#: src/components/common/QuestionsTable/index.tsx:212
#: src/components/common/QuestionsTable/index.tsx:213
#: src/components/modals/EditUserModal/index.tsx:72
#: src/components/modals/InviteUserModal/index.tsx:58
#: src/components/routes/auth/AcceptInvitation/index.tsx:94
#: src/components/routes/auth/Register/index.tsx:89
#: src/components/routes/product-widget/CollectInformation/index.tsx:289
#: src/components/routes/product-widget/CollectInformation/index.tsx:290
#: src/components/routes/product-widget/CollectInformation/index.tsx:400
#: src/components/routes/product-widget/CollectInformation/index.tsx:401
#: src/components/routes/profile/ManageProfile/index.tsx:137
msgid "Last Name"
msgstr "Họ"

#: src/components/common/WebhookTable/index.tsx:239
msgid "Last Response"
msgstr "Phản hồi cuối cùng"

#: src/components/common/WebhookTable/index.tsx:240
msgid "Last Triggered"
msgstr "Trình kích hoạt cuối cùng"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
msgid "Last Used"
msgstr "Được sử dụng lần cuối"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:191
msgid "Leave blank to use the default word \"Invoice\""
msgstr "Để trống để sử dụng từ mặc định \"Hóa đơn\""

#: src/components/routes/welcome/index.tsx:20
msgid "Let's get started by creating your first organizer"
msgstr "Hãy bắt đầu bằng cách tạo người tổ chức đầu tiên của bạn"

#: src/components/routes/event/EventDashboard/index.tsx:194
msgid "Link your Stripe account to receive funds from ticket sales."
msgstr "Liên kết tài khoản Stripe của bạn để nhận tiền từ việc bán vé."

#: src/components/layouts/Event/index.tsx:190
msgid "Live"
msgstr "Trực tiếp"

#: src/components/routes/event/Webhooks/index.tsx:21
msgid "Loading Webhooks"
msgstr "Đang tải Webhooks"

#: src/components/common/ReportTable/index.tsx:210
msgid "Loading..."
msgstr "Đang tải ..."

#: src/components/routes/event/Settings/index.tsx:34
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:84
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:167
msgid "Location"
msgstr "Vị trí"

#: src/components/routes/auth/Login/index.tsx:84
#: src/components/routes/auth/Register/index.tsx:71
msgid "Log in"
msgstr "Đăng nhập"

#: src/components/routes/auth/Login/index.tsx:84
msgid "Logging in"
msgstr "Đăng nhập"

#: src/components/common/GlobalMenu/index.tsx:47
msgid "Logout"
msgstr "Đăng xuất"

#: src/components/common/WidgetEditor/index.tsx:331
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:170
msgid "Make billing address mandatory during checkout"
msgstr "Bắt buộc nhập địa chỉ thanh toán, trong khi thanh toán"

#: src/components/routes/event/EventDashboard/index.tsx:172
#~ msgid "Make Event Live"
#~ msgstr "Phát hành sự kiện"

#: src/components/forms/QuestionForm/index.tsx:212
msgid "Make this question mandatory"
msgstr "Làm cho câu hỏi này bắt buộc"

#: src/components/routes/event/EventDashboard/index.tsx:148
msgid "Make your event live"
msgstr "Làm cho sự kiện của bạn hoạt động"

#: src/components/common/CapacityAssignmentList/index.tsx:143
#: src/components/common/CheckInListList/index.tsx:145
#: src/components/common/EventCard/index.tsx:180
#: src/components/common/OrdersTable/index.tsx:139
#: src/components/common/OrdersTable/index.tsx:154
#: src/components/common/ProductsTable/SortableProduct/index.tsx:256
#: src/components/common/PromoCodeTable/index.tsx:167
#: src/components/layouts/Event/index.tsx:97
msgid "Manage"
msgstr "Quản lý"

#: src/components/common/AttendeeTable/index.tsx:161
msgid "Manage attendee"
msgstr "Quản lý người tham dự"

#: src/components/common/EventCard/index.tsx:150
msgid "Manage event"
msgstr "Quản lý sự kiện"

#: src/components/common/OrdersTable/index.tsx:156
msgid "Manage order"
msgstr "Quản lý đơn hàng"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:99
msgid "Manage payment and invoicing settings for this event."
msgstr "Quản lý cài đặt thanh toán và lập hóa đơn cho sự kiện này."

#: src/components/routes/profile/ManageProfile/index.tsx:101
msgid "Manage Profile"
msgstr "Quản lý hồ sơ"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
msgid "Manage taxes and fees which can be applied to your products"
msgstr "Quản lý thuế và phí có thể được áp dụng cho sản phẩm của bạn"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
msgid "Manage tickets"
msgstr "Quản lý Vé"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:54
msgid "Manage your account details and default settings"
msgstr "Quản lý chi tiết tài khoản của bạn và cài đặt mặc định"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:241
msgid "Manage your payment processing and view platform fees"
msgstr "Quản lý xử lý thanh toán và xem phí nền tảng của bạn"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Manage your users and their permissions"
msgstr "Quản lý người dùng của bạn và quyền của họ"

#: src/components/forms/QuestionForm/index.tsx:211
msgid "Mandatory questions must be answered before the customer can checkout."
msgstr "Các câu hỏi bắt buộc phải được trả lời trước khi khách hàng có thể thanh toán."

#: src/components/common/AttendeeTable/index.tsx:67
msgid "Manually add an Attendee"
msgstr "Thêm một người tham dự theo cách thủ công"

#: src/components/modals/CreateAttendeeModal/index.tsx:97
#: src/components/modals/CreateAttendeeModal/index.tsx:113
msgid "Manually Add Attendee"
msgstr "Thêm người tham dự"

#: src/components/common/OrdersTable/index.tsx:167
msgid "Mark as paid"
msgstr "Đánh dấu đã trả tiền"

#: src/components/layouts/AuthLayout/index.tsx:83
msgid "Match Your Brand"
msgstr "Phù hợp với thương hiệu của bạn"

#: src/components/forms/ProductForm/index.tsx:383
msgid "Maximum Per Order"
msgstr "Tối đa mỗi đơn hàng"

#: src/components/common/AttendeeTable/index.tsx:166
msgid "Message attendee"
msgstr "Tin nhắn cho người tham dự"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:273
msgid "Message Attendees"
msgstr "Tin nhắn cho người tham dự"

#: src/components/modals/SendMessageModal/index.tsx:165
#~ msgid "Message attendees with specific products"
#~ msgstr "Tin nhắn cho người tham dự với các sản phẩm cụ thể"

#: src/components/modals/SendMessageModal/index.tsx:206
msgid "Message attendees with specific tickets"
msgstr "Nhắn tin cho người tham dự có vé cụ thể"

#: src/components/layouts/AuthLayout/index.tsx:74
msgid "Message attendees, manage orders, and handle refunds all in one place"
msgstr "Tin nhắn cho người tham dự, quản lý đơn hàng và xử lý hoàn lại tất cả ở một nơi"

#: src/components/common/OrdersTable/index.tsx:158
msgid "Message buyer"
msgstr "Tin nhắn cho người mua"

#: src/components/modals/SendMessageModal/index.tsx:250
msgid "Message Content"
msgstr "Nội dung tin nhắn"

#: src/components/modals/SendMessageModal/index.tsx:71
msgid "Message individual attendees"
msgstr "Tin nhắn cho những người tham dự cá nhân"

#: src/components/modals/SendMessageModal/index.tsx:218
msgid "Message order owners with specific products"
msgstr "Nhắn tin cho chủ đơn hàng có sản phẩm cụ thể"

#: src/components/modals/SendMessageModal/index.tsx:118
msgid "Message Sent"
msgstr "Tin nhắn được gửi"

#: src/components/layouts/Event/index.tsx:105
#: src/components/routes/event/messages.tsx:31
msgid "Messages"
msgstr "Tin nhắn"

#: src/components/forms/ProductForm/index.tsx:381
msgid "Minimum Per Order"
msgstr "Tối thiểu cho mỗi đơn hàng"

#: src/components/forms/ProductForm/index.tsx:285
msgid "Minimum Price"
msgstr "Giá tối thiểu"

#: src/components/routes/event/Settings/index.tsx:58
msgid "Miscellaneous"
msgstr "Linh tinh"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:69
msgid "Miscellaneous Settings"
msgstr "Cài đặt linh tinh"

#: src/components/layouts/AuthLayout/index.tsx:63
msgid "Mobile Check-in"
msgstr "Check-in trên thiết bị di động"

#: src/components/forms/QuestionForm/index.tsx:106
msgid "Multi line text box"
msgstr "Hộp văn bản đa dòng"

#: src/components/forms/ProductForm/index.tsx:165
msgid "Multiple price options. Perfect for early bird products etc."
msgstr "Nhiều tùy chọn giá. Hoàn hảo cho sản phẩm giảm giá sớm, v.v."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:71
msgid "My amazing event description..."
msgstr "Mô tả sự kiện tuyệt vời của tôi ..."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:65
msgid "My amazing event title..."
msgstr "Tiêu đề sự kiện tuyệt vời của tôi ..."

#: src/components/common/GlobalMenu/index.tsx:27
msgid "My Profile"
msgstr "Hồ sơ của tôi"

#: src/components/common/QuestionAndAnswerList/index.tsx:178
msgid "N/A"
msgstr "Không áp dụng"

#: src/components/common/WidgetEditor/index.tsx:354
msgid "Nam placerat elementum..."
msgstr "Nam placerat elementum..."

#: src/components/common/AttendeeDetails/index.tsx:13
#: src/components/common/AttendeeTable/index.tsx:108
#: src/components/common/OrderDetails/index.tsx:23
#: src/components/forms/CapaciyAssigmentForm/index.tsx:37
#: src/components/forms/CheckInListForm/index.tsx:20
#: src/components/forms/ProductForm/index.tsx:253
#: src/components/forms/TaxAndFeeForm/index.tsx:61
#: src/components/modals/CreateEventModal/index.tsx:129
#: src/components/modals/DuplicateEventModal/index.tsx:78
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:63
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:72
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:107
#: src/components/routes/welcome/index.tsx:83
msgid "Name"
msgstr "Tên"

#: src/components/modals/CreateEventModal/index.tsx:38
msgid "Name should be less than 150 characters"
msgstr "Tên phải nhỏ hơn 150 ký tự"

#: src/components/common/QuestionAndAnswerList/index.tsx:181
#: src/components/common/QuestionAndAnswerList/index.tsx:289
msgid "Navigate to Attendee"
msgstr "Đi tới người tham dự"

#: src/components/common/PromoCodeTable/index.tsx:158
#: src/components/common/WebhookTable/index.tsx:266
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Never"
msgstr "Không bao giờ"

#: src/components/routes/auth/AcceptInvitation/index.tsx:111
#: src/components/routes/auth/ResetPassword/index.tsx:59
#: src/components/routes/profile/ManageProfile/index.tsx:195
msgid "New Password"
msgstr "Mật khẩu mới"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "No"
msgstr "Không"

#: src/components/common/QuestionAndAnswerList/index.tsx:104
#~ msgid "No {0} available."
#~ msgstr "Không {0} Có sẵn."

#: src/components/routes/event/Webhooks/index.tsx:22
msgid "No Active Webhooks"
msgstr "Không có Webhook hoạt động"

#: src/components/common/NoEventsBlankSlate/index.tsx:21
msgid "No archived events to show."
msgstr "Không có sự kiện lưu trữ để hiển thị."

#: src/components/common/AttendeeList/index.tsx:23
msgid "No attendees found for this order."
msgstr "Không có người tham dự tìm thấy cho đơn hàng này."

#: src/components/modals/ManageOrderModal/index.tsx:132
msgid "No attendees have been added to this order."
msgstr "Không có người tham dự đã được thêm vào đơn hàng này."

#: src/components/common/AttendeeTable/index.tsx:56
msgid "No Attendees to show"
msgstr "Không có người tham dự để hiển thị"

#: src/components/forms/CheckInListForm/index.tsx:48
msgid "No attendees will be able to check in before this date using this list"
msgstr "Không người tham dự nào có thể check-in trước ngày này bằng danh sách này"

#: src/components/common/CapacityAssignmentList/index.tsx:42
msgid "No Capacity Assignments"
msgstr "Không có phân bổ sức chứa"

#: src/components/common/CheckInListList/index.tsx:44
msgid "No Check-In Lists"
msgstr "Không có danh sách check-in"

#: src/components/layouts/AuthLayout/index.tsx:34
msgid "No Credit Card Required"
msgstr "Không yêu cầu thẻ tín dụng"

#: src/components/common/ReportTable/index.tsx:218
msgid "No data available"
msgstr "Không có dữ liệu có sẵn"

#: src/components/common/ReportTable/index.tsx:214
msgid "No data to show. Please select a date range"
msgstr "Không có dữ liệu để hiển thị. Vui lòng chọn khoảng thời gian"

#: src/components/forms/PromoCodeForm/index.tsx:47
msgid "No Discount"
msgstr "Không giảm giá"

#: src/components/common/NoEventsBlankSlate/index.tsx:20
msgid "No ended events to show."
msgstr "Không có sự kiện đã kết thúc để hiển thị."

#: src/components/common/NoEventsBlankSlate/index.tsx:14
msgid "No events to show"
msgstr "Không có sự kiện nào hiển thị"

#: src/components/common/FilterModal/index.tsx:230
msgid "No filters available"
msgstr "Không có bộ lọc có sẵn"

#: src/components/modals/WebhookLogsModal/index.tsx:177
msgid "No logs found"
msgstr "Không tìm thấy nhật ký"

#: src/components/common/MessageList/index.tsx:69
msgid "No messages to show"
msgstr "Không có tin nhắn nào hiển thị"

#: src/components/common/OrdersTable/index.tsx:63
msgid "No orders to show"
msgstr "Không có đơn hàng nào để hiển thị"

#: src/components/routes/product-widget/Payment/index.tsx:76
msgid "No payment methods are currently available. Please contact the event organizer for assistance."
msgstr "Hiện không có phương thức thanh toán. Vui lòng liên hệ ban tổ chức sự kiện để được hỗ trợ."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:39
msgid "No Payment Required"
msgstr "Không cần thanh toán"

#: src/components/modals/ManageAttendeeModal/index.tsx:177
msgid "No product associated with this attendee."
msgstr "Không có sản phẩm liên quan đến người tham dự này."

#: src/components/common/ProductSelector/index.tsx:33
msgid "No products available for selection"
msgstr "Không có sản phẩm nào có sẵn để lựa chọn"

#: src/components/modals/CreateProductCategoryModal/index.tsx:22
msgid "No products available in this category."
msgstr "Không có sản phẩm có sẵn trong danh mục này."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:39
msgid "No Products Yet"
msgstr "Chưa có sản phẩm"

#: src/components/common/PromoCodeTable/index.tsx:46
msgid "No Promo Codes to show"
msgstr "Không có mã khuyến mãi để hiển thị"

#: src/components/modals/ManageAttendeeModal/index.tsx:193
msgid "No questions answered by this attendee."
msgstr "Không có câu hỏi nào được trả lời bởi người tham dự này."

#: src/components/modals/ManageOrderModal/index.tsx:119
msgid "No questions have been asked for this order."
msgstr "Không có câu hỏi nào được hỏi cho đơn hàng này."

#: src/components/common/WebhookTable/index.tsx:174
msgid "No response"
msgstr "Không có phản hồi"

#: src/components/common/WebhookTable/index.tsx:141
msgid "No responses yet"
msgstr "Chưa có phản hồi"

#: src/components/common/NoResultsSplash/index.tsx:24
msgid "No results"
msgstr "Không có kết quả"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:20
msgid "No Search Results"
msgstr "Không có kết quả tìm kiếm"

#: src/components/common/NoResultsSplash/index.tsx:29
msgid "No search results."
msgstr "Không có kết quả tìm kiếm."

#: src/components/common/TaxAndFeeList/index.tsx:101
msgid "No Taxes or Fees have been added."
msgstr "Không có thuế hoặc phí đã được thêm vào."

#: src/components/modals/WebhookLogsModal/index.tsx:180
msgid "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."
msgstr "Chưa có sự kiện webhook nào được ghi nhận cho điểm cuối này. Sự kiện sẽ xuất hiện ở đây khi chúng được kích hoạt."

#: src/components/common/WebhookTable/index.tsx:201
msgid "No Webhooks"
msgstr "Không có webhooks"

#: src/components/common/PromoCodeTable/index.tsx:82
msgid "None"
msgstr "Không có"

#: src/components/common/ProductPriceAvailability/index.tsx:27
#: src/components/common/ProductPriceAvailability/index.tsx:48
msgid "Not available"
msgstr "Không có sẵn"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "Not On Sale"
msgstr "Không được bán"

#: src/components/modals/ManageAttendeeModal/index.tsx:130
#: src/components/modals/ManageOrderModal/index.tsx:164
msgid "Notes"
msgstr "Ghi chú"

#: src/components/common/ReportTable/index.tsx:240
msgid "Nothing to show yet"
msgstr "Chưa có gì để hiển thị"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:72
msgid "Notification Settings"
msgstr "Cài đặt thông báo"

#: src/components/modals/RefundOrderModal/index.tsx:108
msgid "Notify buyer of refund"
msgstr "Thông báo cho người mua về hoàn tiền"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:75
msgid "Notify organizer of new orders"
msgstr "Thông báo cho ban tổ chức các đơn hàng mới"

#: src/components/routes/welcome/index.tsx:72
msgid "Now let's create your first event"
msgstr "Bây giờ hãy tạo sự kiện đầu tiên của bạn"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:217
msgid "Number of days allowed for payment (leave blank to omit payment terms from invoices)"
msgstr "Số ngày quá hạn thanh toán (để trống để bỏ qua các điều khoản thanh toán từ hóa đơn)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:199
msgid "Number Prefix"
msgstr "Tiền tố số"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:145
msgid "Offline orders are not reflected in event statistics until the order is marked as paid."
msgstr "Các đơn hàng ngoại tuyến không được phản ánh trong thống kê sự kiện cho đến khi đơn hàng được đánh dấu là được thanh toán."

#: src/components/routes/product-widget/Payment/index.tsx:66
msgid "Offline payment failed. Please try again or contact the event organizer."
msgstr "Thanh toán offline không thành công. Vui lòng thử loại hoặc liên hệ với ban tổ chức sự kiện."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:151
msgid "Offline Payment Instructions"
msgstr "Hướng dẫn thanh toán offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:90
msgid "Offline Payments"
msgstr "Thanh toán offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:139
msgid "Offline Payments Information"
msgstr "Thông tin thanh toán offline"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:134
msgid "Offline Payments Settings"
msgstr "Cài đặt thanh toán offline"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:71
msgid "On sale"
msgstr "Đang Bán"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "On Sale"
msgstr "Đang Bán"

#: src/components/common/NoEventsBlankSlate/index.tsx:19
msgid "Once you create an event, you'll see it here."
msgstr "Khi bạn tạo một sự kiện, bạn sẽ thấy nó ở đây."

#: src/components/common/ReportTable/index.tsx:245
msgid "Once you start collecting data, you'll see it here."
msgstr "Khi bạn bắt đầu thu thập dữ liệu, bạn sẽ thấy nó ở đây."

#: src/components/routes/event/GettingStarted/index.tsx:179
msgid "Once you're ready, set your event live and start selling products."
msgstr "Khi bạn đã sẵn sàng, hãy đặt sự kiện của bạn trực tiếp và bắt đầu bán sản phẩm."

#: src/components/common/EventStatusBadge/index.tsx:18
msgid "Ongoing"
msgstr "Đang diễn ra"

#: src/components/common/EventCard/index.tsx:118
msgid "Online event"
msgstr "Sự kiện trực tuyến"

#: src/components/common/OnlineEventDetails/index.tsx:9
msgid "Online Event Details"
msgstr "Chi tiết sự kiện trực tuyến"

#: src/components/modals/SendMessageModal/index.tsx:279
msgid ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."
msgstr ""
"Chỉ các email quan trọng, liên quan trực tiếp đến sự kiện này, nên được gửi bằng biểu mẫu này.\n"
"Bất kì hành vi sử dụng sai, bao gồm gửi email quảng cáo, sẽ dẫn đến lệnh cấm tài khoản ngay lập tức."

#: src/components/modals/SendMessageModal/index.tsx:226
msgid "Only send to orders with these statuses"
msgstr "Chỉ gửi đến các đơn hàng có trạng thái này"

#: src/components/common/CheckInListList/index.tsx:167
msgid "Open Check-In Page"
msgstr "Mở trang Xác nhận tham dự (Check-In)"

#: src/components/layouts/Event/index.tsx:288
msgid "Open sidebar"
msgstr "Mở thanh bên"

#: src/components/forms/QuestionForm/index.tsx:45
msgid "Option {i}"
msgstr "Tùy chọn {i}"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:253
msgid "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"
msgstr "Thông tin bổ sung tùy chọn xuất hiện trên tất cả các hóa đơn (ví dụ: điều khoản thanh toán, phí thanh toán trễ, chính sách trả lại)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:200
msgid "Optional prefix for invoice numbers (e.g., INV-)"
msgstr "Tiền tố tùy chọn cho số hóa đơn (ví dụ: Inv-)"

#: src/components/forms/QuestionForm/index.tsx:28
msgid "Options"
msgstr "Tùy chọn"

#: src/components/modals/CreateEventModal/index.tsx:118
msgid "or"
msgstr "Hoặc"

#: src/components/common/AttendeeTable/index.tsx:110
msgid "Order"
msgstr "Đơn hàng"

#: src/components/forms/WebhookForm/index.tsx:76
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:63
msgid "Order Cancelled"
msgstr "Đơn hàng bị hủy"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:62
msgid "Order Completed"
msgstr "Đơn hàng đã hoàn thành"

#: src/components/forms/WebhookForm/index.tsx:52
msgid "Order Created"
msgstr "Đơn hàng được tạo"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:122
msgid "Order Date"
msgstr "Ngày đơn hàng"

#: src/components/modals/ManageAttendeeModal/index.tsx:166
#: src/components/modals/ManageOrderModal/index.tsx:86
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:266
msgid "Order Details"
msgstr "Chi tiết đơn hàng"

#: src/components/modals/CancelOrderModal/index.tsx:29
msgid "Order has been canceled and the order owner has been notified."
msgstr "Đơn hàng đã bị hủy và chủ sở hữu đơn hàng đã được thông báo."

#: src/components/common/OrdersTable/index.tsx:79
msgid "Order marked as paid"
msgstr "Đơn hàng được đánh dấu là đã thanh toán"

#: src/components/forms/WebhookForm/index.tsx:64
msgid "Order Marked as Paid"
msgstr "Đơn hàng được đánh dấu là đã trả"

#: src/components/modals/ManageOrderModal/index.tsx:92
msgid "Order Notes"
msgstr "Ghi chú đơn hàng"

#: src/components/common/MessageList/index.tsx:23
msgid "Order owner"
msgstr "Chủ sở hữu đơn hàng"

#: src/components/modals/SendMessageModal/index.tsx:191
msgid "Order owners with a specific product"
msgstr "Chủ đơn hàng có sản phẩm cụ thể"

#: src/components/common/MessageList/index.tsx:19
msgid "Order owners with products"
msgstr "Chủ đơn hàng có sản phẩm"

#: src/components/common/QuestionsTable/index.tsx:313
#: src/components/common/QuestionsTable/index.tsx:355
msgid "Order questions"
msgstr "Câu hỏi đơn hàng"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:112
msgid "Order Reference"
msgstr "Mã đơn hàng"

#: src/components/forms/WebhookForm/index.tsx:70
msgid "Order Refunded"
msgstr "Đơn hàng đã hoàn lại"

#: src/components/routes/event/orders.tsx:45
msgid "Order Status"
msgstr "Trạng thái đơn hàng"

#: src/components/modals/SendMessageModal/index.tsx:228
msgid "Order statuses"
msgstr "Trạng thái đơn hàng"

#: src/components/layouts/Checkout/CheckoutSidebar/index.tsx:28
#: src/components/modals/ManageOrderModal/index.tsx:105
msgid "Order Summary"
msgstr "Tóm tắt đơn hàng"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:90
msgid "Order timeout"
msgstr "Thời gian chờ đơn hàng"

#: src/components/forms/WebhookForm/index.tsx:58
msgid "Order Updated"
msgstr "Đơn hàng cập nhật"

#: src/components/layouts/Event/index.tsx:100
#: src/components/routes/event/orders.tsx:113
msgid "Orders"
msgstr "Đơn hàng"

#: src/components/routes/event/orders.tsx:94
msgid "Orders Exported"
msgstr "Đơn hàng đã được xuất"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:235
msgid "Organization Address"
msgstr "Địa chỉ tổ chức"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:226
msgid "Organization Details"
msgstr "Chi tiết tổ chức"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:228
msgid "Organization Name"
msgstr "Tên tổ chức"

#: src/components/modals/EditUserModal/index.tsx:55
#: src/components/modals/InviteUserModal/index.tsx:47
#: src/components/routes/events/Dashboard/index.tsx:113
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:186
msgid "Organizer"
msgstr "Ban tổ chức"

#: src/components/modals/CreateEventModal/index.tsx:41
msgid "Organizer is required"
msgstr "Người tổ chức là bắt buộc"

#: src/components/forms/OrganizerForm/index.tsx:27
msgid "Organizer Name"
msgstr "Tên ban tổ chức"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
msgid "Organizers can only manage events and products. They cannot manage users, account settings or billing information."
msgstr "Ban tổ chức chỉ có thể quản lý các sự kiện và sản phẩm. Họ không thể quản lý người dùng, tài khoản hoặc thông tin thanh toán."

#: src/components/layouts/Event/index.tsx:87
msgid "Overview"
msgstr "Tổng quan"

#: src/components/common/WidgetEditor/index.tsx:223
msgid "Padding"
msgstr "Khoảng cách lề"

#: src/components/routes/event/HomepageDesigner/index.tsx:150
msgid "Page background color"
msgstr "Màu nền trang"

#: src/components/common/ErrorDisplay/index.tsx:13
msgid "Page not found"
msgstr "Không tìm thấy trang"

#: src/components/common/StatBoxes/index.tsx:45
msgid "Page views"
msgstr "Lượt xem trang"

#: src/components/forms/ProductForm/index.tsx:358
msgid "page."
msgstr "trang."

#: src/components/modals/CreateAttendeeModal/index.tsx:180
msgid "paid"
msgstr "đã trả tiền"

#: src/components/forms/ProductForm/index.tsx:145
msgid "Paid Product"
msgstr "Sản phẩm trả phí"

#: src/components/routes/event/orders.tsx:30
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:54
msgid "Partially Refunded"
msgstr "Hoàn lại tiền một phần"

#: src/components/routes/auth/Login/index.tsx:73
#: src/components/routes/auth/Register/index.tsx:106
#: src/components/routes/profile/ManageProfile/index.tsx:109
msgid "Password"
msgstr "Mật khẩu"

#: src/components/routes/auth/AcceptInvitation/index.tsx:30
msgid "Password must be a minimum  of 8 characters"
msgstr "Mật khẩu phải tối thiểu 8 ký tự"

#: src/components/routes/auth/Register/index.tsx:36
msgid "Password must be at least 8 characters"
msgstr "Mật khẩu phải có ít nhất 8 ký tự"

#: src/components/routes/auth/ResetPassword/index.tsx:46
msgid "Password reset successfully. Please login with your new password."
msgstr "Đặt lại mật khẩu thành công. Vui lòng sử dụng mật khẩu mới để đăng nhập."

#: src/components/routes/auth/AcceptInvitation/index.tsx:31
#: src/components/routes/auth/Register/index.tsx:37
msgid "Passwords are not the same"
msgstr "Mật khẩu không giống nhau"

#: src/components/common/WidgetEditor/index.tsx:269
msgid "Paste this where you want the widget to appear."
msgstr "Dán cái này nơi bạn muốn tiện ích xuất hiện."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:104
msgid "Paste URL"
msgstr "Dán URL"

#: src/components/modals/CreateAttendeeModal/index.tsx:119
msgid "Patrick"
msgstr "Patrick"

#: src/components/modals/CreateAttendeeModal/index.tsx:133
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/forms/WebhookForm/index.tsx:25
msgid "Paused"
msgstr "Tạm dừng"

#: src/components/forms/StripeCheckoutForm/index.tsx:123
msgid "Payment"
msgstr "Thanh toán"

#: src/components/routes/event/Settings/index.tsx:64
msgid "Payment & Invoicing"
msgstr "Thanh toán & Hoá đơn"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:98
msgid "Payment & Invoicing Settings"
msgstr "Cài đặt Thanh toán & Hoá đơn"

#: src/components/routes/account/ManageAccount/index.tsx:38
msgid "Payment & Plan"
msgstr "Thanh toán & Gói"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:216
msgid "Payment Due Period"
msgstr "Thời hạn thanh toán"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:41
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:64
msgid "Payment Failed"
msgstr "Thanh toán thất bại"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:228
#: src/components/routes/product-widget/Payment/PaymentMethods/Offline/index.tsx:14
msgid "Payment Instructions"
msgstr "Hướng dẫn thanh toán"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:105
msgid "Payment Methods"
msgstr "Phương thức thanh toán"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:138
msgid "Payment Processing"
msgstr "Thanh toán đang xử lý"

#: src/components/common/OrderDetails/index.tsx:76
msgid "Payment provider"
msgstr "Nhà cung cấp Thanh toán"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:42
msgid "Payment Received"
msgstr "Thanh toán đã nhận"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:240
msgid "Payment Settings"
msgstr "Cài đặt thanh toán"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:135
msgid "Payment Status"
msgstr "Tình trạng thanh toán"

#: src/components/forms/StripeCheckoutForm/index.tsx:60
msgid "Payment succeeded!"
msgstr "Thanh toán thành công!"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:214
msgid "Payment Terms"
msgstr "Điều khoản thanh toán"

#: src/components/forms/PromoCodeForm/index.tsx:51
#: src/components/forms/TaxAndFeeForm/index.tsx:27
msgid "Percentage"
msgstr "Tỷ lệ phần trăm"

#: src/components/forms/TaxAndFeeForm/index.tsx:71
msgid "Percentage Amount"
msgstr "Tỷ lệ phần trăm"

#: src/components/routes/product-widget/Payment/index.tsx:122
msgid "Place Order"
msgstr "Đặt hàng"

#: src/components/common/WidgetEditor/index.tsx:257
msgid "Place this in the <head> of your website."
msgstr "Đặt cái này vào <Head> của trang web của bạn."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:40
msgid "Platform Fees"
msgstr "Phí nền tảng"

#: src/components/forms/QuestionForm/index.tsx:31
msgid "Please add at least one option"
msgstr "Vui lòng thêm ít nhất một tùy chọn"

#: src/hooks/useFormErrorResponseHandler.tsx:18
msgid "Please check the provided information is correct"
msgstr "Vui lòng kiểm tra thông tin được cung cấp là chính xác"

#: src/components/routes/auth/Login/index.tsx:41
msgid "Please check your email and password and try again"
msgstr "Vui lòng kiểm tra email và mật khẩu của bạn và thử lại"

#: src/components/routes/auth/AcceptInvitation/index.tsx:32
#: src/components/routes/auth/Register/index.tsx:38
msgid "Please check your email is valid"
msgstr "Vui lòng kiểm tra email của bạn là hợp lệ"

#: src/components/routes/profile/ManageProfile/index.tsx:152
msgid "Please check your email to confirm your email address"
msgstr "Vui lòng kiểm tra email của bạn để xác nhận địa chỉ email của bạn"

#: src/components/routes/auth/AcceptInvitation/index.tsx:86
msgid "Please complete the form below to accept your invitation"
msgstr "Vui lòng điền vào biểu mẫu bên dưới để chấp nhận lời mời của bạn"

#: src/components/routes/product-widget/SelectProducts/index.tsx:306
msgid "Please continue in the new tab"
msgstr "Vui lòng tiếp tục trong tab mới"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:47
msgid "Please create a product"
msgstr "Vui lòng tạo một sản phẩm"

#: src/components/modals/CreateCheckInListModal/index.tsx:51
msgid "Please create a ticket"
msgstr "Vui lòng tạo vé"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:45
msgid "Please enter a valid image URL that points to an image."
msgstr "Vui lòng nhập URL hình ảnh hợp lệ trỏ đến một hình ảnh."

#: src/components/modals/CreateWebhookModal/index.tsx:30
msgid "Please enter a valid URL"
msgstr "Vui lòng nhập URL hợp lệ"

#: src/components/routes/auth/ResetPassword/index.tsx:55
msgid "Please enter your new password"
msgstr "Vui lòng nhập mật khẩu mới của bạn"

#: src/components/modals/CancelOrderModal/index.tsx:52
msgid "Please Note"
msgstr "Xin lưu ý"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:34
msgid "Please provide an image."
msgstr "Vui lòng cung cấp một hình ảnh."

#: src/components/layouts/Checkout/index.tsx:159
msgid "Please return to the event page to start over."
msgstr "Vui lòng quay lại trang sự kiện để bắt đầu lại."

#: src/components/modals/SendMessageModal/index.tsx:195
msgid "Please select"
msgstr "Vui lòng chọn"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:53
msgid "Please select an image."
msgstr "Vui lòng chọn một hình ảnh."

#: src/components/routes/product-widget/SelectProducts/index.tsx:237
msgid "Please select at least one product"
msgstr "Vui lòng chọn ít nhất một sản phẩm"

#: src/components/routes/event/attendees.tsx:49
#: src/components/routes/event/orders.tsx:100
msgid "Please try again."
msgstr "Vui lòng thử lại."

#: src/components/routes/profile/ManageProfile/index.tsx:142
msgid "Please verify your email address to access all features"
msgstr "Vui lòng xác minh địa chỉ email của bạn để truy cập tất cả các tính năng"

#: src/components/routes/event/attendees.tsx:40
msgid "Please wait while we prepare your attendees for export..."
msgstr "Vui lòng đợi trong khi chúng tôi chuẩn bị cho người tham dự xuất ra..."

#: src/components/common/OrdersTable/index.tsx:112
#: src/components/layouts/Checkout/index.tsx:50
msgid "Please wait while we prepare your invoice..."
msgstr "Vui lòng đợi trong khi chúng tôi chuẩn bị hóa đơn của bạn ..."

#: src/components/routes/event/orders.tsx:91
msgid "Please wait while we prepare your orders for export..."
msgstr "Vui lòng đợi trong khi chúng tôi chuẩn bị đơn hàng của bạn để xuất ra..."

#: src/components/common/LanguageSwitcher/index.tsx:26
msgid "Portuguese"
msgstr "Tiếng Bồ Đào Nha"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:74
msgid "Post Checkout message"
msgstr "Tin nhắn sau phần thanh toán"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Powered by"
msgstr "Được cung cấp bởi"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:67
msgid "Pre Checkout message"
msgstr "Thông báo trước khi thanh toán"

#: src/components/common/QuestionsTable/index.tsx:347
msgid "Preview"
msgstr "Xem trước"

#: src/components/layouts/Event/index.tsx:205
#: src/components/layouts/Event/index.tsx:209
msgid "Preview Event page"
msgstr "Xem trước trang sự kiện"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:236
#: src/components/forms/ProductForm/index.tsx:73
#: src/components/forms/ProductForm/index.tsx:285
msgid "Price"
msgstr "Giá"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:78
msgid "Price display mode"
msgstr "Chế độ hiển thị giá"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:88
msgid "Price not set"
msgstr "Giá không được đặt"

#: src/components/forms/ProductForm/index.tsx:322
msgid "Price tiers"
msgstr "Hạng giá"

#: src/components/forms/ProductForm/index.tsx:228
msgid "Price Type"
msgstr "Loại giá"

#: src/components/common/WidgetEditor/index.tsx:182
msgid "Primary Color"
msgstr "Màu sắc chính"

#: src/components/routes/event/HomepageDesigner/index.tsx:156
msgid "Primary Colour"
msgstr "Màu sắc chính"

#: src/components/common/WidgetEditor/index.tsx:190
#: src/components/routes/event/HomepageDesigner/index.tsx:158
msgid "Primary Text Color"
msgstr "Màu văn bản chính"

#: src/components/common/AttendeeTicket/index.tsx:81
msgid "Print"
msgstr "In"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:288
msgid "Print All Tickets"
msgstr "In tất cả vé"

#: src/components/layouts/Checkout/index.tsx:114
msgid "Print Tickets"
msgstr "In vé"

#: src/components/common/AttendeeDetails/index.tsx:37
#: src/components/modals/ManageAttendeeModal/index.tsx:119
msgid "Product"
msgstr "Sản phẩm"

#: src/components/forms/ProductForm/index.tsx:266
msgid "Product Category"
msgstr "Danh mục sản phẩm"

#: src/components/modals/EditProductCategoryModal/index.tsx:53
msgid "Product category updated successfully."
msgstr "Danh mục sản phẩm được cập nhật thành công."

#: src/components/forms/WebhookForm/index.tsx:34
msgid "Product Created"
msgstr "Sản phẩm được tạo ra"

#: src/components/forms/WebhookForm/index.tsx:46
msgid "Product Deleted"
msgstr "Xóa sản phẩm"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:55
msgid "Product deleted successfully"
msgstr "Sản phẩm đã xóa thành công"

#: src/components/forms/ProductForm/index.tsx:236
msgid "Product Price Type"
msgstr "Loại giá sản phẩm"

#: src/components/common/QuestionsTable/index.tsx:328
msgid "Product questions"
msgstr "Câu hỏi sản phẩm"

#: src/components/routes/event/EventDashboard/index.tsx:217
#: src/components/routes/event/Reports/index.tsx:17
msgid "Product Sales"
msgstr "Bán Hàng"

#: src/components/routes/event/Reports/index.tsx:18
msgid "Product sales, revenue, and tax breakdown"
msgstr "Chi tiết doanh số sản phẩm, doanh thu và thuế"

#: src/components/common/ProductSelector/index.tsx:63
msgid "Product Tier"
msgstr "Cấp sản phẩm"

#: src/components/forms/ProductForm/index.tsx:214
#: src/components/forms/ProductForm/index.tsx:221
msgid "Product Type"
msgstr "Loại sản phẩm"

#: src/components/forms/WebhookForm/index.tsx:40
msgid "Product Updated"
msgstr "Cập nhật sản phẩm"

#: src/components/common/WidgetEditor/index.tsx:313
msgid "Product Widget Preview"
msgstr "Xem trước tiện ích sản phẩm"

#: src/components/common/PromoCodeTable/index.tsx:148
msgid "Product(s)"
msgstr "Sản phẩm(s)"

#: src/components/common/PromoCodeTable/index.tsx:72
msgid "Products"
msgstr "Sản phẩm"

#: src/components/common/EventCard/index.tsx:126
msgid "products sold"
msgstr "Sản phẩm đã bán"

#: src/components/common/StatBoxes/index.tsx:21
msgid "Products sold"
msgstr "Sản phẩm đã bán"

#: src/components/routes/event/EventDashboard/index.tsx:238
msgid "Products Sold"
msgstr "Sản phẩm đã bán"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:178
msgid "Products sorted successfully"
msgstr "Sản phẩm được sắp xếp thành công"

#: src/components/layouts/AuthLayout/index.tsx:43
msgid "Products, merchandise, and flexible pricing options"
msgstr "Sản phẩm, Hàng hóa và Tùy chọn định giá linh hoạt"

#: src/components/routes/profile/ManageProfile/index.tsx:106
msgid "Profile"
msgstr "Hồ sơ"

#: src/components/routes/profile/ManageProfile/index.tsx:59
msgid "Profile updated successfully"
msgstr "Hồ sơ cập nhật thành công"

#: src/components/routes/product-widget/SelectProducts/index.tsx:178
msgid "Promo {promo_code} code applied"
msgstr "Mã {Promo_code} đã được áp dụng"

#: src/components/common/OrderDetails/index.tsx:86
msgid "Promo code"
msgstr "Mã khuyến mãi"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:20
msgid "Promo Code"
msgstr "Mã khuyến mãi"

#: src/components/forms/ProductForm/index.tsx:409
msgid "Promo Code page"
msgstr "Trang mã khuyến mãi"

#: src/components/routes/event/Reports/index.tsx:30
msgid "Promo code usage and discount breakdown"
msgstr "Chi tiết sử dụng mã khuyến mãi và giảm giá"

#: src/components/layouts/Event/index.tsx:106
#: src/components/routes/event/promo-codes.tsx:31
msgid "Promo Codes"
msgstr "Mã khuyến mãi"

#: src/components/common/PromoCodeTable/index.tsx:51
msgid "Promo codes can be used to offer discounts, presale access, or provide special access to your event."
msgstr "Mã khuyến mãi có thể được sử dụng để giảm giá, truy cập bán trước hoặc cung cấp quyền truy cập đặc biệt vào sự kiện của bạn."

#: src/components/routes/event/Reports/index.tsx:29
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:95
msgid "Promo Codes Report"
msgstr "Báo cáo mã khuyến mãi"

#: src/components/forms/QuestionForm/index.tsx:189
msgid ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."
msgstr ""

#: src/components/routes/event/EventDashboard/index.tsx:159
msgid "Publish Event"
msgstr "Công bố sự kiện"

#: src/components/modals/ShareModal/index.tsx:126
msgid "QR Code"
msgstr "Mã QR"

#: src/components/layouts/AuthLayout/index.tsx:64
msgid "QR code scanning with instant feedback and secure sharing for staff access"
msgstr "Quét mã QR với phản hồi tức thì và chia sẻ an toàn cho quyền truy cập nhân viên"

#: src/components/forms/ProductForm/index.tsx:85
#: src/components/forms/ProductForm/index.tsx:302
msgid "Quantity Available"
msgstr "Số lượng có sẵn"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
msgid "Quantity Sold"
msgstr "Số lượng bán"

#: src/components/common/QuestionsTable/index.tsx:170
msgid "Question deleted"
msgstr "Câu hỏi bị xóa"

#: src/components/forms/QuestionForm/index.tsx:188
msgid "Question Description"
msgstr "Mô tả câu hỏi"

#: src/components/forms/QuestionForm/index.tsx:178
msgid "Question Title"
msgstr "Tiêu đề câu hỏi"

#: src/components/common/QuestionsTable/index.tsx:275
#: src/components/layouts/Event/index.tsx:102
msgid "Questions"
msgstr "Câu hỏi"

#: src/components/modals/ManageAttendeeModal/index.tsx:184
#: src/components/modals/ManageOrderModal/index.tsx:111
msgid "Questions & Answers"
msgstr "Câu hỏi"

#: src/components/common/QuestionsTable/index.tsx:145
msgid "Questions sorted successfully"
msgstr "Câu hỏi được sắp xếp thành công"

#: src/components/forms/QuestionForm/index.tsx:118
msgid "Radio Option"
msgstr "Tùy chọn radio"

#: src/components/common/MessageList/index.tsx:59
msgid "Read less"
msgstr "Đọc ít hơn"

#: src/components/modals/SendMessageModal/index.tsx:36
msgid "Recipient"
msgstr "Người nhận"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:110
#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:195
msgid "Redirecting to Stripe..."
msgstr "Đang chuyển hướng đến Stripe..."

#: src/components/common/OrdersTable/index.tsx:204
#: src/components/common/OrdersTable/index.tsx:274
msgid "Reference"
msgstr "Mã tham chiếu"

#: src/components/modals/RefundOrderModal/index.tsx:104
msgid "Refund amount ({0})"
msgstr "Số tiền hoàn lại ({0})"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:52
msgid "Refund Failed"
msgstr "Hoàn tiền không thành công"

#: src/components/common/OrdersTable/index.tsx:172
msgid "Refund order"
msgstr "Lệnh hoàn trả"

#: src/components/modals/RefundOrderModal/index.tsx:151
msgid "Refund Order"
msgstr "Lệnh hoàn trả"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:51
msgid "Refund Pending"
msgstr "Hoàn tiền chờ xử lý"

#: src/components/routes/event/orders.tsx:51
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:128
msgid "Refund Status"
msgstr "Trạng thái hoàn trả"

#: src/components/common/OrderSummary/index.tsx:80
#: src/components/common/StatBoxes/index.tsx:33
#: src/components/routes/event/orders.tsx:29
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:53
msgid "Refunded"
msgstr "Đã hoàn lại"

#: src/components/routes/auth/Register/index.tsx:129
msgid "Register"
msgstr "Đăng ký"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:82
msgid "Remaining Uses"
msgstr "Sử dụng còn lại"

#: src/components/routes/product-widget/SelectProducts/index.tsx:504
msgid "remove"
msgstr "Loại bỏ"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:140
#: src/components/routes/product-widget/SelectProducts/index.tsx:505
msgid "Remove"
msgstr "Hủy bỏ"

#: src/components/layouts/Event/index.tsx:92
#: src/components/routes/event/Reports/index.tsx:39
msgid "Reports"
msgstr "Báo cáo"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:169
msgid "Require Billing Address"
msgstr "Yêu cầu địa chỉ thanh toán"

#: src/components/routes/event/GettingStarted/index.tsx:197
msgid "Resend confirmation email"
msgstr "Gửi lại email xác nhận"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resend email confirmation"
msgstr "Gửi lại xác nhận email"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Resend invitation"
msgstr "Gửi lại lời mời"

#: src/components/common/OrdersTable/index.tsx:179
msgid "Resend order email"
msgstr "Gửi lại email đơn hàng"

#: src/components/common/AttendeeTable/index.tsx:171
msgid "Resend ticket email"
msgstr "Gửi lại email vé"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resending..."
msgstr "Đang gửi lại ..."

#: src/components/common/FilterModal/index.tsx:248
msgid "Reset"
msgstr "Đặt lại"

#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Reset password"
msgstr "Đặt lại mật khẩu"

#: src/components/routes/auth/ResetPassword/index.tsx:54
msgid "Reset Password"
msgstr "Đặt lại mật khẩu"

#: src/components/routes/auth/ForgotPassword/index.tsx:50
msgid "Reset your password"
msgstr "Đặt lại mật khẩu của bạn"

#: src/components/common/EventCard/index.tsx:167
msgid "Restore event"
msgstr "Khôi phục sự kiện"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:50
#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:63
msgid "Return to event page"
msgstr "Trở lại trang sự kiện"

#: src/components/layouts/Checkout/index.tsx:165
msgid "Return to Event Page"
msgstr "Trở lại trang sự kiện"

#: src/components/routes/event/EventDashboard/index.tsx:249
msgid "Revenue"
msgstr "Doanh thu"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Revoke invitation"
msgstr "Thu hồi lời mời"

#: src/components/modals/EditUserModal/index.tsx:93
#: src/components/modals/InviteUserModal/index.tsx:64
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Role"
msgstr "Vai trò"

#: src/components/forms/ProductForm/index.tsx:96
#: src/components/forms/ProductForm/index.tsx:391
msgid "Sale End Date"
msgstr "Ngày bán kết thúc"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:75
msgid "Sale ended"
msgstr "Kết thúc bán hàng"

#: src/components/forms/ProductForm/index.tsx:91
#: src/components/forms/ProductForm/index.tsx:389
msgid "Sale Start Date"
msgstr "Ngày bắt đầu bán hàng"

#: src/components/common/ProductPriceAvailability/index.tsx:13
#: src/components/common/ProductPriceAvailability/index.tsx:35
msgid "Sales ended"
msgstr "Bán hàng kết thúc"

#: src/components/common/ProductPriceAvailability/index.tsx:19
#: src/components/common/ProductPriceAvailability/index.tsx:40
msgid "Sales start"
msgstr "Bán hàng bắt đầu"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:135
msgid "San Francisco"
msgstr "San Francisco"

#: src/components/common/QuestionAndAnswerList/index.tsx:142
#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:80
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:114
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:96
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:166
#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:94
#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:265
#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:85
msgid "Save"
msgstr "Lưu"

#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/event/HomepageDesigner/index.tsx:166
msgid "Save Changes"
msgstr "Lưu thay đổi"

#: src/components/modals/EditOrganizerModal/index.tsx:70
msgid "Save Organizer"
msgstr "Lưu tổ chức"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:90
msgid "Save Settings"
msgstr "Lưu cài đặt"

#: src/components/layouts/CheckIn/index.tsx:398
#: src/components/layouts/CheckIn/index.tsx:400
msgid "Scan QR Code"
msgstr "Quét mã QR"

#: src/components/modals/ShareModal/index.tsx:229
msgid "Scan this QR code to access the event page or share it with others"
msgstr "Quét mã QR này để truy cập trang sự kiện hoặc chia sẻ nó với những người khác"

#: src/components/routes/event/attendees.tsx:64
msgid "Search by attendee name, email or order #..."
msgstr "Tìm kiếm theo tên người tham dự, email hoặc đơn hàng"

#: src/components/routes/events/Dashboard/index.tsx:69
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:66
msgid "Search by event name..."
msgstr "Tìm kiếm theo tên sự kiện"

#: src/components/routes/event/orders.tsx:126
msgid "Search by name, email, or order #..."
msgstr "Tìm kiếm theo tên, email, hoặc mã đơn hàng #"

#: src/components/layouts/CheckIn/index.tsx:394
msgid "Search by name, order #, attendee # or email..."
msgstr "Tìm kiếm theo tên, mã đơn hàng #, người tham dự, hoặc email..."

#: src/components/routes/event/promo-codes.tsx:34
msgid "Search by name..."
msgstr "Tìm kiếm theo tên..."

#: src/components/routes/event/messages.tsx:34
msgid "Search by subject or content..."
msgstr "Tìm kiếm theo chủ đề hoặc nội dung..."

#: src/components/routes/event/CapacityAssignments/index.tsx:37
msgid "Search capacity assignments..."
msgstr "Tìm kiếm phân bổ sức chứa..."

#: src/components/routes/event/CheckInLists/index.tsx:37
msgid "Search check-in lists..."
msgstr "Tìm kiếm danh sách check-in..."

#: src/components/routes/event/products.tsx:52
msgid "Search products"
msgstr "Tìm kiếm sản phẩm"

#: src/components/common/SearchBar/index.tsx:36
msgid "Search..."
msgstr "Tìm kiếm"

#: src/components/routes/event/HomepageDesigner/index.tsx:160
msgid "Secondary color"
msgstr "Màu phụ"

#: src/components/common/WidgetEditor/index.tsx:198
msgid "Secondary Color"
msgstr "Màu phụ"

#: src/components/routes/event/HomepageDesigner/index.tsx:162
msgid "Secondary text color"
msgstr "Màu chữ phụ"

#: src/components/common/WidgetEditor/index.tsx:206
msgid "Secondary Text Color"
msgstr "Màu chữ phụ"

#: src/components/common/FilterModal/index.tsx:150
msgid "Select {0}"
msgstr "Chọn {0}"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:214
msgid "Select Camera"
msgstr "Chọn máy ảnh"

#: src/components/forms/ProductForm/index.tsx:269
msgid "Select category..."
msgstr "Chọn danh mục..."

#: src/components/forms/WebhookForm/index.tsx:124
msgid "Select event types"
msgstr "Chọn loại sự kiện"

#: src/components/modals/CreateEventModal/index.tsx:110
msgid "Select organizer"
msgstr "Chọn nhà tổ chức"

#: src/components/modals/ManageAttendeeModal/index.tsx:118
msgid "Select Product"
msgstr "Chọn sản phẩm"

#: src/components/common/ProductSelector/index.tsx:65
msgid "Select Product Tier"
msgstr "Chọn bậc sản phẩm"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:49
#: src/components/modals/SendMessageModal/index.tsx:219
msgid "Select products"
msgstr "Chọn sản phẩm"

#: src/components/modals/EditUserModal/index.tsx:104
msgid "Select status"
msgstr "Chọn trạng thái"

#: src/components/modals/CreateAttendeeModal/index.tsx:150
msgid "Select Ticket"
msgstr "Chọn Vé"

#: src/components/forms/CheckInListForm/index.tsx:26
#: src/components/modals/SendMessageModal/index.tsx:207
msgid "Select tickets"
msgstr "Chọn Vé"

#: src/components/common/ReportTable/index.tsx:261
msgid "Select time period"
msgstr "Chọn khoảng thời gian"

#: src/components/forms/WebhookForm/index.tsx:123
msgid "Select which events will trigger this webhook"
msgstr "Chọn những sự kiện nào sẽ kích hoạt webhook này"

#: src/components/forms/ProductForm/index.tsx:352
msgid "Select..."
msgstr "Chọn ..."

#: src/components/layouts/AuthLayout/index.tsx:68
msgid "Sell Anything"
msgstr "Bán bất cứ thứ gì"

#: src/components/layouts/AuthLayout/index.tsx:69
msgid "Sell merchandise alongside tickets with integrated tax and promo code support"
msgstr "Bán hàng hóa cùng với vé, hỗ trợ thuế và mã khuyến mãi tích hợp"

#: src/components/layouts/AuthLayout/index.tsx:42
msgid "Sell More Than Tickets"
msgstr "Bán nhiều hơn vé"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send"
msgstr "Gửi"

#: src/components/modals/SendMessageModal/index.tsx:259
msgid "Send a copy to <0>{0}</0>"
msgstr "Gửi một bản sao đến <0>{0}</0>"

#: src/components/modals/SendMessageModal/index.tsx:139
msgid "Send a message"
msgstr "Gửi tin nhắn"

#: src/components/modals/SendMessageModal/index.tsx:269
msgid "Send as a test. This will send the message to your email address instead of the recipients."
msgstr "Gửi dưới dạng thử nghiệm. Thông báo sẽ được gửi đến địa chỉ email của bạn thay vì người nhận."

#: src/components/routes/event/messages.tsx:41
msgid "Send Message"
msgstr "Gửi tin nhắn"

#: src/components/modals/CreateAttendeeModal/index.tsx:192
msgid "Send order confirmation and ticket email"
msgstr "Gửi email xác nhận và vé"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send Test"
msgstr "Gửi Test"

#: src/components/routes/event/Settings/index.tsx:46
msgid "SEO"
msgstr "SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:70
msgid "SEO Description"
msgstr "Mô tả SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:76
msgid "SEO Keywords"
msgstr "Từ khóa SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:56
msgid "SEO Settings"
msgstr "Cài đặt SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:64
msgid "SEO Title"
msgstr "Tiêu đề SEO"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "Service Fee"
msgstr "Phí dịch vụ"

#: src/components/forms/ProductForm/index.tsx:159
msgid "Set a minimum price and let users pay more if they choose"
msgstr "Đặt giá tối thiểu và cho phép người dùng thanh toán nhiều hơn nếu họ chọn"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:207
msgid "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."
msgstr "Đặt số bắt đầu cho hóa đơn. Sau khi hóa đơn được tạo, số này không thể thay đổi."

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Set up your event"
msgstr "Thiết lập sự kiện của bạn"

#: src/components/routes/event/EventDashboard/index.tsx:190
#~ msgid "Set up your payment processing to receive funds from ticket sales."
#~ msgstr "Thiết lập xử lý thanh toán để nhận tiền từ việc bán vé."

#: src/components/routes/event/GettingStarted/index.tsx:183
msgid "Set your event live"
msgstr "Đặt sự kiện của bạn trực tiếp"

#: src/components/layouts/Event/index.tsx:98
#: src/components/routes/event/Settings/index.tsx:103
msgid "Settings"
msgstr "Cài đặt"

#: src/components/layouts/AuthLayout/index.tsx:26
msgid "Setup in Minutes"
msgstr "Thiết lập trong vài phút"

#: src/components/common/ShareIcon/index.tsx:28
#: src/components/modals/ShareModal/index.tsx:123
#: src/components/modals/ShareModal/index.tsx:141
msgid "Share"
msgstr "Chia sẻ"

#: src/components/layouts/Event/index.tsx:251
#: src/components/modals/ShareModal/index.tsx:116
msgid "Share Event"
msgstr "Chia sẻ sự kiện"

#: src/components/modals/ShareModal/index.tsx:169
msgid "Share to Facebook"
msgstr "Chia sẻ lên Facebook"

#: src/components/modals/ShareModal/index.tsx:163
msgid "Share to LinkedIn"
msgstr "Chia sẻ với LinkedIn"

#: src/components/modals/ShareModal/index.tsx:193
msgid "Share to Pinterest"
msgstr "Chia sẻ với Pinterest"

#: src/components/modals/ShareModal/index.tsx:187
msgid "Share to Reddit"
msgstr "Chia sẻ với Reddit"

#: src/components/modals/ShareModal/index.tsx:148
msgid "Share to Social"
msgstr "Chia sẻ với xã hội"

#: src/components/modals/ShareModal/index.tsx:181
msgid "Share to Telegram"
msgstr "Chia sẻ với Telegram"

#: src/components/modals/ShareModal/index.tsx:175
msgid "Share to WhatsApp"
msgstr "Chia sẻ với WhatsApp"

#: src/components/modals/ShareModal/index.tsx:157
msgid "Share to X"
msgstr "Chia sẻ với X.com"

#: src/components/modals/ShareModal/index.tsx:199
msgid "Share via Email"
msgstr "Chia sẻ qua email"

#: src/components/forms/ProductForm/index.tsx:344
msgid "Show"
msgstr "Hiển thị"

#: src/components/forms/ProductForm/index.tsx:403
msgid "Show available product quantity"
msgstr "Hiển thị số lượng sản phẩm có sẵn"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Show hidden questions"
msgstr "Hiển thị câu hỏi ẩn"

#: src/components/routes/product-widget/SelectProducts/index.tsx:459
msgid "Show more"
msgstr "Hiển thị thêm"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:60
msgid "Show tax and fees separately"
msgstr "Hiển thị thuế và phí riêng biệt"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
msgid "Shown to the customer after they checkout, on the order summary page."
msgstr "Hiển thị cho khách hàng sau khi họ thanh toán, trên trang Tóm tắt đơn hàng."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:69
msgid "Shown to the customer before they checkout"
msgstr "Hiển thị cho khách hàng trước khi họ thanh toán"

#: src/components/forms/QuestionForm/index.tsx:132
msgid "Shows common address fields, including country"
msgstr "Hiển thị các trường địa chỉ chung, bao gồm quốc gia"

#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:151
msgid "Simpson"
msgstr "Simpson"

#: src/components/forms/QuestionForm/index.tsx:100
msgid "Single line text box"
msgstr "Hộp văn bản dòng đơn"

#: src/components/routes/welcome/index.tsx:137
msgid "Skip this step"
msgstr "Bỏ qua bước này"

#: src/components/layouts/AuthLayout/index.tsx:78
msgid "Smart Check-in"
msgstr "Check-in thông minh"

#: src/components/layouts/AuthLayout/index.tsx:53
#~ msgid "Smart Dashboard"
#~ msgstr "Bảng điều khiển thông minh"

#: src/components/routes/auth/Register/index.tsx:90
msgid "Smith"
msgstr "Smith"

#: src/components/common/ProductPriceAvailability/index.tsx:9
#: src/components/common/ProductPriceAvailability/index.tsx:32
msgid "Sold out"
msgstr "Đã bán hết"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:67
msgid "Sold Out"
msgstr "Đã bán hết"

#: src/components/common/ErrorDisplay/index.tsx:14
#: src/components/routes/auth/AcceptInvitation/index.tsx:47
msgid "Something went wrong"
msgstr "Có gì đó không ổn"

#: src/components/common/TaxAndFeeList/index.tsx:39
msgid "Something went wrong while deleting the Tax or Fee"
msgstr "Có gì đó không ổn trong khi xóa thuế hoặc phí"

#: src/components/routes/auth/ForgotPassword/index.tsx:31
msgid "Something went wrong, please try again, or contact support if the problem persists"
msgstr "Có gì đó không ổn, vui lòng thử lại hoặc liên hệ với hỗ trợ nếu vấn đề vẫn còn"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Something went wrong! Please try again"
msgstr "Có gì đó không ổn! Vui lòng thử lại"

#: src/components/forms/StripeCheckoutForm/index.tsx:69
msgid "Something went wrong."
msgstr "Có gì đó không ổn."

#: src/components/routes/profile/ManageProfile/index.tsx:80
#: src/components/routes/profile/ManageProfile/index.tsx:94
msgid "Something went wrong. Please try again."
msgstr "Có gì đó không ổn. Vui lòng thử lại"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:61
msgid "Sorry, something has gone wrong. Please restart the checkout process."
msgstr "Xin lỗi, có gì đó không ổn. Vui lòng thử thanh toán lại"

#: src/components/routes/product-widget/CollectInformation/index.tsx:259
msgid "Sorry, something went wrong loading this page."
msgstr "Xin lỗi, có điều gì đó không ổn khi tải trang này."

#: src/components/routes/product-widget/CollectInformation/index.tsx:247
msgid "Sorry, this order no longer exists."
msgstr "Xin lỗi, đơn hàng này không còn tồn tại."

#: src/components/routes/product-widget/SelectProducts/index.tsx:246
msgid "Sorry, this promo code is not recognized"
msgstr "Xin lỗi, mã khuyến mãi này không được công nhận"

#: src/components/common/LanguageSwitcher/index.tsx:18
msgid "Spanish"
msgstr "Tiếng Tây Ban Nha"

#: src/components/forms/ProductForm/index.tsx:147
msgid "Standard product with a fixed price"
msgstr "Sản phẩm tiêu chuẩn với giá cố định"

#: src/components/modals/CreateEventModal/index.tsx:144
#: src/components/modals/DuplicateEventModal/index.tsx:93
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:87
#: src/components/routes/welcome/index.tsx:90
msgid "Start Date"
msgstr "Ngày bắt đầu"

#: src/components/common/CheckoutQuestion/index.tsx:165
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:139
#: src/components/routes/product-widget/CollectInformation/index.tsx:339
#: src/components/routes/product-widget/CollectInformation/index.tsx:340
msgid "State or Region"
msgstr "Nhà nước hoặc khu vực"

#: src/components/common/AttendeeTable/index.tsx:112
#: src/components/common/OrderDetails/index.tsx:51
#: src/components/common/OrdersTable/index.tsx:209
#: src/components/common/ProductsTable/SortableProduct/index.tsx:222
#: src/components/common/WebhookTable/index.tsx:238
#: src/components/forms/CapaciyAssigmentForm/index.tsx:56
#: src/components/forms/WebhookForm/index.tsx:133
#: src/components/modals/EditUserModal/index.tsx:103
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:88
msgid "Status"
msgstr "Trạng thái"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:85
msgid "Stripe"
msgstr "Stripe"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:48
msgid "Stripe payments are not enabled for this event."
msgstr "Thanh toán Stripe không được kích hoạt cho sự kiện này."

#: src/components/modals/SendMessageModal/index.tsx:245
msgid "Subject"
msgstr "Chủ đề"

#: src/components/common/OrderSummary/index.tsx:41
msgid "Subtotal"
msgstr "Tổng phụ"

#: src/components/common/OrdersTable/index.tsx:115
#: src/components/layouts/Checkout/index.tsx:53
msgid "Success"
msgstr "Thành công"

#: src/components/modals/EditUserModal/index.tsx:40
#: src/components/modals/InviteUserModal/index.tsx:32
msgid "Success! {0} will receive an email shortly."
msgstr "Thành công! {0} sẽ nhận một email trong chốc lát."

#: src/components/common/AttendeeTable/index.tsx:90
msgid "Successfully {0} attendee"
msgstr "{0} Người tham dự thành công"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:23
msgid "Successfully confirmed email address"
msgstr "Địa chỉ email được xác nhận thành công"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:31
msgid "Successfully confirmed email change"
msgstr "Thay đổi email được xác nhận thành công"

#: src/components/modals/CreateAttendeeModal/index.tsx:82
msgid "Successfully created attendee"
msgstr "Người tham dự được tạo thành công"

#: src/components/modals/CreateProductModal/index.tsx:54
msgid "Successfully Created Product"
msgstr "Sản phẩm được tạo thành công"

#: src/components/modals/CreatePromoCodeModal/index.tsx:39
msgid "Successfully Created Promo Code"
msgstr "Mã khuyến mãi được tạo thành công"

#: src/components/modals/CreateQuestionModal/index.tsx:45
#: src/components/modals/EditQuestionModal/index.tsx:66
msgid "Successfully Created Question"
msgstr "Câu hỏi được tạo thành công"

#: src/components/modals/DuplicateProductModal/index.tsx:95
msgid "Successfully Duplicated Product"
msgstr "Sản phẩm nhân đôi thành công"

#: src/components/modals/ManageAttendeeModal/index.tsx:96
msgid "Successfully updated attendee"
msgstr "Người tham dự cập nhật thành công"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:47
msgid "Successfully updated Capacity Assignment"
msgstr "Phân công công suất được cập nhật thành công"

#: src/components/modals/EditCheckInListModal/index.tsx:49
msgid "Successfully updated Check-In List"
msgstr "Danh sách soát vé được cập nhật thành công"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:43
msgid "Successfully Updated Email Settings"
msgstr "Cài đặt email được cập nhật thành công"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:54
msgid "Successfully Updated Event"
msgstr "Sự kiện cập nhật thành công"

#: src/components/routes/event/HomepageDesigner/index.tsx:84
msgid "Successfully Updated Homepage Design"
msgstr "Thiết kế trang sự kiện được cập nhật thành công"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:50
msgid "Successfully Updated Homepage Settings"
msgstr "Cài đặt trang chủ được cập nhật thành công"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:73
msgid "Successfully Updated Location"
msgstr "Vị trí cập nhật thành công"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:43
msgid "Successfully Updated Misc Settings"
msgstr "Cài đặt linh tinh được cập nhật thành công"

#: src/components/modals/ManageOrderModal/index.tsx:74
msgid "Successfully updated order"
msgstr "Đơn hàng cập nhật thành công"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:74
msgid "Successfully Updated Payment & Invoicing Settings"
msgstr "Cập nhật cài đặt thanh toán & lập hóa đơn thành công"

#: src/components/modals/EditProductModal/index.tsx:89
msgid "Successfully updated product"
msgstr "Cập nhật sản phẩm thành công"

#: src/components/modals/EditPromoCodeModal/index.tsx:52
msgid "Successfully Updated Promo Code"
msgstr "Cập nhật mã khuyến mãi thành công"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:45
msgid "Successfully Updated Seo Settings"
msgstr "Cập nhật cài đặt SEO thành công"

#: src/components/modals/EditWebhookModal/index.tsx:44
msgid "Successfully updated Webhook"
msgstr "Cập nhật Webhook thành công"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:128
msgid "Suite 100"
msgstr "Phòng 100"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
msgid "Support Email"
msgstr "Email hỗ trợ"

#: src/components/layouts/AuthLayout/index.tsx:59
msgid "Support for tiered, donation-based, and product sales with customizable pricing and capacity"
msgstr "Hỗ trợ bán hàng theo bậc, dựa trên quyên góp và bán sản phẩm với giá và sức chứa tùy chỉnh"

#: src/components/forms/ProductForm/index.tsx:254
msgid "T-shirt"
msgstr "Áo thun"

#: src/components/forms/TaxAndFeeForm/index.tsx:12
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Tax"
msgstr "Thuế"

#: src/components/routes/account/ManageAccount/index.tsx:27
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:16
msgid "Tax & Fees"
msgstr "Thuế & Phí"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:243
msgid "Tax Details"
msgstr "Chi tiết thuế"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:244
msgid "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"
msgstr "Thông tin thuế sẽ xuất hiện ở cuối tất cả các hóa đơn (ví dụ: mã số thuế VAT, đăng ký thuế)"

#: src/components/common/TaxAndFeeList/index.tsx:36
msgid "Tax or Fee deleted successfully"
msgstr "Xóa thuế hoặc phí thành công"

#: src/components/forms/ProductForm/index.tsx:363
msgid "Taxes"
msgstr "Thuế"

#: src/components/forms/ProductForm/index.tsx:351
#: src/components/forms/ProductForm/index.tsx:358
msgid "Taxes and Fees"
msgstr "Thuế và phí"

#: src/components/routes/product-widget/SelectProducts/index.tsx:138
#: src/components/routes/product-widget/SelectProducts/index.tsx:186
msgid "That promo code is invalid"
msgstr "Mã khuyến mãi không hợp lệ"

#: src/components/layouts/CheckIn/index.tsx:316
msgid "The check-in list you are looking for does not exist."
msgstr "Danh sách check-in bạn đang tìm kiếm không tồn tại."

#: src/components/forms/OrganizerForm/index.tsx:47
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:75
msgid "The default currency for your events."
msgstr "Tiền tệ mặc định cho các sự kiện của bạn."

#: src/components/forms/OrganizerForm/index.tsx:56
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:84
msgid "The default timezone for your events."
msgstr "Múi giờ mặc định cho các sự kiện của bạn."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:16
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:43
msgid "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."
msgstr "Sự kiện bạn đang tìm kiếm hiện không khả dụng. Nó có thể đã bị xóa, hết hạn hoặc URL không chính xác."

#: src/components/modals/CreateAttendeeModal/index.tsx:146
msgid "The language the attendee will receive emails in."
msgstr "Ngôn ngữ mà người tham dự sẽ nhận email."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:22
msgid "The link you clicked is invalid."
msgstr "Liên kết bạn đã nhấp vào không hợp lệ."

#: src/components/routes/product-widget/SelectProducts/index.tsx:444
msgid "The maximum number of products for {0}is {1}"
msgstr "Số lượng sản phẩm tối đa cho {0}là {1}"

#: src/components/common/ErrorDisplay/index.tsx:17
msgid "The page you are looking for does not exist"
msgstr "Trang bạn đang tìm kiếm không tồn tại"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:56
msgid "The price displayed to the customer will include taxes and fees."
msgstr "Giá hiển thị cho khách hàng sẽ bao gồm thuế và phí."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:62
msgid "The price displayed to the customer will not include taxes and fees. They will be shown separately"
msgstr "Giá hiển thị cho khách hàng sẽ không bao gồm thuế và phí. Chúng sẽ được hiển thị riêng biệt."

#: src/components/common/WidgetEditor/index.tsx:169
msgid "The styling settings you choose apply only to copied HTML and won't be stored."
msgstr "Các cài đặt kiểu dáng bạn chọn chỉ áp dụng cho HTML được sao chép và sẽ không được lưu trữ."

#: src/components/forms/ProductForm/index.tsx:355
msgid "The taxes and fees to apply to this product. You can create new taxes and fees on the"
msgstr "Thuế và phí áp dụng cho sản phẩm này. "

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:63
msgid "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"
msgstr "Tiêu đề của sự kiện sẽ được hiển thị trong kết quả của công cụ tìm kiếm và khi chia sẻ trên phương tiện truyền thông xã hội. "

#: src/components/routes/product-widget/SelectProducts/index.tsx:272
msgid "There are no products available for this event"
msgstr "Không có sản phẩm nào cho sự kiện này"

#: src/components/routes/product-widget/SelectProducts/index.tsx:375
msgid "There are no products available in this category"
msgstr "Không có sản phẩm nào trong danh mục này"

#: src/components/modals/RefundOrderModal/index.tsx:137
msgid "There is a refund pending. Please wait for it to complete before requesting another refund."
msgstr "Có một khoản hoàn tiền đang chờ xử lý. Vui lòng đợi hoàn tất trước khi yêu cầu hoàn tiền khác."

#: src/components/common/OrdersTable/index.tsx:80
msgid "There was an error marking the order as paid"
msgstr "Đã xảy ra lỗi khi đánh dấu đơn hàng là đã thanh toán"

#: src/hooks/useFormErrorResponseHandler.tsx:28
msgid "There was an error processing your request. Please try again."
msgstr "Đã xảy ra lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại."

#: src/components/common/OrdersTable/index.tsx:95
msgid "There was an error sending your message"
msgstr "Có một lỗi khi gửi tin nhắn của bạn"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:105
msgid "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."
msgstr "Những chi tiết này sẽ chỉ được hiển thị nếu đơn hàng được hoàn thành thành công. Đơn hàng chờ thanh toán sẽ không hiển thị tin nhắn này."

#: src/components/layouts/CheckIn/index.tsx:201
msgid "This attendee has an unpaid order."
msgstr "Người tham dự này có một đơn hàng không được thanh toán."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:61
msgid "This category doesn't have any products yet."
msgstr "Danh mục này chưa có bất kỳ sản phẩm nào."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:112
msgid "This category is hidden from public view"
msgstr "Danh mục này bị ẩn khỏi chế độ xem công khai"

#: src/components/common/CheckInListList/index.tsx:75
msgid "This check-in list has expired"
msgstr "Danh sách người tham dự này đã hết hạn"

#: src/components/layouts/CheckIn/index.tsx:331
msgid "This check-in list has expired and is no longer available for check-ins."
msgstr "Danh sách người tham dự này đã hết hạn và không còn có sẵn để kiểm tra."

#: src/components/common/CheckInListList/index.tsx:82
msgid "This check-in list is active"
msgstr "Danh sách người tham dự này đang hoạt động"

#: src/components/common/CheckInListList/index.tsx:79
msgid "This check-in list is not active yet"
msgstr "Danh sách người tham dự này chưa hoạt động"

#: src/components/layouts/CheckIn/index.tsx:348
msgid "This check-in list is not yet active and is not available for check-ins."
msgstr "Danh sách người tham dự này chưa hoạt động và không có sẵn để kiểm tra."

#: src/components/forms/CheckInListForm/index.tsx:37
msgid "This description will be shown to the check-in staff"
msgstr "Mô tả này sẽ được hiển thị cho nhân viên kiểm tra vé"

#: src/components/modals/SendMessageModal/index.tsx:285
msgid "This email is not promotional and is directly related to the event."
msgstr "Email này không phải là quảng cáo và liên quan trực tiếp đến sự kiện này."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:8
#~ msgid "This event is not available at the moment. Please check back later."
#~ msgstr "Sự kiện này không có sẵn tại thời điểm này. Vui lòng quay lại sau."

#: src/components/layouts/EventHomepage/index.tsx:49
#~ msgid "This event is not available."
#~ msgstr "Sự kiện này không có sẵn."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
msgid "This information will be shown on the payment page, order summary page, and order confirmation email."
msgstr "Thông tin này sẽ được hiển thị trên trang thanh toán, trang tóm tắt đơn hàng và email xác nhận đơn hàng."

#: src/components/forms/ProductForm/index.tsx:138
msgid "This is a general product, like a t-shirt or a mug. No ticket will be issued"
msgstr "Đây là một sản phẩm chung, như áo phông hoặc cốc. Không có vé nào được phát hành"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:91
msgid "This is an online event"
msgstr "Đây là một sự kiện trực tuyến"

#: src/components/forms/CheckInListForm/index.tsx:57
msgid "This list will no longer be available for check-ins after this date"
msgstr "Danh sách này sẽ không còn có sẵn để kiểm tra sau ngày này"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:68
msgid "This message will be included in the footer of all emails sent from this event"
msgstr "Thông báo này sẽ được bao gồm trong phần chân trang của tất cả các email được gửi từ sự kiện này"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
msgid "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"
msgstr "Thông báo này sẽ chỉ được hiển thị nếu đơn hàng được hoàn thành thành công. Đơn chờ thanh toán sẽ không hiển thị thông báo này."

#: src/components/forms/StripeCheckoutForm/index.tsx:93
msgid "This order has already been paid."
msgstr "Đơn hàng này đã được thanh toán."

#: src/components/modals/RefundOrderModal/index.tsx:141
msgid "This order has already been refunded."
msgstr "Đơn hàng này đã được hoàn trả."

#: src/components/routes/product-widget/CollectInformation/index.tsx:237
msgid "This order has been cancelled"
msgstr "Đơn hàng này đã bị hủy"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:209
msgid "This order has been cancelled."
msgstr "Đơn hàng này đã bị hủy bỏ."

#: src/components/routes/product-widget/CollectInformation/index.tsx:210
msgid "This order has expired. Please start again."
msgstr "Đơn hàng này đã hết hạn. Vui lòng bắt đầu lại."

#: src/components/routes/product-widget/CollectInformation/index.tsx:221
msgid "This order is awaiting payment"
msgstr "Đơn hàng này đang chờ thanh toán"

#: src/components/routes/product-widget/CollectInformation/index.tsx:229
msgid "This order is complete"
msgstr "Đơn hàng này đã hoàn tất"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:211
msgid "This order is complete."
msgstr "Đơn hàng này đã hoàn tất."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:204
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:207
msgid "This order is processing."
msgstr "Đơn hàng này đang xử lý."

#: src/components/forms/StripeCheckoutForm/index.tsx:103
msgid "This order page is no longer available."
msgstr "Trang Đơn hàng này không còn có sẵn."

#: src/components/forms/ProductForm/index.tsx:415
msgid "This overrides all visibility settings and will hide the product from all customers."
msgstr "Điều này ghi đè tất cả các cài đặt khả năng hiển thị và sẽ ẩn sản phẩm khỏi tất cả các khách hàng."

#: src/components/common/ProductsTable/SortableProduct/index.tsx:59
msgid "This product cannot be deleted because it is associated with an order. You can hide it instead."
msgstr "Sản phẩm này không thể bị xóa vì nó được liên kết với một đơn hàng. "

#: src/components/forms/ProductForm/index.tsx:132
msgid "This product is a ticket. Buyers will be issued a ticket upon purchase"
msgstr "Sản phẩm này là vé. Người mua sẽ nhận được vé sau khi mua"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:215
msgid "This product is hidden from public view"
msgstr "Sản phẩm này được ẩn khỏi chế độ xem công khai"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:216
msgid "This product is hidden unless targeted by a Promo Code"
msgstr "Sản phẩm này bị ẩn trừ khi được nhắm mục tiêu bởi mã khuyến mãi"

#: src/components/common/QuestionsTable/index.tsx:90
msgid "This question is only visible to the event organizer"
msgstr "Câu hỏi này chỉ hiển thị cho người tổ chức sự kiện"

#: src/components/routes/auth/ResetPassword/index.tsx:29
msgid "This reset password link is invalid or expired."
msgstr "Liên kết mật khẩu đặt lại này không hợp lệ hoặc hết hạn."

#: src/components/modals/EditUserModal/index.tsx:65
msgid "This user is not active, as they have not accepted their invitation."
msgstr "Người dùng này không hoạt động, vì họ chưa chấp nhận lời mời của họ."

#: src/components/common/AttendeeTable/index.tsx:182
msgid "ticket"
msgstr "vé"

#: src/components/common/AttendeeTable/index.tsx:111
#: src/components/forms/ProductForm/index.tsx:130
#: src/components/modals/CreateAttendeeModal/index.tsx:151
msgid "Ticket"
msgstr "Vé"

#: src/components/common/AttendeeTable/index.tsx:49
msgid "Ticket email has been resent to attendee"
msgstr "Email vé đã được gửi lại với người tham dự"

#: src/components/common/MessageList/index.tsx:22
msgid "Ticket holders"
msgstr "Người sở hữu vé"

#: src/components/routes/event/products.tsx:86
msgid "Ticket or Product"
msgstr "Vé hoặc sản phẩm"

#: src/components/layouts/Event/index.tsx:101
#: src/components/routes/event/products.tsx:46
msgid "Tickets & Products"
msgstr "Vé & Sản phẩm"

#: src/components/routes/product-widget/PrintOrder/index.tsx:43
msgid "Tickets for"
msgstr "Vé cho"

#: src/components/forms/ProductForm/index.tsx:67
msgid "Tier {0}"
msgstr "Cấp {0}"

#: src/components/forms/ProductForm/index.tsx:163
msgid "Tiered Product"
msgstr "Sản phẩm cấp bậc"

#: src/components/forms/ProductForm/index.tsx:243
msgid "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."
msgstr "Sản phẩm theo bậc cho phép bạn cung cấp nhiều tùy chọn giá cho cùng một sản phẩm. Điều này hoàn hảo cho các sản phẩm ưu đãi sớm hoặc các nhóm giá khác nhau cho từng đối tượng."

#: src/components/layouts/Checkout/index.tsx:90
msgid "Time left:"
msgstr "Thời gian còn lại:"

#: src/components/common/PromoCodeTable/index.tsx:71
msgid "Times used"
msgstr "Thời gian được sử dụng"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:25
msgid "Times Used"
msgstr "Thời gian được sử dụng"

#: src/components/forms/OrganizerForm/index.tsx:54
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:82
#: src/components/routes/auth/AcceptInvitation/index.tsx:105
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:109
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:181
#: src/components/routes/profile/ManageProfile/index.tsx:161
msgid "Timezone"
msgstr "Múi giờ"

#: src/components/forms/PromoCodeForm/index.tsx:36
msgid "TIP"
msgstr "Tip"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:206
msgid "Title"
msgstr "Tiêu đề"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:182
msgid "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."
msgstr "Để nhận thanh toán bằng thẻ tín dụng, bạn cần kết nối tài khoản Stripe của mình. Stripe là đối tác xử lý thanh toán của chúng tôi, đảm bảo giao dịch an toàn và thanh toán kịp thời."

#: src/components/common/OrderSummary/index.tsx:41
#~ msgid "Tổng phụ"
#~ msgstr ""

#: src/components/layouts/Event/index.tsx:108
msgid "Tools"
msgstr "Công cụ"

#: src/components/common/OrderSummary/index.tsx:92
msgid "Total"
msgstr "Tổng"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:52
msgid "Total Before Discounts"
msgstr "Tổng trước khi giảm giá"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:58
msgid "Total Discount Amount"
msgstr "Tổng tiền chiết khấu"

#: src/components/routes/event/EventDashboard/index.tsx:273
msgid "Total Fees"
msgstr "Tổng phí"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:46
msgid "Total Gross Sales"
msgstr "Tổng doanh thu"

#: src/components/common/OrderDetails/index.tsx:59
#: src/components/modals/RefundOrderModal/index.tsx:74
msgid "Total order amount"
msgstr "Tổng tiền đơn hàng"

#: src/components/common/OrderDetails/index.tsx:67
#: src/components/modals/RefundOrderModal/index.tsx:82
msgid "Total refunded"
msgstr "Tổng đã hoàn lại"

#: src/components/routes/event/EventDashboard/index.tsx:276
msgid "Total Refunded"
msgstr "Tổng đã hoàn lại"

#: src/components/modals/RefundOrderModal/index.tsx:90
msgid "Total remaining"
msgstr "Tổng còn lại"

#: src/components/routes/event/EventDashboard/index.tsx:275
msgid "Total Tax"
msgstr "Tổng thuế"

#: src/components/layouts/AuthLayout/index.tsx:54
msgid "Track revenue, page views, and sales with detailed analytics and exportable reports"
msgstr "Theo dõi doanh thu, lượt xem trang và bán hàng với các phân tích chi tiết và báo cáo có thể xuất khẩu"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
msgid "Transaction Fee:"
msgstr "Phí giao dịch:"

#: src/components/forms/TaxAndFeeForm/index.tsx:44
msgid "Type"
msgstr "Loại"

#: src/components/layouts/CheckIn/index.tsx:93
msgid "Unable to check in attendee"
msgstr "Không thể kiểm tra người tham dự"

#: src/components/layouts/CheckIn/index.tsx:114
msgid "Unable to check out attendee"
msgstr "Không thể kiểm tra người tham dự"

#: src/components/modals/CreateProductModal/index.tsx:63
msgid "Unable to create product. Please check the your details"
msgstr "Không thể tạo sản phẩm. Vui lòng kiểm tra thông tin của bạn"

#: src/components/routes/product-widget/SelectProducts/index.tsx:123
msgid "Unable to create product. Please check your details"
msgstr "Không thể tạo sản phẩm. Vui lòng kiểm tra thông tin của bạn"

#: src/components/modals/CreateQuestionModal/index.tsx:60
msgid "Unable to create question. Please check the your details"
msgstr "Không thể tạo câu hỏi. Vui lòng kiểm tra thông tin của bạn"

#: src/components/modals/DuplicateProductModal/index.tsx:103
msgid "Unable to duplicate product. Please check the your details"
msgstr "Không thể nhân bản sản phẩm. Vui lòng kiểm tra thông tin của bạn"

#: src/components/layouts/CheckIn/index.tsx:145
msgid "Unable to fetch attendee"
msgstr "Không thể lấy thông tin người tham dự"

#: src/components/modals/EditQuestionModal/index.tsx:84
msgid "Unable to update question. Please check the your details"
msgstr "Không thể cập nhật câu hỏi. Vui lòng kiểm tra thông tin của bạn"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:30
msgid "Unique Customers"
msgstr "Khách hàng duy nhất"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:153
msgid "United States"
msgstr "Hoa Kỳ"

#: src/components/common/QuestionAndAnswerList/index.tsx:284
msgid "Unknown Attendee"
msgstr "Người tham dự không xác định"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:43
#: src/components/forms/ProductForm/index.tsx:83
#: src/components/forms/ProductForm/index.tsx:299
#: src/components/forms/PromoCodeForm/index.tsx:82
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:78
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:84
msgid "Unlimited"
msgstr "Không giới hạn"

#: src/components/routes/product-widget/SelectProducts/index.tsx:407
msgid "Unlimited available"
msgstr "Không giới hạn có sẵn"

#: src/components/common/PromoCodeTable/index.tsx:126
msgid "Unlimited usages allowed"
msgstr "Sử dụng không giới hạn"

#: src/components/layouts/CheckIn/index.tsx:200
msgid "Unpaid Order"
msgstr "Đơn hàng chưa thanh toán"

#: src/components/routes/event/EventDashboard/index.tsx:170
msgid "Unpublish Event"
msgstr "Hủy công bố sự kiện"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:20
#: src/components/common/EventStatusBadge/index.tsx:16
msgid "Upcoming"
msgstr "Sắp tới"

#: src/components/routes/events/Dashboard/index.tsx:55
msgid "Upcoming Events"
msgstr "Các sự kiện sắp tới"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Update {0}"
msgstr "Cập nhật {0}"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:66
msgid "Update event name, description and dates"
msgstr "Cập nhật tên sự kiện, mô tả và ngày"

#: src/components/routes/profile/ManageProfile/index.tsx:177
msgid "Update profile"
msgstr "Cập nhật hồ sơ"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Upload Cover"
msgstr "Tải lên bìa"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:105
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:152
msgid "Upload Image"
msgstr "Tải ảnh lên"

#: src/components/common/WebhookTable/index.tsx:236
#: src/components/modals/ShareModal/index.tsx:129
msgid "URL"
msgstr "URL"

#: src/components/common/PromoCodeTable/index.tsx:175
msgid "URL copied to clipboard"
msgstr "URL được sao chép vào bảng tạm"

#: src/components/modals/CreateWebhookModal/index.tsx:25
msgid "URL is required"
msgstr "URL là bắt buộc"

#: src/components/common/WidgetEditor/index.tsx:298
msgid "Usage Example"
msgstr "Ví dụ sử dụng"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:76
msgid "Usage Limit"
msgstr "Giới hạn sử dụng"

#: src/components/routes/event/HomepageDesigner/index.tsx:139
msgid "Use a blurred version of the cover image as the background"
msgstr "Sử dụng phiên bản làm mờ của ảnh bìa làm nền"

#: src/components/routes/event/HomepageDesigner/index.tsx:137
msgid "Use cover image"
msgstr "Sử dụng hình ảnh bìa"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User"
msgstr "Người dùng"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User Management"
msgstr "Quản lý người dùng"

#: src/components/routes/account/ManageAccount/index.tsx:32
msgid "Users"
msgstr "Người dùng"

#: src/components/modals/EditUserModal/index.tsx:81
msgid "Users can change their email in <0>Profile Settings</0>"
msgstr "Người dùng có thể thay đổi email của họ trong <0>Cài đặt hồ sơ</0>"

#: src/components/forms/OrganizerForm/index.tsx:55
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:83
#: src/components/routes/auth/AcceptInvitation/index.tsx:106
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:110
#: src/components/routes/profile/ManageProfile/index.tsx:162
msgid "UTC"
msgstr "UTC"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "VAT"
msgstr "Thuế VAT"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:116
msgid "Venue Name"
msgstr "Tên địa điểm"

#: src/components/common/LanguageSwitcher/index.tsx:34
msgid "Vietnamese"
msgstr "Tiếng Việt"

#: src/components/modals/ManageAttendeeModal/index.tsx:220
#: src/components/modals/ManageOrderModal/index.tsx:200
msgid "View"
msgstr "Xem"

#: src/components/routes/event/Reports/index.tsx:38
msgid "View and download reports for your event. Please note, only completed orders are included in these reports."
msgstr "Xem và tải xuống báo cáo cho sự kiện của bạn. Lưu ý rằng chỉ các đơn hàng đã hoàn thành mới được đưa vào các báo cáo này."

#: src/components/common/AttendeeList/index.tsx:84
msgid "View Answers"
msgstr "Xem câu trả lời"

#: src/components/common/AttendeeList/index.tsx:103
msgid "View Attendee Details"
msgstr "Xem chi tiết người tham dự"

#: src/components/common/EventCard/index.tsx:145
msgid "View event page"
msgstr "Xem trang sự kiện"

#: src/components/common/MessageList/index.tsx:59
msgid "View full message"
msgstr "Xem tin nhắn đầy đủ"

#: src/components/common/WebhookTable/index.tsx:113
msgid "View logs"
msgstr "Xem nhật ký"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View map"
msgstr "Xem bản đồ"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View on Google Maps"
msgstr "Xem trên Google Maps"

#: src/components/forms/StripeCheckoutForm/index.tsx:94
#: src/components/forms/StripeCheckoutForm/index.tsx:104
#: src/components/routes/product-widget/CollectInformation/index.tsx:231
msgid "View order details"
msgstr "Xem chi tiết đơn hàng"

#: src/components/forms/CheckInListForm/index.tsx:21
msgid "VIP check-in list"
msgstr "Danh sách người tham dự VIP"

#: src/components/forms/ProductForm/index.tsx:254
msgid "VIP Ticket"
msgstr "Vé VIP"

#: src/components/forms/ProductForm/index.tsx:394
msgid "Visibility"
msgstr "Tầm nhìn"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:77
msgid "We could not process your payment. Please try again or contact support."
msgstr "Chúng tôi không thể xử lý thanh toán của bạn. Vui lòng thử lại hoặc liên hệ với ban tổ chức."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:53
msgid "We couldn't delete the category. Please try again."
msgstr "Chúng tôi không thể xóa danh mục. Vui lòng thử lại."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:24
msgid "We couldn't find any tickets matching {0}"
msgstr "Chúng tôi không thể tìm thấy bất kỳ vé nào khớp với {0}"

#: src/components/common/ErrorLoadingMessage/index.tsx:13
msgid "We couldn't load the data. Please try again."
msgstr "Chúng tôi không thể tải dữ liệu. Vui lòng thử lại."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:94
msgid "We couldn't reorder the categories. Please try again."
msgstr "Chúng tôi không thể sắp xếp lại các danh mục. Vui lòng thử lại."

#: src/components/routes/event/HomepageDesigner/index.tsx:118
msgid "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"
msgstr "Chúng tôi đề xuất kích thước 2160px bằng 1080px và kích thước tệp tối đa là 5MB"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:84
msgid "We were unable to confirm your payment. Please try again or contact support."
msgstr "Chúng tôi không thể xác nhận thanh toán của bạn. Vui lòng thử lại hoặc liên hệ với ban tổ chức."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:79
msgid "We're processing your order. Please wait..."
msgstr "Chúng tôi đang xử lý đơn hàng của bạn. Đợi một chút..."

#: src/components/modals/CreateWebhookModal/index.tsx:46
msgid "Webhook created successfully"
msgstr "Webhook tạo thành công"

#: src/components/common/WebhookTable/index.tsx:53
msgid "Webhook deleted successfully"
msgstr "Webhook đã xóa thành công"

#: src/components/modals/WebhookLogsModal/index.tsx:151
msgid "Webhook Logs"
msgstr "Nhật ký webhook"

#: src/components/forms/WebhookForm/index.tsx:117
msgid "Webhook URL"
msgstr "URL Webhook"

#: src/components/forms/WebhookForm/index.tsx:27
msgid "Webhook will not send notifications"
msgstr "Webhook sẽ không gửi thông báo"

#: src/components/forms/WebhookForm/index.tsx:21
msgid "Webhook will send notifications"
msgstr "Webhook sẽ gửi thông báo"

#: src/components/layouts/Event/index.tsx:111
#: src/components/routes/event/Webhooks/index.tsx:30
msgid "Webhooks"
msgstr "Webhooks"

#: src/components/routes/auth/AcceptInvitation/index.tsx:76
msgid "Welcome aboard! Please login to continue."
msgstr "Chào mừng bạn! Vui lòng đăng nhập để tiếp tục."

#: src/components/routes/auth/Login/index.tsx:55
msgid "Welcome back 👋"
msgstr "Chào mừng trở lại 👋"

#: src/components/routes/event/EventDashboard/index.tsx:95
msgid "Welcome back{0} 👋"
msgstr "Chào mừng trở lại{0} 👋"

#: src/components/routes/auth/Register/index.tsx:67
msgid "Welcome to Hi.Events 👋"
msgstr "Chào mừng bạn đến với Hi.Events 👋"

#: src/components/routes/welcome/index.tsx:125
msgid "Welcome to Hi.Events, {0} 👋"
msgstr "Chào mừng bạn đến với hi.events, {0} 👋"

#: src/components/forms/ProductForm/index.tsx:242
msgid "What are Tiered Products?"
msgstr "Sản phẩm cấp bậc là gì?"

#: src/components/forms/CheckInListForm/index.tsx:50
msgid "What date should this check-in list become active?"
msgstr "Danh sách người tham dự này nên hoạt động vào ngày nào?"

#: src/components/modals/CreateProductCategoryModal/index.tsx:53
msgid "What is a Category?"
msgstr "Thể loại là gì?"

#: src/components/routes/event/Webhooks/index.tsx:31
#~ msgid "What is a webhook?"
#~ msgstr "Webhook là gì?"

#: src/components/forms/QuestionForm/index.tsx:159
msgid "What products does this code apply to?"
msgstr "Mã này áp dụng cho sản phẩm nào?"

#: src/components/forms/PromoCodeForm/index.tsx:68
msgid "What products does this code apply to? (Applies to all by default)"
msgstr "Mã này áp dụng cho sản phẩm nào? (Mặc định áp dụng cho tất cả)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:48
msgid "What products should this capacity apply to?"
msgstr "Sản phẩm nào nên áp dụng công suất này?"

#: src/components/forms/QuestionForm/index.tsx:179
msgid "What time will you be arriving?"
msgstr "Bạn sẽ đến lúc mấy giờ?"

#: src/components/forms/QuestionForm/index.tsx:170
msgid "What type of question is this?"
msgstr "Đây là loại câu hỏi nào?"

#: src/components/forms/WebhookForm/index.tsx:108
msgid "When a check-in is deleted"
msgstr "Khi một lượt check-in bị xóa"

#: src/components/forms/WebhookForm/index.tsx:84
msgid "When a new attendee is created"
msgstr "Khi một người tham dự mới được tạo ra"

#: src/components/forms/WebhookForm/index.tsx:54
msgid "When a new order is created"
msgstr "Khi một đơn hàng mới được tạo"

#: src/components/forms/WebhookForm/index.tsx:36
msgid "When a new product is created"
msgstr "Khi một sản phẩm mới được tạo ra"

#: src/components/forms/WebhookForm/index.tsx:48
msgid "When a product is deleted"
msgstr "Khi một sản phẩm bị xóa"

#: src/components/forms/WebhookForm/index.tsx:42
msgid "When a product is updated"
msgstr "Khi một sản phẩm được cập nhật"

#: src/components/forms/WebhookForm/index.tsx:96
msgid "When an attendee is cancelled"
msgstr "Khi một người tham dự bị hủy"

#: src/components/forms/WebhookForm/index.tsx:102
msgid "When an attendee is checked in"
msgstr "Khi một người tham dự được check-in"

#: src/components/forms/WebhookForm/index.tsx:90
msgid "When an attendee is updated"
msgstr "Khi một người tham dự được cập nhật"

#: src/components/forms/WebhookForm/index.tsx:78
msgid "When an order is cancelled"
msgstr "Khi một đơn hàng bị hủy"

#: src/components/forms/WebhookForm/index.tsx:66
msgid "When an order is marked as paid"
msgstr "Khi một đơn hàng được đánh dấu là đã thanh toán"

#: src/components/forms/WebhookForm/index.tsx:72
msgid "When an order is refunded"
msgstr "Khi một đơn hàng được hoàn trả"

#: src/components/forms/WebhookForm/index.tsx:60
msgid "When an order is updated"
msgstr "Khi một đơn hàng được cập nhật"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:182
msgid "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."
msgstr "Khi được bật, hóa đơn sẽ được tạo cho các đơn hàng vé. Hóa đơn sẽ được gửi kèm với email xác nhận đơn hàng. Người tham dự cũng có thể tải hóa đơn của họ từ trang xác nhận đơn hàng."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:141
msgid "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."
msgstr "Khi thanh toán ngoại tuyến được bật, người dùng có thể hoàn tất đơn hàng và nhận vé của họ. Vé của họ sẽ hiển thị rõ ràng rằng đơn hàng chưa được thanh toán, và công cụ check-in sẽ thông báo cho nhân viên check-in nếu đơn hàng cần thanh toán."

#: src/components/forms/CheckInListForm/index.tsx:59
msgid "When should this check-in list expire?"
msgstr "Danh sách người tham dự này nên hết hạn khi nào?"

#: src/components/forms/CheckInListForm/index.tsx:25
msgid "Which tickets should be associated with this check-in list?"
msgstr "Những vé nào nên được liên kết với danh sách người tham dự này?"

#: src/components/modals/CreateEventModal/index.tsx:107
msgid "Who is organizing this event?"
msgstr "Ai đang tổ chức sự kiện này?"

#: src/components/modals/SendMessageModal/index.tsx:194
msgid "Who is this message to?"
msgstr "Tin nhắn này gửi tới ai?"

#: src/components/forms/QuestionForm/index.tsx:151
msgid "Who should be asked this question?"
msgstr "Ai nên được hỏi câu hỏi này?"

#: src/components/layouts/Event/index.tsx:110
msgid "Widget Embed"
msgstr "Nhúng Widget"

#: src/components/common/WidgetEditor/index.tsx:162
msgid "Widget Settings"
msgstr "Cài đặt widget"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Working"
msgstr "Làm việc"

#: src/components/modals/CreateProductCategoryModal/index.tsx:60
#: src/components/modals/CreateProductModal/index.tsx:88
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
#: src/components/modals/CreateQuestionModal/index.tsx:74
#: src/components/modals/DuplicateEventModal/index.tsx:142
#: src/components/modals/DuplicateProductModal/index.tsx:113
#: src/components/modals/EditProductCategoryModal/index.tsx:70
#: src/components/modals/EditProductModal/index.tsx:108
#: src/components/modals/EditPromoCodeModal/index.tsx:84
#: src/components/modals/EditQuestionModal/index.tsx:101
#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/Register/index.tsx:129
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Working..."
msgstr "Làm việc ..."

#: src/components/common/ReportTable/index.tsx:51
msgid "Year to date"
msgstr "Từ đầu năm đến nay"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "Yes"
msgstr "Có"

#: src/components/forms/ProductForm/index.tsx:376
msgid "Yes, remove them"
msgstr "Có, loại bỏ chúng"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:77
msgid "You already scanned this ticket"
msgstr "Bạn đã quét vé này rồi"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:53
msgid "You are changing your email to <0>{0}</0>."
msgstr "Bạn đang thay đổi email của mình thành <0>{0}</0>."

#: src/components/layouts/CheckIn/index.tsx:88
#: src/components/layouts/CheckIn/index.tsx:110
msgid "You are offline"
msgstr "Bạn đang ngoại tuyến"

#: src/components/forms/ProductForm/index.tsx:407
msgid "You can create a promo code which targets this product on the"
msgstr "Bạn có thể tạo mã khuyến mãi nhắm mục tiêu sản phẩm này trên"

#: src/components/forms/ProductForm/index.tsx:208
msgid "You cannot change the product type as there are attendees associated with this product."
msgstr "Bạn không thể thay đổi loại sản phẩm vì có những người tham dự liên quan đến sản phẩm này."

#: src/components/layouts/CheckIn/index.tsx:129
#: src/components/layouts/CheckIn/index.tsx:164
msgid "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."
msgstr "Bạn không thể xác nhận người tham dự với các đơn hàng không được thanh toán. Cài đặt này có thể được thay đổi ở phần cài đặt sự kiện."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:40
msgid "You cannot delete the last category."
msgstr "Bạn không thể xóa danh mục cuối cùng."

#: src/components/forms/ProductForm/index.tsx:57
msgid "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."
msgstr "Bạn không thể xóa cấp giá này vì đã có sản phẩm được bán cho cấp này. Thay vào đó, bạn có thể ẩn nó."

#: src/components/modals/EditUserModal/index.tsx:88
msgid "You cannot edit the role or status of the account owner."
msgstr "Bạn không thể chỉnh sửa vai trò hoặc trạng thái của chủ sở hữu tài khoản."

#: src/components/modals/RefundOrderModal/index.tsx:132
msgid "You cannot refund a manually created order."
msgstr "Bạn không thể hoàn trả một Đơn hàng được tạo thủ công."

#: src/components/common/QuestionsTable/index.tsx:252
msgid "You created a hidden question but disabled the option to show hidden questions. It has been enabled."
msgstr "Bạn đã tạo một câu hỏi ẩn nhưng đã tắt tùy chọn hiển thị câu hỏi ẩn. Nó đã được bật."

#: src/components/modals/ChooseAccountModal/index.tsx:14
msgid "You have access to multiple accounts. Please choose one to continue."
msgstr "Bạn có quyền truy cập vào nhiều tài khoản. Vui lòng chọn một tài khoản để tiếp tục."

#: src/components/routes/auth/AcceptInvitation/index.tsx:57
msgid "You have already accepted this invitation. Please login to continue."
msgstr "Bạn đã chấp nhận lời mời này. Vui lòng đăng nhập để tiếp tục."

#: src/components/common/QuestionsTable/index.tsx:338
msgid "You have no attendee questions."
msgstr "Bạn không có câu hỏi cho người tham dự."

#: src/components/common/QuestionsTable/index.tsx:323
msgid "You have no order questions."
msgstr "Bạn không có câu hỏi nào về đơn hàng."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:41
msgid "You have no pending email change."
msgstr "Bạn không có thay đổi email đang chờ xử lý."

#: src/components/layouts/Checkout/index.tsx:156
msgid "You have run out of time to complete your order."
msgstr "Bạn đã hết thời gian để hoàn thành đơn hàng của mình."

#: src/components/forms/ProductForm/index.tsx:374
msgid "You have taxes and fees added to a Free Product. Would you like to remove them?"
msgstr "Bạn có thuế và phí được thêm vào một sản phẩm miễn phí. Bạn có muốn bỏ chúng?"

#: src/components/common/MessageList/index.tsx:74
msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."
msgstr "Bạn chưa gửi bất kỳ tin nhắn nào. Bạn có thể gửi tin nhắn tới tất cả người tham dự, hoặc cho người giữ sản phẩm cụ thể."

#: src/components/modals/SendMessageModal/index.tsx:108
msgid "You must acknowledge that this email is not promotional"
msgstr "Bạn phải hiểu rằng email này không phải là email quảng cáo"

#: src/components/routes/auth/AcceptInvitation/index.tsx:33
msgid "You must agree to the terms and conditions"
msgstr "Bạn phải đồng ý với các điều khoản và điều kiện"

#: src/components/routes/event/GettingStarted/index.tsx:193
msgid "You must confirm your email address before your event can go live."
msgstr "Bạn phải xác nhận địa chỉ email của bạn trước khi sự kiện của bạn có thể phát hành."

#: src/components/modals/CreateAttendeeModal/index.tsx:98
msgid "You must create a ticket before you can manually add an attendee."
msgstr "Bạn phải tạo một vé trước khi bạn có thể thêm một người tham dự."

#: src/components/forms/ProductForm/index.tsx:60
msgid "You must have at least one price tier"
msgstr "Bạn phải có ít nhất một cấp giá"

#: src/components/modals/SendMessageModal/index.tsx:136
#~ msgid "You need to verify your account before you can send messages."
#~ msgstr "Bạn cần xác minh tài khoản của mình trước khi bạn có thể gửi tin nhắn."

#: src/components/modals/SendMessageModal/index.tsx:151
msgid "You need to verify your account email before you can send messages."
msgstr "Bạn cần xác minh email tài khoản trước khi có thể gửi tin nhắn."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:143
msgid "You will have to mark an order as paid manually. This can be done on the manage order page."
msgstr "Bạn sẽ phải đánh dấu một đơn hàng theo cách thủ công. Được thực hiện trong trang quản lý đơn hàng."

#: src/components/modals/CreateCheckInListModal/index.tsx:55
msgid "You'll need a ticket before you can create a check-in list."
msgstr "Bạn sẽ cần một vé trước khi bạn có thể tạo một danh sách người tham dự."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
msgid "You'll need at a product before you can create a capacity assignment."
msgstr "Bạn sẽ cần tại một sản phẩm trước khi bạn có thể tạo một sự phân công công suất."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:43
msgid "You'll need at least one product to get started. Free, paid or let the user decide what to pay."
msgstr "Bạn cần ít nhất một sản phẩm để bắt đầu. Miễn phí, trả phí hoặc để người dùng quyết định số tiền thanh toán."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:93
msgid "You're going to {0}! 🎉"
msgstr "Bạn sẽ đến {0}! 🎉"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:64
msgid "Your account name is used on event pages and in emails."
msgstr "Tên tài khoản của bạn được sử dụng trên các trang sự kiện và trong email."

#: src/components/routes/event/attendees.tsx:44
msgid "Your attendees have been exported successfully."
msgstr "Những người tham dự của bạn đã được xuất thành công."

#: src/components/common/AttendeeTable/index.tsx:61
msgid "Your attendees will appear here once they have registered for your event. You can also manually add attendees."
msgstr "Người tham dự của bạn sẽ xuất hiện ở đây sau khi họ đăng ký tham gia sự kiện. Bạn cũng có thể thêm người tham dự theo cách thủ công."

#: src/components/common/WidgetEditor/index.tsx:329
msgid "Your awesome website 🎉"
msgstr "Trang web tuyệt vời của bạn 🎉"

#: src/components/routes/product-widget/CollectInformation/index.tsx:276
msgid "Your Details"
msgstr "Thông tin của bạn"

#: src/components/routes/auth/ForgotPassword/index.tsx:52
msgid "Your Email"
msgstr "Email của bạn"

#: src/components/routes/profile/ManageProfile/index.tsx:118
msgid "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"
msgstr "Yêu cầu email của bạn thay đổi thành <0>{0}</0> đang chờ xử lý. "

#: src/components/routes/event/EventDashboard/index.tsx:150
msgid "Your event must be live before you can sell tickets to attendees."
msgstr "Sự kiện của bạn phải được phát trực tiếp trước khi bạn có thể bán vé cho người tham dự."

#: src/components/routes/event/EventDashboard/index.tsx:164
#~ msgid "Your event must be live before you can sell tickets."
#~ msgstr "Sự kiện của bạn phải hoạt động trước khi bạn có thể bán vé."

#: src/components/common/OrdersTable/index.tsx:88
msgid "Your message has been sent"
msgstr "Tin nhắn của bạn đã được gửi"

#: src/components/layouts/Checkout/index.tsx:84
msgid "Your Order"
msgstr "Đơn hàng của bạn"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:94
msgid "Your order has been cancelled"
msgstr "Đơn hàng của bạn đã bị hủy"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:96
msgid "Your order is awaiting payment 🏦"
msgstr "Đơn hàng của bạn đang chờ thanh toán 🏦"

#: src/components/routes/event/orders.tsx:95
msgid "Your orders have been exported successfully."
msgstr "Đơn hàng của bạn đã được xuất thành công."

#: src/components/common/OrdersTable/index.tsx:66
msgid "Your orders will appear here once they start rolling in."
msgstr "Đơn hàng của bạn sẽ xuất hiện ở đây sau khi chúng bắt đầu tham gia."

#: src/components/routes/auth/Login/index.tsx:74
#: src/components/routes/auth/Register/index.tsx:107
msgid "Your password"
msgstr "Mật khẩu của bạn"

#: src/components/forms/StripeCheckoutForm/index.tsx:63
msgid "Your payment is processing."
msgstr "Thanh toán của bạn đang xử lý."

#: src/components/forms/StripeCheckoutForm/index.tsx:66
msgid "Your payment was not successful, please try again."
msgstr "Thanh toán của bạn không thành công, vui lòng thử lại."

#: src/components/forms/StripeCheckoutForm/index.tsx:126
msgid "Your payment was unsuccessful. Please try again."
msgstr "Thanh toán của bạn không thành công. Vui lòng thử lại."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#~ msgid "Your product for"
#~ msgstr "Sản phẩm cho"

#: src/components/modals/RefundOrderModal/index.tsx:58
msgid "Your refund is processing."
msgstr "Hoàn lại tiền của bạn đang xử lý."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:153
msgid "Your Stripe account is connected and ready to process payments."
msgstr "Tài khoản Stripe của bạn được kết nối và sẵn sàng xử lý thanh toán."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#: src/components/routes/product-widget/PrintProduct/index.tsx:42
msgid "Your ticket for"
msgstr "Vé của bạn cho"

#: src/components/routes/product-widget/CollectInformation/index.tsx:348
msgid "ZIP / Postal Code"
msgstr "mã zip / bưu điện"

#: src/components/common/CheckoutQuestion/index.tsx:170
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:146
msgid "Zip or Postal Code"
msgstr "mã zip hoặc bưu điện"

#: src/components/routes/product-widget/CollectInformation/index.tsx:349
msgid "ZIP or Postal Code"
msgstr "mã zip hoặc bưu điện"
