@use "../../../../styles/mixins";

.welcomeHeader {
  margin-bottom: var(--tk-spacing-xl);
  padding: var(--tk-spacing-xl) var(--tk-spacing-lg);
  font-size: 1.75rem;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(
    135deg,
    var(--tk-primary) 0%,
    var(--tk-pink) 100%
  );
  color: white;
  border-radius: var(--tk-radius-lg);
  box-shadow: 0 8px 32px rgba(71, 46, 120, 0.15);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 100%
    );
    pointer-events: none;
  }

  @include mixins.respond-below(sm) {
    font-size: 1.5rem;
    padding: var(--tk-spacing-lg);
  }
}

.actionBar {
  background: linear-gradient(
    135deg,
    var(--tk-primary) 0%,
    var(--tk-pink) 100%
  );
  padding: var(--tk-spacing-lg);
  color: white;
  border-radius: var(--tk-radius-md);
  box-shadow: 0 4px 16px rgba(71, 46, 120, 0.2);
}

.heading {
  font-size: 1.75rem;
  font-weight: 700;
  margin-top: var(--tk-spacing-xl);
  margin-bottom: var(--tk-spacing-lg);
  color: var(--tk-primary);
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(
      90deg,
      var(--tk-primary) 0%,
      var(--tk-pink) 100%
    );
    border-radius: 2px;
  }

  @include mixins.respond-below(sm) {
    font-size: 1.5rem;
  }
}

.subHeading {
  margin-bottom: var(--tk-spacing-lg);
  color: var(--tk-secondary-text);
  font-weight: 500;
}

.orderSummaryContainer {
  padding: var(--tk-spacing-xl);
  border-radius: var(--tk-radius-lg);
  background: linear-gradient(135deg, #faf5ff 0%, #ffffff 100%);
  border: 1px solid rgba(71, 46, 120, 0.1);
  box-shadow: 0 4px 20px rgba(71, 46, 120, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(
      90deg,
      var(--tk-primary) 0%,
      var(--tk-pink) 100%
    );
  }
}

.detailItem {
  min-width: 0;
  overflow: hidden;
  padding: var(--tk-spacing-lg);
  border-radius: var(--tk-radius-md);
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(71, 46, 120, 0.08);
  transition: all 0.2s ease;
  margin-bottom: var(--tk-spacing-md);

  &:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(71, 46, 120, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(71, 46, 120, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }

  .detailContent {
    min-width: 0;
    flex: 1;
  }

  .label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--tk-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .value {
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    font-weight: 500;
    color: var(--tk-text);
    font-size: 1rem;
    line-height: 1.5;

    a {
      color: var(--tk-primary);
      text-decoration: none;
      font-weight: 600;
      transition: color 0.2s ease;

      &:hover {
        color: var(--tk-pink);
        text-decoration: underline;
      }
    }
  }
}

// Enhanced responsive design
@include mixins.respond-below(sm) {
  .detailItem {
    padding: var(--tk-spacing-md);
    margin-bottom: var(--tk-spacing-sm);

    .label {
      font-size: 0.8rem;
    }

    .value {
      font-size: 0.9rem;
    }
  }

  .orderSummaryContainer {
    padding: var(--tk-spacing-lg);
  }

  .heading {
    font-size: 1.25rem;
    margin-top: var(--tk-spacing-lg);
  }
}

// Animation for smooth transitions
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detailItem {
  animation: fadeInUp 0.3s ease-out;
}

.orderSummaryContainer {
  animation: fadeInUp 0.4s ease-out;
}

.welcomeHeader {
  animation: fadeInUp 0.5s ease-out;
}
