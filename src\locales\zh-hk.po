msgid ""
msgstr ""
"POT-Creation-Date: 2025-04-08 16:45+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: zh-hk\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/components/layouts/Event/index.tsx:189
msgid "- Click to Publish"
msgstr "- 點擊發布"

#: src/components/layouts/Event/index.tsx:191
msgid "- Click to Unpublish"
msgstr "- 點擊取消發布"

#: src/components/common/NoResultsSplash/index.tsx:14
msgid "'There\\'s nothing to show yet'"
msgstr "There's nothing to show yet"

#: src/components/modals/SendMessageModal/index.tsx:159
#~ msgid ""
#~ "\"\"Due to the high risk of spam, we require manual verification before you can send messages.\n"
#~ "\"\"Please contact us to request access."
#~ msgstr ""
#~ "由於垃圾郵件風險高，我們需要進行手動驗證後才能發送訊息。\n"
#~ "請聯絡我們以申請權限。"

#: src/components/routes/auth/ForgotPassword/index.tsx:40
#~ msgid ""
#~ "\"\"If you have an account with us, you will receive an email with instructions on how to reset your\n"
#~ "\"\"password."
#~ msgstr "如果您有帳戶，將收到一封電郵，內含重設密碼的指示。"

#: src/components/common/ReportTable/index.tsx:303
#: src/locales.ts:37
msgid "{0}"
msgstr "{0}"

#: src/components/layouts/CheckIn/index.tsx:82
msgid "{0} <0>checked in</0> successfully"
msgstr "{0} <0>簽到</0>成功"

#: src/components/layouts/CheckIn/index.tsx:106
msgid "{0} <0>checked out</0> successfully"
msgstr "{0} <0>簽退</0>成功"

#: src/components/routes/event/Webhooks/index.tsx:24
msgid "{0} Active Webhooks"
msgstr "{0} 個活動的 Webhook"

#: src/components/routes/product-widget/SelectProducts/index.tsx:412
msgid "{0} available"
msgstr "{0}可用"

#: src/components/common/AttendeeCheckInTable/index.tsx:155
#~ msgid "{0} checked in"
#~ msgstr "{0}簽到"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
msgid "{0} created successfully"
msgstr "成功創建 {0}"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
msgid "{0} updated successfully"
msgstr "{0}更新成功"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:57
msgid "{0}'s Events"
msgstr "{0}的活動"

#: src/components/layouts/CheckIn/index.tsx:449
msgid "{0}/{1} checked in"
msgstr "{0}/{1} 已簽到"

#: src/components/common/Countdown/index.tsx:68
msgid "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{days} 天, {hours} 小時, {minutes} 分鐘, 和 {seconds} 秒"

#: src/components/common/WebhookTable/index.tsx:79
msgid "{eventCount} events"
msgstr "{eventCount} 個事件"

#: src/components/common/Countdown/index.tsx:70
msgid "{hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{hours} 小時, {minutes} 分鐘, 和 {seconds} 秒"

#: src/components/common/Countdown/index.tsx:72
msgid "{minutes} minutes and {seconds} seconds"
msgstr "{分}分鐘和{秒}秒鐘"

#: src/components/routes/welcome/index.tsx:59
msgid "{organizerName}'s first event"
msgstr "{組織者名稱}的首次活動"

#: src/components/common/ReportTable/index.tsx:256
#~ msgid "{title}"
#~ msgstr ""

#: src/components/common/CapacityAssignmentList/index.tsx:47
msgid "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"
msgstr "<0>容量分配讓你可以管理票務或整個活動的容量。非常適合多日活動、研討會等，需要控制出席人數的場合。</0><1>例如，你可以將容量分配與<2>第一天</2>和<3>所有天數</3>的票關聯起來。一旦達到容量，這兩種票將自動停止銷售。</1>"

#: src/components/common/CheckInListList/index.tsx:49
msgid "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"
msgstr "<0>簽到列表幫助管理活動的參與者入場。您可以將多個票與一個簽到列表關聯，並確保只有持有效票的人員才能入場。</0>"

#: src/components/common/WidgetEditor/index.tsx:324
msgid "<0>https://</0>your-website.com"
msgstr "<0>https://</0>your-website.com"

#: src/components/forms/ProductForm/index.tsx:287
msgid "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"
msgstr "<0>請輸入不含税費的價格。</0><1>税費可以在下方添加。</1>"

#: src/components/forms/ProductForm/index.tsx:304
msgid "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"
msgstr "<0>該產品的可用數量</0><1>如果該產品有相關的<2>容量限制</2>，此值可以被覆蓋。</1>"

#: src/components/forms/TicketForm/index.tsx:249
#~ msgid "<0>The number of tickets available for this ticket</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this ticket.</1>"
#~ msgstr "<0>此票的可用票數</0><1>如果此票有<2>容量限制</2>，此值可以被覆蓋。</1>"

#: src/components/common/WebhookTable/index.tsx:205
msgid "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"
msgstr "<0>Webhooks 可在事件發生時立即通知外部服務，例如，在註冊時將新與會者添加到您的 CRM 或郵件列表，確保無縫自動化。</0><1>使用第三方服務，如 <2>Zapier</2>、<3>IFTTT</3> 或 <4>Make</4> 來創建自定義工作流並自動化任務。</1>"

#: src/components/routes/event/GettingStarted/index.tsx:134
msgid "⚡️ Set up your event"
msgstr "⚡️ 設置您的活動"

#: src/components/routes/event/GettingStarted/index.tsx:190
msgid "✉️ Confirm your email address"
msgstr "✉️ 確認您的電子郵件地址"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "🎉 Congratulations on creating an event!"
#~ msgstr "恭喜您創建了一個活動！"

#: src/components/routes/event/GettingStarted/index.tsx:67
#~ msgid "🎟️ Add products"
#~ msgstr "🎟️ 添加產品"

#: src/components/routes/event/GettingStarted/index.tsx:119
msgid "🎟️ Add tickets"
msgstr "🎟️ 添加門票"

#: src/components/routes/event/GettingStarted/index.tsx:162
msgid "🎨 Customize your event page"
msgstr "🎨 自定義活動頁面"

#: src/components/routes/event/GettingStarted/index.tsx:147
msgid "💳 Connect with Stripe"
msgstr "與 Stripe 連接"

#: src/components/routes/event/GettingStarted/index.tsx:176
msgid "🚀 Set your event live"
msgstr "🚀 實時設置您的活動"

#: src/components/common/Countdown/index.tsx:34
msgid "0 minutes and 0 seconds"
msgstr "0 分 0 秒"

#: src/components/routes/event/Webhooks/index.tsx:23
msgid "1 Active Webhook"
msgstr "1 個活動的 Webhook"

#: src/components/modals/RefundOrderModal/index.tsx:105
msgid "10.00"
msgstr "10.00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:123
msgid "123 Main Street"
msgstr "緬因街 123 號"

#: src/components/common/WidgetEditor/index.tsx:226
msgid "20"
msgstr "20"

#: src/components/routes/welcome/index.tsx:91
msgid "2024-01-01 10:00"
msgstr "2024-01-01 10:00"

#: src/components/routes/welcome/index.tsx:97
msgid "2024-01-01 18:00"
msgstr "2024-01-01 18:00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:147
msgid "94103"
msgstr "94103"

#: src/components/forms/QuestionForm/index.tsx:138
msgid "A date input. Perfect for asking for a date of birth etc."
msgstr "日期輸入字段。非常適合詢問出生日期等。"

#: src/components/forms/TaxAndFeeForm/index.tsx:88
msgid "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."
msgstr "默認的{type}會自動應用於所有新產品。您可以為每個產品單獨覆蓋此設置。"

#: src/components/forms/TaxAndFeeForm/index.tsx:88
#~ msgid "A default {type} is automaticaly applied to all new tickets. You can override this on a per ticket basis."
#~ msgstr "默認 {type} 會自動應用於所有新票單。您可以根據每張票單的具體情況覆蓋該默認值。"

#: src/components/forms/QuestionForm/index.tsx:126
msgid "A Dropdown input allows only one selection"
msgstr "下拉式輸入法只允許一個選擇"

#: src/components/forms/TaxAndFeeForm/index.tsx:20
msgid "A fee, like a booking fee or a service fee"
msgstr "費用，如預訂費或服務費"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
msgid "A fixed amount per product. E.g, $0.50 per product"
msgstr "每個產品的固定金額。例如，每個產品$0.50"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
#~ msgid "A fixed amount per ticket. E.g, $0.50 per ticket"
#~ msgstr "每張票的固定金額。例如，每張票 0.50 美元"

#: src/components/forms/QuestionForm/index.tsx:108
msgid "A multi line text input"
msgstr "多行文本輸入"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
msgid "A percentage of the product price. E.g., 3.5% of the product price"
msgstr "產品價格的百分比。例如，3.5%的產品價格"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
#~ msgid "A percentage of the ticket price. E.g., 3.5% of the ticket price"
#~ msgstr "票價的百分比。例如，票價的 3.5%"

#: src/components/forms/PromoCodeForm/index.tsx:37
msgid "A promo code with no discount can be used to reveal hidden products."
msgstr "無折扣的促銷代碼可以用來顯示隱藏的產品。"

#: src/components/forms/PromoCodeForm/index.tsx:36
#~ msgid "A promo code with no discount can be used to reveal hidden tickets."
#~ msgstr "使用無折扣的促銷代碼可顯示隱藏的門票。"

#: src/components/forms/QuestionForm/index.tsx:120
msgid "A Radio option has multiple options but only one can be selected."
msgstr "單選題有多個選項，但只能選擇一個。"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:69
msgid "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"
msgstr "活動的簡短描述，將顯示在搜索引擎結果中，並在社交媒體上分享時顯示。默認情況下，將使用活動描述"

#: src/components/forms/QuestionForm/index.tsx:102
msgid "A single line text input"
msgstr "單行文本輸入"

#: src/components/forms/QuestionForm/index.tsx:92
#~ msgid "A single question per attendee. E.g, What is your preferred meal?"
#~ msgstr "每位與會者只需回答一個問題。例如：您喜歡吃什麼？"

#: src/components/forms/QuestionForm/index.tsx:86
#~ msgid "A single question per order. E.g, What is your company name?"
#~ msgstr "每份訂單隻有一個問題。例如：貴公司的名稱是什麼？"

#: src/components/forms/QuestionForm/index.tsx:87
msgid "A single question per order. E.g, What is your shipping address?"
msgstr "每個訂單一個問題。例如，您的送貨地址是什麼？"

#: src/components/forms/QuestionForm/index.tsx:93
msgid "A single question per product. E.g, What is your t-shirt size?"
msgstr "每個產品一個問題。例如，您的T恤尺碼是多少？"

#: src/components/forms/TaxAndFeeForm/index.tsx:14
msgid "A standard tax, like VAT or GST"
msgstr "標準税，如增值税或消費税"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:76
msgid "About"
msgstr "關於"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:211
msgid "About Stripe Connect"
msgstr "關於 Stripe Connect"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:79
#~ msgid "About the event"
#~ msgstr "關於活動"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:91
msgid "Accept bank transfers, checks, or other offline payment methods"
msgstr "接受銀行轉賬、支票或其他線下支付方式"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:86
msgid "Accept credit card payments with Stripe"
msgstr "通過 Stripe 接受信用卡支付"

#: src/components/routes/auth/AcceptInvitation/index.tsx:126
msgid "Accept Invitation"
msgstr "接受邀請"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:125
msgid "Access Denied"
msgstr "訪問被拒絕"

#: src/components/routes/account/ManageAccount/index.tsx:24
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:53
msgid "Account"
msgstr "賬户"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:62
msgid "Account Name"
msgstr "賬户名稱"

#: src/components/common/GlobalMenu/index.tsx:32
#: src/components/routes/account/ManageAccount/index.tsx:19
msgid "Account Settings"
msgstr "賬户設置"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:42
msgid "Account updated successfully"
msgstr "賬户更新成功"

#: src/components/common/AttendeeTable/index.tsx:158
#: src/components/common/ProductsTable/SortableProduct/index.tsx:267
#: src/components/common/QuestionsTable/index.tsx:108
msgid "Actions"
msgstr "行動"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "Activate"
msgstr "激活"

#: src/components/forms/CheckInListForm/index.tsx:47
msgid "Activation date"
msgstr "激活日期"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:19
#: src/components/modals/EditUserModal/index.tsx:110
msgid "Active"
msgstr "活躍"

#: src/components/routes/event/attendees.tsx:59
#~ msgid "Add"
#~ msgstr "添加"

#: src/components/forms/CheckInListForm/index.tsx:39
msgid "Add a description for this check-in list"
msgstr "為此簽到列表添加描述"

#: src/components/modals/ManageAttendeeModal/index.tsx:131
msgid "Add any notes about the attendee. These will not be visible to the attendee."
msgstr "添加有關與會者的任何備註。這些將不會對與會者可見。"

#: src/components/modals/ManageAttendeeModal/index.tsx:133
msgid "Add any notes about the attendee..."
msgstr "添加有關與會者的任何備註..."

#: src/components/modals/ManageOrderModal/index.tsx:165
msgid "Add any notes about the order. These will not be visible to the customer."
msgstr "添加關於訂單的備註。這些信息不會對客户可見。"

#: src/components/modals/ManageOrderModal/index.tsx:169
msgid "Add any notes about the order..."
msgstr "添加關於訂單的備註..."

#: src/components/forms/QuestionForm/index.tsx:202
msgid "Add description"
msgstr "添加描述"

#: src/components/routes/event/GettingStarted/index.tsx:85
#~ msgid "Add event details and and manage event settings."
#~ msgstr "添加活動詳情並管理活動設置。"

#: src/components/routes/event/GettingStarted/index.tsx:137
msgid "Add event details and manage event settings."
msgstr "新增活動詳情並管理活動設定。"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:153
msgid "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"
msgstr "添加線下支付的説明（例如，銀行轉賬詳情、支票寄送地址、付款截止日期）"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add More products"
#~ msgstr "添加更多產品"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add More tickets"
msgstr "添加更多門票"

#: src/components/common/HeadingCard/index.tsx:27
msgid "Add New"
msgstr "添加新內容"

#: src/components/forms/QuestionForm/index.tsx:68
msgid "Add Option"
msgstr "添加選項"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:68
msgid "Add Product"
msgstr "添加產品"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:51
msgid "Add Product to Category"
msgstr "將產品添加到類別"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add products"
#~ msgstr "添加產品"

#: src/components/common/QuestionsTable/index.tsx:281
msgid "Add question"
msgstr "添加問題"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:18
msgid "Add Tax or Fee"
msgstr "加税或費用"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add tickets"
msgstr "添加機票"

#: src/components/forms/ProductForm/index.tsx:337
msgid "Add tier"
msgstr "增加層級"

#: src/components/common/AddEventToCalendarButton/index.tsx:112
#: src/components/common/AddEventToCalendarButton/index.tsx:120
msgid "Add to Calendar"
msgstr "添加到日曆"

#: src/components/common/WebhookTable/index.tsx:223
#: src/components/routes/event/Webhooks/index.tsx:35
msgid "Add Webhook"
msgstr "添加 Webhook"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:219
msgid "Additional Information"
msgstr "附加信息"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/forms/ProductForm/index.tsx:348
msgid "Additional Options"
msgstr "附加選項"

#: src/components/common/OrderDetails/index.tsx:96
#: src/components/forms/QuestionForm/index.tsx:130
msgid "Address"
msgstr "地址"

#: src/components/common/CheckoutQuestion/index.tsx:156
msgid "Address line 1"
msgstr "地址第 1 行"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:122
#: src/components/routes/product-widget/CollectInformation/index.tsx:319
#: src/components/routes/product-widget/CollectInformation/index.tsx:320
msgid "Address Line 1"
msgstr "地址 1"

#: src/components/common/CheckoutQuestion/index.tsx:159
msgid "Address line 2"
msgstr "地址第 2 行"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:127
#: src/components/routes/product-widget/CollectInformation/index.tsx:324
#: src/components/routes/product-widget/CollectInformation/index.tsx:325
msgid "Address Line 2"
msgstr "地址第 2 行"

#: src/components/modals/EditUserModal/index.tsx:49
#: src/components/modals/InviteUserModal/index.tsx:41
msgid "Admin"
msgstr "管理員"

#: src/components/modals/EditUserModal/index.tsx:51
#: src/components/modals/InviteUserModal/index.tsx:43
msgid "Admin users have full access to events and account settings."
msgstr "管理員用户可以完全訪問事件和賬户設置。"

#: src/components/layouts/Event/index.tsx:53
#~ msgid "Affiliates"
#~ msgstr "附屬機構"

#: src/components/common/MessageList/index.tsx:21
msgid "All attendees"
msgstr "所有與會者"

#: src/components/modals/SendMessageModal/index.tsx:187
msgid "All attendees of this event"
msgstr "本次活動的所有與會者"

#: src/components/routes/events/Dashboard/index.tsx:52
#~ msgid "All Events"
#~ msgstr "所有活動"

#: src/components/common/PromoCodeTable/index.tsx:131
msgid "All Products"
msgstr "所有產品"

#: src/components/common/PromoCodeTable/index.tsx:130
#: src/components/forms/PromoCodeForm/index.tsx:67
#~ msgid "All Tickets"
#~ msgstr "所有門票"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:157
msgid "Allow attendees associated with unpaid orders to check in"
msgstr "允許與未支付訂單關聯的參與者簽到"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:82
msgid "Allow search engine indexing"
msgstr "允許搜索引擎索引"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:81
msgid "Allow search engines to index this event"
msgstr "允許搜索引擎索引此事件"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:78
msgid "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."
msgstr "快到了！我們正在等待處理您的付款。只需幾秒鐘。"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:77
msgid "Amazing, Event, Keywords..."
msgstr "令人驚歎, 活動, 關鍵詞..."

#: src/components/common/OrdersTable/index.tsx:207
#: src/components/forms/TaxAndFeeForm/index.tsx:71
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:36
msgid "Amount"
msgstr "金額"

#: src/components/modals/CreateAttendeeModal/index.tsx:165
msgid "Amount paid ({0})"
msgstr "支付金額 ({0})"

#: src/mutations/useExportAnswers.ts:45
msgid "An error occurred while checking export status."
msgstr "檢查導出狀態時發生錯誤。"

#: src/components/common/ErrorDisplay/index.tsx:18
msgid "An error occurred while loading the page"
msgstr "加載頁面時出現錯誤"

#: src/components/common/QuestionsTable/index.tsx:148
msgid "An error occurred while sorting the questions. Please try again or refresh the page"
msgstr "問題排序時發生錯誤。請重試或刷新頁面"

#: src/components/common/TicketsTable/index.tsx:47
#~ msgid "An error occurred while sorting the tickets. Please try again or refresh the page"
#~ msgstr "在整理門票時發生錯誤。請重試或刷新頁面"

#: src/components/routes/welcome/index.tsx:75
#~ msgid "An event is the actual event you are hosting. You can add more details later."
#~ msgstr "事件是您要舉辦的實際活動。您可以稍後添加更多細節。"

#: src/components/routes/welcome/index.tsx:75
msgid "An event is the gathering or occasion you’re organizing. You can add more details later."
msgstr "活動是您正在組織的聚會或場合。您可以稍後添加更多詳細信息。"

#: src/components/routes/welcome/index.tsx:23
msgid "An organizer is the company or person who is hosting the event"
msgstr "主辦方是指舉辦活動的公司或個人"

#: src/components/forms/StripeCheckoutForm/index.tsx:40
msgid "An unexpected error occurred."
msgstr "出現意外錯誤。"

#: src/hooks/useFormErrorResponseHandler.tsx:48
msgid "An unexpected error occurred. Please try again."
msgstr "出現意外錯誤。請重試。"

#: src/components/common/QuestionAndAnswerList/index.tsx:97
msgid "Answer updated successfully."
msgstr "答案更新成功。"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
msgid "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
msgstr "產品持有者的任何查詢都將發送到此電子郵件地址。此地址還將用作從此活動發送的所有電子郵件的“回覆至”地址"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
#~ msgid "Any queries from ticket holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
#~ msgstr "持票人的任何詢問都將發送到此電子郵件地址。該地址也將作為本次活動發送的所有電子郵件的 \"回覆地址\"。"

#: src/components/common/WidgetEditor/index.tsx:213
msgid "Appearance"
msgstr "外觀"

#: src/components/routes/product-widget/SelectProducts/index.tsx:500
msgid "applied"
msgstr "應用"

#: src/components/common/CapacityAssignmentList/index.tsx:97
msgid "Applies to {0} products"
msgstr "適用於{0}個產品"

#: src/components/common/CapacityAssignmentList/index.tsx:97
#~ msgid "Applies to {0} tickets"
#~ msgstr "適用於{0}張票"

#: src/components/common/CapacityAssignmentList/index.tsx:98
msgid "Applies to 1 product"
msgstr "適用於1個產品"

#: src/components/common/CapacityAssignmentList/index.tsx:98
#~ msgid "Applies to 1 ticket"
#~ msgstr "適用於1張票"

#: src/components/common/FilterModal/index.tsx:253
msgid "Apply"
msgstr "應用"

#: src/components/routes/product-widget/SelectProducts/index.tsx:528
msgid "Apply Promo Code"
msgstr "應用促銷代碼"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
msgid "Apply this {type} to all new products"
msgstr "將此{type}應用於所有新產品"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
#~ msgid "Apply this {type} to all new tickets"
#~ msgstr "將此 {類型}應用於所有新票"

#: src/components/common/EventCard/index.tsx:167
msgid "Archive event"
msgstr "歸檔活動"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:32
msgid "Archived"
msgstr "已歸檔"

#: src/components/routes/events/Dashboard/index.tsx:59
msgid "Archived Events"
msgstr "已歸檔的活動"

#: src/components/common/AttendeeTable/index.tsx:76
msgid "Are you sure you want to activate this attendee?"
msgstr "您確定要激活該與會者嗎？"

#: src/components/common/EventCard/index.tsx:58
msgid "Are you sure you want to archive this event?"
msgstr "您確定要歸檔此活動嗎?"

#: src/components/common/AttendeeTable/index.tsx:78
#~ msgid "Are you sure you want to cancel this attendee? This will void their product"
#~ msgstr "您確定要取消該參會者嗎？這將使他們的產品無效"

#: src/components/common/AttendeeTable/index.tsx:77
msgid "Are you sure you want to cancel this attendee? This will void their ticket"
msgstr "您確定要取消該與會者嗎？這將使其門票作廢"

#: src/components/common/PromoCodeTable/index.tsx:37
msgid "Are you sure you want to delete this promo code?"
msgstr "您確定要刪除此促銷代碼嗎？"

#: src/components/common/QuestionsTable/index.tsx:164
msgid "Are you sure you want to delete this question?"
msgstr "您確定要刪除這個問題嗎？"

#: src/components/common/WebhookTable/index.tsx:122
msgid "Are you sure you want to delete this webhook?"
msgstr "您確定要刪除此 Webhook 嗎？"

#: src/components/layouts/Event/index.tsx:68
#: src/components/routes/event/EventDashboard/index.tsx:64
msgid "Are you sure you want to make this event draft? This will make the event invisible to the public"
msgstr "您確定要將此活動設為草稿嗎？這將使公眾無法看到該活動"

#: src/components/layouts/Event/index.tsx:69
#: src/components/routes/event/EventDashboard/index.tsx:65
msgid "Are you sure you want to make this event public? This will make the event visible to the public"
msgstr "您確定要將此事件公開嗎？這將使事件對公眾可見"

#: src/components/common/EventCard/index.tsx:59
msgid "Are you sure you want to restore this event? It will be restored as a draft event."
msgstr "您確定要恢復此活動嗎？它將作為草稿恢復。"

#: src/components/common/CapacityAssignmentList/index.tsx:163
msgid "Are you sure you would like to delete this Capacity Assignment?"
msgstr "您確定要刪除此容量分配嗎？"

#: src/components/common/CheckInListList/index.tsx:183
msgid "Are you sure you would like to delete this Check-In List?"
msgstr "您確定要刪除此簽到列表嗎？"

#: src/components/forms/QuestionForm/index.tsx:90
#~ msgid "Ask once per attendee"
#~ msgstr "每位與會者提問一次"

#: src/components/forms/QuestionForm/index.tsx:85
msgid "Ask once per order"
msgstr "每份訂單詢問一次"

#: src/components/forms/QuestionForm/index.tsx:91
msgid "Ask once per product"
msgstr "每個產品詢問一次"

#: src/components/modals/CreateWebhookModal/index.tsx:33
msgid "At least one event type must be selected"
msgstr "必須選擇至少一種事件類型"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Attendee"
msgstr "與會者"

#: src/components/forms/WebhookForm/index.tsx:94
msgid "Attendee Cancelled"
msgstr "與會者已取消"

#: src/components/forms/WebhookForm/index.tsx:82
msgid "Attendee Created"
msgstr "與會者已創建"

#: src/components/modals/ManageAttendeeModal/index.tsx:147
msgid "Attendee Details"
msgstr "與會者詳情"

#: src/components/layouts/AuthLayout/index.tsx:73
msgid "Attendee Management"
msgstr "與會者管理"

#: src/components/layouts/CheckIn/index.tsx:150
msgid "Attendee not found"
msgstr "未找到參與者"

#: src/components/modals/ManageAttendeeModal/index.tsx:153
msgid "Attendee Notes"
msgstr "與會者備註"

#: src/components/common/QuestionsTable/index.tsx:369
msgid "Attendee questions"
msgstr "與會者提問"

#: src/components/modals/ManageAttendeeModal/index.tsx:172
msgid "Attendee Ticket"
msgstr "參會者票"

#: src/components/forms/WebhookForm/index.tsx:88
msgid "Attendee Updated"
msgstr "與會者已更新"

#: src/components/common/OrdersTable/index.tsx:206
#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
#: src/components/common/StatBoxes/index.tsx:27
#: src/components/layouts/Event/index.tsx:99
#: src/components/modals/ManageOrderModal/index.tsx:126
#: src/components/routes/event/attendees.tsx:59
msgid "Attendees"
msgstr "與會者"

#: src/components/routes/event/attendees.tsx:43
msgid "Attendees Exported"
msgstr "與會者已導出"

#: src/components/routes/event/EventDashboard/index.tsx:239
msgid "Attendees Registered"
msgstr "註冊的參會者"

#: src/components/modals/SendMessageModal/index.tsx:146
#~ msgid "Attendees with a specific product"
#~ msgstr "具有特定產品的參會者"

#: src/components/modals/SendMessageModal/index.tsx:183
msgid "Attendees with a specific ticket"
msgstr "持有特定門票的與會者"

#: src/components/common/WidgetEditor/index.tsx:233
msgid "Auto Resize"
msgstr "自動調整大小"

#: src/components/layouts/AuthLayout/index.tsx:88
#~ msgid "Auto Workflow"
#~ msgstr "自動化工作流"

#: src/components/layouts/AuthLayout/index.tsx:79
msgid "Automated entry management with multiple check-in lists and real-time validation"
msgstr "使用多個簽到列表和實時驗證的自動化入場管理"

#: src/components/common/WidgetEditor/index.tsx:236
msgid "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."
msgstr "根據內容自動調整 widget 高度。禁用時，窗口小部件將填充容器的高度。"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
#~ msgid "Avg Discount/Order"
#~ msgstr "平均折扣/訂單"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
#~ msgid "Avg Order Value"
#~ msgstr "平均訂單價值"

#: src/components/common/OrderStatusBadge/index.tsx:15
#: src/components/modals/SendMessageModal/index.tsx:231
msgid "Awaiting offline payment"
msgstr "等待線下付款"

#: src/components/routes/event/orders.tsx:25
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:43
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:66
msgid "Awaiting Offline Payment"
msgstr "等待線下付款"

#: src/components/layouts/CheckIn/index.tsx:263
msgid "Awaiting payment"
msgstr "等待付款"

#: src/components/common/AttendeeTicket/index.tsx:67
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:40
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:65
msgid "Awaiting Payment"
msgstr "等待付款"

#: src/components/routes/welcome/index.tsx:84
msgid "Awesome Event"
msgstr "精彩活動"

#: src/components/forms/OrganizerForm/index.tsx:28
msgid "Awesome Organizer Ltd."
msgstr "Awesome Organizer Ltd."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:42
msgid "Back to all events"
msgstr "返回所有活動"

#: src/components/layouts/Checkout/index.tsx:73
#: src/components/routes/product-widget/CollectInformation/index.tsx:249
#: src/components/routes/product-widget/CollectInformation/index.tsx:261
msgid "Back to event page"
msgstr "返回活動頁面"

#: src/components/routes/auth/ForgotPassword/index.tsx:59
#: src/components/routes/auth/ResetPassword/index.tsx:66
msgid "Back to login"
msgstr "返回登錄"

#: src/components/common/WidgetEditor/index.tsx:175
msgid "Background Color"
msgstr "背景顏色"

#: src/components/routes/event/HomepageDesigner/index.tsx:144
msgid "Background Type"
msgstr "背景類型"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#~ msgid "Basic Details"
#~ msgstr "基本詳情"

#: src/components/modals/SendMessageModal/index.tsx:278
msgid "Before you send!"
msgstr "發送之前"

#: src/components/routes/event/GettingStarted/index.tsx:60
#~ msgid "Before your event can go live, there are a few things you need to do."
#~ msgstr "在活動上線之前，您需要做幾件事。"

#: src/components/routes/event/GettingStarted/index.tsx:92
msgid "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."
msgstr "在活動上線之前，您需要完成以下幾項步驟。請完成以下所有步驟以開始。"

#: src/components/routes/auth/Register/index.tsx:66
#~ msgid "Begin selling products in minutes"
#~ msgstr "幾分鐘內開始銷售產品"

#: src/components/routes/auth/Register/index.tsx:49
#~ msgid "Begin selling tickets in minutes"
#~ msgstr "幾分鐘內開始售票"

#: src/components/routes/product-widget/CollectInformation/index.tsx:313
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:142
msgid "Billing Address"
msgstr "賬單地址"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:167
msgid "Billing Settings"
msgstr "賬單設置"

#: src/components/layouts/AuthLayout/index.tsx:83
#~ msgid "Brand Control"
#~ msgstr "品牌控制"

#: src/components/common/LanguageSwitcher/index.tsx:28
msgid "Brazilian Portuguese"
msgstr "巴西葡萄牙語"

#: src/components/routes/auth/Register/index.tsx:133
msgid "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."
msgstr "註冊即表示您同意我們的<0>服務條款</0>和<1>隱私政策</1>。"

#: src/components/forms/TaxAndFeeForm/index.tsx:52
msgid "Calculation Type"
msgstr "計算類型"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:140
msgid "California"
msgstr "加利福尼亞州"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:175
msgid "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."
msgstr "相機權限被拒絕。<0>再次請求權限</0>，如果還不行，則需要在瀏覽器設置中<1>授予此頁面</1>訪問相機的權限。"

#: src/components/common/AttendeeTable/index.tsx:182
#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/QuestionAndAnswerList/index.tsx:149
#: src/components/layouts/CheckIn/index.tsx:225
#: src/utilites/confirmationDialog.tsx:11
msgid "Cancel"
msgstr "取消"

#: src/components/routes/profile/ManageProfile/index.tsx:126
msgid "Cancel email change"
msgstr "取消更改電子郵件"

#: src/components/common/OrdersTable/index.tsx:190
#: src/components/modals/RefundOrderModal/index.tsx:112
msgid "Cancel order"
msgstr "取消訂單"

#: src/components/modals/CancelOrderModal/index.tsx:59
msgid "Cancel Order"
msgstr "取消訂單"

#: src/components/modals/CancelOrderModal/index.tsx:44
msgid "Cancel Order {0}"
msgstr "取消訂單 {0}"

#: src/components/common/AttendeeDetails/index.tsx:32
#~ msgid "Canceled"
#~ msgstr "已取消"

#: src/components/modals/CancelOrderModal/index.tsx:54
msgid "Canceling will cancel all products associated with this order, and release the products back into the available pool."
msgstr "取消將會取消與此訂單關聯的所有產品，並將產品釋放回可用庫存。"

#: src/components/modals/CancelOrderModal/index.tsx:54
#~ msgid "Canceling will cancel all tickets associated with this order, and release the tickets back into the available pool."
#~ msgstr "取消將取消與此訂單相關的所有機票，並將機票放回可用票池。"

#: src/components/common/AttendeeTicket/index.tsx:61
#: src/components/common/OrderStatusBadge/index.tsx:12
#: src/components/routes/event/orders.tsx:24
msgid "Cancelled"
msgstr "已取消"

#: src/components/layouts/CheckIn/index.tsx:173
msgid "Cannot Check In"
msgstr "無法簽到"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:42
#: src/components/layouts/Event/index.tsx:103
msgid "Capacity"
msgstr "容量"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:36
msgid "Capacity Assignment created successfully"
msgstr "容量分配創建成功"

#: src/components/common/CapacityAssignmentList/index.tsx:31
msgid "Capacity Assignment deleted successfully"
msgstr "容量分配刪除成功"

#: src/components/routes/event/CapacityAssignments/index.tsx:32
msgid "Capacity Management"
msgstr "容量管理"

#: src/components/modals/CreateProductCategoryModal/index.tsx:56
msgid "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."
msgstr "類別允許您將產品分組。例如，您可以有一個“門票”類別和另一個“商品”類別。"

#: src/components/forms/ProductForm/index.tsx:267
msgid "Categories help you organize your products. This title will be displayed on the public event page."
msgstr "類別幫助您組織產品。此標題將在公共活動頁面上顯示。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:91
msgid "Categories reordered successfully."
msgstr "類別重新排序成功。"

#: src/components/routes/event/products.tsx:96
msgid "Category"
msgstr "類別"

#: src/components/modals/CreateProductCategoryModal/index.tsx:37
msgid "Category Created Successfully"
msgstr "類別創建成功"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Change Cover"
msgstr "更改封面"

#: src/components/routes/profile/ManageProfile/index.tsx:201
msgid "Change password"
msgstr "更改密碼"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check in"
#~ msgstr "報到"

#: src/components/layouts/CheckIn/index.tsx:180
msgid "Check In"
msgstr "報到"

#: src/components/layouts/CheckIn/index.tsx:193
msgid "Check in {0} {1}"
msgstr "簽到 {0} {1}"

#: src/components/layouts/CheckIn/index.tsx:218
msgid "Check in and mark order as paid"
msgstr "簽到並將訂單標記為已支付"

#: src/components/layouts/CheckIn/index.tsx:209
msgid "Check in only"
msgstr "僅簽到"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check out"
#~ msgstr "查看"

#: src/components/layouts/CheckIn/index.tsx:177
msgid "Check Out"
msgstr "簽退"

#: src/components/layouts/Checkout/index.tsx:106
msgid "Check out this event!"
msgstr "看看這個活動吧！"

#: src/components/common/EventCard/index.tsx:156
msgid "Check-in"
msgstr "辦理登機手續"

#: src/components/layouts/Event/index.tsx:52
#~ msgid "Check-In"
#~ msgstr "辦理登機手續"

#: src/components/forms/WebhookForm/index.tsx:100
msgid "Check-in Created"
msgstr "簽到已創建"

#: src/components/forms/WebhookForm/index.tsx:106
msgid "Check-in Deleted"
msgstr "簽到已刪除"

#: src/components/modals/CreateCheckInListModal/index.tsx:40
msgid "Check-In List created successfully"
msgstr "簽到列表創建成功"

#: src/components/common/CheckInListList/index.tsx:33
msgid "Check-In List deleted successfully"
msgstr "簽到列表刪除成功"

#: src/components/layouts/CheckIn/index.tsx:326
msgid "Check-in list has expired"
msgstr "簽到列表已過期"

#: src/components/layouts/CheckIn/index.tsx:343
msgid "Check-in list is not active"
msgstr "簽到列表未激活"

#: src/components/layouts/CheckIn/index.tsx:311
msgid "Check-in list not found"
msgstr "未找到簽到列表"

#: src/components/layouts/Event/index.tsx:104
#: src/components/routes/event/CheckInLists/index.tsx:32
msgid "Check-In Lists"
msgstr "簽到列表"

#: src/components/common/CheckInListList/index.tsx:162
msgid "Check-In URL copied to clipboard"
msgstr "簽到鏈接已複製到剪貼板"

#: src/components/forms/QuestionForm/index.tsx:114
msgid "Checkbox options allow multiple selections"
msgstr "複選框選項允許多重選擇"

#: src/components/forms/QuestionForm/index.tsx:112
msgid "Checkboxes"
msgstr "複選框"

#: src/components/common/AttendeeDetails/index.tsx:29
msgid "Checked In"
msgstr "登記入住"

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "Checked in successfully"
#~ msgstr "簽到成功"

#: src/components/layouts/Checkout/index.tsx:83
#: src/components/routes/event/Settings/index.tsx:40
msgid "Checkout"
msgstr "結賬"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:61
msgid "Checkout Settings"
msgstr "結賬設置"

#: src/locales.ts:46
#~ msgid "Chinese"
#~ msgstr "中文"

#: src/components/common/LanguageSwitcher/index.tsx:30
msgid "Chinese (Simplified)"
msgstr "簡體中文"

#: src/components/common/LanguageSwitcher/index.tsx:32
msgid "Chinese (Traditional)"
msgstr "中文（繁體）"

#: src/components/routes/event/HomepageDesigner/index.tsx:133
msgid "Choose a color for your background"
msgstr "選擇背景顏色"

#: src/components/modals/ChooseAccountModal/index.tsx:13
msgid "Choose an account"
msgstr "選擇賬户"

#: src/components/common/CheckoutQuestion/index.tsx:162
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:134
#: src/components/routes/product-widget/CollectInformation/index.tsx:333
#: src/components/routes/product-widget/CollectInformation/index.tsx:334
msgid "City"
msgstr "城市"

#: src/components/common/SearchBar/index.tsx:75
msgid "Clear Search Text"
msgstr "清除搜索文本"

#: src/components/routes/product-widget/SelectProducts/index.tsx:288
#~ msgid "click here"
#~ msgstr "點擊此處"

#: src/components/common/PromoCodeTable/index.tsx:98
msgid "Click to copy"
msgstr "點擊複製"

#: src/components/routes/product-widget/SelectProducts/index.tsx:533
msgid "close"
msgstr "關閉"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:186
#: src/components/modals/RefundOrderModal/index.tsx:125
#: src/components/routes/product-widget/SelectProducts/index.tsx:534
msgid "Close"
msgstr "關閉"

#: src/components/layouts/Event/index.tsx:281
msgid "Close sidebar"
msgstr "關閉側邊欄"

#: src/components/common/PromoCodeTable/index.tsx:69
#: src/components/forms/PromoCodeForm/index.tsx:34
msgid "Code"
msgstr "代碼"

#: src/components/modals/CreatePromoCodeModal/index.tsx:26
#: src/components/modals/EditPromoCodeModal/index.tsx:38
msgid "Code must be between 3 and 50 characters long"
msgstr "代碼長度必須在 3 至 50 個字符之間"

#: src/components/forms/ProductForm/index.tsx:401
msgid "Collapse this product when the event page is initially loaded"
msgstr "當活動頁面初始加載時摺疊此產品"

#: src/components/forms/TicketForm/index.tsx:346
#~ msgid "Collapse this ticket when the event page is initially loaded"
#~ msgstr "加載活動頁面時摺疊此票"

#: src/components/routes/event/HomepageDesigner/index.tsx:131
msgid "Color"
msgstr "顏色"

#: src/components/common/WidgetEditor/index.tsx:21
msgid "Color must be a valid hex color code. Example: #ffffff"
msgstr "顏色必須是有效的十六進制顏色代碼。例如#ffffff"

#: src/components/common/WidgetEditor/index.tsx:166
#: src/components/routes/event/HomepageDesigner/index.tsx:124
msgid "Colors"
msgstr "顏色"

#: src/components/layouts/Event/index.tsx:158
msgid "Coming Soon"
msgstr "即將推出"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
msgid "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"
msgstr "以逗號分隔的描述活動的關鍵字。搜索引擎將使用這些關鍵字來幫助對活動進行分類和索引"

#: src/components/routes/product-widget/CollectInformation/index.tsx:453
msgid "Complete Order"
msgstr "完整訂單"

#: src/components/routes/product-widget/CollectInformation/index.tsx:223
msgid "Complete payment"
msgstr "完成付款"

#: src/components/routes/product-widget/Payment/index.tsx:131
msgid "Complete Payment"
msgstr "完成付款"

#: src/components/layouts/AuthLayout/index.tsx:68
#~ msgid "Complete Store"
#~ msgstr "完整商店"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:202
#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Complete Stripe Setup"
msgstr "完成 Stripe 設置"

#: src/components/routes/event/EventDashboard/index.tsx:127
msgid "Complete these steps to start selling tickets for your event."
msgstr "完成以下步驟即可開始銷售您的活動門票。"

#: src/components/modals/SendMessageModal/index.tsx:230
#: src/components/routes/event/GettingStarted/index.tsx:70
#: src/components/routes/event/orders.tsx:23
msgid "Completed"
msgstr "已完成"

#: src/components/common/StatBoxes/index.tsx:51
msgid "Completed orders"
msgstr "已完成訂單"

#: src/components/routes/event/EventDashboard/index.tsx:237
msgid "Completed Orders"
msgstr "已完成訂單"

#: src/components/common/WidgetEditor/index.tsx:287
msgid "Component Code"
msgstr "組件代碼"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:35
msgid "Configured Discount"
msgstr "已配置折扣"

#: src/utilites/confirmationDialog.tsx:11
msgid "Confirm"
msgstr "確認"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:51
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:56
msgid "Confirm Email Change"
msgstr "確認電子郵件更改"

#: src/components/routes/profile/ManageProfile/index.tsx:199
msgid "Confirm New Password"
msgstr "確認新密碼"

#: src/components/routes/auth/Register/index.tsx:115
msgid "Confirm password"
msgstr "確認密碼"

#: src/components/routes/auth/AcceptInvitation/index.tsx:112
#: src/components/routes/auth/Register/index.tsx:114
#: src/components/routes/auth/ResetPassword/index.tsx:60
msgid "Confirm Password"
msgstr "確認密碼"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:36
msgid "Confirming email address..."
msgstr "確認電子郵件地址..."

#: src/components/routes/event/GettingStarted/index.tsx:88
msgid "Congratulations on creating an event!"
msgstr "恭喜你成功建立活動！"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:173
msgid "Connect Documentation"
msgstr "連接文檔"

#: src/components/routes/event/EventDashboard/index.tsx:192
msgid "Connect payment processing"
msgstr "連接支付處理"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:52
#~ msgid "Connect Stripe"
#~ msgstr "連接條紋"

#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Connect to Stripe"
msgstr "連接到 Stripe"

#: src/components/layouts/AuthLayout/index.tsx:89
msgid "Connect with CRM and automate tasks using webhooks and integrations"
msgstr "連接 CRM 並使用 Webhook 和集成自動化任務"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:201
#: src/components/routes/event/GettingStarted/index.tsx:154
msgid "Connect with Stripe"
msgstr "與 Stripe 連接"

#: src/components/routes/event/GettingStarted/index.tsx:150
msgid "Connect your Stripe account to start receiving payments."
msgstr "連接 Stripe 賬户，開始接收付款。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:148
msgid "Connected to Stripe"
msgstr "已連接到 Stripe"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:98
msgid "Connection Details"
msgstr "連接詳情"

#: src/components/modals/SendMessageModal/index.tsx:167
msgid "Contact Support"
msgstr "聯繫支持"

#: src/components/modals/SendMessageModal/index.tsx:158
msgid "Contact us to enable messaging"
msgstr "聯繫我們以啟用消息功能"

#: src/components/routes/event/HomepageDesigner/index.tsx:155
msgid "Content background color"
msgstr "內容背景顏色"

#: src/components/common/WidgetEditor/index.tsx:31
#: src/components/common/WidgetEditor/index.tsx:217
#: src/components/layouts/Checkout/CheckoutFooter/index.tsx:38
#: src/components/routes/product-widget/CollectInformation/index.tsx:444
#: src/components/routes/product-widget/SelectProducts/index.tsx:486
msgid "Continue"
msgstr "繼續"

#: src/components/routes/event/HomepageDesigner/index.tsx:164
msgid "Continue button text"
msgstr "繼續按鈕文本"

#: src/components/common/WidgetEditor/index.tsx:216
msgid "Continue Button Text"
msgstr "繼續按鈕文本"

#: src/components/modals/CreateEventModal/index.tsx:153
msgid "Continue Event Setup"
msgstr "繼續活動設置"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Continue set up"
msgstr "繼續設置"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
#~ msgid "Continue Stripe Connect Setup"
#~ msgstr "繼續 Stripe 連接設置"

#: src/components/routes/product-widget/SelectProducts/index.tsx:337
msgid "Continue to Checkout"
msgstr "繼續結帳"

#: src/components/routes/product-widget/CollectInformation/index.tsx:380
#~ msgid "Continue To Payment"
#~ msgstr "繼續付款"

#: src/components/common/AttendeeTicket/index.tsx:91
#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:243
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copied"
msgstr "複製的"

#: src/components/common/PromoCodeTable/index.tsx:106
msgid "copied to clipboard"
msgstr "複製到剪貼板"

#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copy"
msgstr "複製"

#: src/components/common/CheckInListList/index.tsx:156
msgid "Copy Check-In URL"
msgstr "複製簽到鏈接"

#: src/components/routes/product-widget/CollectInformation/index.tsx:306
msgid "Copy details to all attendees"
msgstr "將詳細信息抄送給所有與會者"

#: src/components/common/AttendeeTicket/index.tsx:91
msgid "Copy Link"
msgstr "複製鏈接"

#: src/components/common/PromoCodeTable/index.tsx:177
#: src/components/modals/ShareModal/index.tsx:243
msgid "Copy URL"
msgstr "複製 URL"

#: src/components/common/CheckoutQuestion/index.tsx:174
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:152
#: src/components/routes/product-widget/CollectInformation/index.tsx:354
msgid "Country"
msgstr "國家"

#: src/components/routes/event/HomepageDesigner/index.tsx:117
msgid "Cover"
msgstr "封面"

#: src/components/routes/event/attendees.tsx:71
#: src/components/routes/event/products.tsx:74
msgid "Create"
msgstr "創建"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
msgid "Create {0}"
msgstr "創建 {0}"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:59
msgid "Create a Product"
msgstr "創建一個產品"

#: src/components/common/PromoCodeTable/index.tsx:57
msgid "Create a Promo Code"
msgstr "創建促銷代碼"

#: src/components/modals/CreateCheckInListModal/index.tsx:63
msgid "Create a Ticket"
msgstr "創建票單"

#: src/components/routes/auth/Register/index.tsx:69
msgid "Create an account or <0>{0}</0> to get started"
msgstr "創建賬户或 <0>{0}</0> 開始使用"

#: src/components/modals/CreateEventModal/index.tsx:120
msgid "create an organizer"
msgstr "創建一個組織者"

#: src/components/layouts/AuthLayout/index.tsx:27
msgid "Create and customize your event page instantly"
msgstr "立即創建和自定義您的活動頁面"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Create Attendee"
msgstr "創建與會者"

#: src/components/common/CapacityAssignmentList/index.tsx:63
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:68
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:79
#: src/components/routes/event/CapacityAssignments/index.tsx:46
msgid "Create Capacity Assignment"
msgstr "創建容量分配"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:128
#: src/components/common/ProductsTable/SortableCategory/index.tsx:129
msgid "Create category"
msgstr "創建類別"

#: src/components/modals/CreateProductCategoryModal/index.tsx:45
#: src/components/modals/CreateProductCategoryModal/index.tsx:60
msgid "Create Category"
msgstr "創建類別"

#: src/components/common/CheckInListList/index.tsx:61
#: src/components/modals/CreateCheckInListModal/index.tsx:72
#: src/components/modals/CreateCheckInListModal/index.tsx:82
#: src/components/routes/event/CheckInLists/index.tsx:46
msgid "Create Check-In List"
msgstr "創建簽到列表"

#: src/components/common/NoEventsBlankSlate/index.tsx:27
#: src/components/modals/CreateEventModal/index.tsx:85
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:81
#: src/components/routes/welcome/index.tsx:108
msgid "Create Event"
msgstr "創建活動"

#: src/components/routes/events/Dashboard/index.tsx:91
msgid "Create new"
msgstr "創建新的"

#: src/components/forms/OrganizerForm/index.tsx:111
#: src/components/modals/CreateEventModal/index.tsx:93
#: src/components/modals/CreateOrganizerModal/index.tsx:16
msgid "Create Organizer"
msgstr "創建組織器"

#: src/components/modals/CreateProductModal/index.tsx:80
#: src/components/modals/CreateProductModal/index.tsx:88
msgid "Create Product"
msgstr "創建產品"

#: src/components/routes/event/GettingStarted/index.tsx:70
#~ msgid "Create products for your event, set prices, and manage available quantity."
#~ msgstr "為您的活動創建產品，設置價格，並管理可用數量。"

#: src/components/modals/CreatePromoCodeModal/index.tsx:51
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
msgid "Create Promo Code"
msgstr "創建促銷代碼"

#: src/components/modals/CreateQuestionModal/index.tsx:69
#: src/components/modals/CreateQuestionModal/index.tsx:74
msgid "Create Question"
msgstr "創建問題"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:41
msgid "Create Tax or Fee"
msgstr "創建税費"

#: src/components/modals/CreateTicketModal/index.tsx:83
#: src/components/modals/CreateTicketModal/index.tsx:91
#: src/components/routes/event/tickets.tsx:50
#~ msgid "Create Ticket"
#~ msgstr "創建機票"

#: src/components/routes/event/GettingStarted/index.tsx:122
msgid "Create tickets for your event, set prices, and manage available quantity."
msgstr "為您的活動創建門票、設定價格並管理可用數量。"

#: src/components/modals/CreateWebhookModal/index.tsx:57
#: src/components/modals/CreateWebhookModal/index.tsx:67
msgid "Create Webhook"
msgstr "創建 Webhook"

#: src/components/common/OrdersTable/index.tsx:208
#: src/components/common/OrdersTable/index.tsx:281
msgid "Created"
msgstr "創建"

#: src/components/forms/OrganizerForm/index.tsx:45
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:73
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:100
msgid "Currency"
msgstr "貨幣"

#: src/components/routes/profile/ManageProfile/index.tsx:191
msgid "Current Password"
msgstr "當前密碼"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:159
msgid "Custom Maps URL"
msgstr "自定義地圖 URL"

#: src/components/common/ReportTable/index.tsx:53
msgid "Custom Range"
msgstr "自定義範圍"

#: src/components/common/OrdersTable/index.tsx:205
msgid "Customer"
msgstr "客户"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:55
msgid "Customize the email and notification settings for this event"
msgstr "自定義此事件的電子郵件和通知設置"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:62
msgid "Customize the event homepage and checkout messaging"
msgstr "定製活動主頁和結賬信息"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:70
msgid "Customize the miscellaneous settings for this event"
msgstr "自定義此事件的其他設置"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:57
msgid "Customize the SEO settings for this event"
msgstr "自定義此事件的搜索引擎優化設置"

#: src/components/routes/event/GettingStarted/index.tsx:169
msgid "Customize your event page"
msgstr "定製您的活動頁面"

#: src/components/layouts/AuthLayout/index.tsx:84
msgid "Customize your event page and widget design to match your brand perfectly"
msgstr "自定義您的活動頁面和小部件設計，以完美匹配您的品牌"

#: src/components/routes/event/GettingStarted/index.tsx:165
msgid "Customize your event page to match your brand and style."
msgstr "定製您的活動頁面，以符合您的品牌和風格。"

#: src/components/routes/event/Reports/index.tsx:23
msgid "Daily Sales Report"
msgstr "每日銷售報告"

#: src/components/routes/event/Reports/index.tsx:24
msgid "Daily sales, tax, and fee breakdown"
msgstr "每日銷售、税費和費用明細"

#: src/components/common/CapacityAssignmentList/index.tsx:156
#: src/components/common/CheckInListList/index.tsx:176
#: src/components/common/OrdersTable/index.tsx:186
#: src/components/common/ProductsTable/SortableProduct/index.tsx:287
#: src/components/common/PromoCodeTable/index.tsx:181
#: src/components/common/QuestionsTable/index.tsx:115
#: src/components/common/WebhookTable/index.tsx:116
msgid "Danger zone"
msgstr "危險區"

#: src/components/common/AttendeeTable/index.tsx:179
msgid "Danger Zone"
msgstr "危險區"

#: src/components/layouts/Event/index.tsx:89
msgid "Dashboard"
msgstr "儀表板"

#: src/components/common/OrderDetails/index.tsx:39
#: src/components/forms/QuestionForm/index.tsx:136
msgid "Date"
msgstr "日期"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:40
#~ msgid "Date & Time"
#~ msgstr "日期和時間"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:38
msgid "Day one capacity"
msgstr "第一天容量"

#: src/components/forms/CheckInListForm/index.tsx:21
#~ msgid "Day one check-in list"
#~ msgstr "第一天簽到列表"

#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/TaxAndFeeList/index.tsx:73
msgid "Delete"
msgstr "刪除"

#: src/components/common/CapacityAssignmentList/index.tsx:159
msgid "Delete Capacity"
msgstr "刪除容量"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:146
#: src/components/common/ProductsTable/SortableCategory/index.tsx:147
msgid "Delete category"
msgstr "刪除類別"

#: src/components/common/CheckInListList/index.tsx:179
msgid "Delete Check-In List"
msgstr "刪除簽到列表"

#: src/components/common/PromoCodeTable/index.tsx:185
msgid "Delete code"
msgstr "刪除代碼"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:113
msgid "Delete Cover"
msgstr "刪除封面"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:98
msgid "Delete Image"
msgstr "刪除圖像"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:292
msgid "Delete product"
msgstr "刪除產品"

#: src/components/common/QuestionsTable/index.tsx:120
msgid "Delete question"
msgstr "刪除問題"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:199
#~ msgid "Delete ticket"
#~ msgstr "刪除機票"

#: src/components/common/WebhookTable/index.tsx:127
msgid "Delete webhook"
msgstr "刪除 Webhook"

#: src/components/forms/ProductForm/index.tsx:258
#: src/components/forms/TaxAndFeeForm/index.tsx:81
#: src/components/modals/CreateEventModal/index.tsx:135
#: src/components/modals/DuplicateEventModal/index.tsx:84
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:78
msgid "Description"
msgstr "説明"

#: src/components/forms/CheckInListForm/index.tsx:36
msgid "Description for check-in staff"
msgstr "簽到工作人員的描述"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Details"
msgstr "詳細信息"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
#~ msgid "Disable this capacity track capacity without stopping product sales"
#~ msgstr "禁用此容量以跟蹤容量，而不停止產品銷售"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:26
#~ msgid "Disable this capacity track capacity without stopping ticket sales"
#~ msgstr "禁用此容量以追蹤容量而不停止售票"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
msgid "Disabling this capacity will track sales but not stop them when the limit is reached"
msgstr "禁用此容量將跟蹤銷售情況，但不會在達到限制時停止銷售"

#: src/components/common/PromoCodeTable/index.tsx:70
msgid "Discount"
msgstr "折扣"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount %"
msgstr "折扣率"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount in {0}"
msgstr "{0}中的折扣"

#: src/components/forms/PromoCodeForm/index.tsx:43
msgid "Discount Type"
msgstr "折扣類型"

#: src/components/routes/product-widget/SelectProducts/index.tsx:297
#~ msgid "Dismiss"
#~ msgstr "解散"

#: src/components/routes/product-widget/SelectProducts/index.tsx:354
msgid "Dismiss this message"
msgstr "關閉此訊息"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:190
msgid "Document Label"
msgstr "文檔標籤"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:222
msgid "Documentation"
msgstr "文檔"

#: src/components/layouts/AuthLayout/index.tsx:19
#~ msgid "Does not exist"
#~ msgstr "不存在"

#: src/components/routes/auth/Login/index.tsx:57
msgid "Don't have an account?   <0>Sign up</0>"
msgstr "還沒有賬户？   <0>註冊</0>"

#: src/components/routes/auth/Login/index.tsx:72
#~ msgid "Don't have an account?   <0>Sign Up</0>"
#~ msgstr "沒有帳户？   <0>註冊</0>"

#: src/components/forms/ProductForm/index.tsx:157
msgid "Donation / Pay what you'd like product"
msgstr "捐贈 / 自由定價產品"

#: src/components/forms/TicketForm/index.tsx:139
#~ msgid "Donation / Pay what you'd like ticket"
#~ msgstr "捐款/自費門票"

#: src/components/common/AddEventToCalendarButton/index.tsx:137
msgid "Download .ics"
msgstr "下載 .ics"

#: src/components/common/DownloadCsvButton/index.tsx:48
msgid "Download CSV"
msgstr "下載 CSV"

#: src/components/common/OrdersTable/index.tsx:162
msgid "Download invoice"
msgstr "下載發票"

#: src/components/layouts/Checkout/index.tsx:126
msgid "Download Invoice"
msgstr "下載發票"

#: src/components/modals/ShareModal/index.tsx:224
msgid "Download QR Code"
msgstr "下載二維碼"

#: src/components/common/OrdersTable/index.tsx:111
#: src/components/layouts/Checkout/index.tsx:49
msgid "Downloading Invoice"
msgstr "正在下載發票"

#: src/components/layouts/Event/index.tsx:188
msgid "Draft"
msgstr "草稿"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:83
msgid "Drag and drop or click"
msgstr "拖放或點擊"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Drag to sort"
#~ msgstr "拖動排序"

#: src/components/forms/QuestionForm/index.tsx:124
msgid "Dropdown selection"
msgstr "下拉選擇"

#: src/components/modals/SendMessageModal/index.tsx:159
msgid ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."
msgstr ""
"由於垃圾訊息風險高，我們需要手動驗證才能發送訊息。\n"
"請聯絡我們以申請權限。"

#: src/components/modals/DuplicateEventModal/index.tsx:124
msgid "Duplicate Capacity Assignments"
msgstr "複製容量分配"

#: src/components/modals/DuplicateEventModal/index.tsx:128
msgid "Duplicate Check-In Lists"
msgstr "複製簽到列表"

#: src/components/common/EventCard/index.tsx:162
msgid "Duplicate event"
msgstr "複製活動"

#: src/components/modals/DuplicateEventModal/index.tsx:69
#: src/components/modals/DuplicateEventModal/index.tsx:142
msgid "Duplicate Event"
msgstr "複製活動"

#: src/components/modals/DuplicateEventModal/index.tsx:132
msgid "Duplicate Event Cover Image"
msgstr "複製事件封面圖像"

#: src/components/modals/DuplicateEventModal/index.tsx:103
msgid "Duplicate Options"
msgstr "複製選項"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:285
#: src/components/modals/DuplicateProductModal/index.tsx:109
#: src/components/modals/DuplicateProductModal/index.tsx:113
msgid "Duplicate Product"
msgstr "複製產品"

#: src/components/modals/DuplicateEventModal/index.tsx:108
msgid "Duplicate Products"
msgstr "複製產品"

#: src/components/modals/DuplicateEventModal/index.tsx:120
msgid "Duplicate Promo Codes"
msgstr "複製促銷代碼"

#: src/components/modals/DuplicateEventModal/index.tsx:112
msgid "Duplicate Questions"
msgstr "複製問題"

#: src/components/modals/DuplicateEventModal/index.tsx:116
msgid "Duplicate Settings"
msgstr "複製設置"

#: src/components/modals/DuplicateEventModal/index.tsx:107
#~ msgid "Duplicate Tickets"
#~ msgstr "複製票"

#: src/components/modals/DuplicateEventModal/index.tsx:137
msgid "Duplicate Webhooks"
msgstr "複製 Webhooks"

#: src/components/common/LanguageSwitcher/index.tsx:24
msgid "Dutch"
msgstr "荷蘭語"

#: src/components/forms/ProductForm/index.tsx:78
msgid "Early bird"
msgstr "早起的鳥兒"

#: src/components/common/TaxAndFeeList/index.tsx:65
#: src/components/modals/ManageAttendeeModal/index.tsx:221
#: src/components/modals/ManageOrderModal/index.tsx:203
msgid "Edit"
msgstr "編輯"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
msgid "Edit {0}"
msgstr "編輯 {0}"

#: src/components/common/QuestionAndAnswerList/index.tsx:159
msgid "Edit Answer"
msgstr "編輯答案"

#: src/components/common/AttendeeTable/index.tsx:174
#~ msgid "Edit attendee"
#~ msgstr "編輯與會者"

#: src/components/modals/EditAttendeeModal/index.tsx:72
#: src/components/modals/EditAttendeeModal/index.tsx:110
#~ msgid "Edit Attendee"
#~ msgstr "編輯參會者"

#: src/components/common/CapacityAssignmentList/index.tsx:146
msgid "Edit Capacity"
msgstr "編輯容量"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:66
#: src/components/modals/EditCapacityAssignmentModal/index.tsx:74
msgid "Edit Capacity Assignment"
msgstr "編輯容量分配"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:137
#: src/components/common/ProductsTable/SortableCategory/index.tsx:138
msgid "Edit category"
msgstr "編輯類別"

#: src/components/common/CheckInListList/index.tsx:148
#: src/components/modals/EditCheckInListModal/index.tsx:69
#: src/components/modals/EditCheckInListModal/index.tsx:91
msgid "Edit Check-In List"
msgstr "編輯簽到列表"

#: src/components/common/PromoCodeTable/index.tsx:170
msgid "Edit Code"
msgstr "編輯代碼"

#: src/components/modals/EditOrganizerModal/index.tsx:63
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:51
msgid "Edit Organizer"
msgstr "編輯組織器"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:280
#: src/components/modals/EditProductModal/index.tsx:100
#: src/components/modals/EditProductModal/index.tsx:108
msgid "Edit Product"
msgstr "編輯產品"

#: src/components/modals/EditProductCategoryModal/index.tsx:62
#: src/components/modals/EditProductCategoryModal/index.tsx:70
msgid "Edit Product Category"
msgstr "編輯產品類別"

#: src/components/modals/EditPromoCodeModal/index.tsx:79
#: src/components/modals/EditPromoCodeModal/index.tsx:84
msgid "Edit Promo Code"
msgstr "編輯促銷代碼"

#: src/components/common/QuestionsTable/index.tsx:112
msgid "Edit question"
msgstr "編輯問題"

#: src/components/modals/EditQuestionModal/index.tsx:95
#: src/components/modals/EditQuestionModal/index.tsx:101
msgid "Edit Question"
msgstr "編輯問題"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:191
#: src/components/modals/EditTicketModal/index.tsx:96
#: src/components/modals/EditTicketModal/index.tsx:104
#~ msgid "Edit Ticket"
#~ msgstr "編輯機票"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Edit user"
msgstr "編輯用户"

#: src/components/modals/EditUserModal/index.tsx:62
#: src/components/modals/EditUserModal/index.tsx:120
msgid "Edit User"
msgstr "編輯用户"

#: src/components/common/WebhookTable/index.tsx:104
msgid "Edit webhook"
msgstr "編輯 Webhook"

#: src/components/modals/EditWebhookModal/index.tsx:66
#: src/components/modals/EditWebhookModal/index.tsx:87
msgid "Edit Webhook"
msgstr "編輯 Webhook"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 2.50 for $2.50"
msgstr "例如2.50 換 2.50"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 23.5 for 23.5%"
msgstr "例如23.5 表示 23.5%"

#: src/components/common/AttendeeDetails/index.tsx:21
#: src/components/common/AttendeeTable/index.tsx:109
#: src/components/common/OrderDetails/index.tsx:31
#: src/components/forms/OrganizerForm/index.tsx:32
#: src/components/modals/EditUserModal/index.tsx:80
#: src/components/modals/InviteUserModal/index.tsx:61
#: src/components/routes/auth/AcceptInvitation/index.tsx:98
#: src/components/routes/auth/Login/index.tsx:68
#: src/components/routes/auth/Register/index.tsx:97
#: src/components/routes/event/Settings/index.tsx:52
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:117
#: src/components/routes/profile/ManageProfile/index.tsx:138
msgid "Email"
msgstr "電子郵件"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
msgid "Email & Notification Settings"
msgstr "電子郵件和通知設置"

#: src/components/modals/CreateAttendeeModal/index.tsx:132
#: src/components/modals/ManageAttendeeModal/index.tsx:114
#: src/components/modals/ManageOrderModal/index.tsx:157
msgid "Email address"
msgstr "電子郵件地址"

#: src/components/common/QuestionsTable/index.tsx:220
#: src/components/common/QuestionsTable/index.tsx:221
#: src/components/routes/product-widget/CollectInformation/index.tsx:298
#: src/components/routes/product-widget/CollectInformation/index.tsx:299
#: src/components/routes/product-widget/CollectInformation/index.tsx:408
#: src/components/routes/product-widget/CollectInformation/index.tsx:409
msgid "Email Address"
msgstr "電子郵件地址"

#: src/components/routes/profile/ManageProfile/index.tsx:77
msgid "Email change cancelled successfully"
msgstr "電子郵件更改已成功取消"

#: src/components/routes/profile/ManageProfile/index.tsx:116
msgid "Email change pending"
msgstr "電子郵件更改待定"

#: src/components/routes/profile/ManageProfile/index.tsx:151
msgid "Email confirmation resent"
msgstr "重新發送電子郵件確認"

#: src/components/routes/profile/ManageProfile/index.tsx:90
msgid "Email confirmation resent successfully"
msgstr "成功重新發送電子郵件確認"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:66
msgid "Email footer message"
msgstr "電子郵件頁腳信息"

#: src/components/routes/profile/ManageProfile/index.tsx:141
msgid "Email not verified"
msgstr "電子郵件未經驗證"

#: src/components/common/WidgetEditor/index.tsx:272
msgid "Embed Code"
msgstr "嵌入代碼"

#: src/components/common/WidgetEditor/index.tsx:260
msgid "Embed Script"
msgstr "嵌入腳本"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:181
msgid "Enable Invoicing"
msgstr "啟用發票功能"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:21
msgid "Enable this capacity to stop product sales when the limit is reached"
msgstr "啟用此容量以在達到限制時停止產品銷售"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:20
#~ msgid "Enable this capacity to stop ticket sales when the limit is reached"
#~ msgstr "啟用此容量以在達到限制時停止售票"

#: src/components/forms/WebhookForm/index.tsx:19
msgid "Enabled"
msgstr "已啟用"

#: src/components/modals/CreateEventModal/index.tsx:149
#: src/components/modals/DuplicateEventModal/index.tsx:98
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:92
#: src/components/routes/welcome/index.tsx:96
msgid "End Date"
msgstr "結束日期"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:26
#: src/components/common/EventStatusBadge/index.tsx:14
msgid "Ended"
msgstr "完工"

#: src/components/routes/events/Dashboard/index.tsx:57
msgid "Ended Events"
msgstr "已結束的活動"

#: src/components/common/LanguageSwitcher/index.tsx:16
#: src/components/common/LanguageSwitcher/index.tsx:50
#: src/components/modals/CreateAttendeeModal/index.tsx:145
#: src/components/routes/profile/ManageProfile/index.tsx:173
msgid "English"
msgstr "英語"

#: src/components/modals/CreateAttendeeModal/index.tsx:170
msgid "Enter an amount excluding taxes and fees."
msgstr "輸入不含税費的金額。"

#: src/components/common/OrdersTable/index.tsx:119
#: src/components/layouts/Checkout/index.tsx:57
msgid "Error"
msgstr "錯誤"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:27
msgid "Error confirming email address"
msgstr "確認電子郵件地址出錯"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:35
msgid "Error confirming email change"
msgstr "確認更改電子郵件時出錯"

#: src/components/modals/WebhookLogsModal/index.tsx:166
msgid "Error loading logs"
msgstr "加載日誌時出錯"

#: src/components/forms/OrganizerForm/index.tsx:46
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:74
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:101
msgid "EUR"
msgstr "歐元"

#: src/components/routes/events/Dashboard/index.tsx:103
msgid "Event"
msgstr "活動"

#: src/components/modals/CreateEventModal/index.tsx:76
#~ msgid "Event created successfully 🎉"
#~ msgstr "成功創建事件 🎉"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:161
msgid "Event Date"
msgstr "活動日期"

#: src/components/routes/account/ManageAccount/sections/EventDefaultsSettings/index.tsx:5
msgid "Event Defaults"
msgstr "事件默認值"

#: src/components/routes/event/Settings/index.tsx:28
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:276
msgid "Event Details"
msgstr "活動詳情"

#: src/components/modals/DuplicateEventModal/index.tsx:58
msgid "Event duplicated successfully"
msgstr "事件成功複製"

#: src/components/layouts/Checkout/index.tsx:79
msgid "Event Homepage"
msgstr "活動主頁"

#: src/components/layouts/Event/index.tsx:166
#~ msgid "Event is not visible to the public"
#~ msgstr "公眾無法看到活動"

#: src/components/layouts/Event/index.tsx:165
#~ msgid "Event is visible to the public"
#~ msgstr "活動對公眾可見"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:85
msgid "Event location & venue details"
msgstr "活動地點和場地詳情"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:12
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
msgid "Event Not Available"
msgstr "活動不可用"

#: src/components/layouts/Event/index.tsx:187
#~ msgid "Event page"
#~ msgstr "活動頁面"

#: src/components/layouts/Event/index.tsx:212
msgid "Event Page"
msgstr "活動頁面"

#: src/components/common/EventCard/index.tsx:70
#: src/components/layouts/Event/index.tsx:80
#: src/components/routes/event/EventDashboard/index.tsx:76
#: src/components/routes/event/GettingStarted/index.tsx:63
msgid "Event status update failed. Please try again later"
msgstr "活動狀態更新失敗。請稍後再試"

#: src/components/common/EventCard/index.tsx:67
#: src/components/layouts/Event/index.tsx:77
#: src/components/routes/event/EventDashboard/index.tsx:73
#: src/components/routes/event/GettingStarted/index.tsx:60
msgid "Event status updated"
msgstr "事件狀態已更新"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Event Ticketing by"
msgstr "活動票務由以下提供"

#: src/components/common/WebhookTable/index.tsx:237
#: src/components/forms/WebhookForm/index.tsx:122
msgid "Event Types"
msgstr "事件類型"

#: src/components/modals/ShareModal/index.tsx:237
msgid "Event URL"
msgstr "活動鏈接"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
#~ msgid "Event wdwdeNot Available"
#~ msgstr ""

#: src/components/layouts/Event/index.tsx:226
msgid "Events"
msgstr "活動"

#: src/components/forms/CheckInListForm/index.tsx:56
msgid "Expiration date"
msgstr "過期日期"

#: src/components/common/PromoCodeTable/index.tsx:73
msgid "Expires"
msgstr "到期"

#: src/components/forms/PromoCodeForm/index.tsx:79
msgid "Expiry Date"
msgstr "有效期"

#: src/components/routes/event/attendees.tsx:80
#: src/components/routes/event/orders.tsx:140
msgid "Export"
msgstr "出口"

#: src/components/common/QuestionsTable/index.tsx:267
msgid "Export answers"
msgstr "導出答案"

#: src/mutations/useExportAnswers.ts:39
msgid "Export failed. Please try again."
msgstr "導出失敗。請重試。"

#: src/mutations/useExportAnswers.ts:17
msgid "Export started. Preparing file..."
msgstr "導出已開始。正在準備文件..."

#: src/components/routes/event/attendees.tsx:39
msgid "Exporting Attendees"
msgstr "正在導出與會者"

#: src/mutations/useExportAnswers.ts:33
msgid "Exporting complete. Downloading file..."
msgstr "導出完成。正在下載文件..."

#: src/components/routes/event/orders.tsx:90
msgid "Exporting Orders"
msgstr "正在導出訂單"

#: src/components/common/AttendeeTable/index.tsx:97
msgid "Failed to cancel attendee"
msgstr "取消與會者失敗"

#: src/components/modals/CancelOrderModal/index.tsx:33
msgid "Failed to cancel order"
msgstr "取消訂單失敗"

#: src/components/common/QuestionsTable/index.tsx:175
msgid "Failed to delete message. Please try again."
msgstr "刪除信息失敗。請重試。"

#: src/components/common/OrdersTable/index.tsx:120
#: src/components/layouts/Checkout/index.tsx:58
msgid "Failed to download invoice. Please try again."
msgstr "下載發票失敗。請重試。"

#: src/components/routes/event/attendees.tsx:48
msgid "Failed to export attendees"
msgstr "導出與會者失敗"

#: src/components/routes/event/attendees.tsx:39
#~ msgid "Failed to export attendees. Please try again."
#~ msgstr "導出與會者失敗。請重試。"

#: src/components/routes/event/orders.tsx:99
msgid "Failed to export orders"
msgstr "導出訂單失敗"

#: src/components/routes/event/orders.tsx:88
#~ msgid "Failed to export orders. Please try again."
#~ msgstr "導出訂單失敗。請重試。"

#: src/components/modals/EditCheckInListModal/index.tsx:79
msgid "Failed to load Check-In List"
msgstr "加載簽到列表失敗"

#: src/components/modals/EditWebhookModal/index.tsx:75
msgid "Failed to load Webhook"
msgstr "加載 Webhook 失敗"

#: src/components/common/AttendeeTable/index.tsx:51
#~ msgid "Failed to resend product email"
#~ msgstr "重新發送產品電子郵件失敗"

#: src/components/common/AttendeeTable/index.tsx:50
msgid "Failed to resend ticket email"
msgstr "重新發送票據電子郵件失敗"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:179
msgid "Failed to sort products"
msgstr "產品排序失敗"

#: src/mutations/useExportAnswers.ts:15
msgid "Failed to start export job"
msgstr "無法啟動導出任務"

#: src/components/common/QuestionAndAnswerList/index.tsx:102
msgid "Failed to update answer."
msgstr "更新答案失敗。"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:64
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:69
msgid "Failed to upload image."
msgstr "上傳圖片失敗。"

#: src/components/forms/TaxAndFeeForm/index.tsx:18
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Fee"
msgstr "費用"

#: src/components/common/GlobalMenu/index.tsx:30
#~ msgid "Feedback"
#~ msgstr "反饋意見"

#: src/components/forms/ProductForm/index.tsx:366
msgid "Fees"
msgstr "費用"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:80
msgid "Fees are subject to change. You will be notified of any changes to your fee structure."
msgstr "費用可能會變更。如有變更，您將收到通知。"

#: src/components/routes/event/orders.tsx:121
msgid "Filter Orders"
msgstr "篩選訂單"

#: src/components/common/FilterModal/index.tsx:75
#: src/components/common/FilterModal/index.tsx:223
msgid "Filters"
msgstr "篩選器"

#: src/components/common/FilterModal/index.tsx:223
msgid "Filters ({activeFilterCount})"
msgstr "篩選器 ({activeFilterCount})"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:206
msgid "First Invoice Number"
msgstr "第一張發票號碼"

#: src/components/common/QuestionsTable/index.tsx:208
#: src/components/modals/CreateAttendeeModal/index.tsx:118
#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:144
#: src/components/routes/product-widget/CollectInformation/index.tsx:284
#: src/components/routes/product-widget/CollectInformation/index.tsx:395
msgid "First name"
msgstr "姓名"

#: src/components/common/QuestionsTable/index.tsx:207
#: src/components/modals/EditUserModal/index.tsx:71
#: src/components/modals/InviteUserModal/index.tsx:57
#: src/components/routes/auth/AcceptInvitation/index.tsx:92
#: src/components/routes/auth/Register/index.tsx:83
#: src/components/routes/product-widget/CollectInformation/index.tsx:283
#: src/components/routes/product-widget/CollectInformation/index.tsx:394
#: src/components/routes/profile/ManageProfile/index.tsx:135
msgid "First Name"
msgstr "姓名"

#: src/components/routes/auth/AcceptInvitation/index.tsx:29
msgid "First name must be between 1 and 50 characters"
msgstr "名字必須在 1 至 50 個字符之間"

#: src/components/common/QuestionsTable/index.tsx:349
msgid "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."
msgstr "名字、姓氏和電子郵件地址為默認問題，在結賬過程中始終包含。"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
msgid "First Used"
msgstr "首次使用"

#: src/components/forms/TaxAndFeeForm/index.tsx:33
msgid "Fixed"
msgstr "固定式"

#: src/components/forms/PromoCodeForm/index.tsx:55
msgid "Fixed amount"
msgstr "固定金額"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:65
msgid "Fixed Fee:"
msgstr "固定費用："

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:123
msgid "Flash is not available on this device"
msgstr "該設備不支持閃存"

#: src/components/layouts/AuthLayout/index.tsx:58
msgid "Flexible Ticketing"
msgstr "靈活的票務系統"

#: src/components/routes/auth/Login/index.tsx:80
msgid "Forgot password?"
msgstr "忘記密碼？"

#: src/components/common/AttendeeTicket/index.tsx:40
#: src/components/common/Currency/index.tsx:90
#: src/components/common/OrderSummary/index.tsx:46
#: src/components/common/OrderSummary/index.tsx:99
#: src/components/common/ProductsTable/SortableProduct/index.tsx:93
#: src/components/common/ProductsTable/SortableProduct/index.tsx:103
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:53
msgid "Free"
msgstr "免費"

#: src/components/forms/ProductForm/index.tsx:151
msgid "Free Product"
msgstr "免費產品"

#: src/components/forms/ProductForm/index.tsx:153
msgid "Free product, no payment information required"
msgstr "免費產品，無需付款信息"

#: src/components/forms/TicketForm/index.tsx:133
#~ msgid "Free Ticket"
#~ msgstr "免費門票"

#: src/components/forms/TicketForm/index.tsx:135
#~ msgid "Free ticket, no payment information required"
#~ msgstr "免費門票，無需支付信息"

#: src/components/common/LanguageSwitcher/index.tsx:20
msgid "French"
msgstr "法語"

#: src/components/layouts/AuthLayout/index.tsx:88
msgid "Fully Integrated"
msgstr "全面整合"

#: src/components/forms/ProductForm/index.tsx:136
msgid "General"
msgstr "常規"

#: src/components/common/LanguageSwitcher/index.tsx:14
msgid "German"
msgstr "德國"

#: src/components/layouts/AuthLayout/index.tsx:35
msgid "Get started for free, no subscription fees"
msgstr "免費開始，無訂閲費用"

#: src/components/routes/event/EventDashboard/index.tsx:125
msgid "Get your event ready"
msgstr "準備好您的活動"

#: src/components/layouts/Event/index.tsx:88
msgid "Getting Started"
msgstr "入門"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:23
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:42
msgid "Go back to profile"
msgstr "返回個人資料"

#: src/components/routes/product-widget/CollectInformation/index.tsx:239
msgid "Go to event homepage"
msgstr "前往活動主頁"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:53
msgid "Go to Hi.Events"
msgstr "前往 Hi.Events"

#: src/components/common/ErrorDisplay/index.tsx:64
msgid "Go to home page"
msgstr "返回主頁"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:162
msgid "Go to Stripe Dashboard"
msgstr "前往 Stripe 儀表板"

#: src/components/common/AddEventToCalendarButton/index.tsx:128
msgid "Google Calendar"
msgstr "谷歌日曆"

#: src/components/common/EventCard/index.tsx:133
msgid "gross sales"
msgstr "總銷售額"

#: src/components/common/StatBoxes/index.tsx:39
msgid "Gross sales"
msgstr "銷售總額"

#: src/components/routes/event/EventDashboard/index.tsx:274
msgid "Gross Sales"
msgstr "銷售總額"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:281
msgid "Guests"
msgstr "賓客"

#: src/components/routes/product-widget/SelectProducts/index.tsx:495
msgid "Have a promo code?"
msgstr "有促銷代碼嗎？"

#: src/components/forms/OrganizerForm/index.tsx:33
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/GlobalMenu/index.tsx:24
#~ msgid "Help & Support"
#~ msgstr "幫助與支持"

#: src/components/common/WidgetEditor/index.tsx:295
msgid "Here is an example of how you can use the component in your application."
msgstr "下面舉例説明如何在應用程序中使用該組件。"

#: src/components/common/WidgetEditor/index.tsx:284
msgid "Here is the React component you can use to embed the widget in your application."
msgstr "下面是 React 組件，您可以用它在應用程序中嵌入 widget。"

#: src/components/routes/event/EventDashboard/index.tsx:101
msgid "Hi {0} 👋"
msgstr "你好 {0} 👋"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:43
msgid "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."
msgstr "Hi.Events 收取平台費用以維護和改進我們的服務。這些費用會自動從每筆交易中扣除。"

#: src/components/modals/CreateEventModal/index.tsx:130
#: src/components/modals/DuplicateEventModal/index.tsx:79
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:73
msgid "Hi.Events Conference {0}"
msgstr "Hi.Events Conference {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:117
msgid "Hi.Events Conference Center"
msgstr "Hi.Events 會議中心"

#: src/components/layouts/AuthLayout/index.tsx:131
msgid "hi.events logo"
msgstr "hi.events 徽標"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:79
msgid "Hidden from public view"
msgstr "隱藏於公眾視線之外"

#: src/components/common/QuestionsTable/index.tsx:290
msgid "hidden question"
msgstr "隱藏問題"

#: src/components/common/QuestionsTable/index.tsx:291
msgid "hidden questions"
msgstr "隱藏問題"

#: src/components/forms/QuestionForm/index.tsx:218
msgid "Hidden questions are only visible to the event organizer and not to the customer."
msgstr "隱藏問題只有活動組織者可以看到，客户看不到。"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/routes/product-widget/SelectProducts/index.tsx:460
msgid "Hide"
msgstr "隱藏"

#: src/components/common/AttendeeList/index.tsx:84
msgid "Hide Answers"
msgstr "隱藏答案"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:89
msgid "Hide getting started page"
msgstr "隱藏入門頁面"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Hide hidden questions"
msgstr "隱藏隱藏問題"

#: src/components/forms/ProductForm/index.tsx:399
msgid "Hide product after sale end date"
msgstr "在銷售結束日期後隱藏產品"

#: src/components/forms/ProductForm/index.tsx:397
msgid "Hide product before sale start date"
msgstr "在銷售開始日期前隱藏產品"

#: src/components/forms/ProductForm/index.tsx:412
msgid "Hide product unless user has applicable promo code"
msgstr "除非用户有適用的促銷代碼，否則隱藏產品"

#: src/components/forms/ProductForm/index.tsx:405
msgid "Hide product when sold out"
msgstr "售罄時隱藏產品"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:90
msgid "Hide the getting started page from the sidebar"
msgstr "隱藏側邊欄中的入門頁面"

#: src/components/forms/ProductForm/index.tsx:417
msgid "Hide this product from customers"
msgstr "對客户隱藏此產品"

#: src/components/forms/QuestionForm/index.tsx:219
msgid "Hide this question"
msgstr "隱藏此問題"

#: src/components/forms/TicketForm/index.tsx:362
#~ msgid "Hide this ticket from customers"
#~ msgstr "向客户隱藏此票據"

#: src/components/forms/ProductForm/index.tsx:103
msgid "Hide this tier from users"
msgstr "向用户隱藏此層級"

#: src/components/forms/TicketForm/index.tsx:344
#~ msgid "Hide ticket after sale end date"
#~ msgstr "隱藏銷售截止日期後的機票"

#: src/components/forms/TicketForm/index.tsx:342
#~ msgid "Hide ticket before sale start date"
#~ msgstr "在開售日期前隱藏機票"

#: src/components/forms/TicketForm/index.tsx:357
#~ msgid "Hide ticket unless user has applicable promo code"
#~ msgstr "隱藏機票，除非用户有適用的促銷代碼"

#: src/components/forms/TicketForm/index.tsx:350
#~ msgid "Hide ticket when sold out"
#~ msgstr "售罄後隱藏門票"

#: src/components/forms/ProductForm/index.tsx:101
msgid "Hiding a product will prevent users from seeing it on the event page."
msgstr "隱藏產品將防止用户在活動頁面上看到它。"

#: src/components/forms/TicketForm/index.tsx:98
#~ msgid "Hiding a ticket will prevent users from seeing it on the event page."
#~ msgstr "隱藏票單將阻止用户在活動頁面上看到該票單。"

#: src/components/routes/event/HomepageDesigner/index.tsx:115
msgid "Homepage Design"
msgstr "主頁設計"

#: src/components/layouts/Event/index.tsx:109
msgid "Homepage Designer"
msgstr "主頁設計師"

#: src/components/routes/event/HomepageDesigner/index.tsx:174
msgid "Homepage Preview"
msgstr "主頁預覽"

#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:145
msgid "Homer"
msgstr "荷馬"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
msgid "How many minutes the customer has to complete their order. We recommend at least 15 minutes"
msgstr "客户有多少分鐘來完成訂單。我們建議至少 15 分鐘"

#: src/components/forms/PromoCodeForm/index.tsx:84
msgid "How many times can this code be used?"
msgstr "這個代碼可以使用多少次？"

#: src/components/common/Editor/index.tsx:75
msgid "HTML character limit exceeded: {htmlLength}/{maxLength}"
msgstr "HTML字符限制已超出：{htmlLength}/{maxLength}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:160
msgid "https://example-maps-service.com/..."
msgstr "https://example-maps-service.com/..."

#: src/components/forms/WebhookForm/index.tsx:118
msgid "https://webhook-domain.com/webhook"
msgstr "https://webhook-domain.com/webhook"

#: src/components/routes/auth/AcceptInvitation/index.tsx:118
msgid "I agree to the <0>terms and conditions</0>"
msgstr "我同意<0>條款和條件</0>。"

#: src/components/routes/product-widget/Payment/index.tsx:106
msgid "I would like to pay using an offline method"
msgstr "我想使用線下支付方式付款"

#: src/components/routes/product-widget/Payment/index.tsx:107
msgid "I would like to pay using an online method (credit card etc.)"
msgstr "我想使用在線支付方式（如信用卡）付款"

#: src/components/routes/product-widget/SelectProducts/index.tsx:315
msgid "If a new tab did not open automatically, please click the button below to continue to checkout."
msgstr "如果未自動開啟新分頁，請點擊下方按鈕繼續結帳。"

#: src/components/routes/product-widget/SelectProducts/index.tsx:284
#~ msgid "If a new tab did not open, please  <0><1>{0}</1>.</0>"
#~ msgstr "如果新標籤頁沒有打開，請<0><1>{0}</1>.</0>。"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:149
#~ msgid "If blank, the address will be used to generate a Google map link"
#~ msgstr "如果為空，該地址將用於生成 Google 地圖鏈接"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:158
msgid "If blank, the address will be used to generate a Google Mapa link"
msgstr "如果為空，將使用地址生成 Google 地圖鏈接"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:158
msgid "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."
msgstr "如果啟用，登記工作人員可以將與會者標記為已登記或將訂單標記為已支付並登記與會者。如果禁用，關聯未支付訂單的與會者無法登記。"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:76
msgid "If enabled, the organizer will receive an email notification when a new order is placed"
msgstr "如果啟用，當有新訂單時，組織者將收到電子郵件通知"

#: src/components/routes/profile/ManageProfile/index.tsx:122
msgid "If you did not request this change, please immediately change your password."
msgstr "如果您沒有要求更改密碼，請立即更改密碼。"

#: src/components/routes/auth/ForgotPassword/index.tsx:40
msgid ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."
msgstr "如果您擁有我們的帳戶，您將會收到一封包含重設密碼指引的電郵。"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:108
msgid "Image deleted successfully"
msgstr "圖像已成功刪除"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
#~ msgid "Image dimensions must be between 3000px by 2000px. With a max height of 2000px and max width of 3000px"
#~ msgstr "圖片尺寸必須在 3000px x 2000px 之間。最大高度為 2000px，最大寬度為 3000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
msgid "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"
msgstr "圖像尺寸必須在4000px和4000px之間。最大高度為4000px，最大寬度為4000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:49
msgid "Image must be less than 5MB"
msgstr "圖片必須小於 5MB"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:63
msgid "Image uploaded successfully"
msgstr "圖片上傳成功"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:111
msgid "Image URL"
msgstr "圖片網址"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
msgid "Image width must be at least 900px and height at least 50px"
msgstr "圖片寬度必須至少為 900px，高度必須至少為 50px"

#: src/components/layouts/AuthLayout/index.tsx:53
msgid "In-depth Analytics"
msgstr "深入分析"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:25
#: src/components/modals/EditUserModal/index.tsx:111
msgid "Inactive"
msgstr "不活動"

#: src/components/modals/EditUserModal/index.tsx:107
msgid "Inactive users cannot log in."
msgstr "非活動用户無法登錄。"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee product page"
#~ msgstr "包括您的在線活動的連接詳細信息。這些詳細信息將顯示在訂單摘要頁面和參會者產品頁面上"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page"
#~ msgstr "包括在線活動的連接詳情。這些詳細信息將顯示在訂單摘要頁面和與會者門票頁面上"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:102
msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."
msgstr "包含您的在線活動的連接詳細信息。這些信息將在訂單摘要頁面和參會者門票頁面顯示。"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:54
msgid "Include tax and fees in the price"
msgstr "價格中包含税費"

#: src/components/common/CheckInListList/index.tsx:103
msgid "Includes {0} products"
msgstr "包括{0}個產品"

#: src/components/common/CheckInListList/index.tsx:112
#~ msgid "Includes {0} tickets"
#~ msgstr "包括 {0} 張票"

#: src/components/common/CheckInListList/index.tsx:104
msgid "Includes 1 product"
msgstr "包括1個產品"

#: src/components/common/CheckInListList/index.tsx:113
#~ msgid "Includes 1 ticket"
#~ msgstr "包括 1 張票"

#: src/components/common/MessageList/index.tsx:20
msgid "Individual attendees"
msgstr "個人與會者"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:100
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:121
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:137
msgid "Insert Image"
msgstr "插入圖片"

#: src/components/layouts/Event/index.tsx:54
#~ msgid "Integrations"
#~ msgstr "集成"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation resent!"
msgstr "再次發出邀請！"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation revoked!"
msgstr "撤銷邀請！"

#: src/components/modals/InviteUserModal/index.tsx:54
#: src/components/modals/InviteUserModal/index.tsx:74
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invite User"
msgstr "邀請用户"

#: src/components/common/OrdersTable/index.tsx:116
#: src/components/layouts/Checkout/index.tsx:54
msgid "Invoice downloaded successfully"
msgstr "發票下載成功"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:252
msgid "Invoice Notes"
msgstr "發票備註"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:197
msgid "Invoice Numbering"
msgstr "發票編號"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:177
msgid "Invoice Settings"
msgstr "發票設置"

#: src/components/modals/RefundOrderModal/index.tsx:115
msgid "Issue refund"
msgstr "退款問題"

#: src/components/common/LanguageSwitcher/index.tsx:22
msgid "Italian"
msgstr "意大利語"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Item"
msgstr "項目"

#: src/components/routes/auth/Register/index.tsx:84
msgid "John"
msgstr "約翰"

#: src/components/modals/CreateAttendeeModal/index.tsx:126
msgid "Johnson"
msgstr "約翰遜"

#: src/components/forms/ProductForm/index.tsx:77
msgid "Label"
msgstr "標籤"

#: src/components/common/AttendeeDetails/index.tsx:45
#: src/components/modals/CreateAttendeeModal/index.tsx:144
#: src/components/routes/profile/ManageProfile/index.tsx:172
msgid "Language"
msgstr "語言"

#: src/components/common/ReportTable/index.tsx:52
msgid "Last 12 months"
msgstr "過去 12 個月"

#: src/components/common/ReportTable/index.tsx:47
msgid "Last 14 days"
msgstr "過去 14 天"

#: src/components/common/ReportTable/index.tsx:44
msgid "Last 24 hours"
msgstr "過去 24 小時"

#: src/components/common/ReportTable/index.tsx:48
msgid "Last 30 days"
msgstr "過去 30 天"

#: src/components/common/ReportTable/index.tsx:45
msgid "Last 48 hours"
msgstr "過去 48 小時"

#: src/components/common/ReportTable/index.tsx:50
msgid "Last 6 months"
msgstr "過去 6 個月"

#: src/components/common/ReportTable/index.tsx:46
msgid "Last 7 days"
msgstr "過去 7 天"

#: src/components/common/ReportTable/index.tsx:49
msgid "Last 90 days"
msgstr "過去 90 天"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Last login"
msgstr "最後登錄"

#: src/components/modals/CreateAttendeeModal/index.tsx:125
#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:150
msgid "Last name"
msgstr "姓氏"

#: src/components/common/QuestionsTable/index.tsx:212
#: src/components/common/QuestionsTable/index.tsx:213
#: src/components/modals/EditUserModal/index.tsx:72
#: src/components/modals/InviteUserModal/index.tsx:58
#: src/components/routes/auth/AcceptInvitation/index.tsx:94
#: src/components/routes/auth/Register/index.tsx:89
#: src/components/routes/product-widget/CollectInformation/index.tsx:289
#: src/components/routes/product-widget/CollectInformation/index.tsx:290
#: src/components/routes/product-widget/CollectInformation/index.tsx:400
#: src/components/routes/product-widget/CollectInformation/index.tsx:401
#: src/components/routes/profile/ManageProfile/index.tsx:137
msgid "Last Name"
msgstr "姓氏"

#: src/components/common/WebhookTable/index.tsx:239
msgid "Last Response"
msgstr "最新響應"

#: src/components/common/WebhookTable/index.tsx:240
msgid "Last Triggered"
msgstr "最近觸發"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
msgid "Last Used"
msgstr "最近一次使用"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:56
#~ msgid "Learn more about Stripe"
#~ msgstr "進一步瞭解 Stripe"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:191
msgid "Leave blank to use the default word \"Invoice\""
msgstr "留空以使用默認詞“發票”"

#: src/components/routes/welcome/index.tsx:20
msgid "Let's get started by creating your first organizer"
msgstr "讓我們從創建第一個組織者開始吧"

#: src/components/routes/event/EventDashboard/index.tsx:194
msgid "Link your Stripe account to receive funds from ticket sales."
msgstr "鏈接您的 Stripe 賬户以接收售票資金。"

#: src/components/layouts/Event/index.tsx:190
msgid "Live"
msgstr "已上線"

#: src/components/routes/event/Webhooks/index.tsx:21
msgid "Loading Webhooks"
msgstr "正在加載 Webhook"

#: src/components/common/ReportTable/index.tsx:210
msgid "Loading..."
msgstr "加載中..."

#: src/components/routes/event/Settings/index.tsx:34
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:84
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:167
msgid "Location"
msgstr "地點"

#: src/components/routes/auth/Login/index.tsx:84
#: src/components/routes/auth/Register/index.tsx:71
msgid "Log in"
msgstr "登錄"

#: src/components/routes/auth/Login/index.tsx:84
msgid "Logging in"
msgstr "登錄"

#: src/components/routes/auth/Register/index.tsx:70
#~ msgid "Login"
#~ msgstr "登錄"

#: src/components/common/GlobalMenu/index.tsx:47
msgid "Logout"
msgstr "註銷"

#: src/components/common/WidgetEditor/index.tsx:331
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit.Nam placerat elementum..."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:170
msgid "Make billing address mandatory during checkout"
msgstr "在結賬時強制要求填寫賬單地址"

#: src/components/routes/event/EventDashboard/index.tsx:172
#~ msgid "Make Event Live"
#~ msgstr "使活動上線"

#: src/components/forms/QuestionForm/index.tsx:212
msgid "Make this question mandatory"
msgstr "將此問題作為必答題"

#: src/components/routes/event/EventDashboard/index.tsx:148
msgid "Make your event live"
msgstr "讓您的活動上線"

#: src/components/common/CapacityAssignmentList/index.tsx:143
#: src/components/common/CheckInListList/index.tsx:145
#: src/components/common/EventCard/index.tsx:180
#: src/components/common/OrdersTable/index.tsx:139
#: src/components/common/OrdersTable/index.tsx:154
#: src/components/common/ProductsTable/SortableProduct/index.tsx:256
#: src/components/common/PromoCodeTable/index.tsx:167
#: src/components/layouts/Event/index.tsx:97
msgid "Manage"
msgstr "管理"

#: src/components/common/AttendeeTable/index.tsx:161
msgid "Manage attendee"
msgstr "管理與會者"

#: src/components/common/EventCard/index.tsx:150
msgid "Manage event"
msgstr "管理活動"

#: src/components/common/OrdersTable/index.tsx:156
msgid "Manage order"
msgstr "管理訂單"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:99
msgid "Manage payment and invoicing settings for this event."
msgstr "管理此活動的支付和發票設置。"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
#~ msgid "Manage products"
#~ msgstr "管理產品"

#: src/components/routes/profile/ManageProfile/index.tsx:101
msgid "Manage Profile"
msgstr "管理簡介"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
msgid "Manage taxes and fees which can be applied to your products"
msgstr "管理可以應用於您的產品的税費"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
#~ msgid "Manage taxes and fees which can be applied to your tickets"
#~ msgstr "管理適用於機票的税費"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
msgid "Manage tickets"
msgstr "管理機票"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:54
msgid "Manage your account details and default settings"
msgstr "管理賬户詳情和默認設置"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:241
msgid "Manage your payment processing and view platform fees"
msgstr "管理您的支付處理並查看平台費用"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:91
#~ msgid "Manage your Stripe payment details"
#~ msgstr "管理 Stripe 付款詳情"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Manage your users and their permissions"
msgstr "管理用户及其權限"

#: src/components/forms/QuestionForm/index.tsx:211
msgid "Mandatory questions must be answered before the customer can checkout."
msgstr "在顧客結賬前，必須回答必填問題。"

#: src/components/common/AttendeeTable/index.tsx:67
msgid "Manually add an Attendee"
msgstr "手動添加與會者"

#: src/components/modals/CreateAttendeeModal/index.tsx:97
#: src/components/modals/CreateAttendeeModal/index.tsx:113
msgid "Manually Add Attendee"
msgstr "手動添加與會者"

#: src/components/modals/CreateAttendeeModal/index.tsx:148
#~ msgid "Manually adding an attendee will adjust ticket quantity."
#~ msgstr "手動添加與會者將調整門票數量。"

#: src/components/common/OrdersTable/index.tsx:167
msgid "Mark as paid"
msgstr "標記為已支付"

#: src/components/layouts/AuthLayout/index.tsx:83
msgid "Match Your Brand"
msgstr "配合您的品牌"

#: src/components/forms/ProductForm/index.tsx:383
msgid "Maximum Per Order"
msgstr "每份訂單的最高限額"

#: src/components/common/AttendeeTable/index.tsx:166
msgid "Message attendee"
msgstr "與會者留言"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:273
msgid "Message Attendees"
msgstr "留言參與者"

#: src/components/modals/SendMessageModal/index.tsx:165
#~ msgid "Message attendees with specific products"
#~ msgstr "向有特定產品的參會者發送消息"

#: src/components/modals/SendMessageModal/index.tsx:206
msgid "Message attendees with specific tickets"
msgstr "向與會者發送特定門票的信息"

#: src/components/layouts/AuthLayout/index.tsx:74
msgid "Message attendees, manage orders, and handle refunds all in one place"
msgstr "在一個地方向與會者發送消息、管理訂單並處理退款"

#: src/components/common/OrdersTable/index.tsx:158
msgid "Message buyer"
msgstr "留言買家"

#: src/components/modals/SendMessageModal/index.tsx:250
msgid "Message Content"
msgstr "留言內容"

#: src/components/modals/SendMessageModal/index.tsx:71
msgid "Message individual attendees"
msgstr "給個別與會者留言"

#: src/components/modals/SendMessageModal/index.tsx:218
msgid "Message order owners with specific products"
msgstr "向具有特定產品的訂單所有者發送消息"

#: src/components/modals/SendMessageModal/index.tsx:118
msgid "Message Sent"
msgstr "發送的信息"

#: src/components/layouts/Event/index.tsx:105
#: src/components/routes/event/messages.tsx:31
msgid "Messages"
msgstr "信息"

#: src/components/forms/ProductForm/index.tsx:381
msgid "Minimum Per Order"
msgstr "每次訂購的最低數量"

#: src/components/forms/ProductForm/index.tsx:285
msgid "Minimum Price"
msgstr "最低價格"

#: src/components/routes/event/Settings/index.tsx:58
msgid "Miscellaneous"
msgstr "其他"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:69
msgid "Miscellaneous Settings"
msgstr "雜項設置"

#: src/components/layouts/AuthLayout/index.tsx:63
msgid "Mobile Check-in"
msgstr "移動簽到"

#: src/components/forms/QuestionForm/index.tsx:106
msgid "Multi line text box"
msgstr "多行文本框"

#: src/components/forms/ProductForm/index.tsx:165
msgid "Multiple price options. Perfect for early bird products etc."
msgstr "多種價格選項。非常適合早鳥產品等。"

#: src/components/forms/TicketForm/index.tsx:147
#~ msgid "Multiple price options. Perfect for early bird tickets etc."
#~ msgstr "多種價格選擇。非常適合早鳥票等。"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:71
msgid "My amazing event description..."
msgstr "我的精彩活動描述"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:65
msgid "My amazing event title..."
msgstr "我的精彩活動標題..."

#: src/components/common/GlobalMenu/index.tsx:27
msgid "My Profile"
msgstr "我的簡介"

#: src/components/common/QuestionAndAnswerList/index.tsx:178
msgid "N/A"
msgstr "不適用"

#: src/components/common/WidgetEditor/index.tsx:354
msgid "Nam placerat elementum..."
msgstr "Nam placerat elementum..."

#: src/components/common/AttendeeDetails/index.tsx:13
#: src/components/common/AttendeeTable/index.tsx:108
#: src/components/common/OrderDetails/index.tsx:23
#: src/components/forms/CapaciyAssigmentForm/index.tsx:37
#: src/components/forms/CheckInListForm/index.tsx:20
#: src/components/forms/ProductForm/index.tsx:253
#: src/components/forms/TaxAndFeeForm/index.tsx:61
#: src/components/modals/CreateEventModal/index.tsx:129
#: src/components/modals/DuplicateEventModal/index.tsx:78
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:63
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:72
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:107
#: src/components/routes/welcome/index.tsx:83
msgid "Name"
msgstr "名稱"

#: src/components/modals/CreateEventModal/index.tsx:38
msgid "Name should be less than 150 characters"
msgstr "名稱應少於 150 個字符"

#: src/components/common/QuestionAndAnswerList/index.tsx:181
#: src/components/common/QuestionAndAnswerList/index.tsx:289
msgid "Navigate to Attendee"
msgstr "導航至與會者"

#: src/components/common/PromoCodeTable/index.tsx:158
#: src/components/common/WebhookTable/index.tsx:266
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Never"
msgstr "從不"

#: src/components/routes/auth/AcceptInvitation/index.tsx:111
#: src/components/routes/auth/ResetPassword/index.tsx:59
#: src/components/routes/profile/ManageProfile/index.tsx:195
msgid "New Password"
msgstr "新密碼"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "No"
msgstr "沒有"

#: src/components/common/QuestionAndAnswerList/index.tsx:104
#~ msgid "No {0} available."
#~ msgstr "沒有可用的{0}。"

#: src/components/routes/event/Webhooks/index.tsx:22
msgid "No Active Webhooks"
msgstr "沒有活動的 Webhook"

#: src/components/common/NoEventsBlankSlate/index.tsx:21
msgid "No archived events to show."
msgstr "沒有可顯示的已歸檔活動。"

#: src/components/common/AttendeeList/index.tsx:23
msgid "No attendees found for this order."
msgstr "未找到此訂單的參會者。"

#: src/components/modals/ManageOrderModal/index.tsx:132
msgid "No attendees have been added to this order."
msgstr "尚未向此訂單添加參會者。"

#: src/components/common/AttendeeTable/index.tsx:56
msgid "No Attendees to show"
msgstr "無與會者"

#: src/components/forms/CheckInListForm/index.tsx:48
msgid "No attendees will be able to check in before this date using this list"
msgstr "在此日期前，使用此列表的參與者將無法簽到"

#: src/components/common/CapacityAssignmentList/index.tsx:42
msgid "No Capacity Assignments"
msgstr "沒有容量分配"

#: src/components/common/CheckInListList/index.tsx:44
msgid "No Check-In Lists"
msgstr "沒有簽到列表"

#: src/components/layouts/AuthLayout/index.tsx:34
msgid "No Credit Card Required"
msgstr "無需信用卡"

#: src/components/common/ReportTable/index.tsx:218
msgid "No data available"
msgstr "無可用數據"

#: src/components/common/ReportTable/index.tsx:214
msgid "No data to show. Please select a date range"
msgstr "無數據顯示。請選擇日期範圍"

#: src/components/forms/PromoCodeForm/index.tsx:47
msgid "No Discount"
msgstr "無折扣"

#: src/components/common/NoEventsBlankSlate/index.tsx:20
msgid "No ended events to show."
msgstr "沒有可顯示的已結束活動。"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:88
#~ msgid "No events for this organizer"
#~ msgstr "沒有該組織者的活動"

#: src/components/common/NoEventsBlankSlate/index.tsx:14
msgid "No events to show"
msgstr "無事件顯示"

#: src/components/common/FilterModal/index.tsx:230
msgid "No filters available"
msgstr "沒有可用篩選器"

#: src/components/modals/WebhookLogsModal/index.tsx:177
msgid "No logs found"
msgstr "未找到日誌"

#: src/components/common/MessageList/index.tsx:69
msgid "No messages to show"
msgstr "無信息顯示"

#: src/components/common/OrdersTable/index.tsx:63
msgid "No orders to show"
msgstr "無訂單顯示"

#: src/components/routes/product-widget/Payment/index.tsx:76
msgid "No payment methods are currently available. Please contact the event organizer for assistance."
msgstr "當前沒有可用的支付方式。請聯繫活動組織者以獲取幫助。"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:39
msgid "No Payment Required"
msgstr "無需支付"

#: src/components/modals/ManageAttendeeModal/index.tsx:177
msgid "No product associated with this attendee."
msgstr "此參會者沒有關聯的產品。"

#: src/components/common/ProductSelector/index.tsx:33
msgid "No products available for selection"
msgstr "沒有可供選擇的產品"

#: src/components/modals/CreateProductCategoryModal/index.tsx:22
msgid "No products available in this category."
msgstr "此類別中沒有可用的產品。"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:39
msgid "No Products Yet"
msgstr "尚無產品"

#: src/components/common/PromoCodeTable/index.tsx:46
msgid "No Promo Codes to show"
msgstr "無促銷代碼顯示"

#: src/components/modals/ManageAttendeeModal/index.tsx:193
msgid "No questions answered by this attendee."
msgstr "此與會者未回答任何問題。"

#: src/components/modals/ViewAttendeeModal/index.tsx:75
#~ msgid "No questions have been answered by this attendee."
#~ msgstr "此參會者尚未回答任何問題。"

#: src/components/modals/ManageOrderModal/index.tsx:119
msgid "No questions have been asked for this order."
msgstr "此訂單尚未提出任何問題。"

#: src/components/common/WebhookTable/index.tsx:174
msgid "No response"
msgstr "無響應"

#: src/components/common/WebhookTable/index.tsx:141
msgid "No responses yet"
msgstr "尚無響應"

#: src/components/common/NoResultsSplash/index.tsx:24
msgid "No results"
msgstr "無結果"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:20
msgid "No Search Results"
msgstr "沒有搜索結果"

#: src/components/common/NoResultsSplash/index.tsx:29
msgid "No search results."
msgstr "沒有搜索結果。"

#: src/components/common/TaxAndFeeList/index.tsx:101
msgid "No Taxes or Fees have been added."
msgstr "未加收任何税費。"

#: src/components/common/TicketsTable/index.tsx:65
#~ msgid "No tickets to show"
#~ msgstr "沒有演出門票"

#: src/components/modals/WebhookLogsModal/index.tsx:180
msgid "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."
msgstr "此端點尚未記錄任何 Webhook 事件。事件觸發後將顯示在此處。"

#: src/components/common/WebhookTable/index.tsx:201
msgid "No Webhooks"
msgstr "沒有 Webhooks"

#: src/components/common/PromoCodeTable/index.tsx:82
msgid "None"
msgstr "無"

#: src/components/common/ProductPriceAvailability/index.tsx:27
#: src/components/common/ProductPriceAvailability/index.tsx:48
msgid "Not available"
msgstr "不詳"

#: src/components/common/AttendeeCheckInTable/index.tsx:125
#~ msgid "Not Checked In"
#~ msgstr "未辦理登機手續"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "Not On Sale"
msgstr "非賣品"

#: src/components/modals/ManageAttendeeModal/index.tsx:130
#: src/components/modals/ManageOrderModal/index.tsx:164
msgid "Notes"
msgstr "備註"

#: src/components/common/ReportTable/index.tsx:240
msgid "Nothing to show yet"
msgstr "暫無顯示內容"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:72
msgid "Notification Settings"
msgstr "通知設置"

#: src/components/modals/RefundOrderModal/index.tsx:108
msgid "Notify buyer of refund"
msgstr "通知買方退款"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:75
msgid "Notify organizer of new orders"
msgstr "將新訂單通知組織者"

#: src/components/routes/welcome/index.tsx:72
msgid "Now let's create your first event"
msgstr "現在，讓我們創建第一個事件"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:217
msgid "Number of days allowed for payment (leave blank to omit payment terms from invoices)"
msgstr "允許支付的天數（留空以從發票中省略付款條款）"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:199
msgid "Number Prefix"
msgstr "號碼前綴"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:145
msgid "Offline orders are not reflected in event statistics until the order is marked as paid."
msgstr "線下訂單在標記為已支付之前不會反映在活動統計中。"

#: src/components/routes/product-widget/Payment/index.tsx:66
msgid "Offline payment failed. Please try again or contact the event organizer."
msgstr "線下支付失敗。請重試或聯繫活動組織者。"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:151
msgid "Offline Payment Instructions"
msgstr "線下支付説明"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:90
msgid "Offline Payments"
msgstr "線下支付"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:139
msgid "Offline Payments Information"
msgstr "線下支付信息"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:134
msgid "Offline Payments Settings"
msgstr "線下支付設置"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:71
msgid "On sale"
msgstr "銷售中"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "On Sale"
msgstr "銷售中"

#: src/components/common/NoEventsBlankSlate/index.tsx:19
msgid "Once you create an event, you'll see it here."
msgstr "創建事件後，您就可以在這裏看到它。"

#: src/components/common/ReportTable/index.tsx:245
msgid "Once you start collecting data, you'll see it here."
msgstr "一旦開始收集數據，您將在這裏看到。"

#: src/components/routes/event/GettingStarted/index.tsx:179
msgid "Once you're ready, set your event live and start selling products."
msgstr "一旦準備就緒，將您的活動上線並開始銷售產品。"

#: src/components/routes/event/GettingStarted/index.tsx:108
#~ msgid "Once you're ready, set your event live and start selling tickets."
#~ msgstr "準備就緒後，設置您的活動並開始售票。"

#: src/components/common/EventStatusBadge/index.tsx:18
msgid "Ongoing"
msgstr "持續進行"

#: src/components/common/EventCard/index.tsx:118
msgid "Online event"
msgstr "在線活動"

#: src/components/common/OnlineEventDetails/index.tsx:9
msgid "Online Event Details"
msgstr "在線活動詳情"

#: src/components/modals/SendMessageModal/index.tsx:279
msgid ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."
msgstr ""
"只有與本次活動直接相關的重要郵件才能使用此表單發送。\n"
"任何濫用行為，包括髮送促銷郵件，都將導致賬户立即被封禁。"

#: src/components/modals/SendMessageModal/index.tsx:226
msgid "Only send to orders with these statuses"
msgstr "僅發送給具有這些狀態的訂單"

#: src/components/common/CheckInListList/index.tsx:167
msgid "Open Check-In Page"
msgstr "打開簽到頁面"

#: src/components/layouts/Event/index.tsx:288
msgid "Open sidebar"
msgstr "打開側邊欄"

#: src/components/forms/QuestionForm/index.tsx:45
msgid "Option {i}"
msgstr "方案 {i}"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:253
msgid "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"
msgstr "所有發票上顯示的可選附加信息（例如，付款條款、逾期付款費用、退貨政策）"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:200
msgid "Optional prefix for invoice numbers (e.g., INV-)"
msgstr "發票編號的可選前綴（例如，INV-）"

#: src/components/forms/QuestionForm/index.tsx:28
msgid "Options"
msgstr "選項"

#: src/components/modals/CreateEventModal/index.tsx:118
msgid "or"
msgstr "或"

#: src/components/common/AttendeeTable/index.tsx:110
msgid "Order"
msgstr "訂購"

#: src/components/common/OrdersTable/index.tsx:212
#~ msgid "Order #"
#~ msgstr "訂單號"

#: src/components/forms/WebhookForm/index.tsx:76
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:63
msgid "Order Cancelled"
msgstr "取消訂單"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:62
msgid "Order Completed"
msgstr "訂單已完成"

#: src/components/forms/WebhookForm/index.tsx:52
msgid "Order Created"
msgstr "訂單已創建"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:122
msgid "Order Date"
msgstr "訂購日期"

#: src/components/modals/ManageAttendeeModal/index.tsx:166
#: src/components/modals/ManageOrderModal/index.tsx:86
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:266
msgid "Order Details"
msgstr "訂購詳情"

#: src/components/modals/ViewOrderModal/index.tsx:30
#~ msgid "Order Details {0}"
#~ msgstr "訂單詳情 {0}"

#: src/components/modals/CancelOrderModal/index.tsx:29
msgid "Order has been canceled and the order owner has been notified."
msgstr "訂單已取消，並已通知訂單所有者。"

#: src/components/common/OrdersTable/index.tsx:79
msgid "Order marked as paid"
msgstr "訂單已標記為已支付"

#: src/components/forms/WebhookForm/index.tsx:64
msgid "Order Marked as Paid"
msgstr "訂單標記為已支付"

#: src/components/modals/ManageOrderModal/index.tsx:92
msgid "Order Notes"
msgstr "訂單備註"

#: src/components/common/MessageList/index.tsx:23
msgid "Order owner"
msgstr "訂單所有者"

#: src/components/modals/SendMessageModal/index.tsx:191
msgid "Order owners with a specific product"
msgstr "具有特定產品的訂單所有者"

#: src/components/common/MessageList/index.tsx:19
msgid "Order owners with products"
msgstr "具有產品的訂單所有者"

#: src/components/common/QuestionsTable/index.tsx:313
#: src/components/common/QuestionsTable/index.tsx:355
msgid "Order questions"
msgstr "訂購問題"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:112
msgid "Order Reference"
msgstr "訂購參考"

#: src/components/forms/WebhookForm/index.tsx:70
msgid "Order Refunded"
msgstr "訂單已退款"

#: src/components/routes/event/orders.tsx:45
msgid "Order Status"
msgstr "訂單狀態"

#: src/components/modals/SendMessageModal/index.tsx:228
msgid "Order statuses"
msgstr "訂單狀態"

#: src/components/layouts/Checkout/CheckoutSidebar/index.tsx:28
#: src/components/modals/ManageOrderModal/index.tsx:105
msgid "Order Summary"
msgstr "訂單摘要"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:90
msgid "Order timeout"
msgstr "訂單超時"

#: src/components/forms/WebhookForm/index.tsx:58
msgid "Order Updated"
msgstr "訂單已更新"

#: src/components/layouts/Event/index.tsx:100
#: src/components/routes/event/orders.tsx:113
msgid "Orders"
msgstr "訂單"

#: src/components/common/StatBoxes/index.tsx:51
#: src/components/routes/event/EventDashboard/index.tsx:105
#~ msgid "Orders Created"
#~ msgstr "創建的訂單"

#: src/components/routes/event/orders.tsx:94
msgid "Orders Exported"
msgstr "訂單已導出"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:235
msgid "Organization Address"
msgstr "組織地址"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:226
msgid "Organization Details"
msgstr "組織詳細信息"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:228
msgid "Organization Name"
msgstr "組織名稱"

#: src/components/modals/EditUserModal/index.tsx:55
#: src/components/modals/InviteUserModal/index.tsx:47
#: src/components/routes/events/Dashboard/index.tsx:113
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:186
msgid "Organizer"
msgstr "組織者"

#: src/components/modals/CreateEventModal/index.tsx:41
msgid "Organizer is required"
msgstr "需要組織者"

#: src/components/forms/OrganizerForm/index.tsx:27
msgid "Organizer Name"
msgstr "組織者姓名"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
msgid "Organizers can only manage events and products. They cannot manage users, account settings or billing information."
msgstr "組織者只能管理活動和產品。他們無法管理用户、賬户設置或賬單信息。"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
#~ msgid "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."
#~ msgstr "組織者只能管理活動和門票。他們不能管理用户、賬户設置或賬單信息。"

#: src/components/layouts/Event/index.tsx:87
msgid "Overview"
msgstr "總覽"

#: src/components/common/WidgetEditor/index.tsx:223
msgid "Padding"
msgstr "襯墊"

#: src/components/routes/event/HomepageDesigner/index.tsx:150
msgid "Page background color"
msgstr "頁面背景顏色"

#: src/components/common/ErrorDisplay/index.tsx:13
msgid "Page not found"
msgstr "頁面未找到"

#: src/components/common/StatBoxes/index.tsx:45
msgid "Page views"
msgstr "頁面瀏覽量"

#: src/components/forms/ProductForm/index.tsx:358
msgid "page."
msgstr "page."

#: src/components/modals/CreateAttendeeModal/index.tsx:180
msgid "paid"
msgstr "付訖"

#: src/components/forms/ProductForm/index.tsx:145
msgid "Paid Product"
msgstr "付費產品"

#: src/components/forms/TicketForm/index.tsx:127
#~ msgid "Paid Ticket"
#~ msgstr "付費機票"

#: src/components/routes/event/orders.tsx:30
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:54
msgid "Partially Refunded"
msgstr "部分退款"

#: src/components/routes/auth/Login/index.tsx:73
#: src/components/routes/auth/Register/index.tsx:106
#: src/components/routes/profile/ManageProfile/index.tsx:109
msgid "Password"
msgstr "密碼"

#: src/components/routes/auth/AcceptInvitation/index.tsx:30
msgid "Password must be a minimum  of 8 characters"
msgstr "密碼必須至少包含 8 個字符"

#: src/components/routes/auth/Register/index.tsx:36
msgid "Password must be at least 8 characters"
msgstr "密碼必須至少包含 8 個字符"

#: src/components/routes/auth/ResetPassword/index.tsx:46
msgid "Password reset successfully. Please login with your new password."
msgstr "密碼重置成功。請使用新密碼登錄。"

#: src/components/routes/auth/AcceptInvitation/index.tsx:31
#: src/components/routes/auth/Register/index.tsx:37
msgid "Passwords are not the same"
msgstr "密碼不一樣"

#: src/components/common/WidgetEditor/index.tsx:269
msgid "Paste this where you want the widget to appear."
msgstr "將其粘貼到希望小部件出現的位置。"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:104
msgid "Paste URL"
msgstr "貼上網址"

#: src/components/modals/CreateAttendeeModal/index.tsx:119
msgid "Patrick"
msgstr "帕特里克"

#: src/components/modals/CreateAttendeeModal/index.tsx:133
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/forms/WebhookForm/index.tsx:25
msgid "Paused"
msgstr "已暫停"

#: src/components/forms/StripeCheckoutForm/index.tsx:123
msgid "Payment"
msgstr "付款方式"

#: src/components/routes/event/Settings/index.tsx:64
msgid "Payment & Invoicing"
msgstr "支付和發票"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:98
msgid "Payment & Invoicing Settings"
msgstr "支付和發票設置"

#: src/components/routes/account/ManageAccount/index.tsx:38
msgid "Payment & Plan"
msgstr "付款與計劃"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:216
msgid "Payment Due Period"
msgstr "付款期限"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:41
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:64
msgid "Payment Failed"
msgstr "付款失敗"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:228
#: src/components/routes/product-widget/Payment/PaymentMethods/Offline/index.tsx:14
msgid "Payment Instructions"
msgstr "支付説明"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:105
msgid "Payment Methods"
msgstr "支付方式"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:138
msgid "Payment Processing"
msgstr "支付處理"

#: src/components/common/OrderDetails/index.tsx:76
msgid "Payment provider"
msgstr "支付提供商"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:42
msgid "Payment Received"
msgstr "已收到付款"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:240
msgid "Payment Settings"
msgstr "支付設置"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:135
msgid "Payment Status"
msgstr "支付狀態"

#: src/components/forms/StripeCheckoutForm/index.tsx:60
msgid "Payment succeeded!"
msgstr "付款成功！"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:214
msgid "Payment Terms"
msgstr "付款條款"

#: src/components/forms/PromoCodeForm/index.tsx:51
#: src/components/forms/TaxAndFeeForm/index.tsx:27
msgid "Percentage"
msgstr "百分比"

#: src/components/forms/TaxAndFeeForm/index.tsx:71
msgid "Percentage Amount"
msgstr "百分比 金額"

#: src/components/routes/product-widget/Payment/index.tsx:122
msgid "Place Order"
msgstr "下單"

#: src/components/common/WidgetEditor/index.tsx:257
msgid "Place this in the <head> of your website."
msgstr "將其放在網站的 <head> 中。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:40
msgid "Platform Fees"
msgstr "平台費用"

#: src/components/forms/QuestionForm/index.tsx:31
msgid "Please add at least one option"
msgstr "請至少添加一個選項"

#: src/hooks/useFormErrorResponseHandler.tsx:18
msgid "Please check the provided information is correct"
msgstr "請檢查所提供的信息是否正確"

#: src/components/routes/auth/Login/index.tsx:41
msgid "Please check your email and password and try again"
msgstr "請檢查您的電子郵件和密碼並重試"

#: src/components/routes/auth/AcceptInvitation/index.tsx:32
#: src/components/routes/auth/Register/index.tsx:38
msgid "Please check your email is valid"
msgstr "請檢查您的電子郵件是否有效"

#: src/components/routes/profile/ManageProfile/index.tsx:152
msgid "Please check your email to confirm your email address"
msgstr "請檢查您的電子郵件以確認您的電子郵件地址"

#: src/components/routes/auth/AcceptInvitation/index.tsx:86
msgid "Please complete the form below to accept your invitation"
msgstr "請填寫下表接受邀請"

#: src/components/routes/product-widget/SelectProducts/index.tsx:306
msgid "Please continue in the new tab"
msgstr "請在新標籤頁中繼續"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:47
msgid "Please create a product"
msgstr "請創建一個產品"

#: src/components/modals/CreateCheckInListModal/index.tsx:51
msgid "Please create a ticket"
msgstr "請創建一張票"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:45
msgid "Please enter a valid image URL that points to an image."
msgstr "請輸入指向圖像的有效圖片網址。"

#: src/components/modals/CreateWebhookModal/index.tsx:30
msgid "Please enter a valid URL"
msgstr "請輸入有效的 URL"

#: src/components/routes/auth/ResetPassword/index.tsx:55
msgid "Please enter your new password"
msgstr "請輸入新密碼"

#: src/components/modals/CancelOrderModal/index.tsx:52
msgid "Please Note"
msgstr "請注意"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:34
msgid "Please provide an image."
msgstr "請提供圖片。"

#: src/components/common/TicketsTable/index.tsx:84
#~ msgid "Please remove filters and set sorting to \"Homepage order\" to enable sorting"
#~ msgstr "請移除篩選器，並將排序設置為 \"主頁順序\"，以啟用排序功能"

#: src/components/layouts/Checkout/index.tsx:159
msgid "Please return to the event page to start over."
msgstr "請返回活動頁面重新開始。"

#: src/components/modals/SendMessageModal/index.tsx:195
msgid "Please select"
msgstr "請選擇"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:53
msgid "Please select an image."
msgstr "請選擇圖片。"

#: src/components/routes/product-widget/SelectProducts/index.tsx:237
msgid "Please select at least one product"
msgstr "請選擇至少一個產品"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:227
#~ msgid "Please select at least one ticket"
#~ msgstr "請至少選擇一張票"

#: src/components/routes/event/attendees.tsx:49
#: src/components/routes/event/orders.tsx:100
msgid "Please try again."
msgstr "請再試一次。"

#: src/components/routes/profile/ManageProfile/index.tsx:142
msgid "Please verify your email address to access all features"
msgstr "請驗證您的電子郵件地址，以訪問所有功能"

#: src/components/routes/event/attendees.tsx:40
msgid "Please wait while we prepare your attendees for export..."
msgstr "請稍候，我們正在準備導出您的與會者..."

#: src/components/common/OrdersTable/index.tsx:112
#: src/components/layouts/Checkout/index.tsx:50
msgid "Please wait while we prepare your invoice..."
msgstr "請稍候，我們正在準備您的發票..."

#: src/components/routes/event/orders.tsx:91
msgid "Please wait while we prepare your orders for export..."
msgstr "請稍候，我們正在準備導出您的訂單..."

#: src/components/common/LanguageSwitcher/index.tsx:26
msgid "Portuguese"
msgstr "葡萄牙語"

#: src/locales.ts:47
#~ msgid "Portuguese (Brazil)"
#~ msgstr "葡萄牙語（巴西）"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:74
msgid "Post Checkout message"
msgstr "結賬後信息"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Powered by"
msgstr "技術支持"

#: src/components/forms/StripeCheckoutForm/index.tsx:137
#~ msgid "Powered by Stripe"
#~ msgstr "由 Stripe 提供支持"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:67
msgid "Pre Checkout message"
msgstr "結賬前信息"

#: src/components/common/QuestionsTable/index.tsx:347
msgid "Preview"
msgstr "預覽"

#: src/components/layouts/Event/index.tsx:205
#: src/components/layouts/Event/index.tsx:209
msgid "Preview Event page"
msgstr "預覽活動頁面"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:236
#: src/components/forms/ProductForm/index.tsx:73
#: src/components/forms/ProductForm/index.tsx:285
msgid "Price"
msgstr "價格"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:78
msgid "Price display mode"
msgstr "價格顯示模式"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:88
msgid "Price not set"
msgstr "未設置價格"

#: src/components/forms/ProductForm/index.tsx:322
msgid "Price tiers"
msgstr "價格等級"

#: src/components/forms/ProductForm/index.tsx:228
msgid "Price Type"
msgstr "價格類型"

#: src/components/common/WidgetEditor/index.tsx:182
msgid "Primary Color"
msgstr "原色"

#: src/components/routes/event/HomepageDesigner/index.tsx:156
msgid "Primary Colour"
msgstr "原色"

#: src/components/common/WidgetEditor/index.tsx:190
#: src/components/routes/event/HomepageDesigner/index.tsx:158
msgid "Primary Text Color"
msgstr "主要文字顏色"

#: src/components/common/AttendeeTicket/index.tsx:81
msgid "Print"
msgstr "打印"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:288
msgid "Print All Tickets"
msgstr "打印所有門票"

#: src/components/layouts/Checkout/index.tsx:114
msgid "Print Tickets"
msgstr "打印票"

#: src/components/common/AttendeeTable/index.tsx:190
#~ msgid "product"
#~ msgstr "產品"

#: src/components/common/AttendeeDetails/index.tsx:37
#: src/components/modals/ManageAttendeeModal/index.tsx:119
msgid "Product"
msgstr "產品"

#: src/components/forms/ProductForm/index.tsx:266
msgid "Product Category"
msgstr "產品類別"

#: src/components/modals/EditProductCategoryModal/index.tsx:53
msgid "Product category updated successfully."
msgstr "產品類別更新成功。"

#: src/components/forms/WebhookForm/index.tsx:34
msgid "Product Created"
msgstr "產品已創建"

#: src/components/forms/WebhookForm/index.tsx:46
msgid "Product Deleted"
msgstr "產品已刪除"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:55
msgid "Product deleted successfully"
msgstr "產品刪除成功"

#: src/components/common/AttendeeTable/index.tsx:50
#~ msgid "Product email has been resent to attendee"
#~ msgstr "產品電子郵件已重新發送給參會者"

#: src/components/forms/ProductForm/index.tsx:236
msgid "Product Price Type"
msgstr "產品價格類型"

#: src/components/common/QuestionsTable/index.tsx:328
msgid "Product questions"
msgstr "產品問題"

#: src/components/routes/event/EventDashboard/index.tsx:217
#: src/components/routes/event/Reports/index.tsx:17
msgid "Product Sales"
msgstr "產品銷售"

#: src/components/routes/event/Reports/index.tsx:18
msgid "Product sales, revenue, and tax breakdown"
msgstr "產品銷售、收入和税費明細"

#: src/components/common/ProductSelector/index.tsx:63
msgid "Product Tier"
msgstr "產品等級"

#: src/components/forms/ProductForm/index.tsx:214
#: src/components/forms/ProductForm/index.tsx:221
msgid "Product Type"
msgstr "產品類型"

#: src/components/forms/WebhookForm/index.tsx:40
msgid "Product Updated"
msgstr "產品已更新"

#: src/components/common/WidgetEditor/index.tsx:313
msgid "Product Widget Preview"
msgstr "產品小部件預覽"

#: src/components/common/PromoCodeTable/index.tsx:148
msgid "Product(s)"
msgstr "產品"

#: src/components/common/PromoCodeTable/index.tsx:72
msgid "Products"
msgstr "產品"

#: src/components/common/EventCard/index.tsx:126
msgid "products sold"
msgstr "已售產品"

#: src/components/common/StatBoxes/index.tsx:21
msgid "Products sold"
msgstr "已售產品"

#: src/components/routes/event/EventDashboard/index.tsx:238
msgid "Products Sold"
msgstr "已售產品"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:178
msgid "Products sorted successfully"
msgstr "產品排序成功"

#: src/components/layouts/AuthLayout/index.tsx:43
msgid "Products, merchandise, and flexible pricing options"
msgstr "產品、商品和靈活的定價選項"

#: src/components/routes/profile/ManageProfile/index.tsx:106
msgid "Profile"
msgstr "簡介"

#: src/components/routes/profile/ManageProfile/index.tsx:59
msgid "Profile updated successfully"
msgstr "成功更新個人資料"

#: src/components/routes/product-widget/SelectProducts/index.tsx:178
msgid "Promo {promo_code} code applied"
msgstr "已使用促銷 {promo_code} 代碼"

#: src/components/common/OrderDetails/index.tsx:86
msgid "Promo code"
msgstr "優惠碼"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:20
msgid "Promo Code"
msgstr "促銷代碼"

#: src/components/forms/ProductForm/index.tsx:409
msgid "Promo Code page"
msgstr "促銷代碼頁面"

#: src/components/routes/event/Reports/index.tsx:30
msgid "Promo code usage and discount breakdown"
msgstr "促銷碼使用情況及折扣明細"

#: src/components/layouts/Event/index.tsx:106
#: src/components/routes/event/promo-codes.tsx:31
msgid "Promo Codes"
msgstr "促銷代碼"

#: src/components/common/PromoCodeTable/index.tsx:51
msgid "Promo codes can be used to offer discounts, presale access, or provide special access to your event."
msgstr "促銷代碼可用於提供折扣、預售權限或為您的活動提供特殊權限。"

#: src/components/routes/event/Reports/index.tsx:29
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:95
msgid "Promo Codes Report"
msgstr "促銷代碼報告"

#: src/components/forms/QuestionForm/index.tsx:189
msgid ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."
msgstr "提供此問題的附加背景或説明。使用此字段添加條款和條件、指南或參與者在回答前需要知道的任何重要信息。"

#: src/components/routes/event/EventDashboard/index.tsx:159
msgid "Publish Event"
msgstr "發佈活動"

#: src/components/common/GlobalMenu/index.tsx:25
#~ msgid "Purchase License"
#~ msgstr "購買許可證"

#: src/components/modals/ShareModal/index.tsx:126
msgid "QR Code"
msgstr "二維碼"

#: src/components/layouts/AuthLayout/index.tsx:64
msgid "QR code scanning with instant feedback and secure sharing for staff access"
msgstr "二維碼掃描，提供即時反饋和安全共享，供工作人員使用"

#: src/components/forms/ProductForm/index.tsx:85
#: src/components/forms/ProductForm/index.tsx:302
msgid "Quantity Available"
msgstr "可用數量"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
msgid "Quantity Sold"
msgstr "銷售數量"

#: src/components/common/QuestionsTable/index.tsx:170
msgid "Question deleted"
msgstr "問題已刪除"

#: src/components/forms/QuestionForm/index.tsx:188
msgid "Question Description"
msgstr "問題描述"

#: src/components/forms/QuestionForm/index.tsx:178
msgid "Question Title"
msgstr "問題標題"

#: src/components/common/QuestionsTable/index.tsx:275
#: src/components/layouts/Event/index.tsx:102
msgid "Questions"
msgstr "問題"

#: src/components/modals/ManageAttendeeModal/index.tsx:184
#: src/components/modals/ManageOrderModal/index.tsx:111
msgid "Questions & Answers"
msgstr "問與答"

#: src/components/common/QuestionsTable/index.tsx:145
msgid "Questions sorted successfully"
msgstr "問題已成功分類"

#: src/components/forms/QuestionForm/index.tsx:118
msgid "Radio Option"
msgstr "無線電選項"

#: src/components/common/MessageList/index.tsx:59
msgid "Read less"
msgstr "更多信息"

#: src/components/modals/SendMessageModal/index.tsx:36
msgid "Recipient"
msgstr "受援國"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:110
#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:195
msgid "Redirecting to Stripe..."
msgstr "正在重定向到 Stripe..."

#: src/components/common/OrdersTable/index.tsx:204
#: src/components/common/OrdersTable/index.tsx:274
msgid "Reference"
msgstr "參考資料"

#: src/components/modals/RefundOrderModal/index.tsx:104
msgid "Refund amount ({0})"
msgstr "退款金額 ({0})"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:52
msgid "Refund Failed"
msgstr "退款失敗"

#: src/components/common/OrdersTable/index.tsx:172
msgid "Refund order"
msgstr "退款訂單"

#: src/components/modals/RefundOrderModal/index.tsx:151
msgid "Refund Order"
msgstr "退款訂單"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:51
msgid "Refund Pending"
msgstr "退款處理中"

#: src/components/routes/event/orders.tsx:51
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:128
msgid "Refund Status"
msgstr "退款狀態"

#: src/components/common/OrderSummary/index.tsx:80
#: src/components/common/StatBoxes/index.tsx:33
#: src/components/routes/event/orders.tsx:29
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:53
msgid "Refunded"
msgstr "退款"

#: src/components/routes/auth/Register/index.tsx:129
msgid "Register"
msgstr "註冊"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:82
msgid "Remaining Uses"
msgstr "剩餘使用次數"

#: src/components/routes/product-widget/SelectProducts/index.tsx:504
msgid "remove"
msgstr "去除"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:140
#: src/components/routes/product-widget/SelectProducts/index.tsx:505
msgid "Remove"
msgstr "移除"

#: src/components/layouts/Event/index.tsx:92
#: src/components/routes/event/Reports/index.tsx:39
msgid "Reports"
msgstr "報告"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:169
msgid "Require Billing Address"
msgstr "要求賬單地址"

#: src/components/routes/event/GettingStarted/index.tsx:197
msgid "Resend confirmation email"
msgstr "重新發送確認電子郵件"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resend email confirmation"
msgstr "重新發送電子郵件確認"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Resend invitation"
msgstr "重新發送邀請"

#: src/components/common/OrdersTable/index.tsx:179
msgid "Resend order email"
msgstr "重新發送訂單電子郵件"

#: src/components/common/AttendeeTable/index.tsx:179
#~ msgid "Resend product email"
#~ msgstr "重新發送產品電子郵件"

#: src/components/common/AttendeeTable/index.tsx:171
msgid "Resend ticket email"
msgstr "重新發送票務電子郵件"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resending..."
msgstr "重新發送..."

#: src/components/common/FilterModal/index.tsx:248
msgid "Reset"
msgstr "重置"

#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Reset password"
msgstr "重置密碼"

#: src/components/routes/auth/ResetPassword/index.tsx:54
msgid "Reset Password"
msgstr "重置密碼"

#: src/components/routes/auth/ForgotPassword/index.tsx:50
msgid "Reset your password"
msgstr "重置您的密碼"

#: src/components/common/EventCard/index.tsx:167
msgid "Restore event"
msgstr "恢復活動"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:50
#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:63
msgid "Return to event page"
msgstr "返回活動頁面"

#: src/components/layouts/Checkout/index.tsx:165
msgid "Return to Event Page"
msgstr "返回活動頁面"

#: src/components/routes/event/EventDashboard/index.tsx:249
msgid "Revenue"
msgstr "收入"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Revoke invitation"
msgstr "撤銷邀請"

#: src/components/modals/EditUserModal/index.tsx:93
#: src/components/modals/InviteUserModal/index.tsx:64
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Role"
msgstr "角色"

#: src/components/forms/ProductForm/index.tsx:96
#: src/components/forms/ProductForm/index.tsx:391
msgid "Sale End Date"
msgstr "銷售結束日期"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:75
msgid "Sale ended"
msgstr "銷售結束"

#: src/components/forms/ProductForm/index.tsx:91
#: src/components/forms/ProductForm/index.tsx:389
msgid "Sale Start Date"
msgstr "銷售開始日期"

#: src/components/common/ProductPriceAvailability/index.tsx:13
#: src/components/common/ProductPriceAvailability/index.tsx:35
msgid "Sales ended"
msgstr "銷售結束"

#: src/components/common/ProductPriceAvailability/index.tsx:19
#: src/components/common/ProductPriceAvailability/index.tsx:40
msgid "Sales start"
msgstr "銷售開始"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:135
msgid "San Francisco"
msgstr "舊金山"

#: src/components/common/QuestionAndAnswerList/index.tsx:142
#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:80
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:114
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:96
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:166
#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:94
#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:265
#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:85
msgid "Save"
msgstr "保存"

#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/event/HomepageDesigner/index.tsx:166
msgid "Save Changes"
msgstr "保存更改"

#: src/components/modals/EditOrganizerModal/index.tsx:70
msgid "Save Organizer"
msgstr "保存組織器"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:90
msgid "Save Settings"
msgstr "保存設置"

#: src/components/layouts/CheckIn/index.tsx:398
#: src/components/layouts/CheckIn/index.tsx:400
msgid "Scan QR Code"
msgstr "掃描 QR 碼"

#: src/components/modals/ShareModal/index.tsx:229
msgid "Scan this QR code to access the event page or share it with others"
msgstr "掃描此二維碼以訪問活動頁面或與他人分享"

#: src/components/layouts/CheckIn/index.tsx:288
#~ msgid "Seach by name, order #, attendee # or email..."
#~ msgstr "按姓名、訂單號、參會者編號或電子郵件搜索..."

#: src/components/routes/event/attendees.tsx:64
msgid "Search by attendee name, email or order #..."
msgstr "按與會者姓名、電子郵件或訂單號搜索..."

#: src/components/routes/events/Dashboard/index.tsx:69
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:66
msgid "Search by event name..."
msgstr "按活動名稱搜索..."

#: src/components/routes/event/orders.tsx:126
msgid "Search by name, email, or order #..."
msgstr "按姓名、電子郵件或訂單號搜索..."

#: src/components/layouts/CheckIn/index.tsx:394
msgid "Search by name, order #, attendee # or email..."
msgstr "按姓名、訂單號、參與者號或電子郵件搜索..."

#: src/components/routes/event/promo-codes.tsx:34
msgid "Search by name..."
msgstr "按名稱搜索..."

#: src/components/routes/event/products.tsx:43
#~ msgid "Search by product name..."
#~ msgstr ""

#: src/components/routes/event/messages.tsx:34
msgid "Search by subject or content..."
msgstr "按主題或內容搜索..."

#: src/components/routes/event/tickets.tsx:43
#~ msgid "Search by ticket name..."
#~ msgstr "按票務名稱搜索..."

#: src/components/routes/event/CapacityAssignments/index.tsx:37
msgid "Search capacity assignments..."
msgstr "搜索容量分配..."

#: src/components/routes/event/CheckInLists/index.tsx:37
msgid "Search check-in lists..."
msgstr "搜索簽到列表..."

#: src/components/routes/event/products.tsx:52
msgid "Search products"
msgstr "搜索產品"

#: src/components/common/SearchBar/index.tsx:36
msgid "Search..."
msgstr "搜索..."

#: src/components/routes/event/HomepageDesigner/index.tsx:160
msgid "Secondary color"
msgstr "輔助色"

#: src/components/common/WidgetEditor/index.tsx:198
msgid "Secondary Color"
msgstr "輔助色"

#: src/components/routes/event/HomepageDesigner/index.tsx:162
msgid "Secondary text color"
msgstr "輔助文字顏色"

#: src/components/common/WidgetEditor/index.tsx:206
msgid "Secondary Text Color"
msgstr "輔助文字顏色"

#: src/components/common/FilterModal/index.tsx:150
msgid "Select {0}"
msgstr "選擇 {0}"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:214
msgid "Select Camera"
msgstr "選擇相機"

#: src/components/forms/ProductForm/index.tsx:269
msgid "Select category..."
msgstr "選擇類別..."

#: src/components/forms/WebhookForm/index.tsx:124
msgid "Select event types"
msgstr "選擇事件類型"

#: src/components/modals/CreateEventModal/index.tsx:110
msgid "Select organizer"
msgstr "選擇組織者"

#: src/components/modals/ManageAttendeeModal/index.tsx:118
msgid "Select Product"
msgstr "選擇產品"

#: src/components/common/ProductSelector/index.tsx:65
msgid "Select Product Tier"
msgstr "選擇產品等級"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:49
#: src/components/modals/SendMessageModal/index.tsx:219
msgid "Select products"
msgstr "選擇產品"

#: src/components/modals/EditUserModal/index.tsx:104
msgid "Select status"
msgstr "選擇狀態"

#: src/components/modals/CreateAttendeeModal/index.tsx:150
msgid "Select Ticket"
msgstr "選擇機票"

#: src/components/modals/CreateAttendeeModal/index.tsx:163
#: src/components/modals/EditAttendeeModal/index.tsx:117
#~ msgid "Select Ticket Tier"
#~ msgstr "選擇票價等級"

#: src/components/forms/CheckInListForm/index.tsx:26
#: src/components/modals/SendMessageModal/index.tsx:207
msgid "Select tickets"
msgstr "選擇票"

#: src/components/common/ReportTable/index.tsx:261
msgid "Select time period"
msgstr "選擇時間段"

#: src/components/forms/WebhookForm/index.tsx:123
msgid "Select which events will trigger this webhook"
msgstr "選擇哪些事件將觸發此 Webhook"

#: src/components/forms/ProductForm/index.tsx:352
msgid "Select..."
msgstr "選擇..."

#: src/components/layouts/AuthLayout/index.tsx:68
msgid "Sell Anything"
msgstr "銷售任何產品"

#: src/components/layouts/AuthLayout/index.tsx:69
msgid "Sell merchandise alongside tickets with integrated tax and promo code support"
msgstr "在銷售門票的同時銷售商品，並支持集成税務和促銷代碼"

#: src/components/layouts/AuthLayout/index.tsx:42
msgid "Sell More Than Tickets"
msgstr "銷售不僅僅是門票"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send"
msgstr "發送"

#: src/components/modals/SendMessageModal/index.tsx:259
msgid "Send a copy to <0>{0}</0>"
msgstr "將副本發送至 <0>{0}</0>"

#: src/components/modals/SendMessageModal/index.tsx:139
msgid "Send a message"
msgstr "發送信息"

#: src/components/modals/SendMessageModal/index.tsx:269
msgid "Send as a test. This will send the message to your email address instead of the recipients."
msgstr "作為測試發送。這將把信息發送到您的電子郵件地址，而不是收件人的電子郵件地址。"

#: src/components/routes/event/messages.tsx:41
msgid "Send Message"
msgstr "發送消息"

#: src/components/modals/CreateAttendeeModal/index.tsx:191
#~ msgid "Send order confirmation and product email"
#~ msgstr "發送訂單確認和產品電子郵件"

#: src/components/modals/CreateAttendeeModal/index.tsx:192
msgid "Send order confirmation and ticket email"
msgstr "發送訂單確認和票務電子郵件"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send Test"
msgstr "發送測試"

#: src/components/routes/event/Settings/index.tsx:46
msgid "SEO"
msgstr "SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:70
msgid "SEO Description"
msgstr "搜索引擎優化説明"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:76
msgid "SEO Keywords"
msgstr "搜索引擎優化關鍵詞"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:56
msgid "SEO Settings"
msgstr "搜索引擎優化設置"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:64
msgid "SEO Title"
msgstr "搜索引擎優化標題"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "Service Fee"
msgstr "服務費"

#: src/components/forms/ProductForm/index.tsx:159
msgid "Set a minimum price and let users pay more if they choose"
msgstr "設定最低價格，用户可選擇支付更高的價格"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:207
msgid "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."
msgstr "設置發票編號的起始編號。一旦發票生成，就無法更改。"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Set up your event"
msgstr "準備活動"

#: src/components/routes/event/EventDashboard/index.tsx:190
#~ msgid "Set up your payment processing to receive funds from ticket sales."
#~ msgstr "設置您的支付處理，以接收門票銷售的資金。"

#: src/components/routes/event/GettingStarted/index.tsx:183
msgid "Set your event live"
msgstr "實時設置您的活動"

#: src/components/layouts/Event/index.tsx:98
#: src/components/routes/event/Settings/index.tsx:103
msgid "Settings"
msgstr "設置"

#: src/components/layouts/AuthLayout/index.tsx:26
msgid "Setup in Minutes"
msgstr "幾分鐘內完成設置"

#: src/components/common/ShareIcon/index.tsx:28
#: src/components/modals/ShareModal/index.tsx:123
#: src/components/modals/ShareModal/index.tsx:141
msgid "Share"
msgstr "分享"

#: src/components/layouts/Event/index.tsx:251
#: src/components/modals/ShareModal/index.tsx:116
msgid "Share Event"
msgstr "分享活動"

#: src/components/modals/ShareModal/index.tsx:169
msgid "Share to Facebook"
msgstr "分享到 Facebook"

#: src/components/modals/ShareModal/index.tsx:163
msgid "Share to LinkedIn"
msgstr "分享到 LinkedIn"

#: src/components/modals/ShareModal/index.tsx:193
msgid "Share to Pinterest"
msgstr "分享到 Pinterest"

#: src/components/modals/ShareModal/index.tsx:187
msgid "Share to Reddit"
msgstr "分享到 Reddit"

#: src/components/modals/ShareModal/index.tsx:148
msgid "Share to Social"
msgstr "分享到社交網絡"

#: src/components/modals/ShareModal/index.tsx:181
msgid "Share to Telegram"
msgstr "分享到 Telegram"

#: src/components/modals/ShareModal/index.tsx:175
msgid "Share to WhatsApp"
msgstr "分享到 WhatsApp"

#: src/components/modals/ShareModal/index.tsx:157
msgid "Share to X"
msgstr "分享到 X"

#: src/components/modals/ShareModal/index.tsx:199
msgid "Share via Email"
msgstr "通過電子郵件分享"

#: src/components/forms/ProductForm/index.tsx:344
msgid "Show"
msgstr "顯示"

#: src/components/forms/ProductForm/index.tsx:403
msgid "Show available product quantity"
msgstr "顯示可用產品數量"

#: src/components/forms/TicketForm/index.tsx:348
#~ msgid "Show available ticket quantity"
#~ msgstr "顯示可用門票數量"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Show hidden questions"
msgstr "顯示隱藏問題"

#: src/components/routes/product-widget/SelectProducts/index.tsx:459
msgid "Show more"
msgstr "顯示更多"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:60
msgid "Show tax and fees separately"
msgstr "單獨顯示税費"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:76
#~ msgid "Shown to the customer after they checkout, on the order summary page"
#~ msgstr "在客户結賬後，在訂單摘要頁面上顯示給客户"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
msgid "Shown to the customer after they checkout, on the order summary page."
msgstr "結賬後顯示給客户，在訂單摘要頁面。"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:69
msgid "Shown to the customer before they checkout"
msgstr "在顧客結賬前向他們展示"

#: src/components/forms/QuestionForm/index.tsx:132
msgid "Shows common address fields, including country"
msgstr "顯示常用地址字段，包括國家"

#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:151
msgid "Simpson"
msgstr "辛普森"

#: src/components/forms/QuestionForm/index.tsx:100
msgid "Single line text box"
msgstr "單行文本框"

#: src/components/routes/welcome/index.tsx:137
msgid "Skip this step"
msgstr "跳過此步驟"

#: src/components/layouts/AuthLayout/index.tsx:78
msgid "Smart Check-in"
msgstr "智能簽到"

#: src/components/layouts/AuthLayout/index.tsx:53
#~ msgid "Smart Dashboard"
#~ msgstr "智能儀表板"

#: src/components/routes/auth/Register/index.tsx:90
msgid "Smith"
msgstr "史密斯"

#: src/components/common/ProductPriceAvailability/index.tsx:9
#: src/components/common/ProductPriceAvailability/index.tsx:32
msgid "Sold out"
msgstr "售罄"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:67
msgid "Sold Out"
msgstr "售罄"

#: src/components/common/ErrorDisplay/index.tsx:14
#: src/components/routes/auth/AcceptInvitation/index.tsx:47
msgid "Something went wrong"
msgstr "出了問題"

#: src/components/common/TaxAndFeeList/index.tsx:39
msgid "Something went wrong while deleting the Tax or Fee"
msgstr "刪除税費時出了問題"

#: src/components/routes/auth/ForgotPassword/index.tsx:31
msgid "Something went wrong, please try again, or contact support if the problem persists"
msgstr "出現問題，請重試，或在問題持續時聯繫客服"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Something went wrong! Please try again"
msgstr "出錯了！請重試"

#: src/components/forms/StripeCheckoutForm/index.tsx:69
msgid "Something went wrong."
msgstr "出問題了"

#: src/components/routes/profile/ManageProfile/index.tsx:80
#: src/components/routes/profile/ManageProfile/index.tsx:94
msgid "Something went wrong. Please try again."
msgstr "出錯了。請重試。"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:61
msgid "Sorry, something has gone wrong. Please restart the checkout process."
msgstr "對不起，出了點問題。請重新開始結賬。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:259
msgid "Sorry, something went wrong loading this page."
msgstr "抱歉，在加載此頁面時出了點問題。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:247
msgid "Sorry, this order no longer exists."
msgstr "抱歉，此訂單不再存在。"

#: src/components/routes/product-widget/SelectProducts/index.tsx:246
msgid "Sorry, this promo code is not recognized"
msgstr "對不起，此優惠代碼不可用"

#: src/components/layouts/Checkout/index.tsx:26
#~ msgid "Sorry, your order has expired. Please start a new order."
#~ msgstr "抱歉，您的訂單已過期。請重新訂購。"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Sorting is disabled while filters and sorting are applied"
#~ msgstr "應用篩選器和排序時禁用排序"

#: src/components/common/LanguageSwitcher/index.tsx:18
msgid "Spanish"
msgstr "西班牙語"

#: src/components/forms/ProductForm/index.tsx:147
msgid "Standard product with a fixed price"
msgstr "固定價格的標準產品"

#: src/components/forms/TicketForm/index.tsx:129
#~ msgid "Standard ticket with a fixed price"
#~ msgstr "固定價格標準票"

#: src/components/modals/CreateEventModal/index.tsx:144
#: src/components/modals/DuplicateEventModal/index.tsx:93
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:87
#: src/components/routes/welcome/index.tsx:90
msgid "Start Date"
msgstr "開始日期"

#: src/components/common/CheckoutQuestion/index.tsx:165
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:139
#: src/components/routes/product-widget/CollectInformation/index.tsx:339
#: src/components/routes/product-widget/CollectInformation/index.tsx:340
msgid "State or Region"
msgstr "州或地區"

#: src/components/common/AttendeeTable/index.tsx:112
#: src/components/common/OrderDetails/index.tsx:51
#: src/components/common/OrdersTable/index.tsx:209
#: src/components/common/ProductsTable/SortableProduct/index.tsx:222
#: src/components/common/WebhookTable/index.tsx:238
#: src/components/forms/CapaciyAssigmentForm/index.tsx:56
#: src/components/forms/WebhookForm/index.tsx:133
#: src/components/modals/EditUserModal/index.tsx:103
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:88
msgid "Status"
msgstr "現狀"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:85
msgid "Stripe"
msgstr "Stripe"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:48
msgid "Stripe payments are not enabled for this event."
msgstr "此活動未啟用 Stripe 支付。"

#: src/components/modals/SendMessageModal/index.tsx:245
msgid "Subject"
msgstr "主題"

#: src/components/common/OrderSummary/index.tsx:41
msgid "Subtotal"
msgstr "小計"

#: src/components/common/OrdersTable/index.tsx:115
#: src/components/layouts/Checkout/index.tsx:53
msgid "Success"
msgstr "成功"

#: src/components/modals/EditUserModal/index.tsx:40
#: src/components/modals/InviteUserModal/index.tsx:32
msgid "Success! {0} will receive an email shortly."
msgstr "成功！{0} 將很快收到一封電子郵件。"

#: src/components/common/AttendeeTable/index.tsx:90
msgid "Successfully {0} attendee"
msgstr "成功 {0} 參會者"

#: src/components/common/AttendeeCheckInTable/index.tsx:47
#: src/components/common/AttendeeCheckInTable/index.tsx:71
#~ msgid "Successfully checked <0>{0} {1}</0> {2}"
#~ msgstr "成功檢查 <0>{0} {1}</0> {2}"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:23
msgid "Successfully confirmed email address"
msgstr "成功確認電子郵件地址"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:31
msgid "Successfully confirmed email change"
msgstr "成功確認電子郵件更改"

#: src/components/modals/CreateAttendeeModal/index.tsx:82
msgid "Successfully created attendee"
msgstr "成功創建與會者"

#: src/components/modals/CreateProductModal/index.tsx:54
msgid "Successfully Created Product"
msgstr "產品創建成功"

#: src/components/modals/CreatePromoCodeModal/index.tsx:39
msgid "Successfully Created Promo Code"
msgstr "成功創建促銷代碼"

#: src/components/modals/CreateQuestionModal/index.tsx:45
#: src/components/modals/EditQuestionModal/index.tsx:66
msgid "Successfully Created Question"
msgstr "成功創建問題"

#: src/components/modals/CreateTicketModal/index.tsx:50
#~ msgid "Successfully Created Ticket"
#~ msgstr "成功創建票單"

#: src/components/modals/DuplicateProductModal/index.tsx:95
msgid "Successfully Duplicated Product"
msgstr "產品複製成功"

#: src/components/modals/ManageAttendeeModal/index.tsx:96
msgid "Successfully updated attendee"
msgstr "成功更新與會者"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:47
msgid "Successfully updated Capacity Assignment"
msgstr "容量分配更新成功"

#: src/components/modals/EditCheckInListModal/index.tsx:49
msgid "Successfully updated Check-In List"
msgstr "簽到列表更新成功"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:43
msgid "Successfully Updated Email Settings"
msgstr "成功更新電子郵件設置"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:54
msgid "Successfully Updated Event"
msgstr "成功更新活動"

#: src/components/routes/event/HomepageDesigner/index.tsx:84
msgid "Successfully Updated Homepage Design"
msgstr "成功更新主頁設計"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:50
msgid "Successfully Updated Homepage Settings"
msgstr "成功更新主頁設置"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:73
msgid "Successfully Updated Location"
msgstr "成功更新位置"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:43
msgid "Successfully Updated Misc Settings"
msgstr "成功更新雜項設置"

#: src/components/modals/ManageOrderModal/index.tsx:74
msgid "Successfully updated order"
msgstr "訂單更新成功"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:74
msgid "Successfully Updated Payment & Invoicing Settings"
msgstr "支付和發票設置已成功更新"

#: src/components/modals/EditProductModal/index.tsx:89
msgid "Successfully updated product"
msgstr "產品更新成功"

#: src/components/modals/EditPromoCodeModal/index.tsx:52
msgid "Successfully Updated Promo Code"
msgstr "成功更新促銷代碼"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:45
msgid "Successfully Updated Seo Settings"
msgstr "成功更新搜索引擎設置"

#: src/components/modals/EditTicketModal/index.tsx:85
#~ msgid "Successfully updated ticket"
#~ msgstr "成功更新票單"

#: src/components/modals/EditWebhookModal/index.tsx:44
msgid "Successfully updated Webhook"
msgstr "Webhook 更新成功"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:128
msgid "Suite 100"
msgstr "100 號套房"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
msgid "Support Email"
msgstr "支持電子郵件"

#: src/components/layouts/AuthLayout/index.tsx:59
msgid "Support for tiered, donation-based, and product sales with customizable pricing and capacity"
msgstr "支持分級、基於捐贈和產品銷售，並提供可自定義的定價和容量"

#: src/components/forms/ProductForm/index.tsx:254
msgid "T-shirt"
msgstr "T恤衫"

#: src/components/forms/TaxAndFeeForm/index.tsx:12
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Tax"
msgstr "税收"

#: src/components/routes/account/ManageAccount/index.tsx:27
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:16
msgid "Tax & Fees"
msgstr "税費"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:243
msgid "Tax Details"
msgstr "税務詳情"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:244
msgid "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"
msgstr "所有發票底部顯示的税務信息（例如，增值税號、税務註冊號）"

#: src/components/common/TaxAndFeeList/index.tsx:36
msgid "Tax or Fee deleted successfully"
msgstr "成功刪除税費"

#: src/components/forms/ProductForm/index.tsx:363
msgid "Taxes"
msgstr "税收"

#: src/components/forms/ProductForm/index.tsx:351
#: src/components/forms/ProductForm/index.tsx:358
msgid "Taxes and Fees"
msgstr "税費"

#: src/components/routes/product-widget/SelectProducts/index.tsx:138
#: src/components/routes/product-widget/SelectProducts/index.tsx:186
msgid "That promo code is invalid"
msgstr "促銷代碼無效"

#: src/components/layouts/CheckIn/index.tsx:316
msgid "The check-in list you are looking for does not exist."
msgstr "您查找的簽到列表不存在。"

#: src/components/forms/OrganizerForm/index.tsx:47
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:75
msgid "The default currency for your events."
msgstr "事件的默認貨幣。"

#: src/components/forms/OrganizerForm/index.tsx:56
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:84
msgid "The default timezone for your events."
msgstr "事件的默認時區。"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:16
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:43
msgid "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."
msgstr "您查找的活動目前不可用。它可能已被刪除、過期或 URL 不正確。"

#: src/components/modals/CreateAttendeeModal/index.tsx:146
msgid "The language the attendee will receive emails in."
msgstr "與會者接收電子郵件的語言。"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:22
msgid "The link you clicked is invalid."
msgstr "您點擊的鏈接無效。"

#: src/components/routes/product-widget/SelectProducts/index.tsx:444
msgid "The maximum number of products for {0}is {1}"
msgstr "{0}的最大產品數量是{1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:351
#~ msgid "The maximum numbers number of tickets for {0}is {1}"
#~ msgstr "{0}的最大票數是{1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:318
#~ msgid "The maximum numbers number of tickets for Generals is {0}"
#~ msgstr "將軍票的最大票數為{0}。"

#: src/components/common/ErrorDisplay/index.tsx:17
msgid "The page you are looking for does not exist"
msgstr "您要查找的頁面不存在"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:56
msgid "The price displayed to the customer will include taxes and fees."
msgstr "顯示給客户的價格將包括税費。"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:62
msgid "The price displayed to the customer will not include taxes and fees. They will be shown separately"
msgstr "顯示給客户的價格不包括税費。税費將單獨顯示"

#: src/components/common/WidgetEditor/index.tsx:169
msgid "The styling settings you choose apply only to copied HTML and won't be stored."
msgstr "您選擇的樣式設置只適用於複製的 HTML，不會被保存。"

#: src/components/forms/ProductForm/index.tsx:355
msgid "The taxes and fees to apply to this product. You can create new taxes and fees on the"
msgstr "應用於此產品的税費。您可以在此創建新的税費"

#: src/components/forms/TicketForm/index.tsx:300
#~ msgid "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "適用於該票據的税費。您可以在"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:63
msgid "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"
msgstr "活動標題，將顯示在搜索引擎結果中，並在社交媒體上分享時顯示。默認情況下，將使用事件標題"

#: src/components/routes/product-widget/SelectProducts/index.tsx:272
msgid "There are no products available for this event"
msgstr "此活動沒有可用產品"

#: src/components/routes/product-widget/SelectProducts/index.tsx:375
msgid "There are no products available in this category"
msgstr "此類別中沒有可用產品"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:260
#~ msgid "There are no tickets available for this event"
#~ msgstr "本次活動沒有門票"

#: src/components/modals/RefundOrderModal/index.tsx:137
msgid "There is a refund pending. Please wait for it to complete before requesting another refund."
msgstr "退款正在處理中。請等待退款完成後再申請退款。"

#: src/components/common/OrdersTable/index.tsx:80
msgid "There was an error marking the order as paid"
msgstr "標記訂單為已支付時出錯"

#: src/hooks/useFormErrorResponseHandler.tsx:28
msgid "There was an error processing your request. Please try again."
msgstr "處理您的請求時出現錯誤。請重試。"

#: src/components/common/OrdersTable/index.tsx:95
msgid "There was an error sending your message"
msgstr "發送信息時出現錯誤"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:105
msgid "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."
msgstr "這些詳細信息僅在訂單成功完成後顯示。等待付款的訂單不會顯示此消息。"

#: src/components/layouts/CheckIn/index.tsx:201
msgid "This attendee has an unpaid order."
msgstr "此與會者有未支付的訂單。"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:61
msgid "This category doesn't have any products yet."
msgstr "此類別尚無任何產品。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:112
msgid "This category is hidden from public view"
msgstr "此類別對公眾隱藏"

#: src/components/common/CheckInListList/index.tsx:75
msgid "This check-in list has expired"
msgstr "此簽到列表已過期"

#: src/components/layouts/CheckIn/index.tsx:331
msgid "This check-in list has expired and is no longer available for check-ins."
msgstr "此簽到列表已過期，不再可用於簽到。"

#: src/components/common/CheckInListList/index.tsx:82
msgid "This check-in list is active"
msgstr "此簽到列表已激活"

#: src/components/common/CheckInListList/index.tsx:79
msgid "This check-in list is not active yet"
msgstr "此簽到列表尚未激活"

#: src/components/layouts/CheckIn/index.tsx:348
msgid "This check-in list is not yet active and is not available for check-ins."
msgstr "此簽到列表尚未激活，不能用於簽到。"

#: src/components/forms/CheckInListForm/index.tsx:37
msgid "This description will be shown to the check-in staff"
msgstr "此描述將顯示給簽到工作人員"

#: src/components/modals/SendMessageModal/index.tsx:285
msgid "This email is not promotional and is directly related to the event."
msgstr "此電子郵件並非促銷郵件，與活動直接相關。"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:8
#~ msgid "This event is not available at the moment. Please check back later."
#~ msgstr "此活動暫時不可用。請稍後再查看。"

#: src/components/layouts/EventHomepage/index.tsx:49
#~ msgid "This event is not available."
#~ msgstr "此活動不可用。"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
msgid "This information will be shown on the payment page, order summary page, and order confirmation email."
msgstr "這些信息將顯示在支付頁面、訂單摘要頁面和訂單確認電子郵件中。"

#: src/components/forms/ProductForm/index.tsx:138
msgid "This is a general product, like a t-shirt or a mug. No ticket will be issued"
msgstr "這是一種常規產品，例如T恤或杯子。不發行門票"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:91
msgid "This is an online event"
msgstr "這是一項在線活動"

#: src/components/forms/CheckInListForm/index.tsx:57
msgid "This list will no longer be available for check-ins after this date"
msgstr "此日期後此列表將不再可用於簽到"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:68
msgid "This message will be included in the footer of all emails sent from this event"
msgstr "此信息將包含在本次活動發送的所有電子郵件的頁腳中"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
msgid "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"
msgstr "此消息僅在訂單成功完成後顯示。等待付款的訂單不會顯示此消息。"

#: src/components/forms/StripeCheckoutForm/index.tsx:93
msgid "This order has already been paid."
msgstr "此訂單已付款。"

#: src/components/modals/RefundOrderModal/index.tsx:141
msgid "This order has already been refunded."
msgstr "此訂單已退款。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:237
msgid "This order has been cancelled"
msgstr "此訂單已取消"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:209
msgid "This order has been cancelled."
msgstr "此訂單已取消。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:210
msgid "This order has expired. Please start again."
msgstr "此訂單已過期。請重新開始。"

#: src/components/routes/product-widget/CollectInformation/index.tsx:221
msgid "This order is awaiting payment"
msgstr "此訂單正在等待付款"

#: src/components/routes/product-widget/CollectInformation/index.tsx:229
msgid "This order is complete"
msgstr "此訂單已完成"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:211
msgid "This order is complete."
msgstr "此訂單已完成。"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:204
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:207
msgid "This order is processing."
msgstr "此訂單正在處理中。"

#: src/components/forms/StripeCheckoutForm/index.tsx:103
msgid "This order page is no longer available."
msgstr "此訂購頁面已不可用。"

#: src/components/forms/ProductForm/index.tsx:415
msgid "This overrides all visibility settings and will hide the product from all customers."
msgstr "這將覆蓋所有可見性設置，並將該產品對所有客户隱藏。"

#: src/components/forms/TicketForm/index.tsx:360
#~ msgid "This overrides all visibility settings and will hide the ticket from all customers."
#~ msgstr "這會覆蓋所有可見性設置，並對所有客户隱藏票單。"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:59
msgid "This product cannot be deleted because it is associated with an order. You can hide it instead."
msgstr "此產品無法刪除，因為它與訂單關聯。您可以將其隱藏。"

#: src/components/forms/ProductForm/index.tsx:132
msgid "This product is a ticket. Buyers will be issued a ticket upon purchase"
msgstr "此產品為門票。購買後買家將收到門票"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:215
msgid "This product is hidden from public view"
msgstr "此產品對公眾隱藏"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:216
msgid "This product is hidden unless targeted by a Promo Code"
msgstr "除非由促銷代碼指定，否則此產品是隱藏的"

#: src/components/common/QuestionsTable/index.tsx:90
msgid "This question is only visible to the event organizer"
msgstr "此問題只有活動組織者可見"

#: src/components/routes/auth/ResetPassword/index.tsx:29
msgid "This reset password link is invalid or expired."
msgstr "此重置密碼鏈接無效或已過期。"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:56
#~ msgid ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."
#~ msgstr ""
#~ "不能刪除此票單，因為它\n"
#~ "與訂單相關聯。您可以將其隱藏。"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:131
#~ msgid "This ticket is hidden from public view"
#~ msgstr "此票不對外開放"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:132
#~ msgid "This ticket is hidden unless targeted by a Promo Code"
#~ msgstr "除非使用促銷代碼，否則此票為隱藏票"

#: src/components/modals/EditUserModal/index.tsx:65
msgid "This user is not active, as they have not accepted their invitation."
msgstr "該用户未激活，因為他們沒有接受邀請。"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "ticket"
msgstr "入場券"

#: src/components/common/AttendeeTable/index.tsx:111
#: src/components/forms/ProductForm/index.tsx:130
#: src/components/modals/CreateAttendeeModal/index.tsx:151
msgid "Ticket"
msgstr "門票"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:52
#~ msgid "Ticket deleted successfully"
#~ msgstr "成功刪除票單"

#: src/components/common/AttendeeTable/index.tsx:49
msgid "Ticket email has been resent to attendee"
msgstr "門票電子郵件已重新發送給與會者"

#: src/components/common/MessageList/index.tsx:22
msgid "Ticket holders"
msgstr "票務持有人"

#: src/components/routes/event/products.tsx:86
msgid "Ticket or Product"
msgstr "票券或產品"

#: src/components/routes/event/EventDashboard/index.tsx:53
#~ msgid "Ticket Sales"
#~ msgstr "門票銷售"

#: src/components/modals/CreateAttendeeModal/index.tsx:161
#: src/components/modals/EditAttendeeModal/index.tsx:115
#~ msgid "Ticket Tier"
#~ msgstr "門票等級"

#: src/components/forms/TicketForm/index.tsx:189
#: src/components/forms/TicketForm/index.tsx:196
#~ msgid "Ticket Type"
#~ msgstr "機票類型"

#: src/components/common/WidgetEditor/index.tsx:311
#~ msgid "Ticket Widget Preview"
#~ msgstr "票務小工具預覽"

#: src/components/common/PromoCodeTable/index.tsx:147
#~ msgid "Ticket(s)"
#~ msgstr "機票"

#: src/components/common/PromoCodeTable/index.tsx:71
#: src/components/layouts/Event/index.tsx:40
#: src/components/layouts/EventHomepage/index.tsx:80
#: src/components/routes/event/tickets.tsx:39
#~ msgid "Tickets"
#~ msgstr "門票"

#: src/components/layouts/Event/index.tsx:101
#: src/components/routes/event/products.tsx:46
msgid "Tickets & Products"
msgstr "票券與產品"

#: src/components/routes/product-widget/PrintOrder/index.tsx:43
msgid "Tickets for"
msgstr "門票"

#: src/components/common/EventCard/index.tsx:126
#~ msgid "tickets sold"
#~ msgstr "已售票"

#: src/components/common/StatBoxes/index.tsx:21
#~ msgid "Tickets sold"
#~ msgstr "已售出的門票"

#: src/components/routes/event/EventDashboard/index.tsx:73
#~ msgid "Tickets Sold"
#~ msgstr "已售出的門票"

#: src/components/common/TicketsTable/index.tsx:44
#~ msgid "Tickets sorted successfully"
#~ msgstr "成功分類的門票"

#: src/components/forms/ProductForm/index.tsx:67
msgid "Tier {0}"
msgstr "{0}級"

#: src/components/forms/ProductForm/index.tsx:163
msgid "Tiered Product"
msgstr "分層產品"

#: src/components/forms/ProductForm/index.tsx:240
#~ msgid ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""

#: src/components/forms/ProductForm/index.tsx:243
msgid "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."
msgstr "分層產品允許您為同一產品提供多種價格選項。這非常適合早鳥產品，或為不同人羣提供不同的價格選項。\" # zh-cn"

#: src/components/forms/TicketForm/index.tsx:145
#~ msgid "Tiered Ticket"
#~ msgstr "分層票"

#: src/components/forms/TicketForm/index.tsx:203
#~ msgid ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""
#~ "分層門票允許您為同一張門票提供多種價格選擇。\n"
#~ "這非常適合早鳥票，或為不同人羣提供不同價\n"
#~ "選擇。"

#: src/components/layouts/Checkout/index.tsx:90
msgid "Time left:"
msgstr "剩餘時間："

#: src/components/common/PromoCodeTable/index.tsx:71
msgid "Times used"
msgstr "使用次數"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:25
msgid "Times Used"
msgstr "使用次數"

#: src/components/forms/OrganizerForm/index.tsx:54
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:82
#: src/components/routes/auth/AcceptInvitation/index.tsx:105
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:109
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:181
#: src/components/routes/profile/ManageProfile/index.tsx:161
msgid "Timezone"
msgstr "時區"

#: src/components/forms/PromoCodeForm/index.tsx:36
msgid "TIP"
msgstr "TIP"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:206
msgid "Title"
msgstr "標題"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:182
msgid "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."
msgstr "要接受信用卡付款，您需要連接您的 Stripe 賬户。Stripe 是我們的支付處理合作夥伴，確保安全交易和及時付款。"

#: src/components/layouts/Event/index.tsx:108
msgid "Tools"
msgstr "工具"

#: src/components/common/OrderSummary/index.tsx:92
msgid "Total"
msgstr "總計"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:52
msgid "Total Before Discounts"
msgstr "折扣前總計"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:58
msgid "Total Discount Amount"
msgstr "折扣總金額"

#: src/components/routes/event/EventDashboard/index.tsx:273
msgid "Total Fees"
msgstr "總費用"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:46
msgid "Total Gross Sales"
msgstr "總銷售額"

#: src/components/common/OrderDetails/index.tsx:59
#: src/components/modals/RefundOrderModal/index.tsx:74
msgid "Total order amount"
msgstr "訂單總額"

#: src/components/common/OrderDetails/index.tsx:67
#: src/components/modals/RefundOrderModal/index.tsx:82
msgid "Total refunded"
msgstr "退款總額"

#: src/components/routes/event/EventDashboard/index.tsx:276
msgid "Total Refunded"
msgstr "退款總額"

#: src/components/modals/RefundOrderModal/index.tsx:90
msgid "Total remaining"
msgstr "剩餘總數"

#: src/components/routes/event/EventDashboard/index.tsx:275
msgid "Total Tax"
msgstr "總税額"

#: src/components/layouts/AuthLayout/index.tsx:54
msgid "Track revenue, page views, and sales with detailed analytics and exportable reports"
msgstr "通過詳細的分析和可導出的報告跟蹤收入、頁面瀏覽量和銷售情況"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
msgid "Transaction Fee:"
msgstr "交易手續費："

#: src/components/forms/TaxAndFeeForm/index.tsx:44
msgid "Type"
msgstr "類型"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "Unable to {0} attendee"
#~ msgstr "無法{0}與會者"

#: src/components/layouts/CheckIn/index.tsx:93
msgid "Unable to check in attendee"
msgstr "無法簽到參與者"

#: src/components/layouts/CheckIn/index.tsx:114
msgid "Unable to check out attendee"
msgstr "無法簽退參與者"

#: src/components/modals/CreateProductModal/index.tsx:63
msgid "Unable to create product. Please check the your details"
msgstr "無法創建產品。請檢查您的詳細信息"

#: src/components/routes/product-widget/SelectProducts/index.tsx:123
msgid "Unable to create product. Please check your details"
msgstr "無法創建產品。請檢查您的詳細信息"

#: src/components/modals/CreateQuestionModal/index.tsx:60
msgid "Unable to create question. Please check the your details"
msgstr "無法創建問題。請檢查您的詳細信息"

#: src/components/modals/CreateTicketModal/index.tsx:65
#: src/components/routes/ticket-widget/SelectTickets/index.tsx:117
#~ msgid "Unable to create ticket. Please check the your details"
#~ msgstr "無法創建票單。請檢查您的詳細信息"

#: src/components/modals/DuplicateProductModal/index.tsx:103
msgid "Unable to duplicate product. Please check the your details"
msgstr "無法複製產品。請檢查您的詳細信息"

#: src/components/layouts/CheckIn/index.tsx:145
msgid "Unable to fetch attendee"
msgstr "無法擷取參與者資料"

#: src/components/modals/EditQuestionModal/index.tsx:84
msgid "Unable to update question. Please check the your details"
msgstr "無法更新問題。請檢查您的詳細信息"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:30
msgid "Unique Customers"
msgstr "獨立客户"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:153
msgid "United States"
msgstr "美國"

#: src/components/common/QuestionAndAnswerList/index.tsx:284
msgid "Unknown Attendee"
msgstr "未知參會者"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:43
#: src/components/forms/ProductForm/index.tsx:83
#: src/components/forms/ProductForm/index.tsx:299
#: src/components/forms/PromoCodeForm/index.tsx:82
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:78
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:84
msgid "Unlimited"
msgstr "無限制"

#: src/components/routes/product-widget/SelectProducts/index.tsx:407
msgid "Unlimited available"
msgstr "無限供應"

#: src/components/common/PromoCodeTable/index.tsx:126
msgid "Unlimited usages allowed"
msgstr "允許無限次使用"

#: src/components/layouts/CheckIn/index.tsx:200
msgid "Unpaid Order"
msgstr "未支付訂單"

#: src/components/routes/event/EventDashboard/index.tsx:170
msgid "Unpublish Event"
msgstr "取消發佈活動"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:20
#: src/components/common/EventStatusBadge/index.tsx:16
msgid "Upcoming"
msgstr "即將推出"

#: src/components/routes/events/Dashboard/index.tsx:55
msgid "Upcoming Events"
msgstr "即將舉行的活動"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Update {0}"
msgstr "更新 {0}"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:66
msgid "Update event name, description and dates"
msgstr "更新活動名稱、説明和日期"

#: src/components/routes/profile/ManageProfile/index.tsx:177
msgid "Update profile"
msgstr "更新個人資料"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Upload Cover"
msgstr "上傳封面"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:105
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:152
msgid "Upload Image"
msgstr "上傳圖片"

#: src/components/common/WebhookTable/index.tsx:236
#: src/components/modals/ShareModal/index.tsx:129
msgid "URL"
msgstr "鏈接"

#: src/components/common/PromoCodeTable/index.tsx:175
msgid "URL copied to clipboard"
msgstr "複製到剪貼板的 URL"

#: src/components/modals/CreateWebhookModal/index.tsx:25
msgid "URL is required"
msgstr "URL 是必填項"

#: src/components/common/WidgetEditor/index.tsx:298
msgid "Usage Example"
msgstr "使用示例"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:76
msgid "Usage Limit"
msgstr "使用限制"

#: src/components/routes/event/HomepageDesigner/index.tsx:139
msgid "Use a blurred version of the cover image as the background"
msgstr "使用封面圖片的模糊版本作為背景"

#: src/components/routes/event/HomepageDesigner/index.tsx:137
msgid "Use cover image"
msgstr "使用封面圖片"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User"
msgstr "用户"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User Management"
msgstr "用户管理"

#: src/components/routes/account/ManageAccount/index.tsx:32
msgid "Users"
msgstr "用户"

#: src/components/modals/EditUserModal/index.tsx:81
msgid "Users can change their email in <0>Profile Settings</0>"
msgstr "用户可在 <0>\"配置文件設置\"</0> 中更改自己的電子郵件"

#: src/components/forms/OrganizerForm/index.tsx:55
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:83
#: src/components/routes/auth/AcceptInvitation/index.tsx:106
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:110
#: src/components/routes/profile/ManageProfile/index.tsx:162
msgid "UTC"
msgstr "世界協調時"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "VAT"
msgstr "增值税"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:116
msgid "Venue Name"
msgstr "地點名稱"

#: src/components/common/LanguageSwitcher/index.tsx:34
msgid "Vietnamese"
msgstr "越南語"

#: src/components/modals/ManageAttendeeModal/index.tsx:220
#: src/components/modals/ManageOrderModal/index.tsx:200
msgid "View"
msgstr "查看"

#: src/components/routes/event/Reports/index.tsx:38
msgid "View and download reports for your event. Please note, only completed orders are included in these reports."
msgstr "查看並下載您的活動報告。請注意，報告中僅包含已完成的訂單。"

#: src/components/common/AttendeeList/index.tsx:84
msgid "View Answers"
msgstr "查看答案"

#: src/components/common/AttendeeTable/index.tsx:164
#~ msgid "View attendee"
#~ msgstr "查看與會者"

#: src/components/common/AttendeeList/index.tsx:103
msgid "View Attendee Details"
msgstr "查看參會者詳情"

#: src/components/layouts/Checkout/index.tsx:55
#~ msgid "View event homepage"
#~ msgstr "查看活動主頁"

#: src/components/common/EventCard/index.tsx:145
msgid "View event page"
msgstr "查看活動頁面"

#: src/components/common/MessageList/index.tsx:59
msgid "View full message"
msgstr "查看完整信息"

#: src/components/common/WebhookTable/index.tsx:113
msgid "View logs"
msgstr "查看日誌"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View map"
msgstr "查看地圖"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View on Google Maps"
msgstr "在谷歌地圖上查看"

#: src/components/common/OrdersTable/index.tsx:111
#~ msgid "View order"
#~ msgstr "查看訂單"

#: src/components/forms/StripeCheckoutForm/index.tsx:94
#: src/components/forms/StripeCheckoutForm/index.tsx:104
#: src/components/routes/product-widget/CollectInformation/index.tsx:231
msgid "View order details"
msgstr "查看訂單詳情"

#: src/components/forms/CheckInListForm/index.tsx:21
msgid "VIP check-in list"
msgstr "VIP簽到列表"

#: src/components/forms/ProductForm/index.tsx:254
#~ msgid "VIP Product"
#~ msgstr "VIP產品"

#: src/components/forms/ProductForm/index.tsx:254
msgid "VIP Ticket"
msgstr "貴賓票"

#: src/components/forms/ProductForm/index.tsx:394
msgid "Visibility"
msgstr "可見性"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:77
msgid "We could not process your payment. Please try again or contact support."
msgstr "我們無法處理您的付款。請重試或聯繫技術支持。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:53
msgid "We couldn't delete the category. Please try again."
msgstr "我們無法刪除該類別。請再試一次。"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:24
msgid "We couldn't find any tickets matching {0}"
msgstr "我們找不到與{0}匹配的任何門票"

#: src/components/common/ErrorLoadingMessage/index.tsx:13
msgid "We couldn't load the data. Please try again."
msgstr "我們無法加載數據。請重試。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:94
msgid "We couldn't reorder the categories. Please try again."
msgstr "我們無法重新排序類別。請再試一次。"

#: src/components/routes/event/HomepageDesigner/index.tsx:118
msgid "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"
msgstr "我們建議尺寸為 2160px x 1080px，文件大小不超過 5MB"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:42
#~ msgid "We use Stripe to process payments. Connect your Stripe account to start receiving payments."
#~ msgstr "我們使用 Stripe 處理付款。連接您的 Stripe 賬户即可開始接收付款。"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:84
msgid "We were unable to confirm your payment. Please try again or contact support."
msgstr "我們無法確認您的付款。請重試或聯繫技術支持。"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:79
msgid "We're processing your order. Please wait..."
msgstr "我們正在處理您的訂單。請稍候..."

#: src/components/modals/CreateWebhookModal/index.tsx:46
msgid "Webhook created successfully"
msgstr "Webhook 創建成功"

#: src/components/common/WebhookTable/index.tsx:53
msgid "Webhook deleted successfully"
msgstr "Webhook 刪除成功"

#: src/components/modals/WebhookLogsModal/index.tsx:151
msgid "Webhook Logs"
msgstr "Webhook 日誌"

#: src/components/forms/WebhookForm/index.tsx:117
msgid "Webhook URL"
msgstr "Webhook URL"

#: src/components/forms/WebhookForm/index.tsx:27
msgid "Webhook will not send notifications"
msgstr "Webhook 不會發送通知"

#: src/components/forms/WebhookForm/index.tsx:21
msgid "Webhook will send notifications"
msgstr "Webhook 將發送通知"

#: src/components/layouts/Event/index.tsx:111
#: src/components/routes/event/Webhooks/index.tsx:30
msgid "Webhooks"
msgstr "Webhooks"

#: src/components/routes/auth/AcceptInvitation/index.tsx:76
msgid "Welcome aboard! Please login to continue."
msgstr "歡迎加入！請登錄以繼續。"

#: src/components/routes/auth/Login/index.tsx:55
msgid "Welcome back 👋"
msgstr "歡迎回來 👋"

#: src/components/routes/event/EventDashboard/index.tsx:95
msgid "Welcome back{0} 👋"
msgstr "歡迎回來{0} 👋"

#: src/components/routes/auth/Register/index.tsx:67
msgid "Welcome to Hi.Events 👋"
msgstr "歡迎來到 Hi.Events 👋"

#: src/components/routes/welcome/index.tsx:125
msgid "Welcome to Hi.Events, {0} 👋"
msgstr "歡迎來到 Hi.Events, {0} 👋"

#: src/components/forms/ProductForm/index.tsx:242
msgid "What are Tiered Products?"
msgstr "什麼是分層產品？"

#: src/components/forms/TicketForm/index.tsx:202
#~ msgid "What are Tiered Tickets?"
#~ msgstr "什麼是分層門票？"

#: src/components/forms/CheckInListForm/index.tsx:50
msgid "What date should this check-in list become active?"
msgstr "此簽到列表應在何時激活？"

#: src/components/modals/CreateProductCategoryModal/index.tsx:53
msgid "What is a Category?"
msgstr "什麼是類別？"

#: src/components/routes/event/Webhooks/index.tsx:31
#~ msgid "What is a webhook?"
#~ msgstr "什麼是 Webhook？"

#: src/components/forms/QuestionForm/index.tsx:159
msgid "What products does this code apply to?"
msgstr "此代碼適用於哪些產品？"

#: src/components/forms/PromoCodeForm/index.tsx:68
msgid "What products does this code apply to? (Applies to all by default)"
msgstr "此代碼適用於哪些產品？（默認適用於所有產品）"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:48
msgid "What products should this capacity apply to?"
msgstr "此容量應適用於哪些產品？"

#: src/components/forms/PromoCodeForm/index.tsx:68
#~ msgid "What tickets does this code apply to? (Applies to all by default)"
#~ msgstr "此代碼適用於哪些機票？(默認適用於所有）"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:47
#: src/components/forms/QuestionForm/index.tsx:159
#~ msgid "What tickets should this question be apply to?"
#~ msgstr "該問題應適用於哪些機票？"

#: src/components/forms/QuestionForm/index.tsx:179
msgid "What time will you be arriving?"
msgstr "您什麼時候抵達？"

#: src/components/forms/QuestionForm/index.tsx:170
msgid "What type of question is this?"
msgstr "這是什麼類型的問題？"

#: src/components/forms/WebhookForm/index.tsx:108
msgid "When a check-in is deleted"
msgstr "當簽到被刪除時"

#: src/components/forms/WebhookForm/index.tsx:84
msgid "When a new attendee is created"
msgstr "當新與會者被創建時"

#: src/components/forms/WebhookForm/index.tsx:54
msgid "When a new order is created"
msgstr "當新訂單被創建時"

#: src/components/forms/WebhookForm/index.tsx:36
msgid "When a new product is created"
msgstr "當新產品被創建時"

#: src/components/forms/WebhookForm/index.tsx:48
msgid "When a product is deleted"
msgstr "當產品被刪除時"

#: src/components/forms/WebhookForm/index.tsx:42
msgid "When a product is updated"
msgstr "當產品被更新時"

#: src/components/forms/WebhookForm/index.tsx:96
msgid "When an attendee is cancelled"
msgstr "當與會者被取消時"

#: src/components/forms/WebhookForm/index.tsx:102
msgid "When an attendee is checked in"
msgstr "當與會者簽到時"

#: src/components/forms/WebhookForm/index.tsx:90
msgid "When an attendee is updated"
msgstr "當與會者被更新時"

#: src/components/forms/WebhookForm/index.tsx:78
msgid "When an order is cancelled"
msgstr "當訂單被取消時"

#: src/components/forms/WebhookForm/index.tsx:66
msgid "When an order is marked as paid"
msgstr "當訂單被標記為已支付時"

#: src/components/forms/WebhookForm/index.tsx:72
msgid "When an order is refunded"
msgstr "當訂單被退款時"

#: src/components/forms/WebhookForm/index.tsx:60
msgid "When an order is updated"
msgstr "當訂單被更新時"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:182
msgid "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."
msgstr "啟用後，將為票務訂單生成發票。發票將隨訂單確認郵件一起發送。參與"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:141
msgid "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."
msgstr "啟用線下支付後，用户可以完成訂單並收到門票。他們的門票將清楚地顯示訂單未支付，簽到工具會通知簽到工作人員訂單是否需要支付。"

#: src/components/forms/CheckInListForm/index.tsx:59
msgid "When should this check-in list expire?"
msgstr "此簽到列表應在何時過期？"

#: src/components/forms/CheckInListForm/index.tsx:25
msgid "Which tickets should be associated with this check-in list?"
msgstr "哪些票應與此簽到列表關聯？"

#: src/components/modals/CreateEventModal/index.tsx:107
msgid "Who is organizing this event?"
msgstr "這項活動由誰組織？"

#: src/components/modals/SendMessageModal/index.tsx:194
msgid "Who is this message to?"
msgstr "這條信息是發給誰的？"

#: src/components/forms/QuestionForm/index.tsx:151
msgid "Who should be asked this question?"
msgstr "這個問題應該問誰？"

#: src/components/layouts/Event/index.tsx:110
msgid "Widget Embed"
msgstr "嵌入小部件"

#: src/components/common/WidgetEditor/index.tsx:162
msgid "Widget Settings"
msgstr "小部件設置"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Working"
msgstr "工作"

#: src/components/modals/CreateProductCategoryModal/index.tsx:60
#: src/components/modals/CreateProductModal/index.tsx:88
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
#: src/components/modals/CreateQuestionModal/index.tsx:74
#: src/components/modals/DuplicateEventModal/index.tsx:142
#: src/components/modals/DuplicateProductModal/index.tsx:113
#: src/components/modals/EditProductCategoryModal/index.tsx:70
#: src/components/modals/EditProductModal/index.tsx:108
#: src/components/modals/EditPromoCodeModal/index.tsx:84
#: src/components/modals/EditQuestionModal/index.tsx:101
#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/Register/index.tsx:129
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Working..."
msgstr "工作..."

#: src/components/common/ReportTable/index.tsx:51
msgid "Year to date"
msgstr "年度至今"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "Yes"
msgstr "是"

#: src/components/forms/ProductForm/index.tsx:376
msgid "Yes, remove them"
msgstr "是的，移除它們"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:77
msgid "You already scanned this ticket"
msgstr "您已經掃描過此票"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:53
msgid "You are changing your email to <0>{0}</0>."
msgstr "您正在將電子郵件更改為 <0>{0}</0>。"

#: src/components/layouts/CheckIn/index.tsx:88
#: src/components/layouts/CheckIn/index.tsx:110
msgid "You are offline"
msgstr "您處於離線狀態"

#: src/components/forms/ProductForm/index.tsx:407
msgid "You can create a promo code which targets this product on the"
msgstr "您可以創建一個促銷代碼，針對該產品"

#: src/components/forms/TicketForm/index.tsx:352
#~ msgid "You can create a promo code which targets this ticket on the"
#~ msgstr "您可以在"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:31
#~ msgid "You can now start receiving payments through Stripe."
#~ msgstr "現在您可以開始通過 Stripe 接收付款。"

#: src/components/forms/ProductForm/index.tsx:208
msgid "You cannot change the product type as there are attendees associated with this product."
msgstr "您無法更改產品類型，因為有與該產品關聯的參會者。"

#: src/components/forms/TicketForm/index.tsx:184
#~ msgid "You cannot change the ticket type as there are attendees associated with this ticket."
#~ msgstr "您不能更改票務類型，因為該票務已關聯了與會者。"

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "You cannot check in attendees with unpaid orders."
#~ msgstr "您無法為未支付訂單的與會者簽到。"

#: src/components/layouts/CheckIn/index.tsx:129
#: src/components/layouts/CheckIn/index.tsx:164
msgid "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."
msgstr "您無法為未支付訂單的與會者簽到。此設置可在活動設置中更改。"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:40
msgid "You cannot delete the last category."
msgstr "您不能刪除最後一個類別。"

#: src/components/forms/ProductForm/index.tsx:57
msgid "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."
msgstr "您無法刪除此價格層，因為此層已有售出的產品。您可以將其隱藏。"

#: src/components/forms/TicketForm/index.tsx:54
#~ msgid "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."
#~ msgstr "您不能刪除此價格等級，因為此等級已售出門票。您可以將其隱藏。"

#: src/components/modals/EditUserModal/index.tsx:88
msgid "You cannot edit the role or status of the account owner."
msgstr "不能編輯賬户所有者的角色或狀態。"

#: src/components/modals/RefundOrderModal/index.tsx:132
msgid "You cannot refund a manually created order."
msgstr "您不能退還手動創建的訂單。"

#: src/components/common/QuestionsTable/index.tsx:252
msgid "You created a hidden question but disabled the option to show hidden questions. It has been enabled."
msgstr "您創建了一個隱藏問題，但禁用了顯示隱藏問題的選項。該選項已啟用。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:77
#~ msgid "You do not have permission to access this page"
#~ msgstr "您沒有訪問該頁面的權限"

#: src/components/modals/ChooseAccountModal/index.tsx:14
msgid "You have access to multiple accounts. Please choose one to continue."
msgstr "您可以訪問多個賬户。請選擇一個繼續。"

#: src/components/routes/auth/AcceptInvitation/index.tsx:57
msgid "You have already accepted this invitation. Please login to continue."
msgstr "您已接受此邀請。請登錄以繼續。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:29
#~ msgid "You have connected your Stripe account"
#~ msgstr "您已連接 Stripe 賬户"

#: src/components/common/QuestionsTable/index.tsx:338
msgid "You have no attendee questions."
msgstr "沒有與會者提問。"

#: src/components/common/QuestionsTable/index.tsx:323
msgid "You have no order questions."
msgstr "您沒有訂單問題。"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:41
msgid "You have no pending email change."
msgstr "您沒有待處理的電子郵件更改。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:39
#~ msgid "You have not completed your Stripe Connect setup"
#~ msgstr "您尚未完成 Stripe Connect 設置"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:38
#~ msgid "You have not connected your Stripe account"
#~ msgstr "您尚未連接 Stripe 賬户"

#: src/components/layouts/Checkout/index.tsx:156
msgid "You have run out of time to complete your order."
msgstr "您已超時，未能完成訂單。"

#: src/components/forms/ProductForm/index.tsx:374
#~ msgid "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"
#~ msgstr "您已為免費產品添加了税費。您想刪除或隱藏它們嗎？"

#: src/components/forms/ProductForm/index.tsx:374
msgid "You have taxes and fees added to a Free Product. Would you like to remove them?"
msgstr "您已向免費產品添加了税費。您想要刪除它們嗎？"

#: src/components/forms/TicketForm/index.tsx:319
#~ msgid "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"
#~ msgstr "您在免費機票上添加了税費。您想刪除或隱藏它們嗎？"

#: src/components/common/MessageList/index.tsx:74
msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."
msgstr "您尚未發送任何消息。您可以向所有參會者發送消息，或向特定產品持有者發送消息。"

#: src/components/common/MessageList/index.tsx:64
#~ msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."
#~ msgstr "您尚未發送任何信息。您可以向所有與會者或特定持票人發送信息。"

#: src/components/modals/SendMessageModal/index.tsx:108
msgid "You must acknowledge that this email is not promotional"
msgstr "您必須確認此電子郵件並非促銷郵件"

#: src/components/routes/auth/AcceptInvitation/index.tsx:33
msgid "You must agree to the terms and conditions"
msgstr "您必須同意條款和條件"

#: src/components/routes/event/GettingStarted/index.tsx:193
msgid "You must confirm your email address before your event can go live."
msgstr "您必須在活動上線前確認您的電子郵件地址。"

#: src/components/modals/CreateAttendeeModal/index.tsx:98
msgid "You must create a ticket before you can manually add an attendee."
msgstr "必須先創建機票，然後才能手動添加與會者。"

#: src/components/forms/ProductForm/index.tsx:60
msgid "You must have at least one price tier"
msgstr "您必須至少有一個價格等級"

#: src/components/modals/SendMessageModal/index.tsx:136
#~ msgid "You need to verify your account before you can send messages."
#~ msgstr "發送信息前，您需要驗證您的賬户。"

#: src/components/modals/SendMessageModal/index.tsx:151
msgid "You need to verify your account email before you can send messages."
msgstr "您需要驗證您的帳户電子郵件才能發送消息。"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:143
msgid "You will have to mark an order as paid manually. This can be done on the manage order page."
msgstr "您必須手動將訂單標記為已支付。這可以在訂單管理頁面上完成。"

#: src/components/modals/CreateCheckInListModal/index.tsx:55
msgid "You'll need a ticket before you can create a check-in list."
msgstr "在創建簽到列表之前，您需要先獲得票。"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
msgid "You'll need at a product before you can create a capacity assignment."
msgstr "在您創建容量分配之前，您需要一個產品。"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
#~ msgid "You'll need at a ticket before you can create a capacity assignment."
#~ msgstr "在創建容量分配之前，您需要一張票。"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:43
msgid "You'll need at least one product to get started. Free, paid or let the user decide what to pay."
msgstr "您需要至少一個產品才能開始。免費、付費或讓用户決定支付金額。"

#: src/components/common/TicketsTable/index.tsx:69
#~ msgid "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."
#~ msgstr "您至少需要一張票才能開始。免費、付費或讓用户決定支付方式。"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:93
msgid "You're going to {0}! 🎉"
msgstr "你要去 {0}! 🎉"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:64
msgid "Your account name is used on event pages and in emails."
msgstr "您的賬户名稱會在活動頁面和電子郵件中使用。"

#: src/components/routes/event/attendees.tsx:44
msgid "Your attendees have been exported successfully."
msgstr "您的與會者已成功導出。"

#: src/components/common/AttendeeTable/index.tsx:61
msgid "Your attendees will appear here once they have registered for your event. You can also manually add attendees."
msgstr "與會者註冊參加活動後，就會出現在這裏。您也可以手動添加與會者。"

#: src/components/common/WidgetEditor/index.tsx:329
msgid "Your awesome website 🎉"
msgstr "您的網站真棒 🎉"

#: src/components/routes/product-widget/CollectInformation/index.tsx:276
msgid "Your Details"
msgstr "您的詳細信息"

#: src/components/routes/auth/ForgotPassword/index.tsx:52
msgid "Your Email"
msgstr "您的電子郵件"

#: src/components/routes/profile/ManageProfile/index.tsx:118
msgid "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"
msgstr "您要求將電子郵件更改為<0>{0}</0>的申請正在處理中。請檢查您的電子郵件以確認"

#: src/components/routes/event/EventDashboard/index.tsx:150
msgid "Your event must be live before you can sell tickets to attendees."
msgstr "在向與會者銷售門票之前，您的活動必須處於上線狀態。"

#: src/components/routes/event/EventDashboard/index.tsx:164
#~ msgid "Your event must be live before you can sell tickets."
#~ msgstr "您的活動必須上線後才能銷售門票。"

#: src/components/common/OrdersTable/index.tsx:88
msgid "Your message has been sent"
msgstr "您的信息已發送"

#: src/components/layouts/Checkout/index.tsx:84
msgid "Your Order"
msgstr "您的訂單"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:94
msgid "Your order has been cancelled"
msgstr "您的訂單已被取消"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:96
msgid "Your order is awaiting payment 🏦"
msgstr "您的訂單正在等待支付 🏦"

#: src/components/routes/event/orders.tsx:95
msgid "Your orders have been exported successfully."
msgstr "您的訂單已成功導出。"

#: src/components/common/OrdersTable/index.tsx:66
msgid "Your orders will appear here once they start rolling in."
msgstr "您的訂單一旦開始滾動，就會出現在這裏。"

#: src/components/routes/auth/Login/index.tsx:74
#: src/components/routes/auth/Register/index.tsx:107
msgid "Your password"
msgstr "您的密碼"

#: src/components/forms/StripeCheckoutForm/index.tsx:63
msgid "Your payment is processing."
msgstr "您的付款正在處理中。"

#: src/components/forms/StripeCheckoutForm/index.tsx:66
msgid "Your payment was not successful, please try again."
msgstr "您的付款未成功，請重試。"

#: src/components/forms/StripeCheckoutForm/index.tsx:126
msgid "Your payment was unsuccessful. Please try again."
msgstr "您的付款未成功。請重試。"

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#~ msgid "Your product for"
#~ msgstr "您的產品為"

#: src/components/modals/RefundOrderModal/index.tsx:58
msgid "Your refund is processing."
msgstr "您的退款正在處理中。"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:153
msgid "Your Stripe account is connected and ready to process payments."
msgstr "您的 Stripe 賬户已連接並準備好處理支付。"

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#: src/components/routes/product-widget/PrintProduct/index.tsx:42
msgid "Your ticket for"
msgstr "您的入場券"

#: src/components/routes/product-widget/CollectInformation/index.tsx:348
msgid "ZIP / Postal Code"
msgstr "郵政編碼"

#: src/components/common/CheckoutQuestion/index.tsx:170
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:146
msgid "Zip or Postal Code"
msgstr "郵政編碼"

#: src/components/routes/product-widget/CollectInformation/index.tsx:349
msgid "ZIP or Postal Code"
msgstr "郵政編碼"
