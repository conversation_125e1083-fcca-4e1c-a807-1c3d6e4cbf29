msgid ""
msgstr ""
"POT-Creation-Date: 2023-11-17 13:53-0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: en\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/components/layouts/Event/index.tsx:189
msgid "- Click to Publish"
msgstr "- Click to Publish"

#: src/components/layouts/Event/index.tsx:191
msgid "- Click to Unpublish"
msgstr "- Click to Unpublish"

#: src/components/layouts/Event/index.tsx:143
#~ msgid "..."
#~ msgstr "..."

#: src/components/common/NoResultsSplash/index.tsx:14
msgid "'There\\'s nothing to show yet'"
msgstr "'There\\'s nothing to show yet'"

#: src/components/common/QuestionsTable/index.tsx:267
#~ msgid "{0, plural, one {# hidden question} other {# hidden questions}}"
#~ msgstr "{0, plural, one {# hidden question} other {# hidden questions}}"

#: src/components/common/ReportTable/index.tsx:303
#: src/locales.ts:37
msgid "{0}"
msgstr "{0}"

#: src/components/common/QuestionsTable/index.tsx:267
#~ msgid "{0} {1}"
#~ msgstr "{0} {1}"

#: src/components/layouts/CheckIn/index.tsx:82
msgid "{0} <0>checked in</0> successfully"
msgstr "{0} <0>checked in</0> successfully"

#: src/components/layouts/CheckIn/index.tsx:106
msgid "{0} <0>checked out</0> successfully"
msgstr "{0} <0>checked out</0> successfully"

#: src/components/routes/event/Webhooks/index.tsx:24
msgid "{0} Active Webhooks"
msgstr "{0} Active Webhooks"

#: src/components/routes/product-widget/SelectProducts/index.tsx:412
msgid "{0} available"
msgstr "{0} available"

#: src/components/common/AttendeeCheckInTable/index.tsx:155
#~ msgid "{0} checked in"
#~ msgstr "{0} checked in"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
msgid "{0} created successfully"
msgstr "{0} created successfully"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
msgid "{0} updated successfully"
msgstr "{0} updated successfully"

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:57
msgid "{0}'s Events"
msgstr "{0}'s Events"

#: src/components/common/QuestionsTable/index.tsx:267
#~ msgid "{0}{1}"
#~ msgstr "{0}{1}"

#: src/components/layouts/CheckIn/index.tsx:449
msgid "{0}/{1} checked in"
msgstr "{0}/{1} checked in"

#: src/components/common/Countdown/index.tsx:68
msgid "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{days} days, {hours} hours, {minutes} minutes, and {seconds} seconds"

#: src/components/common/WebhookTable/index.tsx:79
msgid "{eventCount} events"
msgstr "{eventCount} events"

#: src/components/common/Countdown/index.tsx:70
msgid "{hours} hours, {minutes} minutes, and {seconds} seconds"
msgstr "{hours} hours, {minutes} minutes, and {seconds} seconds"

#: src/components/modals/RefundOrderModal/index.tsx:121
#~ msgid "{message}"
#~ msgstr "{message}"

#: src/components/common/Countdown/index.tsx:72
msgid "{minutes} minutes and {seconds} seconds"
msgstr "{minutes} minutes and {seconds} seconds"

#: src/components/routes/welcome/index.tsx:59
msgid "{organizerName}'s first event"
msgstr "{organizerName}'s first event"

#: src/components/common/ReportTable/index.tsx:256
#~ msgid "{title}"
#~ msgstr "{title}"

#: src/components/common/CapacityAssignmentList/index.tsx:47
msgid "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"
msgstr "<0>Capacity assignments let you manage capacity across tickets or an entire event. Ideal for multi-day events, workshops, and more, where controlling attendance is crucial.</0><1>For instance, you can associate a capacity assignment with <2>Day One</2> and <3>All Days</3> ticket. Once the capacity is reached, both tickets will automatically stop being available for sale.</1>"

#: src/components/common/CheckInListList/index.tsx:49
msgid "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"
msgstr "<0>Check-in lists help manage attendee entry for your event. You can associate multiple tickets with a check-in list and ensure only those with valid tickets can enter.</0>"

#: src/components/common/WidgetEditor/index.tsx:324
msgid "<0>https://</0>your-website.com"
msgstr "<0>https://</0>your-website.com"

#: src/components/forms/ProductForm/index.tsx:287
msgid "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"
msgstr "<0>Please enter the price excluding taxes and fees.</0><1>Taxes and fees can be added below.</1>"

#: src/components/forms/ProductForm/index.tsx:304
msgid "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"
msgstr "<0>The number of products available for this product</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this product.</1>"

#: src/components/forms/TicketForm/index.tsx:249
#~ msgid "<0>The number of tickets available for this ticket</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this ticket.</1>"
#~ msgstr "<0>The number of tickets available for this ticket</0><1>This value can be overridden if there are <2>Capacity Limits</2> associated with this ticket.</1>"

#: src/components/common/WebhookTable/index.tsx:205
msgid "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"
msgstr "<0>Webhooks instantly notify external services when events happen, like adding a new attendee to your CRM or mailing list upon registration, ensuring seamless automation.</0><1>Use third-party services like <2>Zapier</2>, <3>IFTTT</3> or <4>Make</4> to create custom workflows and automate tasks.</1>"

#: src/components/routes/event/GettingStarted/index.tsx:134
msgid "⚡️ Set up your event"
msgstr "⚡️ Set up your event"

#: src/components/routes/event/GettingStarted/index.tsx:190
msgid "✉️ Confirm your email address"
msgstr "✉️ Confirm your email address"

#: src/components/routes/event/GettingStarted/index.tsx:56
#~ msgid "🎉 Congratulation on creating an event!"
#~ msgstr "🎉 Congratulation on creating an event!"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "🎉 Congratulations on creating an event!"
#~ msgstr "🎉 Congratulations on creating an event!"

#: src/components/routes/event/GettingStarted/index.tsx:67
#~ msgid "🎟️ Add products"
#~ msgstr "🎟️ Add products"

#: src/components/routes/event/GettingStarted/index.tsx:119
msgid "🎟️ Add tickets"
msgstr "🎟️ Add tickets"

#: src/components/routes/event/GettingStarted/index.tsx:162
msgid "🎨 Customize your event page"
msgstr "🎨 Customize your event page"

#: src/components/routes/event/GettingStarted/index.tsx:147
msgid "💳 Connect with Stripe"
msgstr "💳 Connect with Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:52
#~ msgid "📢 Promote your event"
#~ msgstr "📢 Promote your event"

#: src/components/routes/event/GettingStarted/index.tsx:176
msgid "🚀 Set your event live"
msgstr "🚀 Set your event live"

#: src/components/common/Countdown/index.tsx:34
msgid "0 minutes and 0 seconds"
msgstr "0 minutes and 0 seconds"

#: src/components/forms/TaxAndFeeForm/index.tsx:73
#~ msgid "0.50 for $0.50"
#~ msgstr "0.50 for $0.50"

#: src/components/routes/event/Webhooks/index.tsx:23
msgid "1 Active Webhook"
msgstr "1 Active Webhook"

#: src/components/forms/TaxAndFeeForm/index.tsx:73
#~ msgid "1.75 for 1.75%"
#~ msgstr "1.75 for 1.75%"

#: src/components/modals/RefundOrderModal/index.tsx:105
msgid "10.00"
msgstr "10.00"

#: src/components/routes/event/settings.tsx:118
#~ msgid "10001"
#~ msgstr "10001"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:123
msgid "123 Main Street"
msgstr "123 Main Street"

#: src/components/common/WidgetEditor/index.tsx:226
msgid "20"
msgstr "20"

#: src/components/routes/welcome/index.tsx:91
msgid "2024-01-01 10:00"
msgstr "2024-01-01 10:00"

#: src/components/routes/welcome/index.tsx:97
msgid "2024-01-01 18:00"
msgstr "2024-01-01 18:00"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:147
msgid "94103"
msgstr "94103"

#: src/components/forms/QuestionForm/index.tsx:138
msgid "A date input. Perfect for asking for a date of birth etc."
msgstr "A date input. Perfect for asking for a date of birth etc."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
msgid "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."
msgstr "A default {type} is automaticaly applied to all new products. You can override this on a per product basis."

#: src/components/forms/TaxAndFeeForm/index.tsx:88
#~ msgid "A default {type} is automaticaly applied to all new tickets. You can override this on a per ticket basis."
#~ msgstr "A default {type} is automaticaly applied to all new tickets. You can override this on a per ticket basis."

#: src/components/forms/QuestionForm/index.tsx:126
msgid "A Dropdown input allows only one selection"
msgstr "A Dropdown input allows only one selection"

#: src/components/forms/TaxAndFeeForm/index.tsx:20
msgid "A fee, like a booking fee or a service fee"
msgstr "A fee, like a booking fee or a service fee"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
msgid "A fixed amount per product. E.g, $0.50 per product"
msgstr "A fixed amount per product. E.g, $0.50 per product"

#: src/components/forms/TaxAndFeeForm/index.tsx:35
#~ msgid "A fixed amount per ticket. E.g, $0.50 per ticket"
#~ msgstr "A fixed amount per ticket. E.g, $0.50 per ticket"

#: src/components/forms/QuestionForm/index.tsx:108
msgid "A multi line text input"
msgstr "A multi line text input"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
msgid "A percentage of the product price. E.g., 3.5% of the product price"
msgstr "A percentage of the product price. E.g., 3.5% of the product price"

#: src/components/forms/TaxAndFeeForm/index.tsx:29
#~ msgid "A percentage of the ticket price. E.g., 3.5% of the ticket price"
#~ msgstr "A percentage of the ticket price. E.g., 3.5% of the ticket price"

#: src/components/forms/PromoCodeForm/index.tsx:37
msgid "A promo code with no discount can be used to reveal hidden products."
msgstr "A promo code with no discount can be used to reveal hidden products."

#: src/components/forms/PromoCodeForm/index.tsx:36
#~ msgid "A promo code with no discount can be used to reveal hidden tickets."
#~ msgstr "A promo code with no discount can be used to reveal hidden tickets."

#: src/components/forms/QuestionForm/index.tsx:114
#~ msgid "A Radio option allows has multiple options but only one can be selected."
#~ msgstr "A Radio option allows has multiple options but only one can be selected."

#: src/components/forms/QuestionForm/index.tsx:114
#~ msgid "A Radio Option allows only one selection"
#~ msgstr "A Radio Option allows only one selection"

#: src/components/forms/QuestionForm/index.tsx:120
msgid "A Radio option has multiple options but only one can be selected."
msgstr "A Radio option has multiple options but only one can be selected."

#: src/components/routes/welcome/index.tsx:85
#~ msgid "A short description of Awesome Events Ltd."
#~ msgstr "A short description of Awesome Events Ltd."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:69
msgid "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"
msgstr "A short description of the event that will be displayed in search engine results and when sharing on social media. By default, the event description will be used"

#: src/components/forms/QuestionForm/index.tsx:102
msgid "A single line text input"
msgstr "A single line text input"

#: src/components/forms/QuestionForm/index.tsx:92
#~ msgid "A single question per attendee. E.g, What is your preferred meal?"
#~ msgstr "A single question per attendee. E.g, What is your preferred meal?"

#: src/components/forms/QuestionForm/index.tsx:86
#~ msgid "A single question per order. E.g, What is your company name?"
#~ msgstr "A single question per order. E.g, What is your company name?"

#: src/components/forms/QuestionForm/index.tsx:87
msgid "A single question per order. E.g, What is your shipping address?"
msgstr "A single question per order. E.g, What is your shipping address?"

#: src/components/forms/QuestionForm/index.tsx:93
msgid "A single question per product. E.g, What is your t-shirt size?"
msgstr "A single question per product. E.g, What is your t-shirt size?"

#: src/components/forms/TaxAndFeeForm/index.tsx:14
msgid "A standard tax, like VAT or GST"
msgstr "A standard tax, like VAT or GST"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:76
msgid "About"
msgstr "About"

#: src/components/common/GlobalMenu/index.tsx:25
#~ msgid "About hi.events"
#~ msgstr "About hi.events"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:211
msgid "About Stripe Connect"
msgstr "About Stripe Connect"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:79
#~ msgid "About the event"
#~ msgstr "About the event"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:91
msgid "Accept bank transfers, checks, or other offline payment methods"
msgstr "Accept bank transfers, checks, or other offline payment methods"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:86
msgid "Accept credit card payments with Stripe"
msgstr "Accept credit card payments with Stripe"

#: src/components/routes/auth/AcceptInvitation/index.tsx:126
msgid "Accept Invitation"
msgstr "Accept Invitation"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:125
msgid "Access Denied"
msgstr "Access Denied"

#: src/components/forms/TicketForm/index.tsx:168
#~ msgid "Access to the VIP area..."
#~ msgstr "Access to the VIP area..."

#: src/components/routes/account/ManageAccount/index.tsx:24
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:53
msgid "Account"
msgstr "Account"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:70
#~ msgid "Account Email"
#~ msgstr "Account Email"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:62
msgid "Account Name"
msgstr "Account Name"

#: src/components/common/GlobalMenu/index.tsx:32
#: src/components/routes/account/ManageAccount/index.tsx:19
msgid "Account Settings"
msgstr "Account Settings"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:42
msgid "Account updated successfully"
msgstr "Account updated successfully"

#: src/components/common/AttendeeTable/index.tsx:158
#: src/components/common/ProductsTable/SortableProduct/index.tsx:267
#: src/components/common/QuestionsTable/index.tsx:108
msgid "Actions"
msgstr "Actions"

#: src/components/common/AttendeeTable/index.tsx:182
msgid "Activate"
msgstr "Activate"

#: src/components/forms/CheckInListForm/index.tsx:47
msgid "Activation date"
msgstr "Activation date"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:19
#: src/components/modals/EditUserModal/index.tsx:110
msgid "Active"
msgstr "Active"

#: src/components/routes/event/attendees.tsx:59
#~ msgid "Add"
#~ msgstr "Add"

#: src/components/routes/event/GettingStarted/index.tsx:44
#~ msgid "Add a cover image, description, and more to make your event stand out."
#~ msgstr "Add a cover image, description, and more to make your event stand out."

#: src/components/forms/CheckInListForm/index.tsx:39
msgid "Add a description for this check-in list"
msgstr "Add a description for this check-in list"

#: src/components/modals/ManageAttendeeModal/index.tsx:131
msgid "Add any notes about the attendee. These will not be visible to the attendee."
msgstr "Add any notes about the attendee. These will not be visible to the attendee."

#: src/components/modals/ManageAttendeeModal/index.tsx:133
msgid "Add any notes about the attendee..."
msgstr "Add any notes about the attendee..."

#: src/components/modals/ManageOrderModal/index.tsx:165
msgid "Add any notes about the order. These will not be visible to the customer."
msgstr "Add any notes about the order. These will not be visible to the customer."

#: src/components/modals/ManageOrderModal/index.tsx:169
msgid "Add any notes about the order..."
msgstr "Add any notes about the order..."

#: src/components/forms/QuestionForm/index.tsx:202
msgid "Add description"
msgstr "Add description"

#: src/components/routes/event/GettingStarted/index.tsx:85
#~ msgid "Add event details and and manage event settings."
#~ msgstr "Add event details and and manage event settings."

#: src/components/routes/event/GettingStarted/index.tsx:137
msgid "Add event details and manage event settings."
msgstr "Add event details and manage event settings."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:153
msgid "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"
msgstr "Add instructions for offline payments (e.g., bank transfer details, where to send checks, payment deadlines)"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add More products"
#~ msgstr "Add More products"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add More tickets"
msgstr "Add More tickets"

#: src/components/common/HeadingCard/index.tsx:27
msgid "Add New"
msgstr "Add New"

#: src/components/forms/QuestionForm/index.tsx:68
msgid "Add Option"
msgstr "Add Option"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:68
msgid "Add Product"
msgstr "Add Product"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:51
msgid "Add Product to Category"
msgstr "Add Product to Category"

#: src/components/routes/event/GettingStarted/index.tsx:75
#~ msgid "Add products"
#~ msgstr "Add products"

#: src/components/common/QuestionsTable/index.tsx:281
msgid "Add question"
msgstr "Add question"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:18
msgid "Add Tax or Fee"
msgstr "Add Tax or Fee"

#: src/components/routes/event/GettingStarted/index.tsx:127
msgid "Add tickets"
msgstr "Add tickets"

#: src/components/forms/ProductForm/index.tsx:337
msgid "Add tier"
msgstr "Add tier"

#: src/components/common/AddEventToCalendarButton/index.tsx:112
#: src/components/common/AddEventToCalendarButton/index.tsx:120
msgid "Add to Calendar"
msgstr "Add to Calendar"

#: src/components/common/WebhookTable/index.tsx:223
#: src/components/routes/event/Webhooks/index.tsx:35
msgid "Add Webhook"
msgstr "Add Webhook"

#: src/components/forms/EventForm/index.tsx:82
#~ msgid "Additional Details"
#~ msgstr "Additional Details"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:219
msgid "Additional Information"
msgstr "Additional Information"

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/forms/ProductForm/index.tsx:348
msgid "Additional Options"
msgstr "Additional Options"

#: src/components/common/OrderDetails/index.tsx:96
#: src/components/forms/QuestionForm/index.tsx:130
msgid "Address"
msgstr "Address"

#: src/components/common/CheckoutQuestion/index.tsx:156
msgid "Address line 1"
msgstr "Address line 1"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:122
#: src/components/routes/product-widget/CollectInformation/index.tsx:319
#: src/components/routes/product-widget/CollectInformation/index.tsx:320
msgid "Address Line 1"
msgstr "Address Line 1"

#: src/components/common/CheckoutQuestion/index.tsx:159
msgid "Address line 2"
msgstr "Address line 2"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:127
#: src/components/routes/product-widget/CollectInformation/index.tsx:324
#: src/components/routes/product-widget/CollectInformation/index.tsx:325
msgid "Address Line 2"
msgstr "Address Line 2"

#: src/components/modals/EditUserModal/index.tsx:49
#: src/components/modals/InviteUserModal/index.tsx:41
msgid "Admin"
msgstr "Admin"

#: src/components/modals/EditUserModal/index.tsx:51
#: src/components/modals/InviteUserModal/index.tsx:43
msgid "Admin users have full access to events and account settings."
msgstr "Admin users have full access to events and account settings."

#: src/components/layouts/Event/index.tsx:53
#~ msgid "Affiliates"
#~ msgstr "Affiliates"

#: src/components/common/MessageList/index.tsx:21
msgid "All attendees"
msgstr "All attendees"

#: src/components/modals/SendMessageModal/index.tsx:187
msgid "All attendees of this event"
msgstr "All attendees of this event"

#: src/components/routes/events/Dashboard/index.tsx:52
#~ msgid "All Events"
#~ msgstr "All Events"

#: src/components/common/PromoCodeTable/index.tsx:131
msgid "All Products"
msgstr "All Products"

#: src/components/common/PromoCodeTable/index.tsx:130
#: src/components/forms/PromoCodeForm/index.tsx:67
#~ msgid "All Tickets"
#~ msgstr "All Tickets"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:157
msgid "Allow attendees associated with unpaid orders to check in"
msgstr "Allow attendees associated with unpaid orders to check in"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:82
msgid "Allow search engine indexing"
msgstr "Allow search engine indexing"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:81
msgid "Allow search engines to index this event"
msgstr "Allow search engines to index this event"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:78
msgid "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."
msgstr "Almost there! We're just waiting for your payment to be processed. This should only take a few seconds.."

#: src/components/routes/auth/register.tsx:68
#~ msgid "Already have an account?"
#~ msgstr "Already have an account?"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:77
msgid "Amazing, Event, Keywords..."
msgstr "Amazing, Event, Keywords..."

#: src/components/common/OrdersTable/index.tsx:207
#: src/components/forms/TaxAndFeeForm/index.tsx:71
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:36
msgid "Amount"
msgstr "Amount"

#: src/components/forms/PromoCodeForm/index.tsx:53
#~ msgid "amount in {0}"
#~ msgstr "amount in {0}"

#: src/components/modals/CreateAttendeeModal/index.tsx:165
msgid "Amount paid ({0})"
msgstr "Amount paid ({0})"

#: src/components/modals/CreateAttendeeModal/index.tsx:81
#~ msgid "Amount paid ${0}"
#~ msgstr "Amount paid ${0}"

#: src/mutations/useExportAnswers.ts:45
msgid "An error occurred while checking export status."
msgstr "An error occurred while checking export status."

#: src/components/common/ErrorDisplay/index.tsx:18
msgid "An error occurred while loading the page"
msgstr "An error occurred while loading the page"

#: src/components/common/QuestionsTable/index.tsx:148
msgid "An error occurred while sorting the questions. Please try again or refresh the page"
msgstr "An error occurred while sorting the questions. Please try again or refresh the page"

#: src/components/common/TicketsTable/index.tsx:47
#~ msgid "An error occurred while sorting the tickets. Please try again or refresh the page"
#~ msgstr "An error occurred while sorting the tickets. Please try again or refresh the page"

#: src/components/routes/welcome/index.tsx:111
#~ msgid "An event is the actual event you are hosting"
#~ msgstr "An event is the actual event you are hosting"

#: src/components/routes/welcome/index.tsx:75
#~ msgid "An event is the actual event you are hosting. You can add more details later."
#~ msgstr "An event is the actual event you are hosting. You can add more details later."

#: src/components/routes/welcome/index.tsx:75
msgid "An event is the gathering or occasion you’re organizing. You can add more details later."
msgstr "An event is the gathering or occasion you’re organizing. You can add more details later."

#: src/components/routes/welcome/index.tsx:23
msgid "An organizer is the company or person who is hosting the event"
msgstr "An organizer is the company or person who is hosting the event"

#: src/components/forms/StripeCheckoutForm/index.tsx:40
msgid "An unexpected error occurred."
msgstr "An unexpected error occurred."

#: src/hooks/useFormErrorResponseHandler.tsx:48
msgid "An unexpected error occurred. Please try again."
msgstr "An unexpected error occurred. Please try again."

#: src/components/common/QuestionAndAnswerList/index.tsx:97
msgid "Answer updated successfully."
msgstr "Answer updated successfully."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
msgid "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
msgstr "Any queries from product holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
#~ msgid "Any queries from ticket holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"
#~ msgstr "Any queries from ticket holders will be sent to this email address. This will also be used as the \"reply-to\" address for all emails sent from this event"

#: src/components/common/WidgetEditor/index.tsx:213
msgid "Appearance"
msgstr "Appearance"

#: src/components/routes/product-widget/SelectProducts/index.tsx:500
msgid "applied"
msgstr "applied"

#: src/components/common/CapacityAssignmentList/index.tsx:97
msgid "Applies to {0} products"
msgstr "Applies to {0} products"

#: src/components/common/CapacityAssignmentList/index.tsx:97
#~ msgid "Applies to {0} tickets"
#~ msgstr "Applies to {0} tickets"

#: src/components/common/CapacityAssignmentList/index.tsx:98
msgid "Applies to 1 product"
msgstr "Applies to 1 product"

#: src/components/common/CapacityAssignmentList/index.tsx:98
#~ msgid "Applies to 1 ticket"
#~ msgstr "Applies to 1 ticket"

#: src/components/common/FilterModal/index.tsx:253
msgid "Apply"
msgstr "Apply"

#: src/components/routes/product-widget/SelectProducts/index.tsx:528
msgid "Apply Promo Code"
msgstr "Apply Promo Code"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
msgid "Apply this {type} to all new products"
msgstr "Apply this {type} to all new products"

#: src/components/forms/TaxAndFeeForm/index.tsx:86
#~ msgid "Apply this {type} to all new tickets"
#~ msgstr "Apply this {type} to all new tickets"

#: src/components/common/EventCard/index.tsx:167
msgid "Archive event"
msgstr "Archive event"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:32
msgid "Archived"
msgstr "Archived"

#: src/components/routes/events/Dashboard/index.tsx:59
msgid "Archived Events"
msgstr "Archived Events"

#: src/components/common/AttendeeTable/index.tsx:76
msgid "Are you sure you want to activate this attendee?"
msgstr "Are you sure you want to activate this attendee?"

#: src/components/common/EventCard/index.tsx:58
msgid "Are you sure you want to archive this event?"
msgstr "Are you sure you want to archive this event?"

#: src/components/common/AttendeeTable/index.tsx:78
#~ msgid "Are you sure you want to cancel this attendee? This will void their product"
#~ msgstr "Are you sure you want to cancel this attendee? This will void their product"

#: src/components/common/AttendeeTable/index.tsx:77
msgid "Are you sure you want to cancel this attendee? This will void their ticket"
msgstr "Are you sure you want to cancel this attendee? This will void their ticket"

#: src/components/common/PromoCodeTable/index.tsx:37
msgid "Are you sure you want to delete this promo code?"
msgstr "Are you sure you want to delete this promo code?"

#: src/components/common/QuestionsTable/index.tsx:164
msgid "Are you sure you want to delete this question?"
msgstr "Are you sure you want to delete this question?"

#: src/components/common/WebhookTable/index.tsx:122
msgid "Are you sure you want to delete this webhook?"
msgstr "Are you sure you want to delete this webhook?"

#: src/components/layouts/Event/index.tsx:68
#: src/components/routes/event/EventDashboard/index.tsx:64
msgid "Are you sure you want to make this event draft? This will make the event invisible to the public"
msgstr "Are you sure you want to make this event draft? This will make the event invisible to the public"

#: src/components/layouts/Event/index.tsx:69
#: src/components/routes/event/EventDashboard/index.tsx:65
msgid "Are you sure you want to make this event public? This will make the event visible to the public"
msgstr "Are you sure you want to make this event public? This will make the event visible to the public"

#: src/components/common/EventCard/index.tsx:59
msgid "Are you sure you want to restore this event? It will be restored as a draft event."
msgstr "Are you sure you want to restore this event? It will be restored as a draft event."

#: src/components/common/CapacityAssignmentList/index.tsx:163
msgid "Are you sure you would like to delete this Capacity Assignment?"
msgstr "Are you sure you would like to delete this Capacity Assignment?"

#: src/components/common/CheckInListList/index.tsx:183
msgid "Are you sure you would like to delete this Check-In List?"
msgstr "Are you sure you would like to delete this Check-In List?"

#: src/components/forms/QuestionForm/index.tsx:90
#~ msgid "Ask once per attendee"
#~ msgstr "Ask once per attendee"

#: src/components/forms/QuestionForm/index.tsx:85
msgid "Ask once per order"
msgstr "Ask once per order"

#: src/components/forms/QuestionForm/index.tsx:91
msgid "Ask once per product"
msgstr "Ask once per product"

#: src/components/modals/CreateWebhookModal/index.tsx:33
msgid "At least one event type must be selected"
msgstr "At least one event type must be selected"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Attendee"
msgstr "Attendee"

#: src/components/forms/WebhookForm/index.tsx:94
msgid "Attendee Cancelled"
msgstr "Attendee Cancelled"

#: src/components/forms/WebhookForm/index.tsx:82
msgid "Attendee Created"
msgstr "Attendee Created"

#: src/components/modals/ManageAttendeeModal/index.tsx:147
msgid "Attendee Details"
msgstr "Attendee Details"

#: src/components/layouts/AuthLayout/index.tsx:73
msgid "Attendee Management"
msgstr "Attendee Management"

#: src/components/layouts/CheckIn/index.tsx:150
msgid "Attendee not found"
msgstr "Attendee not found"

#: src/components/modals/ManageAttendeeModal/index.tsx:153
msgid "Attendee Notes"
msgstr "Attendee Notes"

#: src/components/common/QuestionsTable/index.tsx:369
msgid "Attendee questions"
msgstr "Attendee questions"

#: src/components/common/QuestionsTable/index.tsx:346
#~ msgid "Attendee questions are asked once per attendee. By default, people are asked for their first name, last name, and email address."
#~ msgstr "Attendee questions are asked once per attendee. By default, people are asked for their first name, last name, and email address."

#: src/components/common/QuestionsTable/index.tsx:244
#~ msgid "Attendee questions are asked once per attendee. By default, we ask for the attendee's first name, last name, and email address."
#~ msgstr "Attendee questions are asked once per attendee. By default, we ask for the attendee's first name, last name, and email address."

#: src/components/modals/ManageAttendeeModal/index.tsx:172
msgid "Attendee Ticket"
msgstr "Attendee Ticket"

#: src/components/forms/WebhookForm/index.tsx:88
msgid "Attendee Updated"
msgstr "Attendee Updated"

#: src/components/common/OrdersTable/index.tsx:206
#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
#: src/components/common/StatBoxes/index.tsx:27
#: src/components/layouts/Event/index.tsx:99
#: src/components/modals/ManageOrderModal/index.tsx:126
#: src/components/routes/event/attendees.tsx:59
msgid "Attendees"
msgstr "Attendees"

#: src/components/routes/event/attendees.tsx:43
msgid "Attendees Exported"
msgstr "Attendees Exported"

#: src/components/routes/event/EventDashboard/index.tsx:239
msgid "Attendees Registered"
msgstr "Attendees Registered"

#: src/components/modals/SendMessageModal/index.tsx:146
#~ msgid "Attendees with a specific product"
#~ msgstr "Attendees with a specific product"

#: src/components/modals/SendMessageModal/index.tsx:183
msgid "Attendees with a specific ticket"
msgstr "Attendees with a specific ticket"

#: src/components/common/WidgetEditor/index.tsx:233
msgid "Auto Resize"
msgstr "Auto Resize"

#: src/components/layouts/AuthLayout/index.tsx:88
#~ msgid "Auto Workflow"
#~ msgstr "Auto Workflow"

#: src/components/layouts/AuthLayout/index.tsx:79
msgid "Automated entry management with multiple check-in lists and real-time validation"
msgstr "Automated entry management with multiple check-in lists and real-time validation"

#: src/components/common/WidgetEditor/index.tsx:236
msgid "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."
msgstr "Automatically resize the widget height based on the content. When disabled, the widget will fill the height of the container."

#: src/components/common/TicketsTable/index.tsx:103
#~ msgid "Availability"
#~ msgstr "Availability"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
#~ msgid "Avg Discount/Order"
#~ msgstr "Avg Discount/Order"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
#~ msgid "Avg Order Value"
#~ msgstr "Avg Order Value"

#: src/components/common/OrderStatusBadge/index.tsx:15
#: src/components/modals/SendMessageModal/index.tsx:231
msgid "Awaiting offline payment"
msgstr "Awaiting offline payment"

#: src/components/routes/event/orders.tsx:25
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:43
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:66
msgid "Awaiting Offline Payment"
msgstr "Awaiting Offline Payment"

#: src/components/layouts/CheckIn/index.tsx:263
msgid "Awaiting payment"
msgstr "Awaiting payment"

#: src/components/common/AttendeeTicket/index.tsx:67
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:40
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:65
msgid "Awaiting Payment"
msgstr "Awaiting Payment"

#: src/components/routes/welcome/index.tsx:84
msgid "Awesome Event"
msgstr "Awesome Event"

#: src/components/forms/OrganizerForm/index.tsx:59
#~ msgid "Awesome Events Ltd."
#~ msgstr "Awesome Events Ltd."

#: src/components/forms/OrganizerForm/index.tsx:28
msgid "Awesome Organizer Ltd."
msgstr "Awesome Organizer Ltd."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:42
msgid "Back to all events"
msgstr "Back to all events"

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:212
#~ msgid "Back to event homepage"
#~ msgstr "Back to event homepage"

#: src/components/layouts/Checkout/index.tsx:73
#: src/components/routes/product-widget/CollectInformation/index.tsx:249
#: src/components/routes/product-widget/CollectInformation/index.tsx:261
msgid "Back to event page"
msgstr "Back to event page"

#: src/components/routes/auth/ForgotPassword/index.tsx:59
#: src/components/routes/auth/ResetPassword/index.tsx:66
msgid "Back to login"
msgstr "Back to login"

#: src/components/routes/event/HomepageDesigner/index.tsx:83
#~ msgid "Background color"
#~ msgstr "Background color"

#: src/components/common/WidgetEditor/index.tsx:175
msgid "Background Color"
msgstr "Background Color"

#: src/components/routes/event/HomepageDesigner/index.tsx:144
msgid "Background Type"
msgstr "Background Type"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#~ msgid "Basic Details"
#~ msgstr "Basic Details"

#: src/components/modals/EditUserModal/index.tsx:85
#~ msgid "Because you are the account owner, you cannot change your role or status."
#~ msgstr "Because you are the account owner, you cannot change your role or status."

#: src/components/modals/SendMessageModal/index.tsx:278
msgid "Before you send!"
msgstr "Before you send!"

#: src/components/routes/event/GettingStarted/index.tsx:19
#~ msgid "Before you're event can go live, there's a few thing to do."
#~ msgstr "Before you're event can go live, there's a few thing to do."

#: src/components/routes/event/GettingStarted/index.tsx:60
#~ msgid "Before your event can go live, there are a few things you need to do."
#~ msgstr "Before your event can go live, there are a few things you need to do."

#: src/components/routes/event/GettingStarted/index.tsx:92
msgid "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."
msgstr "Before your event can go live, there are a few things you need to do. Complete all the steps below to get started."

#: src/components/routes/auth/Register/index.tsx:66
#~ msgid "Begin selling products in minutes"
#~ msgstr "Begin selling products in minutes"

#: src/components/routes/auth/Register/index.tsx:49
#~ msgid "Begin selling tickets in minutes"
#~ msgstr "Begin selling tickets in minutes"

#: src/components/routes/account/ManageAccount/index.tsx:32
#: src/components/routes/account/ManageAccount/sections/BillingSettings/index.tsx:10
#: src/components/routes/account/ManageAccount/sections/BillingSettings/index.tsx:14
#~ msgid "Billing"
#~ msgstr "Billing"

#: src/components/routes/product-widget/CollectInformation/index.tsx:313
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:142
msgid "Billing Address"
msgstr "Billing Address"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:167
msgid "Billing Settings"
msgstr "Billing Settings"

#: src/components/layouts/AuthLayout/index.tsx:83
#~ msgid "Brand Control"
#~ msgstr "Brand Control"

#: src/components/common/LanguageSwitcher/index.tsx:28
msgid "Brazilian Portuguese"
msgstr "Brazilian Portuguese"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:87
#~ msgid "Button color"
#~ msgstr "Button color"

#: src/components/routes/auth/Register/index.tsx:133
msgid "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."
msgstr "By registering you agree to our <0>Terms of Service</0> and <1>Privacy Policy</1>."

#: src/components/forms/TaxAndFeeForm/index.tsx:52
msgid "Calculation Type"
msgstr "Calculation Type"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:140
msgid "California"
msgstr "California"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:175
msgid "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."
msgstr "Camera permission was denied. <0>Request Permission</0> again, or if this doesn't work, you will need to <1>grant this page</1> access to your camera in your browser settings."

#: src/components/routes/events/Dashboard/dashboard.tsx:18
#~ msgid "Can't load events"
#~ msgstr "Can't load events"

#: src/components/common/AttendeeTable/index.tsx:182
#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/QuestionAndAnswerList/index.tsx:149
#: src/components/layouts/CheckIn/index.tsx:225
#: src/utilites/confirmationDialog.tsx:11
msgid "Cancel"
msgstr "Cancel"

#: src/components/routes/profile/ManageProfile/index.tsx:126
msgid "Cancel email change"
msgstr "Cancel email change"

#: src/components/common/OrdersTable/index.tsx:190
#: src/components/modals/RefundOrderModal/index.tsx:112
msgid "Cancel order"
msgstr "Cancel order"

#: src/components/modals/CancelOrderModal/index.tsx:59
msgid "Cancel Order"
msgstr "Cancel Order"

#: src/components/modals/CancelOrderModal/index.tsx:44
msgid "Cancel Order {0}"
msgstr "Cancel Order {0}"

#: src/components/common/AttendeeDetails/index.tsx:32
#~ msgid "Canceled"
#~ msgstr "Canceled"

#: src/components/modals/CancelOrderModal/index.tsx:54
msgid "Canceling will cancel all products associated with this order, and release the products back into the available pool."
msgstr "Canceling will cancel all products associated with this order, and release the products back into the available pool."

#: src/components/modals/CancelOrderModal/index.tsx:54
#~ msgid "Canceling will cancel all tickets associated with this order, and release the tickets back into the available pool."
#~ msgstr "Canceling will cancel all tickets associated with this order, and release the tickets back into the available pool."

#: src/components/common/AttendeeTicket/index.tsx:61
#: src/components/common/OrderStatusBadge/index.tsx:12
#: src/components/routes/event/orders.tsx:24
msgid "Cancelled"
msgstr "Cancelled"

#: src/components/layouts/CheckIn/index.tsx:173
msgid "Cannot Check In"
msgstr "Cannot Check In"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:42
#: src/components/layouts/Event/index.tsx:103
msgid "Capacity"
msgstr "Capacity"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:36
msgid "Capacity Assignment created successfully"
msgstr "Capacity Assignment created successfully"

#: src/components/common/CapacityAssignmentList/index.tsx:31
msgid "Capacity Assignment deleted successfully"
msgstr "Capacity Assignment deleted successfully"

#: src/components/routes/event/CapacityAssignments/index.tsx:32
msgid "Capacity Management"
msgstr "Capacity Management"

#: src/components/modals/CreateProductCategoryModal/index.tsx:56
msgid "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."
msgstr "Categories allow you to group products together. For example, you might have a category for \"Tickets\" and another for \"Merchandise\"."

#: src/components/forms/ProductForm/index.tsx:267
msgid "Categories help you organize your products. This title will be displayed on the public event page."
msgstr "Categories help you organize your products. This title will be displayed on the public event page."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:91
msgid "Categories reordered successfully."
msgstr "Categories reordered successfully."

#: src/components/routes/event/products.tsx:96
msgid "Category"
msgstr "Category"

#: src/components/modals/CreateProductCategoryModal/index.tsx:37
msgid "Category Created Successfully"
msgstr "Category Created Successfully"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Change Cover"
msgstr "Change Cover"

#: src/components/routes/profile/ManageProfile/index.tsx:201
msgid "Change password"
msgstr "Change password"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check in"
#~ msgstr "check in"

#: src/components/layouts/CheckIn/index.tsx:180
msgid "Check In"
msgstr "Check In"

#: src/components/layouts/CheckIn/index.tsx:193
msgid "Check in {0} {1}"
msgstr "Check in {0} {1}"

#: src/components/layouts/CheckIn/index.tsx:218
msgid "Check in and mark order as paid"
msgstr "Check in and mark order as paid"

#: src/components/layouts/CheckIn/index.tsx:209
msgid "Check in only"
msgstr "Check in only"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "check out"
#~ msgstr "check out"

#: src/components/layouts/CheckIn/index.tsx:177
msgid "Check Out"
msgstr "Check Out"

#: src/components/layouts/Checkout/index.tsx:106
msgid "Check out this event!"
msgstr "Check out this event!"

#: src/components/common/EventCard/index.tsx:156
msgid "Check-in"
msgstr "Check-in"

#: src/components/layouts/Event/index.tsx:52
#~ msgid "Check-In"
#~ msgstr "Check-In"

#: src/components/forms/WebhookForm/index.tsx:100
msgid "Check-in Created"
msgstr "Check-in Created"

#: src/components/forms/WebhookForm/index.tsx:106
msgid "Check-in Deleted"
msgstr "Check-in Deleted"

#: src/components/modals/CreateCheckInListModal/index.tsx:40
msgid "Check-In List created successfully"
msgstr "Check-In List created successfully"

#: src/components/common/CheckInListList/index.tsx:33
msgid "Check-In List deleted successfully"
msgstr "Check-In List deleted successfully"

#: src/components/layouts/CheckIn/index.tsx:326
msgid "Check-in list has expired"
msgstr "Check-in list has expired"

#: src/components/layouts/CheckIn/index.tsx:343
msgid "Check-in list is not active"
msgstr "Check-in list is not active"

#: src/components/layouts/CheckIn/index.tsx:311
msgid "Check-in list not found"
msgstr "Check-in list not found"

#: src/components/layouts/Event/index.tsx:104
#: src/components/routes/event/CheckInLists/index.tsx:32
msgid "Check-In Lists"
msgstr "Check-In Lists"

#: src/components/common/CheckInListList/index.tsx:162
msgid "Check-In URL copied to clipboard"
msgstr "Check-In URL copied to clipboard"

#: src/components/forms/QuestionForm/index.tsx:114
msgid "Checkbox options allow multiple selections"
msgstr "Checkbox options allow multiple selections"

#: src/components/forms/QuestionForm/index.tsx:112
msgid "Checkboxes"
msgstr "Checkboxes"

#: src/components/common/AttendeeDetails/index.tsx:29
msgid "Checked In"
msgstr "Checked In"

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "Checked in successfully"
#~ msgstr "Checked in successfully"

#: src/components/layouts/Checkout/index.tsx:83
#: src/components/routes/event/Settings/index.tsx:40
msgid "Checkout"
msgstr "Checkout"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:57
#~ msgid "Checkout Messaging"
#~ msgstr "Checkout Messaging"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:61
msgid "Checkout Settings"
msgstr "Checkout Settings"

#: src/locales.ts:46
#~ msgid "Chinese"
#~ msgstr "Chinese"

#: src/components/common/LanguageSwitcher/index.tsx:30
msgid "Chinese (Simplified)"
msgstr "Chinese (Simplified)"

#: src/components/common/LanguageSwitcher/index.tsx:32
msgid "Chinese (Traditional)"
msgstr "Chinese (Traditional)"

#: src/components/routes/event/HomepageDesigner/index.tsx:133
msgid "Choose a color for your background"
msgstr "Choose a color for your background"

#: src/components/modals/ChooseAccountModal/index.tsx:13
msgid "Choose an account"
msgstr "Choose an account"

#: src/components/routes/event/settings.tsx:32
#: src/components/routes/event/settings.tsx:75
#~ msgid "Choose what notifications you want to receive"
#~ msgstr "Choose what notifications you want to receive"

#: src/components/common/CheckoutQuestion/index.tsx:162
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:134
#: src/components/routes/product-widget/CollectInformation/index.tsx:333
#: src/components/routes/product-widget/CollectInformation/index.tsx:334
msgid "City"
msgstr "City"

#: src/components/common/SearchBar/index.tsx:75
msgid "Clear Search Text"
msgstr "Clear Search Text"

#: src/components/routes/product-widget/SelectProducts/index.tsx:288
#~ msgid "click here"
#~ msgstr "click here"

#: src/components/common/PromoCodeTable/index.tsx:98
msgid "Click to copy"
msgstr "Click to copy"

#: src/components/routes/product-widget/SelectProducts/index.tsx:533
msgid "close"
msgstr "close"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:186
#: src/components/modals/RefundOrderModal/index.tsx:125
#: src/components/routes/product-widget/SelectProducts/index.tsx:534
msgid "Close"
msgstr "Close"

#: src/components/layouts/Event/index.tsx:281
msgid "Close sidebar"
msgstr "Close sidebar"

#: src/components/common/PromoCodeTable/index.tsx:69
#: src/components/forms/PromoCodeForm/index.tsx:34
msgid "Code"
msgstr "Code"

#: src/components/modals/CreatePromoCodeModal/index.tsx:26
#: src/components/modals/EditPromoCodeModal/index.tsx:38
msgid "Code must be between 3 and 50 characters long"
msgstr "Code must be between 3 and 50 characters long"

#: src/components/forms/ProductForm/index.tsx:401
msgid "Collapse this product when the event page is initially loaded"
msgstr "Collapse this product when the event page is initially loaded"

#: src/components/forms/TicketForm/index.tsx:346
#~ msgid "Collapse this ticket when the event page is initially loaded"
#~ msgstr "Collapse this ticket when the event page is initially loaded"

#: src/components/routes/event/HomepageDesigner/index.tsx:131
msgid "Color"
msgstr "Color"

#: src/components/common/WidgetEditor/index.tsx:21
msgid "Color must be a valid hex color code. Example: #ffffff"
msgstr "Color must be a valid hex color code. Example: #ffffff"

#: src/components/common/WidgetEditor/index.tsx:166
#: src/components/routes/event/HomepageDesigner/index.tsx:124
msgid "Colors"
msgstr "Colors"

#: src/components/layouts/Event/index.tsx:158
msgid "Coming Soon"
msgstr "Coming Soon"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:75
msgid "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"
msgstr "Comma seperated keywords that describe the event. These will be used by search engines to help categorize and index the event"

#: src/components/routes/product-widget/CollectInformation/index.tsx:453
msgid "Complete Order"
msgstr "Complete Order"

#: src/components/routes/product-widget/CollectInformation/index.tsx:223
msgid "Complete payment"
msgstr "Complete payment"

#: src/components/routes/product-widget/Payment/index.tsx:131
msgid "Complete Payment"
msgstr "Complete Payment"

#: src/components/layouts/AuthLayout/index.tsx:68
#~ msgid "Complete Store"
#~ msgstr "Complete Store"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:202
#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Complete Stripe Setup"
msgstr "Complete Stripe Setup"

#: src/components/routes/event/EventDashboard/index.tsx:127
msgid "Complete these steps to start selling tickets for your event."
msgstr "Complete these steps to start selling tickets for your event."

#: src/components/modals/SendMessageModal/index.tsx:230
#: src/components/routes/event/GettingStarted/index.tsx:70
#: src/components/routes/event/orders.tsx:23
msgid "Completed"
msgstr "Completed"

#: src/components/common/StatBoxes/index.tsx:51
msgid "Completed orders"
msgstr "Completed orders"

#: src/components/routes/event/EventDashboard/index.tsx:237
msgid "Completed Orders"
msgstr "Completed Orders"

#: src/components/common/WidgetEditor/index.tsx:287
msgid "Component Code"
msgstr "Component Code"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:35
msgid "Configured Discount"
msgstr "Configured Discount"

#: src/utilites/confirmationDialog.tsx:11
msgid "Confirm"
msgstr "Confirm"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:51
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:56
msgid "Confirm Email Change"
msgstr "Confirm Email Change"

#: src/components/routes/profile/ManageProfile/index.tsx:199
msgid "Confirm New Password"
msgstr "Confirm New Password"

#: src/components/routes/auth/Register/index.tsx:115
msgid "Confirm password"
msgstr "Confirm password"

#: src/components/routes/auth/AcceptInvitation/index.tsx:112
#: src/components/routes/auth/Register/index.tsx:114
#: src/components/routes/auth/ResetPassword/index.tsx:60
msgid "Confirm Password"
msgstr "Confirm Password"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:36
msgid "Confirming email address..."
msgstr "Confirming email address..."

#: src/components/routes/event/GettingStarted/index.tsx:13
#~ msgid "Congratulation on creating an event!"
#~ msgstr "Congratulation on creating an event!"

#: src/components/routes/event/GettingStarted/index.tsx:88
msgid "Congratulations on creating an event!"
msgstr "Congratulations on creating an event!"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:173
msgid "Connect Documentation"
msgstr "Connect Documentation"

#: src/components/routes/event/EventDashboard/index.tsx:192
msgid "Connect payment processing"
msgstr "Connect payment processing"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:52
#~ msgid "Connect Stripe"
#~ msgstr "Connect Stripe"

#: src/components/routes/event/EventDashboard/index.tsx:205
msgid "Connect to Stripe"
msgstr "Connect to Stripe"

#: src/components/layouts/AuthLayout/index.tsx:89
msgid "Connect with CRM and automate tasks using webhooks and integrations"
msgstr "Connect with CRM and automate tasks using webhooks and integrations"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:201
#: src/components/routes/event/GettingStarted/index.tsx:154
msgid "Connect with Stripe"
msgstr "Connect with Stripe"

#: src/components/routes/event/GettingStarted/index.tsx:150
msgid "Connect your Stripe account to start receiving payments."
msgstr "Connect your Stripe account to start receiving payments."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:148
msgid "Connected to Stripe"
msgstr "Connected to Stripe"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:98
msgid "Connection Details"
msgstr "Connection Details"

#: src/components/routes/event/settings.tsx:243
#~ msgid "Contact email"
#~ msgstr "Contact email"

#: src/components/modals/SendMessageModal/index.tsx:167
msgid "Contact Support"
msgstr "Contact Support"

#: src/components/modals/SendMessageModal/index.tsx:158
msgid "Contact us to enable messaging"
msgstr "Contact us to enable messaging"

#: src/components/routes/event/HomepageDesigner/index.tsx:155
msgid "Content background color"
msgstr "Content background color"

#: src/components/common/WidgetEditor/index.tsx:31
#: src/components/common/WidgetEditor/index.tsx:217
#: src/components/layouts/Checkout/CheckoutFooter/index.tsx:38
#: src/components/routes/product-widget/CollectInformation/index.tsx:444
#: src/components/routes/product-widget/SelectProducts/index.tsx:486
msgid "Continue"
msgstr "Continue"

#: src/components/routes/event/HomepageDesigner/index.tsx:164
msgid "Continue button text"
msgstr "Continue button text"

#: src/components/common/WidgetEditor/index.tsx:216
msgid "Continue Button Text"
msgstr "Continue Button Text"

#: src/components/modals/CreateEventModal/index.tsx:153
msgid "Continue Event Setup"
msgstr "Continue Event Setup"

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Continue set up"
msgstr "Continue set up"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
#~ msgid "Continue Stripe Connect Setup"
#~ msgstr "Continue Stripe Connect Setup"

#: src/components/routes/product-widget/SelectProducts/index.tsx:337
msgid "Continue to Checkout"
msgstr "Continue to Checkout"

#: src/components/routes/product-widget/CollectInformation/index.tsx:380
#~ msgid "Continue To Payment"
#~ msgstr "Continue To Payment"

#: src/components/common/AttendeeTicket/index.tsx:91
#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:243
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copied"
msgstr "Copied"

#: src/components/common/PromoCodeTable/index.tsx:106
msgid "copied to clipboard"
msgstr "copied to clipboard"

#: src/components/common/CopyButton/index.tsx:13
#: src/components/modals/ShareModal/index.tsx:250
msgid "Copy"
msgstr "Copy"

#: src/components/common/CheckInListList/index.tsx:156
msgid "Copy Check-In URL"
msgstr "Copy Check-In URL"

#: src/components/routes/product-widget/CollectInformation/index.tsx:306
msgid "Copy details to all attendees"
msgstr "Copy details to all attendees"

#: src/components/common/AttendeeTicket/index.tsx:91
msgid "Copy Link"
msgstr "Copy Link"

#: src/components/common/PromoCodeTable/index.tsx:177
#: src/components/modals/ShareModal/index.tsx:243
msgid "Copy URL"
msgstr "Copy URL"

#: src/components/common/CheckoutQuestion/index.tsx:174
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:152
#: src/components/routes/product-widget/CollectInformation/index.tsx:354
msgid "Country"
msgstr "Country"

#: src/components/routes/event/HomepageDesigner/index.tsx:117
msgid "Cover"
msgstr "Cover"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:74
#~ msgid "Cover Image"
#~ msgstr "Cover Image"

#: src/components/routes/event/attendees.tsx:71
#: src/components/routes/event/products.tsx:74
msgid "Create"
msgstr "Create"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
msgid "Create {0}"
msgstr "Create {0}"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:59
msgid "Create a Product"
msgstr "Create a Product"

#: src/components/common/PromoCodeTable/index.tsx:57
msgid "Create a Promo Code"
msgstr "Create a Promo Code"

#: src/components/modals/CreateCheckInListModal/index.tsx:63
msgid "Create a Ticket"
msgstr "Create a Ticket"

#: src/components/routes/auth/Register/index.tsx:69
msgid "Create an account or <0>{0}</0> to get started"
msgstr "Create an account or <0>{0}</0> to get started"

#: src/components/modals/CreateEventModal/index.tsx:120
msgid "create an organizer"
msgstr "create an organizer"

#: src/components/layouts/AuthLayout/index.tsx:27
msgid "Create and customize your event page instantly"
msgstr "Create and customize your event page instantly"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Create Attendee"
msgstr "Create Attendee"

#: src/components/common/CapacityAssignmentList/index.tsx:63
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:68
#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:79
#: src/components/routes/event/CapacityAssignments/index.tsx:46
msgid "Create Capacity Assignment"
msgstr "Create Capacity Assignment"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:128
#: src/components/common/ProductsTable/SortableCategory/index.tsx:129
msgid "Create category"
msgstr "Create category"

#: src/components/modals/CreateProductCategoryModal/index.tsx:45
#: src/components/modals/CreateProductCategoryModal/index.tsx:60
msgid "Create Category"
msgstr "Create Category"

#: src/components/common/CheckInListList/index.tsx:61
#: src/components/modals/CreateCheckInListModal/index.tsx:72
#: src/components/modals/CreateCheckInListModal/index.tsx:82
#: src/components/routes/event/CheckInLists/index.tsx:46
msgid "Create Check-In List"
msgstr "Create Check-In List"

#: src/components/common/NoEventsBlankSlate/index.tsx:27
#: src/components/modals/CreateEventModal/index.tsx:85
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:81
#: src/components/routes/welcome/index.tsx:108
msgid "Create Event"
msgstr "Create Event"

#: src/components/routes/events/Dashboard/index.tsx:91
msgid "Create new"
msgstr "Create new"

#: src/components/forms/OrganizerForm/index.tsx:111
#: src/components/modals/CreateEventModal/index.tsx:93
#: src/components/modals/CreateOrganizerModal/index.tsx:16
msgid "Create Organizer"
msgstr "Create Organizer"

#: src/components/modals/CreateProductModal/index.tsx:80
#: src/components/modals/CreateProductModal/index.tsx:88
msgid "Create Product"
msgstr "Create Product"

#: src/components/routes/event/GettingStarted/index.tsx:70
#~ msgid "Create products for your event, set prices, and manage available quantity."
#~ msgstr "Create products for your event, set prices, and manage available quantity."

#: src/components/modals/CreatePromoCodeModal/index.tsx:51
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
msgid "Create Promo Code"
msgstr "Create Promo Code"

#: src/components/routes/event/GettingStarted/index.tsx:58
#~ msgid "Create promo codes"
#~ msgstr "Create promo codes"

#: src/components/routes/event/GettingStarted/index.tsx:55
#~ msgid "Create promo codes to offer discounts to your attendees."
#~ msgstr "Create promo codes to offer discounts to your attendees."

#: src/components/modals/CreateQuestionModal/index.tsx:69
#: src/components/modals/CreateQuestionModal/index.tsx:74
msgid "Create Question"
msgstr "Create Question"

#: src/components/modals/CreateTaxOrFeeModal/index.tsx:41
msgid "Create Tax or Fee"
msgstr "Create Tax or Fee"

#: src/components/modals/CreateTicketModal/index.tsx:83
#: src/components/modals/CreateTicketModal/index.tsx:91
#: src/components/routes/event/tickets.tsx:50
#~ msgid "Create Ticket"
#~ msgstr "Create Ticket"

#: src/components/routes/event/GettingStarted/index.tsx:122
msgid "Create tickets for your event, set prices, and manage available quantity."
msgstr "Create tickets for your event, set prices, and manage available quantity."

#: src/components/modals/CreateWebhookModal/index.tsx:57
#: src/components/modals/CreateWebhookModal/index.tsx:67
msgid "Create Webhook"
msgstr "Create Webhook"

#: src/components/common/OrdersTable/index.tsx:208
#: src/components/common/OrdersTable/index.tsx:281
msgid "Created"
msgstr "Created"

#: src/components/forms/OrganizerForm/index.tsx:45
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:73
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:100
msgid "Currency"
msgstr "Currency"

#: src/components/routes/profile/ManageProfile/index.tsx:191
msgid "Current Password"
msgstr "Current Password"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:159
msgid "Custom Maps URL"
msgstr "Custom Maps URL"

#: src/components/common/ReportTable/index.tsx:53
msgid "Custom Range"
msgstr "Custom Range"

#: src/components/common/OrdersTable/index.tsx:205
msgid "Customer"
msgstr "Customer"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:55
msgid "Customize the email and notification settings for this event"
msgstr "Customize the email and notification settings for this event"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
#~ msgid "Customize the email settings for this event"
#~ msgstr "Customize the email settings for this event"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:72
#~ msgid "Customize the event homepage and checkout experience"
#~ msgstr "Customize the event homepage and checkout experience"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:62
msgid "Customize the event homepage and checkout messaging"
msgstr "Customize the event homepage and checkout messaging"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:70
msgid "Customize the miscellaneous settings for this event"
msgstr "Customize the miscellaneous settings for this event"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:57
msgid "Customize the SEO settings for this event"
msgstr "Customize the SEO settings for this event"

#: src/components/routes/event/GettingStarted/index.tsx:169
msgid "Customize your event page"
msgstr "Customize your event page"

#: src/components/layouts/AuthLayout/index.tsx:84
msgid "Customize your event page and widget design to match your brand perfectly"
msgstr "Customize your event page and widget design to match your brand perfectly"

#: src/components/routes/event/GettingStarted/index.tsx:165
msgid "Customize your event page to match your brand and style."
msgstr "Customize your event page to match your brand and style."

#: src/components/routes/event/Reports/index.tsx:23
msgid "Daily Sales Report"
msgstr "Daily Sales Report"

#: src/components/routes/event/Reports/index.tsx:24
msgid "Daily sales, tax, and fee breakdown"
msgstr "Daily sales, tax, and fee breakdown"

#: src/components/common/CapacityAssignmentList/index.tsx:156
#: src/components/common/CheckInListList/index.tsx:176
#: src/components/common/OrdersTable/index.tsx:186
#: src/components/common/ProductsTable/SortableProduct/index.tsx:287
#: src/components/common/PromoCodeTable/index.tsx:181
#: src/components/common/QuestionsTable/index.tsx:115
#: src/components/common/WebhookTable/index.tsx:116
msgid "Danger zone"
msgstr "Danger zone"

#: src/components/common/AttendeeTable/index.tsx:179
msgid "Danger Zone"
msgstr "Danger Zone"

#: src/components/layouts/Event/index.tsx:89
msgid "Dashboard"
msgstr "Dashboard"

#: src/components/common/OrderDetails/index.tsx:39
#: src/components/forms/QuestionForm/index.tsx:136
msgid "Date"
msgstr "Date"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:40
#~ msgid "Date & Time"
#~ msgstr "Date & Time"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:38
msgid "Day one capacity"
msgstr "Day one capacity"

#: src/components/forms/CheckInListForm/index.tsx:21
#~ msgid "Day one check-in list"
#~ msgstr "Day one check-in list"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#~ msgid "Deactivate user"
#~ msgstr "Deactivate user"

#: src/components/common/PromoCodeTable/index.tsx:40
#: src/components/common/TaxAndFeeList/index.tsx:73
msgid "Delete"
msgstr "Delete"

#: src/components/common/CapacityAssignmentList/index.tsx:159
msgid "Delete Capacity"
msgstr "Delete Capacity"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:146
#: src/components/common/ProductsTable/SortableCategory/index.tsx:147
msgid "Delete category"
msgstr "Delete category"

#: src/components/common/CheckInListList/index.tsx:179
msgid "Delete Check-In List"
msgstr "Delete Check-In List"

#: src/components/common/PromoCodeTable/index.tsx:185
msgid "Delete code"
msgstr "Delete code"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:113
msgid "Delete Cover"
msgstr "Delete Cover"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:98
msgid "Delete Image"
msgstr "Delete Image"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:292
msgid "Delete product"
msgstr "Delete product"

#: src/components/common/QuestionsTable/index.tsx:120
msgid "Delete question"
msgstr "Delete question"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:199
#~ msgid "Delete ticket"
#~ msgstr "Delete ticket"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#~ msgid "Delete user"
#~ msgstr "Delete user"

#: src/components/common/WebhookTable/index.tsx:127
msgid "Delete webhook"
msgstr "Delete webhook"

#: src/components/forms/ProductForm/index.tsx:258
#: src/components/forms/TaxAndFeeForm/index.tsx:81
#: src/components/modals/CreateEventModal/index.tsx:135
#: src/components/modals/DuplicateEventModal/index.tsx:84
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:78
msgid "Description"
msgstr "Description"

#: src/components/forms/CheckInListForm/index.tsx:36
msgid "Description for check-in staff"
msgstr "Description for check-in staff"

#: src/components/modals/CreateEventModal/index.tsx:40
#~ msgid "Description should be less than 50,000 characters"
#~ msgstr "Description should be less than 50,000 characters"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Details"
msgstr "Details"

#: src/components/common/PromoCodeTable/index.tsx:167
#~ msgid "Disable code"
#~ msgstr "Disable code"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
#~ msgid "Disable this capacity track capacity without stopping product sales"
#~ msgstr "Disable this capacity track capacity without stopping product sales"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:26
#~ msgid "Disable this capacity track capacity without stopping ticket sales"
#~ msgstr "Disable this capacity track capacity without stopping ticket sales"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:27
msgid "Disabling this capacity will track sales but not stop them when the limit is reached"
msgstr "Disabling this capacity will track sales but not stop them when the limit is reached"

#: src/components/common/PromoCodeTable/index.tsx:70
msgid "Discount"
msgstr "Discount"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount %"
msgstr "Discount %"

#: src/components/forms/PromoCodeForm/index.tsx:63
msgid "Discount in {0}"
msgstr "Discount in {0}"

#: src/components/forms/PromoCodeForm/index.tsx:43
msgid "Discount Type"
msgstr "Discount Type"

#: src/components/routes/product-widget/SelectProducts/index.tsx:297
#~ msgid "Dismiss"
#~ msgstr "Dismiss"

#: src/components/routes/product-widget/SelectProducts/index.tsx:354
msgid "Dismiss this message"
msgstr "Dismiss this message"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:190
msgid "Document Label"
msgstr "Document Label"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:222
msgid "Documentation"
msgstr "Documentation"

#: src/components/layouts/AuthLayout/index.tsx:19
#~ msgid "Does not exist"
#~ msgstr "Does not exist"

#: src/components/routes/auth/Login/index.tsx:57
msgid "Don't have an account?   <0>Sign up</0>"
msgstr "Don't have an account?   <0>Sign up</0>"

#: src/components/routes/auth/Login/index.tsx:72
#~ msgid "Don't have an account?   <0>Sign Up</0>"
#~ msgstr "Don't have an account?   <0>Sign Up</0>"

#: src/components/forms/ProductForm/index.tsx:157
msgid "Donation / Pay what you'd like product"
msgstr "Donation / Pay what you'd like product"

#: src/components/forms/TicketForm/index.tsx:139
#~ msgid "Donation / Pay what you'd like ticket"
#~ msgstr "Donation / Pay what you'd like ticket"

#: src/components/routes/ticket-widget/SelectTickets/Prices/Tiered/index.tsx:59
#~ msgid "Donation amount"
#~ msgstr "Donation amount"

#: src/components/forms/TicketForm/index.tsx:139
#~ msgid "Donation Ticket"
#~ msgstr "Donation Ticket"

#: src/components/common/AddEventToCalendarButton/index.tsx:137
msgid "Download .ics"
msgstr "Download .ics"

#: src/components/common/DownloadCsvButton/index.tsx:48
msgid "Download CSV"
msgstr "Download CSV"

#: src/components/common/OrdersTable/index.tsx:162
msgid "Download invoice"
msgstr "Download invoice"

#: src/components/layouts/Checkout/index.tsx:126
msgid "Download Invoice"
msgstr "Download Invoice"

#: src/components/modals/ShareModal/index.tsx:224
msgid "Download QR Code"
msgstr "Download QR Code"

#: src/components/routes/ticket-widget/OrderSummaryAndTickets/index.tsx:91
#~ msgid "Download Tickets PDF"
#~ msgstr "Download Tickets PDF"

#: src/components/common/OrdersTable/index.tsx:111
#: src/components/layouts/Checkout/index.tsx:49
msgid "Downloading Invoice"
msgstr "Downloading Invoice"

#: src/components/layouts/Event/index.tsx:188
msgid "Draft"
msgstr "Draft"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:83
msgid "Drag and drop or click"
msgstr "Drag and drop or click"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Drag to sort"
#~ msgstr "Drag to sort"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:83
#~ msgid "Drop an image, or click here to replace the Cover Image"
#~ msgstr "Drop an image, or click here to replace the Cover Image"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:84
#~ msgid "Drop an image, or click here to upload the Cover Image"
#~ msgstr "Drop an image, or click here to upload the Cover Image"

#: src/components/forms/QuestionForm/index.tsx:124
msgid "Dropdown selection"
msgstr "Dropdown selection"

#: src/components/modals/SendMessageModal/index.tsx:159
msgid ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."
msgstr ""
"Due to the high risk of spam, we require manual verification before you can send messages.\n"
"Please contact us to request access."

#: src/components/modals/DuplicateEventModal/index.tsx:124
msgid "Duplicate Capacity Assignments"
msgstr "Duplicate Capacity Assignments"

#: src/components/modals/DuplicateEventModal/index.tsx:128
msgid "Duplicate Check-In Lists"
msgstr "Duplicate Check-In Lists"

#: src/components/common/EventCard/index.tsx:162
msgid "Duplicate event"
msgstr "Duplicate event"

#: src/components/modals/DuplicateEventModal/index.tsx:69
#: src/components/modals/DuplicateEventModal/index.tsx:142
msgid "Duplicate Event"
msgstr "Duplicate Event"

#: src/components/modals/DuplicateEventModal/index.tsx:132
msgid "Duplicate Event Cover Image"
msgstr "Duplicate Event Cover Image"

#: src/components/modals/DuplicateEventModal/index.tsx:103
msgid "Duplicate Options"
msgstr "Duplicate Options"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:285
#: src/components/modals/DuplicateProductModal/index.tsx:109
#: src/components/modals/DuplicateProductModal/index.tsx:113
msgid "Duplicate Product"
msgstr "Duplicate Product"

#: src/components/modals/DuplicateEventModal/index.tsx:108
msgid "Duplicate Products"
msgstr "Duplicate Products"

#: src/components/modals/DuplicateEventModal/index.tsx:120
msgid "Duplicate Promo Codes"
msgstr "Duplicate Promo Codes"

#: src/components/modals/DuplicateEventModal/index.tsx:112
msgid "Duplicate Questions"
msgstr "Duplicate Questions"

#: src/components/modals/DuplicateEventModal/index.tsx:116
msgid "Duplicate Settings"
msgstr "Duplicate Settings"

#: src/components/modals/DuplicateEventModal/index.tsx:107
#~ msgid "Duplicate Tickets"
#~ msgstr "Duplicate Tickets"

#: src/components/modals/DuplicateEventModal/index.tsx:137
msgid "Duplicate Webhooks"
msgstr "Duplicate Webhooks"

#: src/components/common/LanguageSwitcher/index.tsx:24
msgid "Dutch"
msgstr "Dutch"

#: src/components/forms/ProductForm/index.tsx:78
msgid "Early bird"
msgstr "Early bird"

#: src/components/common/TaxAndFeeList/index.tsx:65
#: src/components/modals/ManageAttendeeModal/index.tsx:221
#: src/components/modals/ManageOrderModal/index.tsx:203
msgid "Edit"
msgstr "Edit"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
msgid "Edit {0}"
msgstr "Edit {0}"

#: src/components/common/QuestionAndAnswerList/index.tsx:159
msgid "Edit Answer"
msgstr "Edit Answer"

#: src/components/common/AttendeeTable/index.tsx:174
#~ msgid "Edit attendee"
#~ msgstr "Edit attendee"

#: src/components/modals/EditAttendeeModal/index.tsx:72
#: src/components/modals/EditAttendeeModal/index.tsx:110
#~ msgid "Edit Attendee"
#~ msgstr "Edit Attendee"

#: src/components/common/CapacityAssignmentList/index.tsx:146
msgid "Edit Capacity"
msgstr "Edit Capacity"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:66
#: src/components/modals/EditCapacityAssignmentModal/index.tsx:74
msgid "Edit Capacity Assignment"
msgstr "Edit Capacity Assignment"

#: src/components/common/ProductsTable/SortableCategory/index.tsx:137
#: src/components/common/ProductsTable/SortableCategory/index.tsx:138
msgid "Edit category"
msgstr "Edit category"

#: src/components/common/CheckInListList/index.tsx:148
#: src/components/modals/EditCheckInListModal/index.tsx:69
#: src/components/modals/EditCheckInListModal/index.tsx:91
msgid "Edit Check-In List"
msgstr "Edit Check-In List"

#: src/components/common/PromoCodeTable/index.tsx:170
msgid "Edit Code"
msgstr "Edit Code"

#: src/components/modals/EditOrganizerModal/index.tsx:63
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:51
msgid "Edit Organizer"
msgstr "Edit Organizer"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:280
#: src/components/modals/EditProductModal/index.tsx:100
#: src/components/modals/EditProductModal/index.tsx:108
msgid "Edit Product"
msgstr "Edit Product"

#: src/components/modals/EditProductCategoryModal/index.tsx:62
#: src/components/modals/EditProductCategoryModal/index.tsx:70
msgid "Edit Product Category"
msgstr "Edit Product Category"

#: src/components/modals/EditPromoCodeModal/index.tsx:79
#: src/components/modals/EditPromoCodeModal/index.tsx:84
msgid "Edit Promo Code"
msgstr "Edit Promo Code"

#: src/components/common/QuestionsTable/index.tsx:112
msgid "Edit question"
msgstr "Edit question"

#: src/components/modals/EditQuestionModal/index.tsx:95
#: src/components/modals/EditQuestionModal/index.tsx:101
msgid "Edit Question"
msgstr "Edit Question"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:191
#: src/components/modals/EditTicketModal/index.tsx:96
#: src/components/modals/EditTicketModal/index.tsx:104
#~ msgid "Edit Ticket"
#~ msgstr "Edit Ticket"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Edit user"
msgstr "Edit user"

#: src/components/modals/EditUserModal/index.tsx:62
#: src/components/modals/EditUserModal/index.tsx:120
msgid "Edit User"
msgstr "Edit User"

#: src/components/common/WebhookTable/index.tsx:104
msgid "Edit webhook"
msgstr "Edit webhook"

#: src/components/modals/EditWebhookModal/index.tsx:66
#: src/components/modals/EditWebhookModal/index.tsx:87
msgid "Edit Webhook"
msgstr "Edit Webhook"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 2.50 for $2.50"
msgstr "eg. 2.50 for $2.50"

#: src/components/forms/TaxAndFeeForm/index.tsx:74
msgid "eg. 23.5 for 23.5%"
msgstr "eg. 23.5 for 23.5%"

#: src/components/common/AttendeeDetails/index.tsx:21
#: src/components/common/AttendeeTable/index.tsx:109
#: src/components/common/OrderDetails/index.tsx:31
#: src/components/forms/OrganizerForm/index.tsx:32
#: src/components/modals/EditUserModal/index.tsx:80
#: src/components/modals/InviteUserModal/index.tsx:61
#: src/components/routes/auth/AcceptInvitation/index.tsx:98
#: src/components/routes/auth/Login/index.tsx:68
#: src/components/routes/auth/Register/index.tsx:97
#: src/components/routes/event/Settings/index.tsx:52
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:117
#: src/components/routes/profile/ManageProfile/index.tsx:138
msgid "Email"
msgstr "Email"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:54
msgid "Email & Notification Settings"
msgstr "Email & Notification Settings"

#: src/components/modals/CreateAttendeeModal/index.tsx:132
#: src/components/modals/ManageAttendeeModal/index.tsx:114
#: src/components/modals/ManageOrderModal/index.tsx:157
msgid "Email address"
msgstr "Email address"

#: src/components/common/QuestionsTable/index.tsx:220
#: src/components/common/QuestionsTable/index.tsx:221
#: src/components/routes/product-widget/CollectInformation/index.tsx:298
#: src/components/routes/product-widget/CollectInformation/index.tsx:299
#: src/components/routes/product-widget/CollectInformation/index.tsx:408
#: src/components/routes/product-widget/CollectInformation/index.tsx:409
msgid "Email Address"
msgstr "Email Address"

#: src/components/routes/profile/ManageProfile/index.tsx:77
msgid "Email change cancelled successfully"
msgstr "Email change cancelled successfully"

#: src/components/routes/profile/ManageProfile/index.tsx:116
msgid "Email change pending"
msgstr "Email change pending"

#: src/components/routes/event/settings.tsx:72
#~ msgid "Email Configuration"
#~ msgstr "Email Configuration"

#: src/components/routes/profile/ManageProfile/index.tsx:151
msgid "Email confirmation resent"
msgstr "Email confirmation resent"

#: src/components/routes/profile/ManageProfile/index.tsx:90
msgid "Email confirmation resent successfully"
msgstr "Email confirmation resent successfully"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:66
msgid "Email footer message"
msgstr "Email footer message"

#: src/components/routes/profile/ManageProfile/index.tsx:141
msgid "Email not verified"
msgstr "Email not verified"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:53
#~ msgid "Email Settings"
#~ msgstr "Email Settings"

#: src/components/common/WidgetEditor/index.tsx:272
msgid "Embed Code"
msgstr "Embed Code"

#: src/components/common/WidgetEditor/index.tsx:260
msgid "Embed Script"
msgstr "Embed Script"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:181
msgid "Enable Invoicing"
msgstr "Enable Invoicing"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:21
msgid "Enable this capacity to stop product sales when the limit is reached"
msgstr "Enable this capacity to stop product sales when the limit is reached"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:20
#~ msgid "Enable this capacity to stop ticket sales when the limit is reached"
#~ msgstr "Enable this capacity to stop ticket sales when the limit is reached"

#: src/components/forms/WebhookForm/index.tsx:19
msgid "Enabled"
msgstr "Enabled"

#: src/components/modals/CreateEventModal/index.tsx:149
#: src/components/modals/DuplicateEventModal/index.tsx:98
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:92
#: src/components/routes/welcome/index.tsx:96
msgid "End Date"
msgstr "End Date"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:26
#: src/components/common/EventStatusBadge/index.tsx:14
msgid "Ended"
msgstr "Ended"

#: src/components/routes/events/Dashboard/index.tsx:57
msgid "Ended Events"
msgstr "Ended Events"

#: src/components/common/LanguageSwitcher/index.tsx:16
#: src/components/common/LanguageSwitcher/index.tsx:50
#: src/components/modals/CreateAttendeeModal/index.tsx:145
#: src/components/routes/profile/ManageProfile/index.tsx:173
msgid "English"
msgstr "English"

#: src/components/modals/CreateAttendeeModal/index.tsx:170
msgid "Enter an amount excluding taxes and fees."
msgstr "Enter an amount excluding taxes and fees."

#: src/components/common/OrdersTable/index.tsx:119
#: src/components/layouts/Checkout/index.tsx:57
msgid "Error"
msgstr "Error"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:27
msgid "Error confirming email address"
msgstr "Error confirming email address"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:35
msgid "Error confirming email change"
msgstr "Error confirming email change"

#: src/components/modals/WebhookLogsModal/index.tsx:166
msgid "Error loading logs"
msgstr "Error loading logs"

#: src/components/forms/OrganizerForm/index.tsx:46
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:74
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:101
msgid "EUR"
msgstr "EUR"

#: src/components/routes/events/Dashboard/index.tsx:103
msgid "Event"
msgstr "Event"

#: src/components/routes/events/CreateEvent/index.tsx:45
#~ msgid "Event created successfully"
#~ msgstr "Event created successfully"

#: src/components/modals/CreateEventModal/index.tsx:76
#~ msgid "Event created successfully 🎉"
#~ msgstr "Event created successfully 🎉"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:161
msgid "Event Date"
msgstr "Event Date"

#: src/components/routes/account/ManageAccount/sections/EventDefaultsSettings/index.tsx:5
msgid "Event Defaults"
msgstr "Event Defaults"

#: src/components/routes/event/Settings/index.tsx:28
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:65
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:276
msgid "Event Details"
msgstr "Event Details"

#: src/components/modals/DuplicateEventModal/index.tsx:58
msgid "Event duplicated successfully"
msgstr "Event duplicated successfully"

#: src/components/layouts/Checkout/index.tsx:79
msgid "Event Homepage"
msgstr "Event Homepage"

#: src/components/layouts/Event/index.tsx:166
#~ msgid "Event is not visible to the public"
#~ msgstr "Event is not visible to the public"

#: src/components/layouts/Event/index.tsx:165
#~ msgid "Event is visible to the public"
#~ msgstr "Event is visible to the public"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:85
msgid "Event location & venue details"
msgstr "Event location & venue details"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:12
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
msgid "Event Not Available"
msgstr "Event Not Available"

#: src/components/layouts/Event/index.tsx:187
#~ msgid "Event page"
#~ msgstr "Event page"

#: src/components/layouts/Event/index.tsx:212
msgid "Event Page"
msgstr "Event Page"

#: src/components/routes/event/settings.tsx:42
#~ msgid "Event Settings"
#~ msgstr "Event Settings"

#: src/components/common/EventCard/index.tsx:70
#: src/components/layouts/Event/index.tsx:80
#: src/components/routes/event/EventDashboard/index.tsx:76
#: src/components/routes/event/GettingStarted/index.tsx:63
msgid "Event status update failed. Please try again later"
msgstr "Event status update failed. Please try again later"

#: src/components/common/EventCard/index.tsx:67
#: src/components/layouts/Event/index.tsx:77
#: src/components/routes/event/EventDashboard/index.tsx:73
#: src/components/routes/event/GettingStarted/index.tsx:60
msgid "Event status updated"
msgstr "Event status updated"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Event Ticketing by"
msgstr "Event Ticketing by"

#: src/components/common/WebhookTable/index.tsx:237
#: src/components/forms/WebhookForm/index.tsx:122
msgid "Event Types"
msgstr "Event Types"

#: src/components/modals/ShareModal/index.tsx:237
msgid "Event URL"
msgstr "Event URL"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:39
#~ msgid "Event wdwdeNot Available"
#~ msgstr "Event wdwdeNot Available"

#: src/components/layouts/Event/index.tsx:226
msgid "Events"
msgstr "Events"

#: src/components/forms/CheckInListForm/index.tsx:56
msgid "Expiration date"
msgstr "Expiration date"

#: src/components/common/PromoCodeTable/index.tsx:73
msgid "Expires"
msgstr "Expires"

#: src/components/forms/PromoCodeForm/index.tsx:79
msgid "Expiry Date"
msgstr "Expiry Date"

#: src/components/routes/event/attendees.tsx:80
#: src/components/routes/event/orders.tsx:140
msgid "Export"
msgstr "Export"

#: src/components/common/QuestionsTable/index.tsx:267
msgid "Export answers"
msgstr "Export answers"

#: src/mutations/useExportAnswers.ts:39
msgid "Export failed. Please try again."
msgstr "Export failed. Please try again."

#: src/mutations/useExportAnswers.ts:17
msgid "Export started. Preparing file..."
msgstr "Export started. Preparing file..."

#: src/components/routes/event/attendees.tsx:39
msgid "Exporting Attendees"
msgstr "Exporting Attendees"

#: src/mutations/useExportAnswers.ts:33
msgid "Exporting complete. Downloading file..."
msgstr "Exporting complete. Downloading file..."

#: src/components/routes/event/orders.tsx:90
msgid "Exporting Orders"
msgstr "Exporting Orders"

#: src/components/common/AttendeeTable/index.tsx:97
msgid "Failed to cancel attendee"
msgstr "Failed to cancel attendee"

#: src/components/modals/CancelOrderModal/index.tsx:33
msgid "Failed to cancel order"
msgstr "Failed to cancel order"

#: src/components/common/QuestionsTable/index.tsx:175
msgid "Failed to delete message. Please try again."
msgstr "Failed to delete message. Please try again."

#: src/components/common/OrdersTable/index.tsx:120
#: src/components/layouts/Checkout/index.tsx:58
msgid "Failed to download invoice. Please try again."
msgstr "Failed to download invoice. Please try again."

#: src/components/routes/event/attendees.tsx:48
msgid "Failed to export attendees"
msgstr "Failed to export attendees"

#: src/components/routes/event/attendees.tsx:39
#~ msgid "Failed to export attendees. Please try again."
#~ msgstr "Failed to export attendees. Please try again."

#: src/components/routes/event/orders.tsx:99
msgid "Failed to export orders"
msgstr "Failed to export orders"

#: src/components/routes/event/orders.tsx:88
#~ msgid "Failed to export orders. Please try again."
#~ msgstr "Failed to export orders. Please try again."

#: src/components/modals/EditCheckInListModal/index.tsx:79
msgid "Failed to load Check-In List"
msgstr "Failed to load Check-In List"

#: src/components/modals/EditWebhookModal/index.tsx:75
msgid "Failed to load Webhook"
msgstr "Failed to load Webhook"

#: src/components/common/AttendeeTable/index.tsx:51
#~ msgid "Failed to resend product email"
#~ msgstr "Failed to resend product email"

#: src/components/common/AttendeeTable/index.tsx:50
msgid "Failed to resend ticket email"
msgstr "Failed to resend ticket email"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:179
msgid "Failed to sort products"
msgstr "Failed to sort products"

#: src/mutations/useExportAnswers.ts:15
msgid "Failed to start export job"
msgstr "Failed to start export job"

#: src/components/common/QuestionAndAnswerList/index.tsx:102
msgid "Failed to update answer."
msgstr "Failed to update answer."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:64
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:69
msgid "Failed to upload image."
msgstr "Failed to upload image."

#: src/components/forms/TaxAndFeeForm/index.tsx:18
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Fee"
msgstr "Fee"

#: src/components/common/GlobalMenu/index.tsx:30
#~ msgid "Feedback"
#~ msgstr "Feedback"

#: src/components/forms/ProductForm/index.tsx:366
msgid "Fees"
msgstr "Fees"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:80
msgid "Fees are subject to change. You will be notified of any changes to your fee structure."
msgstr "Fees are subject to change. You will be notified of any changes to your fee structure."

#: src/components/routes/event/orders.tsx:121
msgid "Filter Orders"
msgstr "Filter Orders"

#: src/components/common/FilterModal/index.tsx:75
#: src/components/common/FilterModal/index.tsx:223
msgid "Filters"
msgstr "Filters"

#: src/components/common/FilterModal/index.tsx:223
msgid "Filters ({activeFilterCount})"
msgstr "Filters ({activeFilterCount})"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:206
msgid "First Invoice Number"
msgstr "First Invoice Number"

#: src/components/common/QuestionsTable/index.tsx:208
#: src/components/modals/CreateAttendeeModal/index.tsx:118
#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:144
#: src/components/routes/product-widget/CollectInformation/index.tsx:284
#: src/components/routes/product-widget/CollectInformation/index.tsx:395
msgid "First name"
msgstr "First name"

#: src/components/common/QuestionsTable/index.tsx:207
#: src/components/modals/EditUserModal/index.tsx:71
#: src/components/modals/InviteUserModal/index.tsx:57
#: src/components/routes/auth/AcceptInvitation/index.tsx:92
#: src/components/routes/auth/Register/index.tsx:83
#: src/components/routes/product-widget/CollectInformation/index.tsx:283
#: src/components/routes/product-widget/CollectInformation/index.tsx:394
#: src/components/routes/profile/ManageProfile/index.tsx:135
msgid "First Name"
msgstr "First Name"

#: src/components/routes/auth/AcceptInvitation/index.tsx:29
msgid "First name must be between 1 and 50 characters"
msgstr "First name must be between 1 and 50 characters"

#: src/components/common/QuestionsTable/index.tsx:349
msgid "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."
msgstr "First Name, Last Name, and Email Address are default questions and are always included in the checkout process."

#: src/components/common/QuestionsTable/index.tsx:327
#~ msgid "First Name, Last Name, and Email Address are default questions that are always included in the checkout process."
#~ msgstr "First Name, Last Name, and Email Address are default questions that are always included in the checkout process."

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:64
msgid "First Used"
msgstr "First Used"

#: src/components/forms/TaxAndFeeForm/index.tsx:33
msgid "Fixed"
msgstr "Fixed"

#: src/components/forms/PromoCodeForm/index.tsx:55
msgid "Fixed amount"
msgstr "Fixed amount"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:65
msgid "Fixed Fee:"
msgstr "Fixed Fee:"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:123
msgid "Flash is not available on this device"
msgstr "Flash is not available on this device"

#: src/components/layouts/AuthLayout/index.tsx:58
msgid "Flexible Ticketing"
msgstr "Flexible Ticketing"

#: src/components/routes/auth/Login/index.tsx:80
msgid "Forgot password?"
msgstr "Forgot password?"

#: src/components/common/AttendeeTicket/index.tsx:40
#: src/components/common/Currency/index.tsx:90
#: src/components/common/OrderSummary/index.tsx:46
#: src/components/common/OrderSummary/index.tsx:99
#: src/components/common/ProductsTable/SortableProduct/index.tsx:93
#: src/components/common/ProductsTable/SortableProduct/index.tsx:103
#: src/components/routes/product-widget/SelectProducts/Prices/Tiered/index.tsx:53
msgid "Free"
msgstr "Free"

#: src/components/forms/ProductForm/index.tsx:151
msgid "Free Product"
msgstr "Free Product"

#: src/components/forms/ProductForm/index.tsx:153
msgid "Free product, no payment information required"
msgstr "Free product, no payment information required"

#: src/components/forms/TicketForm/index.tsx:133
#~ msgid "Free Ticket"
#~ msgstr "Free Ticket"

#: src/components/forms/TicketForm/index.tsx:135
#~ msgid "Free ticket, no payment information required"
#~ msgstr "Free ticket, no payment information required"

#: src/components/common/LanguageSwitcher/index.tsx:20
msgid "French"
msgstr "French"

#: src/components/layouts/EventHomepage/index.tsx:34
#~ msgid "From"
#~ msgstr "From"

#: src/components/layouts/AuthLayout/index.tsx:88
msgid "Fully Integrated"
msgstr "Fully Integrated"

#: src/components/forms/ProductForm/index.tsx:136
msgid "General"
msgstr "General"

#: src/components/common/LanguageSwitcher/index.tsx:14
msgid "German"
msgstr "German"

#: src/components/layouts/AuthLayout/index.tsx:35
msgid "Get started for free, no subscription fees"
msgstr "Get started for free, no subscription fees"

#: src/components/layouts/EventHomepage/index.tsx:42
#~ msgid "Get Tickets"
#~ msgstr "Get Tickets"

#: src/components/routes/event/EventDashboard/index.tsx:125
msgid "Get your event ready"
msgstr "Get your event ready"

#: src/components/layouts/Event/index.tsx:88
msgid "Getting Started"
msgstr "Getting Started"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:23
#: src/components/routes/profile/ConfirmEmailChange/index.tsx:42
msgid "Go back to profile"
msgstr "Go back to profile"

#: src/components/routes/event/GettingStarted/index.tsx:22
#~ msgid "Go to Dashboard"
#~ msgstr "Go to Dashboard"

#: src/components/routes/product-widget/CollectInformation/index.tsx:239
msgid "Go to event homepage"
msgstr "Go to event homepage"

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:53
msgid "Go to Hi.Events"
msgstr "Go to Hi.Events"

#: src/components/common/ErrorDisplay/index.tsx:64
msgid "Go to home page"
msgstr "Go to home page"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:162
msgid "Go to Stripe Dashboard"
msgstr "Go to Stripe Dashboard"

#: src/components/common/AddEventToCalendarButton/index.tsx:128
msgid "Google Calendar"
msgstr "Google Calendar"

#: src/components/common/EventCard/index.tsx:133
msgid "gross sales"
msgstr "gross sales"

#: src/components/common/StatBoxes/index.tsx:39
msgid "Gross sales"
msgstr "Gross sales"

#: src/components/routes/event/EventDashboard/index.tsx:274
msgid "Gross Sales"
msgstr "Gross Sales"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:281
msgid "Guests"
msgstr "Guests"

#: src/components/routes/product-widget/SelectProducts/index.tsx:495
msgid "Have a promo code?"
msgstr "Have a promo code?"

#: src/components/forms/OrganizerForm/index.tsx:33
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/GlobalMenu/index.tsx:24
#~ msgid "Help & Support"
#~ msgstr "Help & Support"

#: src/components/common/WidgetEditor/index.tsx:295
msgid "Here is an example of how you can use the component in your application."
msgstr "Here is an example of how you can use the component in your application."

#: src/components/common/WidgetEditor/index.tsx:284
msgid "Here is the React component you can use to embed the widget in your application."
msgstr "Here is the React component you can use to embed the widget in your application."

#: src/components/routes/event/EventDashboard/index.tsx:101
msgid "Hi {0} 👋"
msgstr "Hi {0} 👋"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:43
msgid "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."
msgstr "Hi.Events charges platform fees to maintain and improve our services. These fees are automatically deducted from each transaction."

#: src/components/modals/CreateEventModal/index.tsx:130
#: src/components/modals/DuplicateEventModal/index.tsx:79
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:73
msgid "Hi.Events Conference {0}"
msgstr "Hi.Events Conference {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:117
msgid "Hi.Events Conference Center"
msgstr "Hi.Events Conference Center"

#: src/components/layouts/AuthLayout/index.tsx:131
msgid "hi.events logo"
msgstr "hi.events logo"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:79
msgid "Hidden from public view"
msgstr "Hidden from public view"

#: src/components/common/QuestionsTable/index.tsx:290
msgid "hidden question"
msgstr "hidden question"

#: src/components/common/QuestionsTable/index.tsx:291
msgid "hidden questions"
msgstr "hidden questions"

#: src/components/forms/QuestionForm/index.tsx:218
msgid "Hidden questions are only visible to the event organizer and not to the customer."
msgstr "Hidden questions are only visible to the event organizer and not to the customer."

#: src/components/forms/QuestionForm/index.tsx:187
#~ msgid "Hidden will not be shown to customers."
#~ msgstr "Hidden will not be shown to customers."

#: src/components/forms/ProductForm/index.tsx:344
#: src/components/routes/product-widget/SelectProducts/index.tsx:460
msgid "Hide"
msgstr "Hide"

#: src/components/common/AttendeeList/index.tsx:84
msgid "Hide Answers"
msgstr "Hide Answers"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:89
msgid "Hide getting started page"
msgstr "Hide getting started page"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Hide hidden questions"
msgstr "Hide hidden questions"

#: src/components/forms/ProductForm/index.tsx:399
msgid "Hide product after sale end date"
msgstr "Hide product after sale end date"

#: src/components/forms/ProductForm/index.tsx:397
msgid "Hide product before sale start date"
msgstr "Hide product before sale start date"

#: src/components/forms/ProductForm/index.tsx:412
msgid "Hide product unless user has applicable promo code"
msgstr "Hide product unless user has applicable promo code"

#: src/components/forms/ProductForm/index.tsx:405
msgid "Hide product when sold out"
msgstr "Hide product when sold out"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:90
msgid "Hide the getting started page from the sidebar"
msgstr "Hide the getting started page from the sidebar"

#: src/components/forms/ProductForm/index.tsx:417
msgid "Hide this product from customers"
msgstr "Hide this product from customers"

#: src/components/forms/QuestionForm/index.tsx:219
msgid "Hide this question"
msgstr "Hide this question"

#: src/components/forms/TicketForm/index.tsx:362
#~ msgid "Hide this ticket from customers"
#~ msgstr "Hide this ticket from customers"

#: src/components/forms/ProductForm/index.tsx:103
msgid "Hide this tier from users"
msgstr "Hide this tier from users"

#: src/components/forms/TicketForm/index.tsx:344
#~ msgid "Hide ticket after sale end date"
#~ msgstr "Hide ticket after sale end date"

#: src/components/forms/TicketForm/index.tsx:342
#~ msgid "Hide ticket before sale start date"
#~ msgstr "Hide ticket before sale start date"

#: src/components/forms/TicketForm/index.tsx:357
#~ msgid "Hide ticket unless user has applicable promo code"
#~ msgstr "Hide ticket unless user has applicable promo code"

#: src/components/forms/TicketForm/index.tsx:350
#~ msgid "Hide ticket when sold out"
#~ msgstr "Hide ticket when sold out"

#: src/components/forms/ProductForm/index.tsx:101
msgid "Hiding a product will prevent users from seeing it on the event page."
msgstr "Hiding a product will prevent users from seeing it on the event page."

#: src/components/forms/TicketForm/index.tsx:98
#~ msgid "Hiding a ticket will prevent users from seeing it on the event page."
#~ msgstr "Hiding a ticket will prevent users from seeing it on the event page."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:71
#~ msgid "Homepage & Checkout"
#~ msgstr "Homepage & Checkout"

#: src/components/routes/event/settings.tsx:133
#~ msgid "Homepage & Checkout Settings"
#~ msgstr "Homepage & Checkout Settings"

#: src/components/routes/event/HomepageDesigner/index.tsx:115
msgid "Homepage Design"
msgstr "Homepage Design"

#: src/components/layouts/Event/index.tsx:109
msgid "Homepage Designer"
msgstr "Homepage Designer"

#: src/components/routes/event/HomepageDesigner/index.tsx:174
msgid "Homepage Preview"
msgstr "Homepage Preview"

#: src/components/modals/ManageAttendeeModal/index.tsx:110
#: src/components/modals/ManageOrderModal/index.tsx:145
msgid "Homer"
msgstr "Homer"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:88
#~ msgid "How many minutes the customer has to complete their order"
#~ msgstr "How many minutes the customer has to complete their order"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
msgid "How many minutes the customer has to complete their order. We recommend at least 15 minutes"
msgstr "How many minutes the customer has to complete their order. We recommend at least 15 minutes"

#: src/components/forms/PromoCodeForm/index.tsx:84
msgid "How many times can this code be used?"
msgstr "How many times can this code be used?"

#: src/components/common/Editor/index.tsx:75
msgid "HTML character limit exceeded: {htmlLength}/{maxLength}"
msgstr "HTML character limit exceeded: {htmlLength}/{maxLength}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:160
msgid "https://example-maps-service.com/..."
msgstr "https://example-maps-service.com/..."

#: src/components/forms/WebhookForm/index.tsx:118
msgid "https://webhook-domain.com/webhook"
msgstr "https://webhook-domain.com/webhook"

#: src/components/routes/auth/AcceptInvitation/index.tsx:118
msgid "I agree to the <0>terms and conditions</0>"
msgstr "I agree to the <0>terms and conditions</0>"

#: src/components/routes/auth/AcceptInvitation/index.tsx:108
#~ msgid "I agree to the terms and conditions"
#~ msgstr "I agree to the terms and conditions"

#: src/components/routes/product-widget/Payment/index.tsx:106
msgid "I would like to pay using an offline method"
msgstr "I would like to pay using an offline method"

#: src/components/routes/product-widget/Payment/index.tsx:107
msgid "I would like to pay using an online method (credit card etc.)"
msgstr "I would like to pay using an online method (credit card etc.)"

#: src/components/routes/product-widget/SelectProducts/index.tsx:315
msgid "If a new tab did not open automatically, please click the button below to continue to checkout."
msgstr "If a new tab did not open automatically, please click the button below to continue to checkout."

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:236
#~ msgid "If a new tab did not open, please  <0>{0}.</0>"
#~ msgstr "If a new tab did not open, please  <0>{0}.</0>"

#: src/components/routes/product-widget/SelectProducts/index.tsx:284
#~ msgid "If a new tab did not open, please  <0><1>{0}</1>.</0>"
#~ msgstr "If a new tab did not open, please  <0><1>{0}</1>.</0>"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:149
#~ msgid "If blank, the address will be used to generate a Google map link"
#~ msgstr "If blank, the address will be used to generate a Google map link"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:158
msgid "If blank, the address will be used to generate a Google Mapa link"
msgstr "If blank, the address will be used to generate a Google Mapa link"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:158
msgid "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."
msgstr "If enabled, check-in staff can either mark attendees as checked in or mark the order as paid and check in the attendees. If disabled, attendees associated with unpaid orders cannot be checked in."

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:76
msgid "If enabled, the organizer will receive an email notification when a new order is placed"
msgstr "If enabled, the organizer will receive an email notification when a new order is placed"

#: src/components/routes/profile/ManageProfile/index.tsx:122
msgid "If you did not request this change, please immediately change your password."
msgstr "If you did not request this change, please immediately change your password."

#: src/components/routes/auth/ForgotPassword/index.tsx:40
msgid ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."
msgstr ""
"If you have an account with us, you will receive an email with instructions on how to reset your\n"
"password."

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:108
msgid "Image deleted successfully"
msgstr "Image deleted successfully"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:52
#~ msgid "Image dimensions must be at least 600px by 300px"
#~ msgstr "Image dimensions must be at least 600px by 300px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
#~ msgid "Image dimensions must be at least 900px by 450px"
#~ msgstr "Image dimensions must be at least 900px by 450px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
#~ msgid "Image dimensions must be between 3000px by 2000px. With a max height of 2000px and max width of 3000px"
#~ msgstr "Image dimensions must be between 3000px by 2000px. With a max height of 2000px and max width of 3000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:46
msgid "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"
msgstr "Image dimensions must be between 4000px by 4000px. With a max height of 4000px and max width of 4000px"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:49
msgid "Image must be less than 5MB"
msgstr "Image must be less than 5MB"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:63
msgid "Image uploaded successfully"
msgstr "Image uploaded successfully"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:111
msgid "Image URL"
msgstr "Image URL"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:52
msgid "Image width must be at least 900px and height at least 50px"
msgstr "Image width must be at least 900px and height at least 50px"

#: src/components/layouts/AuthLayout/index.tsx:53
msgid "In-depth Analytics"
msgstr "In-depth Analytics"

#: src/components/common/CheckInListList/index.tsx:116
#: src/components/forms/CapaciyAssigmentForm/index.tsx:25
#: src/components/modals/EditUserModal/index.tsx:111
msgid "Inactive"
msgstr "Inactive"

#: src/components/modals/EditUserModal/index.tsx:107
msgid "Inactive users cannot log in."
msgstr "Inactive users cannot log in."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:92
#~ msgid "Include connection details for your online event"
#~ msgstr "Include connection details for your online event"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:92
#~ msgid "Include connection details for your online event. These details will be after successful registration."
#~ msgstr "Include connection details for your online event. These details will be after successful registration."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:92
#~ msgid "Include connection details for your online event. These details will be shown after successful registration."
#~ msgstr "Include connection details for your online event. These details will be shown after successful registration."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee product page"
#~ msgstr "Include connection details for your online event. These details will be shown on the order summary page and attendee product page"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:99
#~ msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page"
#~ msgstr "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:102
msgid "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."
msgstr "Include connection details for your online event. These details will be shown on the order summary page and attendee ticket page."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:54
msgid "Include tax and fees in the price"
msgstr "Include tax and fees in the price"

#: src/components/common/CheckInListList/index.tsx:103
msgid "Includes {0} products"
msgstr "Includes {0} products"

#: src/components/common/CheckInListList/index.tsx:112
#~ msgid "Includes {0} tickets"
#~ msgstr "Includes {0} tickets"

#: src/components/common/CheckInListList/index.tsx:104
msgid "Includes 1 product"
msgstr "Includes 1 product"

#: src/components/common/CheckInListList/index.tsx:113
#~ msgid "Includes 1 ticket"
#~ msgstr "Includes 1 ticket"

#: src/components/common/MessageList/index.tsx:20
msgid "Individual attendees"
msgstr "Individual attendees"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:100
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:121
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:137
msgid "Insert Image"
msgstr "Insert Image"

#: src/components/layouts/Event/index.tsx:54
#~ msgid "Integrations"
#~ msgstr "Integrations"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation resent!"
msgstr "Invitation resent!"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invitation revoked!"
msgstr "Invitation revoked!"

#: src/components/modals/InviteUserModal/index.tsx:54
#: src/components/modals/InviteUserModal/index.tsx:74
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Invite User"
msgstr "Invite User"

#: src/components/common/OrdersTable/index.tsx:116
#: src/components/layouts/Checkout/index.tsx:54
msgid "Invoice downloaded successfully"
msgstr "Invoice downloaded successfully"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:252
msgid "Invoice Notes"
msgstr "Invoice Notes"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:197
msgid "Invoice Numbering"
msgstr "Invoice Numbering"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:177
msgid "Invoice Settings"
msgstr "Invoice Settings"

#: src/components/modals/RefundOrderModal/index.tsx:115
msgid "Issue refund"
msgstr "Issue refund"

#: src/components/common/LanguageSwitcher/index.tsx:22
msgid "Italian"
msgstr "Italian"

#: src/components/routes/product-widget/CollectInformation/index.tsx:386
msgid "Item"
msgstr "Item"

#: src/components/routes/auth/Register/index.tsx:84
msgid "John"
msgstr "John"

#: src/components/modals/CreateAttendeeModal/index.tsx:126
msgid "Johnson"
msgstr "Johnson"

#: src/components/modals/CreateEventModal/index.tsx:104
#~ msgid "KittenTech Conference {0}"
#~ msgstr "KittenTech Conference {0}"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:101
#~ msgid "KittenTech Conference Center"
#~ msgstr "KittenTech Conference Center"

#: src/components/forms/ProductForm/index.tsx:77
msgid "Label"
msgstr "Label"

#: src/components/common/AttendeeDetails/index.tsx:45
#: src/components/modals/CreateAttendeeModal/index.tsx:144
#: src/components/routes/profile/ManageProfile/index.tsx:172
msgid "Language"
msgstr "Language"

#: src/components/common/ReportTable/index.tsx:52
msgid "Last 12 months"
msgstr "Last 12 months"

#: src/components/common/ReportTable/index.tsx:47
msgid "Last 14 days"
msgstr "Last 14 days"

#: src/components/common/ReportTable/index.tsx:44
msgid "Last 24 hours"
msgstr "Last 24 hours"

#: src/components/common/ReportTable/index.tsx:48
msgid "Last 30 days"
msgstr "Last 30 days"

#: src/components/common/ReportTable/index.tsx:45
msgid "Last 48 hours"
msgstr "Last 48 hours"

#: src/components/common/ReportTable/index.tsx:50
msgid "Last 6 months"
msgstr "Last 6 months"

#: src/components/common/ReportTable/index.tsx:46
msgid "Last 7 days"
msgstr "Last 7 days"

#: src/components/common/ReportTable/index.tsx:49
msgid "Last 90 days"
msgstr "Last 90 days"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Last login"
msgstr "Last login"

#: src/components/modals/CreateAttendeeModal/index.tsx:125
#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:150
msgid "Last name"
msgstr "Last name"

#: src/components/common/QuestionsTable/index.tsx:212
#: src/components/common/QuestionsTable/index.tsx:213
#: src/components/modals/EditUserModal/index.tsx:72
#: src/components/modals/InviteUserModal/index.tsx:58
#: src/components/routes/auth/AcceptInvitation/index.tsx:94
#: src/components/routes/auth/Register/index.tsx:89
#: src/components/routes/product-widget/CollectInformation/index.tsx:289
#: src/components/routes/product-widget/CollectInformation/index.tsx:290
#: src/components/routes/product-widget/CollectInformation/index.tsx:400
#: src/components/routes/product-widget/CollectInformation/index.tsx:401
#: src/components/routes/profile/ManageProfile/index.tsx:137
msgid "Last Name"
msgstr "Last Name"

#: src/components/common/WebhookTable/index.tsx:239
msgid "Last Response"
msgstr "Last Response"

#: src/components/common/WebhookTable/index.tsx:240
msgid "Last Triggered"
msgstr "Last Triggered"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:70
msgid "Last Used"
msgstr "Last Used"

#: src/components/routes/event/EventDashboard/index.tsx:85
#~ msgid "Latest Orders"
#~ msgstr "Latest Orders"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:56
#~ msgid "Learn more about Stripe"
#~ msgstr "Learn more about Stripe"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:191
msgid "Leave blank to use the default word \"Invoice\""
msgstr "Leave blank to use the default word \"Invoice\""

#: src/components/routes/welcome/index.tsx:108
#~ msgid "Let's get started by creating your first event"
#~ msgstr "Let's get started by creating your first event"

#: src/components/routes/welcome/index.tsx:20
msgid "Let's get started by creating your first organizer"
msgstr "Let's get started by creating your first organizer"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
#~ msgid "Link color"
#~ msgstr "Link color"

#: src/components/routes/event/EventDashboard/index.tsx:194
msgid "Link your Stripe account to receive funds from ticket sales."
msgstr "Link your Stripe account to receive funds from ticket sales."

#: src/components/layouts/Event/index.tsx:190
msgid "Live"
msgstr "Live"

#: src/components/routes/event/Webhooks/index.tsx:21
msgid "Loading Webhooks"
msgstr "Loading Webhooks"

#: src/components/common/ReportTable/index.tsx:210
msgid "Loading..."
msgstr "Loading..."

#: src/components/routes/event/Settings/index.tsx:34
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:84
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:167
msgid "Location"
msgstr "Location"

#: src/components/routes/auth/Login/index.tsx:84
#: src/components/routes/auth/Register/index.tsx:71
msgid "Log in"
msgstr "Log in"

#: src/components/routes/auth/Login/index.tsx:84
msgid "Logging in"
msgstr "Logging in"

#: src/components/routes/auth/Register/index.tsx:70
#~ msgid "Login"
#~ msgstr "Login"

#: src/components/common/GlobalMenu/index.tsx:47
msgid "Logout"
msgstr "Logout"

#: src/components/common/WidgetEditor/index.tsx:331
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam placerat elementum..."

#: src/components/common/WidgetEditor/index.tsx:176
#~ msgid "Lorem ipsum..."
#~ msgstr "Lorem ipsum..."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:170
msgid "Make billing address mandatory during checkout"
msgstr "Make billing address mandatory during checkout"

#: src/components/routes/event/EventDashboard/index.tsx:172
#~ msgid "Make Event Live"
#~ msgstr "Make Event Live"

#: src/components/forms/QuestionForm/index.tsx:212
msgid "Make this question mandatory"
msgstr "Make this question mandatory"

#: src/components/routes/event/EventDashboard/index.tsx:148
msgid "Make your event live"
msgstr "Make your event live"

#: src/components/common/CapacityAssignmentList/index.tsx:143
#: src/components/common/CheckInListList/index.tsx:145
#: src/components/common/EventCard/index.tsx:180
#: src/components/common/OrdersTable/index.tsx:139
#: src/components/common/OrdersTable/index.tsx:154
#: src/components/common/ProductsTable/SortableProduct/index.tsx:256
#: src/components/common/PromoCodeTable/index.tsx:167
#: src/components/layouts/Event/index.tsx:97
msgid "Manage"
msgstr "Manage"

#: src/components/common/AttendeeTable/index.tsx:161
msgid "Manage attendee"
msgstr "Manage attendee"

#: src/components/routes/account/ManageAccount/index.tsx:32
#~ msgid "Manage billing information and view invoices"
#~ msgstr "Manage billing information and view invoices"

#: src/components/routes/account/ManageAccount/index.tsx:24
#~ msgid "Manage default settings for new events"
#~ msgstr "Manage default settings for new events"

#: src/components/common/EventCard/index.tsx:150
msgid "Manage event"
msgstr "Manage event"

#: src/components/routes/account/ManageAccount/index.tsx:16
#~ msgid "Manage general account settings"
#~ msgstr "Manage general account settings"

#: src/components/common/OrdersTable/index.tsx:156
msgid "Manage order"
msgstr "Manage order"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:99
msgid "Manage payment and invoicing settings for this event."
msgstr "Manage payment and invoicing settings for this event."

#: src/components/modals/CreateAttendeeModal/index.tsx:106
#~ msgid "Manage products"
#~ msgstr "Manage products"

#: src/components/routes/profile/ManageProfile/index.tsx:101
msgid "Manage Profile"
msgstr "Manage Profile"

#: src/components/routes/account/ManageAccount/index.tsx:20
#~ msgid "Manage taxes and fees which can be applied to tickets"
#~ msgstr "Manage taxes and fees which can be applied to tickets"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
msgid "Manage taxes and fees which can be applied to your products"
msgstr "Manage taxes and fees which can be applied to your products"

#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:17
#~ msgid "Manage taxes and fees which can be applied to your tickets"
#~ msgstr "Manage taxes and fees which can be applied to your tickets"

#: src/components/modals/CreateAttendeeModal/index.tsx:106
msgid "Manage tickets"
msgstr "Manage tickets"

#: src/components/routes/account/ManageAccount/index.tsx:28
#~ msgid "Manage users and their permissions"
#~ msgstr "Manage users and their permissions"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:54
msgid "Manage your account details and default settings"
msgstr "Manage your account details and default settings"

#: src/components/routes/account/ManageAccount/sections/BillingSettings/index.tsx:11
#~ msgid "Manage your billing and payment details"
#~ msgstr "Manage your billing and payment details"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:241
msgid "Manage your payment processing and view platform fees"
msgstr "Manage your payment processing and view platform fees"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:91
#~ msgid "Manage your Stripe payment details"
#~ msgstr "Manage your Stripe payment details"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Manage your users and their permissions"
msgstr "Manage your users and their permissions"

#: src/components/forms/QuestionForm/index.tsx:211
msgid "Mandatory questions must be answered before the customer can checkout."
msgstr "Mandatory questions must be answered before the customer can checkout."

#: src/components/common/AttendeeTable/index.tsx:67
msgid "Manually add an Attendee"
msgstr "Manually add an Attendee"

#: src/components/modals/CreateAttendeeModal/index.tsx:97
#: src/components/modals/CreateAttendeeModal/index.tsx:113
msgid "Manually Add Attendee"
msgstr "Manually Add Attendee"

#: src/components/modals/CreateAttendeeModal/index.tsx:148
#~ msgid "Manually adding an attendee will adjust ticket quantity."
#~ msgstr "Manually adding an attendee will adjust ticket quantity."

#: src/components/common/OrdersTable/index.tsx:167
msgid "Mark as paid"
msgstr "Mark as paid"

#: src/components/layouts/AuthLayout/index.tsx:83
msgid "Match Your Brand"
msgstr "Match Your Brand"

#: src/components/forms/ProductForm/index.tsx:383
msgid "Maximum Per Order"
msgstr "Maximum Per Order"

#: src/components/common/AttendeeTable/index.tsx:166
msgid "Message attendee"
msgstr "Message attendee"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:273
msgid "Message Attendees"
msgstr "Message Attendees"

#: src/components/modals/SendMessageModal/index.tsx:165
#~ msgid "Message attendees with specific products"
#~ msgstr "Message attendees with specific products"

#: src/components/modals/SendMessageModal/index.tsx:206
msgid "Message attendees with specific tickets"
msgstr "Message attendees with specific tickets"

#: src/components/layouts/AuthLayout/index.tsx:74
msgid "Message attendees, manage orders, and handle refunds all in one place"
msgstr "Message attendees, manage orders, and handle refunds all in one place"

#: src/components/common/OrdersTable/index.tsx:158
msgid "Message buyer"
msgstr "Message buyer"

#: src/components/modals/SendMessageModal/index.tsx:250
msgid "Message Content"
msgstr "Message Content"

#: src/components/modals/SendMessageModal/index.tsx:71
msgid "Message individual attendees"
msgstr "Message individual attendees"

#: src/components/modals/SendMessageModal/index.tsx:218
msgid "Message order owners with specific products"
msgstr "Message order owners with specific products"

#: src/components/modals/SendMessageModal/index.tsx:118
msgid "Message Sent"
msgstr "Message Sent"

#: src/components/layouts/Event/index.tsx:105
#: src/components/routes/event/messages.tsx:31
msgid "Messages"
msgstr "Messages"

#: src/components/forms/ProductForm/index.tsx:381
msgid "Minimum Per Order"
msgstr "Minimum Per Order"

#: src/components/forms/ProductForm/index.tsx:285
msgid "Minimum Price"
msgstr "Minimum Price"

#: src/components/routes/event/Settings/index.tsx:58
msgid "Miscellaneous"
msgstr "Miscellaneous"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:69
msgid "Miscellaneous Settings"
msgstr "Miscellaneous Settings"

#: src/components/layouts/AuthLayout/index.tsx:63
msgid "Mobile Check-in"
msgstr "Mobile Check-in"

#: src/components/forms/QuestionForm/index.tsx:106
msgid "Multi line text box"
msgstr "Multi line text box"

#: src/components/forms/TicketForm/index.tsx:54
#~ msgid "Multiple price options, users can choose which to pay"
#~ msgstr "Multiple price options, users can choose which to pay"

#: src/components/forms/ProductForm/index.tsx:165
msgid "Multiple price options. Perfect for early bird products etc."
msgstr "Multiple price options. Perfect for early bird products etc."

#: src/components/forms/TicketForm/index.tsx:147
#~ msgid "Multiple price options. Perfect for early bird tickets etc."
#~ msgstr "Multiple price options. Perfect for early bird tickets etc."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:71
msgid "My amazing event description..."
msgstr "My amazing event description..."

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:65
msgid "My amazing event title..."
msgstr "My amazing event title..."

#: src/components/common/GlobalMenu/index.tsx:27
msgid "My Profile"
msgstr "My Profile"

#: src/components/common/QuestionAndAnswerList/index.tsx:178
msgid "N/A"
msgstr "N/A"

#: src/components/common/WidgetEditor/index.tsx:354
msgid "Nam placerat elementum..."
msgstr "Nam placerat elementum..."

#: src/components/common/AttendeeDetails/index.tsx:13
#: src/components/common/AttendeeTable/index.tsx:108
#: src/components/common/OrderDetails/index.tsx:23
#: src/components/forms/CapaciyAssigmentForm/index.tsx:37
#: src/components/forms/CheckInListForm/index.tsx:20
#: src/components/forms/ProductForm/index.tsx:253
#: src/components/forms/TaxAndFeeForm/index.tsx:61
#: src/components/modals/CreateEventModal/index.tsx:129
#: src/components/modals/DuplicateEventModal/index.tsx:78
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:63
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:72
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:107
#: src/components/routes/welcome/index.tsx:83
msgid "Name"
msgstr "Name"

#: src/components/modals/CreateEventModal/index.tsx:38
msgid "Name should be less than 150 characters"
msgstr "Name should be less than 150 characters"

#: src/components/common/QuestionAndAnswerList/index.tsx:181
#: src/components/common/QuestionAndAnswerList/index.tsx:289
msgid "Navigate to Attendee"
msgstr "Navigate to Attendee"

#: src/components/common/PromoCodeTable/index.tsx:158
#: src/components/common/WebhookTable/index.tsx:266
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Never"
msgstr "Never"

#: src/components/routes/auth/AcceptInvitation/index.tsx:111
#: src/components/routes/auth/ResetPassword/index.tsx:59
#: src/components/routes/profile/ManageProfile/index.tsx:195
msgid "New Password"
msgstr "New Password"

#: src/components/routes/event/settings.tsx:108
#: src/components/routes/event/settings.tsx:113
#~ msgid "New York"
#~ msgstr "New York"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "No"
msgstr "No"

#: src/components/common/QuestionAndAnswerList/index.tsx:104
#~ msgid "No {0} available."
#~ msgstr "No {0} available."

#: src/components/routes/event/Webhooks/index.tsx:22
msgid "No Active Webhooks"
msgstr "No Active Webhooks"

#: src/components/common/NoEventsBlankSlate/index.tsx:21
msgid "No archived events to show."
msgstr "No archived events to show."

#: src/components/common/AttendeeList/index.tsx:23
msgid "No attendees found for this order."
msgstr "No attendees found for this order."

#: src/components/modals/ManageOrderModal/index.tsx:132
msgid "No attendees have been added to this order."
msgstr "No attendees have been added to this order."

#: src/components/common/AttendeeTable/index.tsx:56
msgid "No Attendees to show"
msgstr "No Attendees to show"

#: src/components/forms/CheckInListForm/index.tsx:48
msgid "No attendees will be able to check in before this date using this list"
msgstr "No attendees will be able to check in before this date using this list"

#: src/components/common/CapacityAssignmentList/index.tsx:42
msgid "No Capacity Assignments"
msgstr "No Capacity Assignments"

#: src/components/common/CheckInListList/index.tsx:44
msgid "No Check-In Lists"
msgstr "No Check-In Lists"

#: src/components/layouts/AuthLayout/index.tsx:34
msgid "No Credit Card Required"
msgstr "No Credit Card Required"

#: src/components/common/ReportTable/index.tsx:218
msgid "No data available"
msgstr "No data available"

#: src/components/common/ReportTable/index.tsx:214
msgid "No data to show. Please select a date range"
msgstr "No data to show. Please select a date range"

#: src/components/forms/PromoCodeForm/index.tsx:47
msgid "No Discount"
msgstr "No Discount"

#: src/components/common/NoEventsBlankSlate/index.tsx:20
msgid "No ended events to show."
msgstr "No ended events to show."

#: src/components/routes/organizer/OrganizerDashboard/index.tsx:88
#~ msgid "No events for this organizer"
#~ msgstr "No events for this organizer"

#: src/components/common/NoEventsBlankSlate/index.tsx:14
msgid "No events to show"
msgstr "No events to show"

#: src/components/common/FilterModal/index.tsx:230
msgid "No filters available"
msgstr "No filters available"

#: src/components/modals/WebhookLogsModal/index.tsx:177
msgid "No logs found"
msgstr "No logs found"

#: src/components/common/MessageList/index.tsx:69
msgid "No messages to show"
msgstr "No messages to show"

#: src/components/common/OrdersTable/index.tsx:63
msgid "No orders to show"
msgstr "No orders to show"

#: src/components/routes/product-widget/Payment/index.tsx:76
msgid "No payment methods are currently available. Please contact the event organizer for assistance."
msgstr "No payment methods are currently available. Please contact the event organizer for assistance."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:39
msgid "No Payment Required"
msgstr "No Payment Required"

#: src/components/modals/ManageAttendeeModal/index.tsx:177
msgid "No product associated with this attendee."
msgstr "No product associated with this attendee."

#: src/components/common/ProductSelector/index.tsx:33
msgid "No products available for selection"
msgstr "No products available for selection"

#: src/components/modals/CreateProductCategoryModal/index.tsx:22
msgid "No products available in this category."
msgstr "No products available in this category."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:39
msgid "No Products Yet"
msgstr "No Products Yet"

#: src/components/common/PromoCodeTable/index.tsx:46
msgid "No Promo Codes to show"
msgstr "No Promo Codes to show"

#: src/components/modals/ManageAttendeeModal/index.tsx:193
msgid "No questions answered by this attendee."
msgstr "No questions answered by this attendee."

#: src/components/modals/ViewAttendeeModal/index.tsx:75
#~ msgid "No questions have been answered by this attendee."
#~ msgstr "No questions have been answered by this attendee."

#: src/components/modals/ManageOrderModal/index.tsx:119
msgid "No questions have been asked for this order."
msgstr "No questions have been asked for this order."

#: src/components/common/WebhookTable/index.tsx:174
msgid "No response"
msgstr "No response"

#: src/components/common/WebhookTable/index.tsx:141
msgid "No responses yet"
msgstr "No responses yet"

#: src/components/common/NoResultsSplash/index.tsx:24
msgid "No results"
msgstr "No results"

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:20
msgid "No Search Results"
msgstr "No Search Results"

#: src/components/common/NoResultsSplash/index.tsx:29
msgid "No search results."
msgstr "No search results."

#: src/components/common/TaxAndFeeList/index.tsx:101
#~ msgid "No Taxes or Fees have been added yet."
#~ msgstr "No Taxes or Fees have been added yet."

#: src/components/common/TaxAndFeeList/index.tsx:101
msgid "No Taxes or Fees have been added."
msgstr "No Taxes or Fees have been added."

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:197
#~ msgid "No tickets available"
#~ msgstr "No tickets available"

#: src/components/common/TicketsTable/index.tsx:65
#~ msgid "No tickets to show"
#~ msgstr "No tickets to show"

#: src/components/modals/WebhookLogsModal/index.tsx:180
msgid "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."
msgstr "No webhook events have been recorded for this endpoint yet. Events will appear here once they are triggered."

#: src/components/common/WebhookTable/index.tsx:201
msgid "No Webhooks"
msgstr "No Webhooks"

#: src/components/common/PromoCodeTable/index.tsx:82
msgid "None"
msgstr "None"

#: src/components/common/ProductPriceAvailability/index.tsx:27
#: src/components/common/ProductPriceAvailability/index.tsx:48
msgid "Not available"
msgstr "Not available"

#: src/components/common/AttendeeCheckInTable/index.tsx:125
#~ msgid "Not Checked In"
#~ msgstr "Not Checked In"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "Not On Sale"
msgstr "Not On Sale"

#: src/components/modals/ManageAttendeeModal/index.tsx:130
#: src/components/modals/ManageOrderModal/index.tsx:164
msgid "Notes"
msgstr "Notes"

#: src/components/common/ReportTable/index.tsx:240
msgid "Nothing to show yet"
msgstr "Nothing to show yet"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:72
msgid "Notification Settings"
msgstr "Notification Settings"

#: src/components/modals/RefundOrderModal/index.tsx:108
msgid "Notify buyer of refund"
msgstr "Notify buyer of refund"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:75
msgid "Notify organizer of new orders"
msgstr "Notify organizer of new orders"

#: src/components/routes/welcome/index.tsx:72
msgid "Now let's create your first event"
msgstr "Now let's create your first event"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:217
msgid "Number of days allowed for payment (leave blank to omit payment terms from invoices)"
msgstr "Number of days allowed for payment (leave blank to omit payment terms from invoices)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:199
msgid "Number Prefix"
msgstr "Number Prefix"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:145
msgid "Offline orders are not reflected in event statistics until the order is marked as paid."
msgstr "Offline orders are not reflected in event statistics until the order is marked as paid."

#: src/components/routes/product-widget/Payment/index.tsx:66
msgid "Offline payment failed. Please try again or contact the event organizer."
msgstr "Offline payment failed. Please try again or contact the event organizer."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:151
msgid "Offline Payment Instructions"
msgstr "Offline Payment Instructions"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:90
msgid "Offline Payments"
msgstr "Offline Payments"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:139
msgid "Offline Payments Information"
msgstr "Offline Payments Information"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:134
msgid "Offline Payments Settings"
msgstr "Offline Payments Settings"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:71
msgid "On sale"
msgstr "On sale"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:82
#: src/components/common/ProductsTable/SortableProduct/index.tsx:227
msgid "On Sale"
msgstr "On Sale"

#: src/components/common/NoEventsBlankSlate/index.tsx:19
msgid "Once you create an event, you'll see it here."
msgstr "Once you create an event, you'll see it here."

#: src/components/common/ReportTable/index.tsx:245
msgid "Once you start collecting data, you'll see it here."
msgstr "Once you start collecting data, you'll see it here."

#: src/components/routes/event/GettingStarted/index.tsx:179
msgid "Once you're ready, set your event live and start selling products."
msgstr "Once you're ready, set your event live and start selling products."

#: src/components/routes/event/GettingStarted/index.tsx:108
#~ msgid "Once you're ready, set your event live and start selling tickets."
#~ msgstr "Once you're ready, set your event live and start selling tickets."

#: src/components/common/EventStatusBadge/index.tsx:18
msgid "Ongoing"
msgstr "Ongoing"

#: src/components/common/EventCard/index.tsx:118
msgid "Online event"
msgstr "Online event"

#: src/components/common/OnlineEventDetails/index.tsx:9
msgid "Online Event Details"
msgstr "Online Event Details"

#: src/components/modals/SendMessageModal/index.tsx:279
msgid ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."
msgstr ""
"Only important emails, which are directly related to this event, should be sent using this form.\n"
"Any misuse, including sending promotional emails, will lead to an immediate account ban."

#: src/components/modals/SendMessageModal/index.tsx:226
msgid "Only send to orders with these statuses"
msgstr "Only send to orders with these statuses"

#: src/components/common/CheckInListList/index.tsx:167
msgid "Open Check-In Page"
msgstr "Open Check-In Page"

#: src/components/layouts/Event/index.tsx:288
msgid "Open sidebar"
msgstr "Open sidebar"

#: src/components/forms/QuestionForm/index.tsx:45
msgid "Option {i}"
msgstr "Option {i}"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:253
msgid "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"
msgstr "Optional additional information to appear on all invoices (e.g., payment terms, late payment fees, return policy)"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:200
msgid "Optional prefix for invoice numbers (e.g., INV-)"
msgstr "Optional prefix for invoice numbers (e.g., INV-)"

#: src/components/forms/QuestionForm/index.tsx:28
msgid "Options"
msgstr "Options"

#: src/components/modals/CreateEventModal/index.tsx:118
msgid "or"
msgstr "or"

#: src/components/common/AttendeeTable/index.tsx:110
msgid "Order"
msgstr "Order"

#: src/components/common/OrdersTable/index.tsx:212
#~ msgid "Order #"
#~ msgstr "Order #"

#: src/components/forms/WebhookForm/index.tsx:76
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:63
msgid "Order Cancelled"
msgstr "Order Cancelled"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:62
msgid "Order Completed"
msgstr "Order Completed"

#: src/components/forms/WebhookForm/index.tsx:52
msgid "Order Created"
msgstr "Order Created"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:122
msgid "Order Date"
msgstr "Order Date"

#: src/components/modals/ManageAttendeeModal/index.tsx:166
#: src/components/modals/ManageOrderModal/index.tsx:86
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:266
msgid "Order Details"
msgstr "Order Details"

#: src/components/modals/ViewOrderModal/index.tsx:30
#~ msgid "Order Details {0}"
#~ msgstr "Order Details {0}"

#: src/components/modals/CancelOrderModal/index.tsx:29
msgid "Order has been canceled and the order owner has been notified."
msgstr "Order has been canceled and the order owner has been notified."

#: src/components/common/OrdersTable/index.tsx:79
msgid "Order marked as paid"
msgstr "Order marked as paid"

#: src/components/forms/WebhookForm/index.tsx:64
msgid "Order Marked as Paid"
msgstr "Order Marked as Paid"

#: src/components/modals/ManageOrderModal/index.tsx:92
msgid "Order Notes"
msgstr "Order Notes"

#: src/components/common/MessageList/index.tsx:23
msgid "Order owner"
msgstr "Order owner"

#: src/components/modals/SendMessageModal/index.tsx:191
msgid "Order owners with a specific product"
msgstr "Order owners with a specific product"

#: src/components/common/MessageList/index.tsx:19
msgid "Order owners with products"
msgstr "Order owners with products"

#: src/components/common/QuestionsTable/index.tsx:313
#: src/components/common/QuestionsTable/index.tsx:355
msgid "Order questions"
msgstr "Order questions"

#: src/components/common/QuestionsTable/index.tsx:326
#~ msgid "Order questions are asked once per order. By default, people are asked for their first name, last name, and email address."
#~ msgstr "Order questions are asked once per order. By default, people are asked for their first name, last name, and email address."

#: src/components/common/QuestionsTable/index.tsx:226
#~ msgid "Order questions are asked once per order. By default, we ask for the first name, last name, and email address."
#~ msgstr "Order questions are asked once per order. By default, we ask for the first name, last name, and email address."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:112
msgid "Order Reference"
msgstr "Order Reference"

#: src/components/forms/WebhookForm/index.tsx:70
msgid "Order Refunded"
msgstr "Order Refunded"

#: src/components/routes/event/orders.tsx:45
msgid "Order Status"
msgstr "Order Status"

#: src/components/modals/SendMessageModal/index.tsx:228
msgid "Order statuses"
msgstr "Order statuses"

#: src/components/layouts/Checkout/CheckoutSidebar/index.tsx:28
#: src/components/modals/ManageOrderModal/index.tsx:105
msgid "Order Summary"
msgstr "Order Summary"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:90
msgid "Order timeout"
msgstr "Order timeout"

#: src/components/forms/WebhookForm/index.tsx:58
msgid "Order Updated"
msgstr "Order Updated"

#: src/components/layouts/Event/index.tsx:100
#: src/components/routes/event/orders.tsx:113
msgid "Orders"
msgstr "Orders"

#: src/components/common/StatBoxes/index.tsx:51
#: src/components/routes/event/EventDashboard/index.tsx:105
#~ msgid "Orders Created"
#~ msgstr "Orders Created"

#: src/components/routes/event/orders.tsx:94
msgid "Orders Exported"
msgstr "Orders Exported"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:235
msgid "Organization Address"
msgstr "Organization Address"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:226
msgid "Organization Details"
msgstr "Organization Details"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:228
msgid "Organization Name"
msgstr "Organization Name"

#: src/components/modals/EditUserModal/index.tsx:55
#: src/components/modals/InviteUserModal/index.tsx:47
#: src/components/routes/events/Dashboard/index.tsx:113
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:186
msgid "Organizer"
msgstr "Organizer"

#: src/components/routes/event/settings.tsx:231
#~ msgid "Organizer Details"
#~ msgstr "Organizer Details"

#: src/components/modals/CreateEventModal/index.tsx:41
msgid "Organizer is required"
msgstr "Organizer is required"

#: src/components/forms/OrganizerForm/index.tsx:27
msgid "Organizer Name"
msgstr "Organizer Name"

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
msgid "Organizers can only manage events and products. They cannot manage users, account settings or billing information."
msgstr "Organizers can only manage events and products. They cannot manage users, account settings or billing information."

#: src/components/modals/EditUserModal/index.tsx:57
#: src/components/modals/InviteUserModal/index.tsx:49
#~ msgid "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."
#~ msgstr "Organizers can only manage events and tickets. They cannot manage users, account settings or billing information."

#: src/components/layouts/Event/index.tsx:87
msgid "Overview"
msgstr "Overview"

#: src/components/common/WidgetEditor/index.tsx:223
msgid "Padding"
msgstr "Padding"

#: src/components/routes/event/HomepageDesigner/index.tsx:150
msgid "Page background color"
msgstr "Page background color"

#: src/components/common/ErrorDisplay/index.tsx:13
msgid "Page not found"
msgstr "Page not found"

#: src/components/common/StatBoxes/index.tsx:45
msgid "Page views"
msgstr "Page views"

#: src/components/forms/ProductForm/index.tsx:358
msgid "page."
msgstr "page."

#: src/components/modals/CreateAttendeeModal/index.tsx:180
msgid "paid"
msgstr "paid"

#: src/components/forms/ProductForm/index.tsx:145
msgid "Paid Product"
msgstr "Paid Product"

#: src/components/forms/TicketForm/index.tsx:127
#~ msgid "Paid Ticket"
#~ msgstr "Paid Ticket"

#: src/components/routes/event/orders.tsx:30
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:54
msgid "Partially Refunded"
msgstr "Partially Refunded"

#: src/components/routes/auth/Login/index.tsx:73
#: src/components/routes/auth/Register/index.tsx:106
#: src/components/routes/profile/ManageProfile/index.tsx:109
msgid "Password"
msgstr "Password"

#: src/components/routes/auth/AcceptInvitation/index.tsx:30
msgid "Password must be a minimum  of 8 characters"
msgstr "Password must be a minimum  of 8 characters"

#: src/components/routes/auth/Register/index.tsx:36
msgid "Password must be at least 8 characters"
msgstr "Password must be at least 8 characters"

#: src/components/routes/auth/ResetPassword/index.tsx:46
msgid "Password reset successfully. Please login with your new password."
msgstr "Password reset successfully. Please login with your new password."

#: src/components/routes/auth/AcceptInvitation/index.tsx:31
#: src/components/routes/auth/Register/index.tsx:37
msgid "Passwords are not the same"
msgstr "Passwords are not the same"

#: src/components/common/WidgetEditor/index.tsx:269
msgid "Paste this where you want the widget to appear."
msgstr "Paste this where you want the widget to appear."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:104
msgid "Paste URL"
msgstr "Paste URL"

#: src/components/modals/CreateAttendeeModal/index.tsx:119
msgid "Patrick"
msgstr "Patrick"

#: src/components/modals/CreateAttendeeModal/index.tsx:133
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: src/components/common/TicketsTable/index.tsx:138
#~ msgid "Pause Ticket"
#~ msgstr "Pause Ticket"

#: src/components/forms/WebhookForm/index.tsx:25
msgid "Paused"
msgstr "Paused"

#: src/components/forms/StripeCheckoutForm/index.tsx:123
msgid "Payment"
msgstr "Payment"

#: src/components/routes/event/Settings/index.tsx:64
msgid "Payment & Invoicing"
msgstr "Payment & Invoicing"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:98
msgid "Payment & Invoicing Settings"
msgstr "Payment & Invoicing Settings"

#: src/components/routes/account/ManageAccount/index.tsx:38
msgid "Payment & Plan"
msgstr "Payment & Plan"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:216
msgid "Payment Due Period"
msgstr "Payment Due Period"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:41
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:64
msgid "Payment Failed"
msgstr "Payment Failed"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:228
#: src/components/routes/product-widget/Payment/PaymentMethods/Offline/index.tsx:14
msgid "Payment Instructions"
msgstr "Payment Instructions"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:105
msgid "Payment Methods"
msgstr "Payment Methods"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:138
msgid "Payment Processing"
msgstr "Payment Processing"

#: src/components/common/OrderDetails/index.tsx:76
msgid "Payment provider"
msgstr "Payment provider"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:42
msgid "Payment Received"
msgstr "Payment Received"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:240
msgid "Payment Settings"
msgstr "Payment Settings"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:135
msgid "Payment Status"
msgstr "Payment Status"

#: src/components/forms/StripeCheckoutForm/index.tsx:60
msgid "Payment succeeded!"
msgstr "Payment succeeded!"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:214
msgid "Payment Terms"
msgstr "Payment Terms"

#: src/components/common/AttendeeTicket/index.tsx:70
#~ msgid "PDF"
#~ msgstr "PDF"

#: src/components/forms/PromoCodeForm/index.tsx:53
#~ msgid "percent"
#~ msgstr "percent"

#: src/components/forms/PromoCodeForm/index.tsx:51
#: src/components/forms/TaxAndFeeForm/index.tsx:27
msgid "Percentage"
msgstr "Percentage"

#: src/components/forms/TaxAndFeeForm/index.tsx:71
msgid "Percentage Amount"
msgstr "Percentage Amount"

#: src/components/routes/product-widget/Payment/index.tsx:122
msgid "Place Order"
msgstr "Place Order"

#: src/components/common/WidgetEditor/index.tsx:257
msgid "Place this in the <head> of your website."
msgstr "Place this in the <head> of your website."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:40
msgid "Platform Fees"
msgstr "Platform Fees"

#: src/components/forms/QuestionForm/index.tsx:31
msgid "Please add at least one option"
msgstr "Please add at least one option"

#: src/hooks/useFormErrorResponseHandler.tsx:18
msgid "Please check the provided information is correct"
msgstr "Please check the provided information is correct"

#: src/components/routes/auth/Login/index.tsx:41
msgid "Please check your email and password and try again"
msgstr "Please check your email and password and try again"

#: src/components/routes/auth/AcceptInvitation/index.tsx:32
#: src/components/routes/auth/Register/index.tsx:38
msgid "Please check your email is valid"
msgstr "Please check your email is valid"

#: src/components/routes/profile/ManageProfile/index.tsx:152
msgid "Please check your email to confirm your email address"
msgstr "Please check your email to confirm your email address"

#: src/components/routes/auth/AcceptInvitation/index.tsx:86
msgid "Please complete the form below to accept your invitation"
msgstr "Please complete the form below to accept your invitation"

#: src/components/routes/product-widget/SelectProducts/index.tsx:306
msgid "Please continue in the new tab"
msgstr "Please continue in the new tab"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:242
#~ msgid "Please continue your order in the new tab"
#~ msgstr "Please continue your order in the new tab"

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:47
msgid "Please create a product"
msgstr "Please create a product"

#: src/components/modals/CreateCheckInListModal/index.tsx:51
msgid "Please create a ticket"
msgstr "Please create a ticket"

#: src/components/modals/SendMessageModal/index.tsx:207
#~ msgid ""
#~ "Please ensure you only send emails directly related to the order. Promotional emails\n"
#~ "should not be sent using this form."
#~ msgstr ""
#~ "Please ensure you only send emails directly related to the order. Promotional emails\n"
#~ "should not be sent using this form."

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:45
msgid "Please enter a valid image URL that points to an image."
msgstr "Please enter a valid image URL that points to an image."

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:178
#~ msgid "Please enter a valid promo code"
#~ msgstr "Please enter a valid promo code"

#: src/components/modals/CreateWebhookModal/index.tsx:30
msgid "Please enter a valid URL"
msgstr "Please enter a valid URL"

#: src/components/routes/auth/ResetPassword/index.tsx:55
msgid "Please enter your new password"
msgstr "Please enter your new password"

#: src/components/modals/CancelOrderModal/index.tsx:52
msgid "Please Note"
msgstr "Please Note"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:34
msgid "Please provide an image."
msgstr "Please provide an image."

#: src/components/common/TicketsTable/index.tsx:84
#~ msgid "Please remove filters and set sorting to \"Homepage order\" to enable sorting"
#~ msgstr "Please remove filters and set sorting to \"Homepage order\" to enable sorting"

#: src/components/layouts/Checkout/index.tsx:159
msgid "Please return to the event page to start over."
msgstr "Please return to the event page to start over."

#: src/components/modals/SendMessageModal/index.tsx:195
msgid "Please select"
msgstr "Please select"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:53
msgid "Please select an image."
msgstr "Please select an image."

#: src/components/routes/product-widget/SelectProducts/index.tsx:237
msgid "Please select at least one product"
msgstr "Please select at least one product"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:227
#~ msgid "Please select at least one ticket"
#~ msgstr "Please select at least one ticket"

#: src/components/routes/event/attendees.tsx:49
#: src/components/routes/event/orders.tsx:100
msgid "Please try again."
msgstr "Please try again."

#: src/components/routes/profile/ManageProfile/index.tsx:142
msgid "Please verify your email address to access all features"
msgstr "Please verify your email address to access all features"

#: src/components/routes/event/attendees.tsx:40
msgid "Please wait while we prepare your attendees for export..."
msgstr "Please wait while we prepare your attendees for export..."

#: src/components/common/OrdersTable/index.tsx:112
#: src/components/layouts/Checkout/index.tsx:50
msgid "Please wait while we prepare your invoice..."
msgstr "Please wait while we prepare your invoice..."

#: src/components/routes/event/orders.tsx:91
msgid "Please wait while we prepare your orders for export..."
msgstr "Please wait while we prepare your orders for export..."

#: src/components/common/LanguageSwitcher/index.tsx:26
msgid "Portuguese"
msgstr "Portuguese"

#: src/locales.ts:47
#~ msgid "Portuguese (Brazil)"
#~ msgstr "Portuguese (Brazil)"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:74
msgid "Post Checkout message"
msgstr "Post Checkout message"

#: src/components/common/PoweredByFooter/index.tsx:28
msgid "Powered by"
msgstr "Powered by"

#: src/components/common/EventDocumentHead/index.tsx:12
#~ msgid "Powered By"
#~ msgstr "Powered By"

#: src/components/layouts/EventHomepage/Footer/index.tsx:7
#~ msgid "Powered by <0>Hi.Events</0> 👋"
#~ msgstr "Powered by <0>Hi.Events</0> 👋"

#: src/components/common/EventDocumentHead/index.tsx:14
#~ msgid "Powered By Hi.Events"
#~ msgstr "Powered By Hi.Events"

#: src/components/forms/StripeCheckoutForm/index.tsx:137
#~ msgid "Powered by Stripe"
#~ msgstr "Powered by Stripe"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:67
msgid "Pre Checkout message"
msgstr "Pre Checkout message"

#: src/components/common/QuestionsTable/index.tsx:347
msgid "Preview"
msgstr "Preview"

#: src/components/layouts/Event/index.tsx:205
#: src/components/layouts/Event/index.tsx:209
msgid "Preview Event page"
msgstr "Preview Event page"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:236
#: src/components/forms/ProductForm/index.tsx:73
#: src/components/forms/ProductForm/index.tsx:285
msgid "Price"
msgstr "Price"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:78
msgid "Price display mode"
msgstr "Price display mode"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:88
msgid "Price not set"
msgstr "Price not set"

#: src/components/forms/ProductForm/index.tsx:322
msgid "Price tiers"
msgstr "Price tiers"

#: src/components/forms/TicketForm/index.tsx:191
#~ msgid "Price Tiers"
#~ msgstr "Price Tiers"

#: src/components/forms/ProductForm/index.tsx:228
msgid "Price Type"
msgstr "Price Type"

#: src/components/common/WidgetEditor/index.tsx:182
msgid "Primary Color"
msgstr "Primary Color"

#: src/components/routes/event/HomepageDesigner/index.tsx:156
msgid "Primary Colour"
msgstr "Primary Colour"

#: src/components/common/WidgetEditor/index.tsx:190
#: src/components/routes/event/HomepageDesigner/index.tsx:158
msgid "Primary Text Color"
msgstr "Primary Text Color"

#: src/components/common/AttendeeTicket/index.tsx:81
msgid "Print"
msgstr "Print"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:288
msgid "Print All Tickets"
msgstr "Print All Tickets"

#: src/components/layouts/Checkout/index.tsx:114
msgid "Print Tickets"
msgstr "Print Tickets"

#: src/components/common/AttendeeTable/index.tsx:190
#~ msgid "product"
#~ msgstr "product"

#: src/components/common/AttendeeDetails/index.tsx:37
#: src/components/modals/ManageAttendeeModal/index.tsx:119
msgid "Product"
msgstr "Product"

#: src/components/forms/ProductForm/index.tsx:266
msgid "Product Category"
msgstr "Product Category"

#: src/components/modals/EditProductCategoryModal/index.tsx:53
msgid "Product category updated successfully."
msgstr "Product category updated successfully."

#: src/components/forms/WebhookForm/index.tsx:34
msgid "Product Created"
msgstr "Product Created"

#: src/components/forms/WebhookForm/index.tsx:46
msgid "Product Deleted"
msgstr "Product Deleted"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:55
msgid "Product deleted successfully"
msgstr "Product deleted successfully"

#: src/components/common/AttendeeTable/index.tsx:50
#~ msgid "Product email has been resent to attendee"
#~ msgstr "Product email has been resent to attendee"

#: src/components/forms/ProductForm/index.tsx:236
msgid "Product Price Type"
msgstr "Product Price Type"

#: src/components/common/QuestionsTable/index.tsx:328
msgid "Product questions"
msgstr "Product questions"

#: src/components/routes/event/EventDashboard/index.tsx:217
#: src/components/routes/event/Reports/index.tsx:17
msgid "Product Sales"
msgstr "Product Sales"

#: src/components/routes/event/Reports/index.tsx:18
msgid "Product sales, revenue, and tax breakdown"
msgstr "Product sales, revenue, and tax breakdown"

#: src/components/common/ProductSelector/index.tsx:63
msgid "Product Tier"
msgstr "Product Tier"

#: src/components/forms/ProductForm/index.tsx:214
#: src/components/forms/ProductForm/index.tsx:221
msgid "Product Type"
msgstr "Product Type"

#: src/components/forms/WebhookForm/index.tsx:40
msgid "Product Updated"
msgstr "Product Updated"

#: src/components/common/WidgetEditor/index.tsx:313
msgid "Product Widget Preview"
msgstr "Product Widget Preview"

#: src/components/common/PromoCodeTable/index.tsx:148
msgid "Product(s)"
msgstr "Product(s)"

#: src/components/common/PromoCodeTable/index.tsx:72
msgid "Products"
msgstr "Products"

#: src/components/common/EventCard/index.tsx:126
msgid "products sold"
msgstr "products sold"

#: src/components/common/StatBoxes/index.tsx:21
msgid "Products sold"
msgstr "Products sold"

#: src/components/routes/event/EventDashboard/index.tsx:238
msgid "Products Sold"
msgstr "Products Sold"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:178
msgid "Products sorted successfully"
msgstr "Products sorted successfully"

#: src/components/layouts/AuthLayout/index.tsx:43
msgid "Products, merchandise, and flexible pricing options"
msgstr "Products, merchandise, and flexible pricing options"

#: src/components/routes/profile/ManageProfile/index.tsx:106
msgid "Profile"
msgstr "Profile"

#: src/components/routes/profile/ManageProfile/index.tsx:59
msgid "Profile updated successfully"
msgstr "Profile updated successfully"

#: src/components/routes/product-widget/SelectProducts/index.tsx:178
msgid "Promo {promo_code} code applied"
msgstr "Promo {promo_code} code applied"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:119
#~ msgid "Promo {promoCode} code applied"
#~ msgstr "Promo {promoCode} code applied"

#: src/components/common/OrderDetails/index.tsx:86
msgid "Promo code"
msgstr "Promo code"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:20
msgid "Promo Code"
msgstr "Promo Code"

#: src/components/forms/ProductForm/index.tsx:409
msgid "Promo Code page"
msgstr "Promo Code page"

#: src/components/routes/event/Reports/index.tsx:30
msgid "Promo code usage and discount breakdown"
msgstr "Promo code usage and discount breakdown"

#: src/components/layouts/Event/index.tsx:106
#: src/components/routes/event/promo-codes.tsx:31
msgid "Promo Codes"
msgstr "Promo Codes"

#: src/components/common/PromoCodeTable/index.tsx:51
msgid "Promo codes can be used to offer discounts, presale access, or provide special access to your event."
msgstr "Promo codes can be used to offer discounts, presale access, or provide special access to your event."

#: src/components/routes/event/Reports/index.tsx:29
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:95
msgid "Promo Codes Report"
msgstr "Promo Codes Report"

#: src/components/forms/QuestionForm/index.tsx:189
msgid ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."
msgstr ""
"Provide additional context or instructions for this question. Use this field to add terms\n"
"and conditions, guidelines, or any important information that attendees need to know before answering."

#: src/components/routes/event/EventDashboard/index.tsx:159
msgid "Publish Event"
msgstr "Publish Event"

#: src/components/common/GlobalMenu/index.tsx:25
#~ msgid "Purchase License"
#~ msgstr "Purchase License"

#: src/components/modals/ShareModal/index.tsx:126
msgid "QR Code"
msgstr "QR Code"

#: src/components/layouts/AuthLayout/index.tsx:64
msgid "QR code scanning with instant feedback and secure sharing for staff access"
msgstr "QR code scanning with instant feedback and secure sharing for staff access"

#: src/components/forms/ProductForm/index.tsx:85
#: src/components/forms/ProductForm/index.tsx:302
msgid "Quantity Available"
msgstr "Quantity Available"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:243
msgid "Quantity Sold"
msgstr "Quantity Sold"

#: src/components/common/QuestionsTable/index.tsx:170
msgid "Question deleted"
msgstr "Question deleted"

#: src/components/forms/QuestionForm/index.tsx:188
msgid "Question Description"
msgstr "Question Description"

#: src/components/forms/QuestionForm/index.tsx:178
msgid "Question Title"
msgstr "Question Title"

#: src/components/common/QuestionsTable/index.tsx:275
#: src/components/layouts/Event/index.tsx:102
msgid "Questions"
msgstr "Questions"

#: src/components/modals/ManageAttendeeModal/index.tsx:184
#: src/components/modals/ManageOrderModal/index.tsx:111
msgid "Questions & Answers"
msgstr "Questions & Answers"

#: src/components/common/QuestionsTable/index.tsx:145
msgid "Questions sorted successfully"
msgstr "Questions sorted successfully"

#: src/components/forms/QuestionForm/index.tsx:118
msgid "Radio Option"
msgstr "Radio Option"

#: src/components/common/MessageList/index.tsx:59
msgid "Read less"
msgstr "Read less"

#: src/components/modals/SendMessageModal/index.tsx:36
msgid "Recipient"
msgstr "Recipient"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:110
#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:195
msgid "Redirecting to Stripe..."
msgstr "Redirecting to Stripe..."

#: src/components/common/OrdersTable/index.tsx:204
#: src/components/common/OrdersTable/index.tsx:274
msgid "Reference"
msgstr "Reference"

#: src/components/modals/RefundOrderModal/index.tsx:104
msgid "Refund amount ({0})"
msgstr "Refund amount ({0})"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:52
msgid "Refund Failed"
msgstr "Refund Failed"

#: src/components/common/OrdersTable/index.tsx:172
msgid "Refund order"
msgstr "Refund order"

#: src/components/modals/RefundOrderModal/index.tsx:151
msgid "Refund Order"
msgstr "Refund Order"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:51
msgid "Refund Pending"
msgstr "Refund Pending"

#: src/components/routes/event/orders.tsx:51
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:128
msgid "Refund Status"
msgstr "Refund Status"

#: src/components/common/OrderSummary/index.tsx:80
#: src/components/common/StatBoxes/index.tsx:33
#: src/components/routes/event/orders.tsx:29
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:53
msgid "Refunded"
msgstr "Refunded"

#: src/components/routes/auth/Register/index.tsx:129
msgid "Register"
msgstr "Register"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:82
msgid "Remaining Uses"
msgstr "Remaining Uses"

#: src/components/routes/product-widget/SelectProducts/index.tsx:504
msgid "remove"
msgstr "remove"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:140
#: src/components/routes/product-widget/SelectProducts/index.tsx:505
msgid "Remove"
msgstr "Remove"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
#~ msgid "Reply to email"
#~ msgstr "Reply to email"

#: src/components/layouts/Event/index.tsx:92
#: src/components/routes/event/Reports/index.tsx:39
msgid "Reports"
msgstr "Reports"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:169
msgid "Require Billing Address"
msgstr "Require Billing Address"

#: src/components/routes/event/GettingStarted/index.tsx:197
msgid "Resend confirmation email"
msgstr "Resend confirmation email"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resend email confirmation"
msgstr "Resend email confirmation"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Resend invitation"
msgstr "Resend invitation"

#: src/components/common/OrdersTable/index.tsx:179
msgid "Resend order email"
msgstr "Resend order email"

#: src/components/common/AttendeeTable/index.tsx:179
#~ msgid "Resend product email"
#~ msgstr "Resend product email"

#: src/components/common/AttendeeTable/index.tsx:171
msgid "Resend ticket email"
msgstr "Resend ticket email"

#: src/components/routes/profile/ManageProfile/index.tsx:144
msgid "Resending..."
msgstr "Resending..."

#: src/components/common/FilterModal/index.tsx:248
msgid "Reset"
msgstr "Reset"

#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Reset password"
msgstr "Reset password"

#: src/components/routes/auth/ResetPassword/index.tsx:54
msgid "Reset Password"
msgstr "Reset Password"

#: src/components/routes/auth/ForgotPassword/index.tsx:50
msgid "Reset your password"
msgstr "Reset your password"

#: src/components/common/EventCard/index.tsx:167
msgid "Restore event"
msgstr "Restore event"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:50
#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:63
msgid "Return to event page"
msgstr "Return to event page"

#: src/components/layouts/Checkout/index.tsx:165
msgid "Return to Event Page"
msgstr "Return to Event Page"

#: src/components/routes/event/EventDashboard/index.tsx:249
msgid "Revenue"
msgstr "Revenue"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Revoke invitation"
msgstr "Revoke invitation"

#: src/components/modals/EditUserModal/index.tsx:93
#: src/components/modals/InviteUserModal/index.tsx:64
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Role"
msgstr "Role"

#: src/components/forms/ProductForm/index.tsx:96
#: src/components/forms/ProductForm/index.tsx:391
msgid "Sale End Date"
msgstr "Sale End Date"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:75
msgid "Sale ended"
msgstr "Sale ended"

#: src/components/forms/ProductForm/index.tsx:91
#: src/components/forms/ProductForm/index.tsx:389
msgid "Sale Start Date"
msgstr "Sale Start Date"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:35
#: src/components/routes/ticket-widget/SelectTickets/Prices/Tiered/index.tsx:29
#~ msgid "Sale starts"
#~ msgstr "Sale starts"

#: src/components/common/ProductPriceAvailability/index.tsx:13
#: src/components/common/ProductPriceAvailability/index.tsx:35
msgid "Sales ended"
msgstr "Sales ended"

#: src/components/common/ProductPriceAvailability/index.tsx:19
#: src/components/common/ProductPriceAvailability/index.tsx:40
msgid "Sales start"
msgstr "Sales start"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:135
msgid "San Francisco"
msgstr "San Francisco"

#: src/components/common/QuestionAndAnswerList/index.tsx:142
#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:80
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:114
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:96
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:166
#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:94
#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:265
#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:85
msgid "Save"
msgstr "Save"

#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/event/HomepageDesigner/index.tsx:166
msgid "Save Changes"
msgstr "Save Changes"

#: src/components/modals/EditOrganizerModal/index.tsx:70
msgid "Save Organizer"
msgstr "Save Organizer"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:90
msgid "Save Settings"
msgstr "Save Settings"

#: src/components/layouts/CheckIn/index.tsx:398
#: src/components/layouts/CheckIn/index.tsx:400
msgid "Scan QR Code"
msgstr "Scan QR Code"

#: src/components/modals/ShareModal/index.tsx:229
msgid "Scan this QR code to access the event page or share it with others"
msgstr "Scan this QR code to access the event page or share it with others"

#: src/components/layouts/CheckIn/index.tsx:288
#~ msgid "Seach by name, order #, attendee # or email..."
#~ msgstr "Seach by name, order #, attendee # or email..."

#: src/components/routes/event/attendees.tsx:64
msgid "Search by attendee name, email or order #..."
msgstr "Search by attendee name, email or order #..."

#: src/components/routes/events/Dashboard/index.tsx:44
#~ msgid "Search by event name or description..."
#~ msgstr "Search by event name or description..."

#: src/components/routes/events/Dashboard/index.tsx:69
#: src/components/routes/organizer/OrganizerDashboard/index.tsx:66
msgid "Search by event name..."
msgstr "Search by event name..."

#: src/components/routes/event/tickets.tsx:43
#~ msgid "Search by name or description..."
#~ msgstr "Search by name or description..."

#: src/components/routes/event/orders.tsx:126
msgid "Search by name, email, or order #..."
msgstr "Search by name, email, or order #..."

#: src/components/layouts/CheckIn/index.tsx:394
msgid "Search by name, order #, attendee # or email..."
msgstr "Search by name, order #, attendee # or email..."

#: src/components/routes/event/promo-codes.tsx:34
msgid "Search by name..."
msgstr "Search by name..."

#: src/components/routes/event/orders.tsx:46
#~ msgid "Search by order #, name or email..."
#~ msgstr "Search by order #, name or email..."

#: src/components/routes/event/products.tsx:43
#~ msgid "Search by product name..."
#~ msgstr "Search by product name..."

#: src/components/routes/event/messages.tsx:37
#~ msgid "Search by subject or body..."
#~ msgstr "Search by subject or body..."

#: src/components/routes/event/messages.tsx:34
msgid "Search by subject or content..."
msgstr "Search by subject or content..."

#: src/components/routes/event/tickets.tsx:43
#~ msgid "Search by ticket name..."
#~ msgstr "Search by ticket name..."

#: src/components/routes/event/CapacityAssignments/index.tsx:37
msgid "Search capacity assignments..."
msgstr "Search capacity assignments..."

#: src/components/routes/event/CheckInLists/index.tsx:37
msgid "Search check-in lists..."
msgstr "Search check-in lists..."

#: src/components/routes/event/products.tsx:52
msgid "Search products"
msgstr "Search products"

#: src/components/common/SearchBar/index.tsx:36
msgid "Search..."
msgstr "Search..."

#: src/components/routes/event/HomepageDesigner/index.tsx:160
msgid "Secondary color"
msgstr "Secondary color"

#: src/components/common/WidgetEditor/index.tsx:198
msgid "Secondary Color"
msgstr "Secondary Color"

#: src/components/routes/event/HomepageDesigner/index.tsx:162
msgid "Secondary text color"
msgstr "Secondary text color"

#: src/components/common/WidgetEditor/index.tsx:206
msgid "Secondary Text Color"
msgstr "Secondary Text Color"

#: src/components/common/FilterModal/index.tsx:150
msgid "Select {0}"
msgstr "Select {0}"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:214
msgid "Select Camera"
msgstr "Select Camera"

#: src/components/forms/ProductForm/index.tsx:269
msgid "Select category..."
msgstr "Select category..."

#: src/components/forms/WebhookForm/index.tsx:124
msgid "Select event types"
msgstr "Select event types"

#: src/components/modals/CreateEventModal/index.tsx:110
msgid "Select organizer"
msgstr "Select organizer"

#: src/components/modals/ManageAttendeeModal/index.tsx:118
msgid "Select Product"
msgstr "Select Product"

#: src/components/common/ProductSelector/index.tsx:65
msgid "Select Product Tier"
msgstr "Select Product Tier"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:49
#: src/components/modals/SendMessageModal/index.tsx:219
msgid "Select products"
msgstr "Select products"

#: src/components/modals/EditUserModal/index.tsx:104
msgid "Select status"
msgstr "Select status"

#: src/components/modals/CreateAttendeeModal/index.tsx:150
msgid "Select Ticket"
msgstr "Select Ticket"

#: src/components/modals/CreateAttendeeModal/index.tsx:163
#: src/components/modals/EditAttendeeModal/index.tsx:117
#~ msgid "Select Ticket Tier"
#~ msgstr "Select Ticket Tier"

#: src/components/forms/CheckInListForm/index.tsx:26
#: src/components/modals/SendMessageModal/index.tsx:207
msgid "Select tickets"
msgstr "Select tickets"

#: src/components/layouts/EventHomepage/index.tsx:20
#~ msgid "Select Tickets"
#~ msgstr "Select Tickets"

#: src/components/common/ReportTable/index.tsx:261
msgid "Select time period"
msgstr "Select time period"

#: src/components/forms/WebhookForm/index.tsx:123
msgid "Select which events will trigger this webhook"
msgstr "Select which events will trigger this webhook"

#: src/components/forms/ProductForm/index.tsx:352
msgid "Select..."
msgstr "Select..."

#: src/components/layouts/AuthLayout/index.tsx:68
msgid "Sell Anything"
msgstr "Sell Anything"

#: src/components/layouts/AuthLayout/index.tsx:69
msgid "Sell merchandise alongside tickets with integrated tax and promo code support"
msgstr "Sell merchandise alongside tickets with integrated tax and promo code support"

#: src/components/layouts/AuthLayout/index.tsx:42
msgid "Sell More Than Tickets"
msgstr "Sell More Than Tickets"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send"
msgstr "Send"

#: src/components/modals/SendMessageModal/index.tsx:259
msgid "Send a copy to <0>{0}</0>"
msgstr "Send a copy to <0>{0}</0>"

#: src/components/modals/SendMessageModal/index.tsx:139
msgid "Send a message"
msgstr "Send a message"

#: src/components/modals/SendMessageModal/index.tsx:269
msgid "Send as a test. This will send the message to your email address instead of the recipients."
msgstr "Send as a test. This will send the message to your email address instead of the recipients."

#: src/components/modals/CreateAttendeeModal/index.tsx:184
#~ msgid "Send confirmation email"
#~ msgstr "Send confirmation email"

#: src/components/routes/event/messages.tsx:41
msgid "Send Message"
msgstr "Send Message"

#: src/components/modals/CreateAttendeeModal/index.tsx:191
#~ msgid "Send order confirmation and product email"
#~ msgstr "Send order confirmation and product email"

#: src/components/modals/CreateAttendeeModal/index.tsx:192
msgid "Send order confirmation and ticket email"
msgstr "Send order confirmation and ticket email"

#: src/components/modals/SendMessageModal/index.tsx:295
msgid "Send Test"
msgstr "Send Test"

#: src/components/routes/event/Settings/index.tsx:46
msgid "SEO"
msgstr "SEO"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:70
msgid "SEO Description"
msgstr "SEO Description"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:76
msgid "SEO Keywords"
msgstr "SEO Keywords"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:56
msgid "SEO Settings"
msgstr "SEO Settings"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:64
msgid "SEO Title"
msgstr "SEO Title"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "Service Fee"
msgstr "Service Fee"

#: src/components/forms/TicketForm/index.tsx:141
#~ msgid "Set a minimum price and let users donate more"
#~ msgstr "Set a minimum price and let users donate more"

#: src/components/forms/ProductForm/index.tsx:159
msgid "Set a minimum price and let users pay more if they choose"
msgstr "Set a minimum price and let users pay more if they choose"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:207
msgid "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."
msgstr "Set the starting number for invoice numbering. This cannot be changed once invoices have been generated."

#: src/components/routes/event/GettingStarted/index.tsx:140
msgid "Set up your event"
msgstr "Set up your event"

#: src/components/routes/event/EventDashboard/index.tsx:190
#~ msgid "Set up your payment processing to receive funds from ticket sales."
#~ msgstr "Set up your payment processing to receive funds from ticket sales."

#: src/components/routes/event/GettingStarted/index.tsx:183
msgid "Set your event live"
msgstr "Set your event live"

#: src/components/layouts/Event/index.tsx:98
#: src/components/routes/event/Settings/index.tsx:103
msgid "Settings"
msgstr "Settings"

#: src/components/layouts/AuthLayout/index.tsx:26
msgid "Setup in Minutes"
msgstr "Setup in Minutes"

#: src/components/common/ShareIcon/index.tsx:28
#: src/components/modals/ShareModal/index.tsx:123
#: src/components/modals/ShareModal/index.tsx:141
msgid "Share"
msgstr "Share"

#: src/components/layouts/Event/index.tsx:251
#: src/components/modals/ShareModal/index.tsx:116
msgid "Share Event"
msgstr "Share Event"

#: src/components/modals/ShareModal/index.tsx:169
msgid "Share to Facebook"
msgstr "Share to Facebook"

#: src/components/modals/ShareModal/index.tsx:163
msgid "Share to LinkedIn"
msgstr "Share to LinkedIn"

#: src/components/modals/ShareModal/index.tsx:193
msgid "Share to Pinterest"
msgstr "Share to Pinterest"

#: src/components/modals/ShareModal/index.tsx:187
msgid "Share to Reddit"
msgstr "Share to Reddit"

#: src/components/modals/ShareModal/index.tsx:148
msgid "Share to Social"
msgstr "Share to Social"

#: src/components/modals/ShareModal/index.tsx:181
msgid "Share to Telegram"
msgstr "Share to Telegram"

#: src/components/modals/ShareModal/index.tsx:175
msgid "Share to WhatsApp"
msgstr "Share to WhatsApp"

#: src/components/modals/ShareModal/index.tsx:157
msgid "Share to X"
msgstr "Share to X"

#: src/components/modals/ShareModal/index.tsx:199
msgid "Share via Email"
msgstr "Share via Email"

#: src/components/forms/TaxAndFeeForm/index.tsx:85
#~ msgid "Should this {type} be applied to all new tickets?"
#~ msgstr "Should this {type} be applied to all new tickets?"

#: src/components/forms/ProductForm/index.tsx:344
msgid "Show"
msgstr "Show"

#: src/components/forms/ProductForm/index.tsx:403
msgid "Show available product quantity"
msgstr "Show available product quantity"

#: src/components/forms/TicketForm/index.tsx:348
#~ msgid "Show available ticket quantity"
#~ msgstr "Show available ticket quantity"

#: src/components/common/QuestionsTable/index.tsx:295
msgid "Show hidden questions"
msgstr "Show hidden questions"

#: src/components/routes/product-widget/SelectProducts/index.tsx:459
msgid "Show more"
msgstr "Show more"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:60
msgid "Show tax and fees separately"
msgstr "Show tax and fees separately"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:76
#~ msgid "Shown to the customer after they checkout, on the order summary page"
#~ msgstr "Shown to the customer after they checkout, on the order summary page"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
msgid "Shown to the customer after they checkout, on the order summary page."
msgstr "Shown to the customer after they checkout, on the order summary page."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:69
msgid "Shown to the customer before they checkout"
msgstr "Shown to the customer before they checkout"

#: src/components/forms/QuestionForm/index.tsx:132
msgid "Shows common address fields, including country"
msgstr "Shows common address fields, including country"

#: src/components/modals/ManageAttendeeModal/index.tsx:111
#: src/components/modals/ManageOrderModal/index.tsx:151
msgid "Simpson"
msgstr "Simpson"

#: src/components/forms/QuestionForm/index.tsx:100
msgid "Single line text box"
msgstr "Single line text box"

#: src/components/routes/welcome/index.tsx:137
msgid "Skip this step"
msgstr "Skip this step"

#: src/components/layouts/AuthLayout/index.tsx:78
msgid "Smart Check-in"
msgstr "Smart Check-in"

#: src/components/layouts/AuthLayout/index.tsx:53
#~ msgid "Smart Dashboard"
#~ msgstr "Smart Dashboard"

#: src/components/routes/auth/Register/index.tsx:90
msgid "Smith"
msgstr "Smith"

#: src/components/common/ProductPriceAvailability/index.tsx:9
#: src/components/common/ProductPriceAvailability/index.tsx:32
msgid "Sold out"
msgstr "Sold out"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:67
msgid "Sold Out"
msgstr "Sold Out"

#: src/components/common/ErrorDisplay/index.tsx:14
#: src/components/routes/auth/AcceptInvitation/index.tsx:47
msgid "Something went wrong"
msgstr "Something went wrong"

#: src/components/common/TaxAndFeeList/index.tsx:39
msgid "Something went wrong while deleting the Tax or Fee"
msgstr "Something went wrong while deleting the Tax or Fee"

#: src/components/routes/auth/ForgotPassword/index.tsx:31
msgid "Something went wrong, please try again, or contact support if the problem persists"
msgstr "Something went wrong, please try again, or contact support if the problem persists"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "Something went wrong! Please try again"
msgstr "Something went wrong! Please try again"

#: src/components/forms/StripeCheckoutForm/index.tsx:69
msgid "Something went wrong."
msgstr "Something went wrong."

#: src/components/routes/profile/ManageProfile/index.tsx:80
#: src/components/routes/profile/ManageProfile/index.tsx:94
msgid "Something went wrong. Please try again."
msgstr "Something went wrong. Please try again."

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:61
msgid "Sorry, something has gone wrong. Please restart the checkout process."
msgstr "Sorry, something has gone wrong. Please restart the checkout process."

#: src/components/routes/product-widget/CollectInformation/index.tsx:259
msgid "Sorry, something went wrong loading this page."
msgstr "Sorry, something went wrong loading this page."

#: src/components/routes/product-widget/CollectInformation/index.tsx:247
msgid "Sorry, this order no longer exists."
msgstr "Sorry, this order no longer exists."

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:208
#~ msgid "Sorry, this promo code is invalid'"
#~ msgstr "Sorry, this promo code is invalid'"

#: src/components/routes/product-widget/SelectProducts/index.tsx:246
msgid "Sorry, this promo code is not recognized"
msgstr "Sorry, this promo code is not recognized"

#: src/components/layouts/Checkout/index.tsx:26
#~ msgid "Sorry, your order has expired. Please start a new order."
#~ msgstr "Sorry, your order has expired. Please start a new order."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:115
#~ msgid "Sorting is disabled while filters and sorting are applied"
#~ msgstr "Sorting is disabled while filters and sorting are applied"

#: src/components/common/LanguageSwitcher/index.tsx:18
msgid "Spanish"
msgstr "Spanish"

#: src/components/forms/ProductForm/index.tsx:147
msgid "Standard product with a fixed price"
msgstr "Standard product with a fixed price"

#: src/components/forms/TicketForm/index.tsx:129
#~ msgid "Standard ticket with a fixed price"
#~ msgstr "Standard ticket with a fixed price"

#: src/components/modals/CreateEventModal/index.tsx:144
#: src/components/modals/DuplicateEventModal/index.tsx:93
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:87
#: src/components/routes/welcome/index.tsx:90
msgid "Start Date"
msgstr "Start Date"

#: src/components/routes/event/settings.tsx:112
#~ msgid "State"
#~ msgstr "State"

#: src/components/common/CheckoutQuestion/index.tsx:165
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:139
#: src/components/routes/product-widget/CollectInformation/index.tsx:339
#: src/components/routes/product-widget/CollectInformation/index.tsx:340
msgid "State or Region"
msgstr "State or Region"

#: src/components/common/AttendeeTable/index.tsx:112
#: src/components/common/OrderDetails/index.tsx:51
#: src/components/common/OrdersTable/index.tsx:209
#: src/components/common/ProductsTable/SortableProduct/index.tsx:222
#: src/components/common/WebhookTable/index.tsx:238
#: src/components/forms/CapaciyAssigmentForm/index.tsx:56
#: src/components/forms/WebhookForm/index.tsx:133
#: src/components/modals/EditUserModal/index.tsx:103
#: src/components/routes/account/ManageAccount/sections/Users/<USER>
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:88
msgid "Status"
msgstr "Status"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:85
msgid "Stripe"
msgstr "Stripe"

#: src/components/routes/product-widget/Payment/PaymentMethods/Stripe/index.tsx:48
msgid "Stripe payments are not enabled for this event."
msgstr "Stripe payments are not enabled for this event."

#: src/components/modals/SendMessageModal/index.tsx:245
msgid "Subject"
msgstr "Subject"

#: src/components/common/OrderSummary/index.tsx:41
msgid "Subtotal"
msgstr "Subtotal"

#: src/components/common/OrdersTable/index.tsx:115
#: src/components/layouts/Checkout/index.tsx:53
msgid "Success"
msgstr "Success"

#: src/components/modals/EditUserModal/index.tsx:40
#: src/components/modals/InviteUserModal/index.tsx:32
msgid "Success! {0} will receive an email shortly."
msgstr "Success! {0} will receive an email shortly."

#: src/components/common/AttendeeTable/index.tsx:90
msgid "Successfully {0} attendee"
msgstr "Successfully {0} attendee"

#: src/components/common/AttendeeTable/index.tsx:51
#~ msgid "Successfully ${0} attendee"
#~ msgstr "Successfully ${0} attendee"

#: src/components/common/AttendeeCheckInTable/index.tsx:47
#: src/components/common/AttendeeCheckInTable/index.tsx:71
#~ msgid "Successfully checked <0>{0} {1}</0> {2}"
#~ msgstr "Successfully checked <0>{0} {1}</0> {2}"

#: src/components/routes/profile/ConfirmEmailAddress/index.tsx:23
msgid "Successfully confirmed email address"
msgstr "Successfully confirmed email address"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:31
msgid "Successfully confirmed email change"
msgstr "Successfully confirmed email change"

#: src/components/modals/CreateAttendeeModal/index.tsx:82
msgid "Successfully created attendee"
msgstr "Successfully created attendee"

#: src/components/modals/CreateProductModal/index.tsx:54
msgid "Successfully Created Product"
msgstr "Successfully Created Product"

#: src/components/modals/CreatePromoCodeModal/index.tsx:39
msgid "Successfully Created Promo Code"
msgstr "Successfully Created Promo Code"

#: src/components/modals/CreateQuestionModal/index.tsx:45
#: src/components/modals/EditQuestionModal/index.tsx:66
msgid "Successfully Created Question"
msgstr "Successfully Created Question"

#: src/components/modals/CreateTicketModal/index.tsx:50
#~ msgid "Successfully Created Ticket"
#~ msgstr "Successfully Created Ticket"

#: src/components/common/TicketsTable/index.tsx:48
#~ msgid "Successfully deleted ticket"
#~ msgstr "Successfully deleted ticket"

#: src/components/modals/DuplicateProductModal/index.tsx:95
msgid "Successfully Duplicated Product"
msgstr "Successfully Duplicated Product"

#: src/components/modals/ManageAttendeeModal/index.tsx:96
msgid "Successfully updated attendee"
msgstr "Successfully updated attendee"

#: src/components/modals/EditCapacityAssignmentModal/index.tsx:47
msgid "Successfully updated Capacity Assignment"
msgstr "Successfully updated Capacity Assignment"

#: src/components/modals/EditCheckInListModal/index.tsx:49
msgid "Successfully updated Check-In List"
msgstr "Successfully updated Check-In List"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:43
msgid "Successfully Updated Email Settings"
msgstr "Successfully Updated Email Settings"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:54
msgid "Successfully Updated Event"
msgstr "Successfully Updated Event"

#: src/components/routes/event/HomepageDesigner/index.tsx:84
msgid "Successfully Updated Homepage Design"
msgstr "Successfully Updated Homepage Design"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:50
msgid "Successfully Updated Homepage Settings"
msgstr "Successfully Updated Homepage Settings"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:73
msgid "Successfully Updated Location"
msgstr "Successfully Updated Location"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:43
msgid "Successfully Updated Misc Settings"
msgstr "Successfully Updated Misc Settings"

#: src/components/modals/ManageOrderModal/index.tsx:74
msgid "Successfully updated order"
msgstr "Successfully updated order"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:74
msgid "Successfully Updated Payment & Invoicing Settings"
msgstr "Successfully Updated Payment & Invoicing Settings"

#: src/components/modals/EditProductModal/index.tsx:89
msgid "Successfully updated product"
msgstr "Successfully updated product"

#: src/components/modals/EditPromoCodeModal/index.tsx:52
msgid "Successfully Updated Promo Code"
msgstr "Successfully Updated Promo Code"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:45
msgid "Successfully Updated Seo Settings"
msgstr "Successfully Updated Seo Settings"

#: src/components/modals/EditTicketModal/index.tsx:85
#~ msgid "Successfully updated ticket"
#~ msgstr "Successfully updated ticket"

#: src/components/modals/EditWebhookModal/index.tsx:44
msgid "Successfully updated Webhook"
msgstr "Successfully updated Webhook"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:128
msgid "Suite 100"
msgstr "Suite 100"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:62
msgid "Support Email"
msgstr "Support Email"

#: src/components/layouts/AuthLayout/index.tsx:59
msgid "Support for tiered, donation-based, and product sales with customizable pricing and capacity"
msgstr "Support for tiered, donation-based, and product sales with customizable pricing and capacity"

#: src/components/forms/ProductForm/index.tsx:254
msgid "T-shirt"
msgstr "T-shirt"

#: src/components/forms/TaxAndFeeForm/index.tsx:12
#: src/components/forms/TaxAndFeeForm/index.tsx:39
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:32
#: src/components/modals/CreateTaxOrFeeModal/index.tsx:48
#: src/components/modals/EditTaxOrFeeModal/index.tsx:42
#: src/components/modals/EditTaxOrFeeModal/index.tsx:52
#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Tax"
msgstr "Tax"

#: src/components/routes/account/ManageAccount/index.tsx:27
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:16
msgid "Tax & Fees"
msgstr "Tax & Fees"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:243
msgid "Tax Details"
msgstr "Tax Details"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:244
msgid "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"
msgstr "Tax information to appear at the bottom of all invoices (e.g., VAT number, tax registration)"

#: src/components/common/TaxAndFeeList/index.tsx:36
msgid "Tax or Fee deleted successfully"
msgstr "Tax or Fee deleted successfully"

#: src/components/forms/ProductForm/index.tsx:363
msgid "Taxes"
msgstr "Taxes"

#: src/components/routes/account/ManageAccount/index.tsx:19
#: src/components/routes/account/ManageAccount/sections/TaxSettings/index.tsx:14
#~ msgid "Taxes & Fees"
#~ msgstr "Taxes & Fees"

#: src/components/forms/ProductForm/index.tsx:351
#: src/components/forms/ProductForm/index.tsx:358
msgid "Taxes and Fees"
msgstr "Taxes and Fees"

#: src/components/common/TaxAndFeeList/index.tsx:102
#~ msgid "Taxes and Fees can be associated with tickets and will be added to the ticket price."
#~ msgstr "Taxes and Fees can be associated with tickets and will be added to the ticket price."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:81
#~ msgid "Text Colour"
#~ msgstr "Text Colour"

#: src/components/routes/product-widget/SelectProducts/index.tsx:138
#: src/components/routes/product-widget/SelectProducts/index.tsx:186
msgid "That promo code is invalid"
msgstr "That promo code is invalid"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:78
#~ msgid "The background color for the event homepage"
#~ msgstr "The background color for the event homepage"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:98
#~ msgid "The background color for the ticket widget on the event homepage"
#~ msgstr "The background color for the ticket widget on the event homepage"

#: src/components/routes/event/settings.tsx:50
#: src/components/routes/event/settings.tsx:134
#: src/components/routes/event/settings.tsx:210
#~ msgid "The basic details of your event"
#~ msgstr "The basic details of your event"

#: src/components/layouts/CheckIn/index.tsx:316
msgid "The check-in list you are looking for does not exist."
msgstr "The check-in list you are looking for does not exist."

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:88
#~ msgid "The color of the buttons on the event homepage"
#~ msgstr "The color of the buttons on the event homepage"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:92
#~ msgid "The color of the links on the event homepage"
#~ msgstr "The color of the links on the event homepage"

#: src/components/forms/OrganizerForm/index.tsx:47
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:75
msgid "The default currency for your events."
msgstr "The default currency for your events."

#: src/components/forms/OrganizerForm/index.tsx:56
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:84
msgid "The default timezone for your events."
msgstr "The default timezone for your events."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:16
#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:43
msgid "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."
msgstr "The event you're looking for is not available at the moment. It may have been removed, expired, or the URL might be incorrect."

#: src/components/modals/CreateAttendeeModal/index.tsx:146
msgid "The language the attendee will receive emails in."
msgstr "The language the attendee will receive emails in."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:22
msgid "The link you clicked is invalid."
msgstr "The link you clicked is invalid."

#: src/components/routes/event/settings.tsx:85
#~ msgid "The location of your event"
#~ msgstr "The location of your event"

#: src/components/routes/product-widget/SelectProducts/index.tsx:444
msgid "The maximum number of products for {0}is {1}"
msgstr "The maximum number of products for {0}is {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:351
#~ msgid "The maximum numbers number of tickets for {0}is {1}"
#~ msgstr "The maximum numbers number of tickets for {0}is {1}"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:318
#~ msgid "The maximum numbers number of tickets for Generals is {0}"
#~ msgstr "The maximum numbers number of tickets for Generals is {0}"

#: src/components/routes/event/settings.tsx:232
#~ msgid "The organizer details of your event"
#~ msgstr "The organizer details of your event"

#: src/components/common/ErrorDisplay/index.tsx:17
msgid "The page you are looking for does not exist"
msgstr "The page you are looking for does not exist"

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:56
msgid "The price displayed to the customer will include taxes and fees."
msgstr "The price displayed to the customer will include taxes and fees."

#: src/components/routes/event/Settings/Sections/MiscSettings/index.tsx:62
msgid "The price displayed to the customer will not include taxes and fees. They will be shown separately"
msgstr "The price displayed to the customer will not include taxes and fees. They will be shown separately"

#: src/components/common/WidgetEditor/index.tsx:169
msgid "The styling settings you choose apply only to copied HTML and won't be stored."
msgstr "The styling settings you choose apply only to copied HTML and won't be stored."

#: src/components/forms/TicketForm/index.tsx:223
#~ msgid "The tax and fee to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "The tax and fee to apply to this ticket. You can create new taxes and fees on the"

#: src/components/forms/ProductForm/index.tsx:355
msgid "The taxes and fees to apply to this product. You can create new taxes and fees on the"
msgstr "The taxes and fees to apply to this product. You can create new taxes and fees on the"

#: src/components/forms/TicketForm/index.tsx:300
#~ msgid "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"
#~ msgstr "The taxes and fees to apply to this ticket. You can create new taxes and fees on the"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
#~ msgid "The text color for the event homepage"
#~ msgstr "The text color for the event homepage"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:102
#~ msgid "The text color for the ticket widget on the event homepage"
#~ msgstr "The text color for the ticket widget on the event homepage"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:91
#~ msgid "The text to display in the 'Continue' button. Defaults to 'Continue'"
#~ msgstr "The text to display in the 'Continue' button. Defaults to 'Continue'"

#: src/components/routes/event/Settings/Sections/SeoSettings/index.tsx:63
msgid "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"
msgstr "The title of the event that will be displayed in search engine results and when sharing on social media. By default, the event title will be used"

#: src/components/modals/EditUserModal/index.tsx:79
#~ msgid "The user must login to change their email."
#~ msgstr "The user must login to change their email."

#: src/components/routes/product-widget/SelectProducts/index.tsx:272
msgid "There are no products available for this event"
msgstr "There are no products available for this event"

#: src/components/routes/product-widget/SelectProducts/index.tsx:375
msgid "There are no products available in this category"
msgstr "There are no products available in this category"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:260
#~ msgid "There are no tickets available for this event"
#~ msgstr "There are no tickets available for this event"

#: src/components/modals/RefundOrderModal/index.tsx:137
msgid "There is a refund pending. Please wait for it to complete before requesting another refund."
msgstr "There is a refund pending. Please wait for it to complete before requesting another refund."

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:226
#~ msgid "There was an error loading this content. Please refresh the page and try again."
#~ msgstr "There was an error loading this content. Please refresh the page and try again."

#: src/components/common/OrdersTable/index.tsx:80
msgid "There was an error marking the order as paid"
msgstr "There was an error marking the order as paid"

#: src/hooks/useFormErrorResponseHandler.tsx:28
msgid "There was an error processing your request. Please try again."
msgstr "There was an error processing your request. Please try again."

#: src/components/common/OrdersTable/index.tsx:95
msgid "There was an error sending your message"
msgstr "There was an error sending your message"

#: src/components/forms/QuestionForm/index.tsx:108
#~ msgid "These allow multiple selections"
#~ msgstr "These allow multiple selections"

#: src/components/common/WidgetEditor/index.tsx:86
#~ msgid "These colors are not saved in our system."
#~ msgstr "These colors are not saved in our system."

#: src/components/common/WidgetEditor/index.tsx:85
#~ msgid "These colors are not saved in our system. They are only used to generate the widget."
#~ msgstr "These colors are not saved in our system. They are only used to generate the widget."

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:105
msgid "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."
msgstr "These details will only be shown if order is completed successfully. Orders awaiting payment will not show this message."

#: src/components/layouts/CheckIn/index.tsx:201
msgid "This attendee has an unpaid order."
msgstr "This attendee has an unpaid order."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:61
msgid "This category doesn't have any products yet."
msgstr "This category doesn't have any products yet."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:112
msgid "This category is hidden from public view"
msgstr "This category is hidden from public view"

#: src/components/common/CheckInListList/index.tsx:75
msgid "This check-in list has expired"
msgstr "This check-in list has expired"

#: src/components/layouts/CheckIn/index.tsx:331
msgid "This check-in list has expired and is no longer available for check-ins."
msgstr "This check-in list has expired and is no longer available for check-ins."

#: src/components/common/CheckInListList/index.tsx:82
msgid "This check-in list is active"
msgstr "This check-in list is active"

#: src/components/common/CheckInListList/index.tsx:79
msgid "This check-in list is not active yet"
msgstr "This check-in list is not active yet"

#: src/components/layouts/CheckIn/index.tsx:348
msgid "This check-in list is not yet active and is not available for check-ins."
msgstr "This check-in list is not yet active and is not available for check-ins."

#: src/components/forms/CheckInListForm/index.tsx:37
msgid "This description will be shown to the check-in staff"
msgstr "This description will be shown to the check-in staff"

#: src/components/modals/SendMessageModal/index.tsx:285
msgid "This email is not promotional and is directly related to the event."
msgstr "This email is not promotional and is directly related to the event."

#: src/components/layouts/EventHomepage/EventNotAvailable/index.tsx:8
#~ msgid "This event is not available at the moment. Please check back later."
#~ msgstr "This event is not available at the moment. Please check back later."

#: src/components/layouts/EventHomepage/index.tsx:49
#~ msgid "This event is not available."
#~ msgstr "This event is not available."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:152
msgid "This information will be shown on the payment page, order summary page, and order confirmation email."
msgstr "This information will be shown on the payment page, order summary page, and order confirmation email."

#: src/components/forms/ProductForm/index.tsx:138
msgid "This is a general product, like a t-shirt or a mug. No ticket will be issued"
msgstr "This is a general product, like a t-shirt or a mug. No ticket will be issued"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:91
msgid "This is an online event"
msgstr "This is an online event"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:61
#~ msgid "This is the email address that will be used as the reply-to address for all emails sent from this event"
#~ msgstr "This is the email address that will be used as the reply-to address for all emails sent from this event"

#: src/components/forms/CheckInListForm/index.tsx:57
msgid "This list will no longer be available for check-ins after this date"
msgstr "This list will no longer be available for check-ins after this date"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:65
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:72
#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:79
#~ msgid "This message is how below the"
#~ msgstr "This message is how below the"

#: src/components/routes/event/Settings/Sections/EmailSettings/index.tsx:68
msgid "This message will be included in the footer of all emails sent from this event"
msgstr "This message will be included in the footer of all emails sent from this event"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:82
msgid "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"
msgstr "This message will only be shown if order is completed successfully. Orders awaiting payment will not show this message"

#: src/components/forms/StripeCheckoutForm/index.tsx:93
msgid "This order has already been paid."
msgstr "This order has already been paid."

#: src/components/routes/ticket-widget/Payment/index.tsx:40
#~ msgid "This order has already been paid. <0>View order details</0>"
#~ msgstr "This order has already been paid. <0>View order details</0>"

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:199
#~ msgid "This order has already been processed."
#~ msgstr "This order has already been processed."

#: src/components/modals/RefundOrderModal/index.tsx:141
msgid "This order has already been refunded."
msgstr "This order has already been refunded."

#: src/components/routes/product-widget/CollectInformation/index.tsx:237
msgid "This order has been cancelled"
msgstr "This order has been cancelled"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:209
msgid "This order has been cancelled."
msgstr "This order has been cancelled."

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:182
#~ msgid "This order has been completed."
#~ msgstr "This order has been completed."

#: src/components/routes/product-widget/CollectInformation/index.tsx:210
msgid "This order has expired. Please start again."
msgstr "This order has expired. Please start again."

#: src/components/routes/product-widget/CollectInformation/index.tsx:221
msgid "This order is awaiting payment"
msgstr "This order is awaiting payment"

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:188
#~ msgid "This order is awaiting payment."
#~ msgstr "This order is awaiting payment."

#: src/components/routes/product-widget/CollectInformation/index.tsx:229
msgid "This order is complete"
msgstr "This order is complete"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:211
msgid "This order is complete."
msgstr "This order is complete."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:204
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:207
msgid "This order is processing."
msgstr "This order is processing."

#: src/components/routes/ticket-widget/OrderSummaryAndTickets/index.tsx:32
#: src/components/routes/ticket-widget/OrderSummaryAndTickets/index.tsx:48
#~ msgid "This order is processing. TODO - a nice image and poll the API"
#~ msgstr "This order is processing. TODO - a nice image and poll the API"

#: src/components/forms/StripeCheckoutForm/index.tsx:103
msgid "This order page is no longer available."
msgstr "This order page is no longer available."

#: src/components/forms/ProductForm/index.tsx:415
msgid "This overrides all visibility settings and will hide the product from all customers."
msgstr "This overrides all visibility settings and will hide the product from all customers."

#: src/components/forms/TicketForm/index.tsx:360
#~ msgid "This overrides all visibility settings and will hide the ticket from all customers."
#~ msgstr "This overrides all visibility settings and will hide the ticket from all customers."

#: src/components/routes/ticket-widget/Payment/index.tsx:53
#~ msgid "This page has expired. <0>View order details</0>"
#~ msgstr "This page has expired. <0>View order details</0>"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:59
msgid "This product cannot be deleted because it is associated with an order. You can hide it instead."
msgstr "This product cannot be deleted because it is associated with an order. You can hide it instead."

#: src/components/forms/ProductForm/index.tsx:132
msgid "This product is a ticket. Buyers will be issued a ticket upon purchase"
msgstr "This product is a ticket. Buyers will be issued a ticket upon purchase"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:215
msgid "This product is hidden from public view"
msgstr "This product is hidden from public view"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:216
msgid "This product is hidden unless targeted by a Promo Code"
msgstr "This product is hidden unless targeted by a Promo Code"

#: src/components/common/QuestionsTable/index.tsx:90
msgid "This question is only visible to the event organizer"
msgstr "This question is only visible to the event organizer"

#: src/components/routes/auth/ResetPassword/index.tsx:29
msgid "This reset password link is invalid or expired."
msgstr "This reset password link is invalid or expired."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:56
#~ msgid ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."
#~ msgstr ""
#~ "This ticket cannot be deleted because it is\n"
#~ "associated with an order. You can hide it instead."

#: src/components/common/TicketsTable/SortableTicket/index.tsx:131
#~ msgid "This ticket is hidden from public view"
#~ msgstr "This ticket is hidden from public view"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:132
#~ msgid "This ticket is hidden unless targeted by a Promo Code"
#~ msgstr "This ticket is hidden unless targeted by a Promo Code"

#: src/components/modals/EditUserModal/index.tsx:65
msgid "This user is not active, as they have not accepted their invitation."
msgstr "This user is not active, as they have not accepted their invitation."

#: src/components/common/AttendeeTable/index.tsx:182
msgid "ticket"
msgstr "ticket"

#: src/components/common/AttendeeTable/index.tsx:111
#: src/components/forms/ProductForm/index.tsx:130
#: src/components/modals/CreateAttendeeModal/index.tsx:151
msgid "Ticket"
msgstr "Ticket"

#: src/components/common/TicketsTable/SortableTicket/index.tsx:52
#~ msgid "Ticket deleted successfully"
#~ msgstr "Ticket deleted successfully"

#: src/components/common/AttendeeTable/index.tsx:49
msgid "Ticket email has been resent to attendee"
msgstr "Ticket email has been resent to attendee"

#: src/components/common/MessageList/index.tsx:22
msgid "Ticket holders"
msgstr "Ticket holders"

#: src/components/routes/event/products.tsx:86
msgid "Ticket or Product"
msgstr "Ticket or Product"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:63
#~ msgid "Ticket page message"
#~ msgstr "Ticket page message"

#: src/components/routes/event/EventDashboard/index.tsx:53
#~ msgid "Ticket Sales"
#~ msgstr "Ticket Sales"

#: src/components/modals/CreateAttendeeModal/index.tsx:161
#: src/components/modals/EditAttendeeModal/index.tsx:115
#~ msgid "Ticket Tier"
#~ msgstr "Ticket Tier"

#: src/components/forms/TicketForm/index.tsx:189
#: src/components/forms/TicketForm/index.tsx:196
#~ msgid "Ticket Type"
#~ msgstr "Ticket Type"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:97
#~ msgid "Ticket widget background color"
#~ msgstr "Ticket widget background color"

#: src/components/common/WidgetEditor/index.tsx:311
#~ msgid "Ticket Widget Preview"
#~ msgstr "Ticket Widget Preview"

#: src/components/routes/event/Settings/Sections/HomepageAndCheckoutSettings/index.tsx:101
#~ msgid "Ticket widget text color"
#~ msgstr "Ticket widget text color"

#: src/components/common/PromoCodeTable/index.tsx:147
#~ msgid "Ticket(s)"
#~ msgstr "Ticket(s)"

#: src/components/common/PromoCodeTable/index.tsx:71
#: src/components/layouts/Event/index.tsx:40
#: src/components/layouts/EventHomepage/index.tsx:80
#: src/components/routes/event/tickets.tsx:39
#~ msgid "Tickets"
#~ msgstr "Tickets"

#: src/components/layouts/Event/index.tsx:101
#: src/components/routes/event/products.tsx:46
msgid "Tickets & Products"
msgstr "Tickets & Products"

#: src/components/routes/product-widget/PrintOrder/index.tsx:43
msgid "Tickets for"
msgstr "Tickets for"

#: src/components/common/EventCard/index.tsx:126
#~ msgid "tickets sold"
#~ msgstr "tickets sold"

#: src/components/common/StatBoxes/index.tsx:21
#~ msgid "Tickets sold"
#~ msgstr "Tickets sold"

#: src/components/routes/event/EventDashboard/index.tsx:73
#~ msgid "Tickets Sold"
#~ msgstr "Tickets Sold"

#: src/components/common/TicketsTable/index.tsx:44
#~ msgid "Tickets sorted successfully"
#~ msgstr "Tickets sorted successfully"

#: src/components/forms/PromoCodeForm/index.tsx:59
#~ msgid "Tickets to which the promo code applies (Applies to all by default)"
#~ msgstr "Tickets to which the promo code applies (Applies to all by default)"

#: src/components/forms/ProductForm/index.tsx:67
msgid "Tier {0}"
msgstr "Tier {0}"

#: src/components/forms/ProductForm/index.tsx:163
msgid "Tiered Product"
msgstr "Tiered Product"

#: src/components/forms/ProductForm/index.tsx:240
#~ msgid ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""
#~ "Tiered products allow you to offer multiple price options for the same product.\n"
#~ "This is perfect for early bird products, or offering different price\n"
#~ "options for different groups of people."

#: src/components/forms/ProductForm/index.tsx:243
msgid "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."
msgstr "Tiered products allow you to offer multiple price options for the same product. This is perfect for early bird products, or offering different price options for different groups of people."

#: src/components/forms/TicketForm/index.tsx:145
#~ msgid "Tiered Ticket"
#~ msgstr "Tiered Ticket"

#: src/components/forms/TicketForm/index.tsx:52
#~ msgid "Tiered Ticket - Coming Soon"
#~ msgstr "Tiered Ticket - Coming Soon"

#: src/components/forms/TicketForm/index.tsx:203
#~ msgid ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."
#~ msgstr ""
#~ "Tiered tickets allow you to offer multiple price options for the same ticket.\n"
#~ "This is perfect for early bird tickets, or offering different price\n"
#~ "options for different groups of people."

#: src/components/forms/TicketForm/index.tsx:156
#~ msgid "Tiered tickets allow you to offer multiple price options for the same ticket. This is perfect for early bird tickets or offering different price options for different groups of people."
#~ msgstr "Tiered tickets allow you to offer multiple price options for the same ticket. This is perfect for early bird tickets or offering different price options for different groups of people."

#: src/components/layouts/Checkout/index.tsx:90
msgid "Time left:"
msgstr "Time left:"

#: src/components/common/PromoCodeTable/index.tsx:71
msgid "Times used"
msgstr "Times used"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:25
msgid "Times Used"
msgstr "Times Used"

#: src/components/forms/OrganizerForm/index.tsx:54
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:82
#: src/components/routes/auth/AcceptInvitation/index.tsx:105
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:109
#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:181
#: src/components/routes/profile/ManageProfile/index.tsx:161
msgid "Timezone"
msgstr "Timezone"

#: src/components/forms/PromoCodeForm/index.tsx:36
msgid "TIP"
msgstr "TIP"

#: src/components/common/ProductsTable/SortableProduct/index.tsx:206
msgid "Title"
msgstr "Title"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:182
msgid "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."
msgstr "To receive credit card payments, you need to connect your Stripe account. Stripe is our payment processing partner that ensures secure transactions and timely payouts."

#: src/components/layouts/Event/index.tsx:108
msgid "Tools"
msgstr "Tools"

#: src/components/common/OrderSummary/index.tsx:92
msgid "Total"
msgstr "Total"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:52
msgid "Total Before Discounts"
msgstr "Total Before Discounts"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:58
msgid "Total Discount Amount"
msgstr "Total Discount Amount"

#: src/components/routes/event/EventDashboard/index.tsx:273
msgid "Total Fees"
msgstr "Total Fees"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:46
msgid "Total Gross Sales"
msgstr "Total Gross Sales"

#: src/components/common/OrderDetails/index.tsx:59
#: src/components/modals/RefundOrderModal/index.tsx:74
msgid "Total order amount"
msgstr "Total order amount"

#: src/components/common/OrderDetails/index.tsx:67
#: src/components/modals/RefundOrderModal/index.tsx:82
msgid "Total refunded"
msgstr "Total refunded"

#: src/components/routes/event/EventDashboard/index.tsx:276
msgid "Total Refunded"
msgstr "Total Refunded"

#: src/components/modals/RefundOrderModal/index.tsx:90
msgid "Total remaining"
msgstr "Total remaining"

#: src/components/routes/event/EventDashboard/index.tsx:275
msgid "Total Tax"
msgstr "Total Tax"

#: src/components/layouts/AuthLayout/index.tsx:54
msgid "Track revenue, page views, and sales with detailed analytics and exportable reports"
msgstr "Track revenue, page views, and sales with detailed analytics and exportable reports"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:53
msgid "Transaction Fee:"
msgstr "Transaction Fee:"

#: src/components/forms/TaxAndFeeForm/index.tsx:44
msgid "Type"
msgstr "Type"

#: src/components/common/AttendeeCheckInTable/index.tsx:54
#: src/components/common/AttendeeCheckInTable/index.tsx:81
#~ msgid "Unable to {0} attendee"
#~ msgstr "Unable to {0} attendee"

#: src/components/layouts/CheckIn/index.tsx:93
msgid "Unable to check in attendee"
msgstr "Unable to check in attendee"

#: src/components/layouts/CheckIn/index.tsx:114
msgid "Unable to check out attendee"
msgstr "Unable to check out attendee"

#: src/components/modals/CreateProductModal/index.tsx:63
msgid "Unable to create product. Please check the your details"
msgstr "Unable to create product. Please check the your details"

#: src/components/routes/product-widget/SelectProducts/index.tsx:123
msgid "Unable to create product. Please check your details"
msgstr "Unable to create product. Please check your details"

#: src/components/modals/CreateQuestionModal/index.tsx:60
msgid "Unable to create question. Please check the your details"
msgstr "Unable to create question. Please check the your details"

#: src/components/modals/CreateTicketModal/index.tsx:65
#: src/components/routes/ticket-widget/SelectTickets/index.tsx:117
#~ msgid "Unable to create ticket. Please check the your details"
#~ msgstr "Unable to create ticket. Please check the your details"

#: src/components/modals/DuplicateProductModal/index.tsx:103
msgid "Unable to duplicate product. Please check the your details"
msgstr "Unable to duplicate product. Please check the your details"

#: src/components/layouts/CheckIn/index.tsx:145
msgid "Unable to fetch attendee"
msgstr "Unable to fetch attendee"

#: src/components/modals/EditQuestionModal/index.tsx:84
msgid "Unable to update question. Please check the your details"
msgstr "Unable to update question. Please check the your details"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:30
msgid "Unique Customers"
msgstr "Unique Customers"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:153
msgid "United States"
msgstr "United States"

#: src/components/common/QuestionAndAnswerList/index.tsx:284
msgid "Unknown Attendee"
msgstr "Unknown Attendee"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:43
#: src/components/forms/ProductForm/index.tsx:83
#: src/components/forms/ProductForm/index.tsx:299
#: src/components/forms/PromoCodeForm/index.tsx:82
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:78
#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:84
msgid "Unlimited"
msgstr "Unlimited"

#: src/components/routes/product-widget/SelectProducts/index.tsx:407
msgid "Unlimited available"
msgstr "Unlimited available"

#: src/components/common/TicketsTable/index.tsx:106
#~ msgid "Unlimited ticket quantity available"
#~ msgstr "Unlimited ticket quantity available"

#: src/components/common/PromoCodeTable/index.tsx:126
msgid "Unlimited usages allowed"
msgstr "Unlimited usages allowed"

#: src/components/layouts/CheckIn/index.tsx:200
msgid "Unpaid Order"
msgstr "Unpaid Order"

#: src/components/routes/event/EventDashboard/index.tsx:170
msgid "Unpublish Event"
msgstr "Unpublish Event"

#: src/components/common/EventsDashboardStatusButtons/index.tsx:20
#: src/components/common/EventStatusBadge/index.tsx:16
msgid "Upcoming"
msgstr "Upcoming"

#: src/components/routes/events/Dashboard/index.tsx:55
msgid "Upcoming Events"
msgstr "Upcoming Events"

#: src/components/modals/EditTaxOrFeeModal/index.tsx:59
msgid "Update {0}"
msgstr "Update {0}"

#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:66
msgid "Update event name, description and dates"
msgstr "Update event name, description and dates"

#: src/components/routes/profile/ManageProfile/index.tsx:177
msgid "Update profile"
msgstr "Update profile"

#: src/components/routes/event/Settings/Sections/ImageSettings/index.tsx:75
#~ msgid "Upload an image to be displayed on the event page"
#~ msgstr "Upload an image to be displayed on the event page"

#: src/components/routes/event/HomepageDesigner/CoverUpload/index.tsx:80
msgid "Upload Cover"
msgstr "Upload Cover"

#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:105
#: src/components/common/Editor/Controls/InsertImageControl/index.tsx:152
msgid "Upload Image"
msgstr "Upload Image"

#: src/components/common/WebhookTable/index.tsx:236
#: src/components/modals/ShareModal/index.tsx:129
msgid "URL"
msgstr "URL"

#: src/components/common/PromoCodeTable/index.tsx:175
msgid "URL copied to clipboard"
msgstr "URL copied to clipboard"

#: src/components/modals/CreateWebhookModal/index.tsx:25
msgid "URL is required"
msgstr "URL is required"

#: src/components/common/WidgetEditor/index.tsx:298
msgid "Usage Example"
msgstr "Usage Example"

#: src/components/routes/event/Reports/PromoCodesReport/index.tsx:76
msgid "Usage Limit"
msgstr "Usage Limit"

#: src/components/routes/event/HomepageDesigner/index.tsx:139
msgid "Use a blurred version of the cover image as the background"
msgstr "Use a blurred version of the cover image as the background"

#: src/components/routes/event/HomepageDesigner/index.tsx:137
msgid "Use cover image"
msgstr "Use cover image"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User"
msgstr "User"

#: src/components/routes/account/ManageAccount/sections/Users/<USER>
msgid "User Management"
msgstr "User Management"

#: src/components/routes/account/ManageAccount/index.tsx:32
msgid "Users"
msgstr "Users"

#: src/components/modals/EditUserModal/index.tsx:81
msgid "Users can change their email in <0>Profile Settings</0>"
msgstr "Users can change their email in <0>Profile Settings</0>"

#: src/components/forms/OrganizerForm/index.tsx:55
#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:83
#: src/components/routes/auth/AcceptInvitation/index.tsx:106
#: src/components/routes/event/Settings/Sections/EventDetailsForm/index.tsx:110
#: src/components/routes/profile/ManageProfile/index.tsx:162
msgid "UTC"
msgstr "UTC"

#: src/components/forms/TaxAndFeeForm/index.tsx:62
msgid "VAT"
msgstr "VAT"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:116
msgid "Venue Name"
msgstr "Venue Name"

#: src/components/common/LanguageSwitcher/index.tsx:34
msgid "Vietnamese"
msgstr "Vietnamese"

#: src/components/modals/ManageAttendeeModal/index.tsx:220
#: src/components/modals/ManageOrderModal/index.tsx:200
msgid "View"
msgstr "View"

#: src/components/routes/event/Reports/index.tsx:38
msgid "View and download reports for your event. Please note, only completed orders are included in these reports."
msgstr "View and download reports for your event. Please note, only completed orders are included in these reports."

#: src/components/common/AttendeeList/index.tsx:84
msgid "View Answers"
msgstr "View Answers"

#: src/components/common/AttendeeTable/index.tsx:164
#~ msgid "View attendee"
#~ msgstr "View attendee"

#: src/components/common/AttendeeList/index.tsx:103
msgid "View Attendee Details"
msgstr "View Attendee Details"

#: src/components/layouts/Checkout/index.tsx:55
#~ msgid "View event homepage"
#~ msgstr "View event homepage"

#: src/components/common/EventCard/index.tsx:145
msgid "View event page"
msgstr "View event page"

#: src/components/common/MessageList/index.tsx:59
msgid "View full message"
msgstr "View full message"

#: src/components/common/WebhookTable/index.tsx:113
msgid "View logs"
msgstr "View logs"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View map"
msgstr "View map"

#: src/components/layouts/EventHomepage/EventInformation/index.tsx:65
msgid "View on Google Maps"
msgstr "View on Google Maps"

#: src/components/common/OrdersTable/index.tsx:111
#~ msgid "View order"
#~ msgstr "View order"

#: src/components/forms/StripeCheckoutForm/index.tsx:94
#: src/components/forms/StripeCheckoutForm/index.tsx:104
#: src/components/routes/product-widget/CollectInformation/index.tsx:231
msgid "View order details"
msgstr "View order details"

#: src/components/forms/CheckInListForm/index.tsx:21
msgid "VIP check-in list"
msgstr "VIP check-in list"

#: src/components/forms/ProductForm/index.tsx:254
#~ msgid "VIP Product"
#~ msgstr "VIP Product"

#: src/components/forms/ProductForm/index.tsx:254
msgid "VIP Ticket"
msgstr "VIP Ticket"

#: src/components/forms/ProductForm/index.tsx:394
msgid "Visibility"
msgstr "Visibility"

#: src/components/routes/product-widget/PaymentReturn/index.tsx:77
msgid "We could not process your payment. Please try again or contact support."
msgstr "We could not process your payment. Please try again or contact support."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:53
msgid "We couldn't delete the category. Please try again."
msgstr "We couldn't delete the category. Please try again."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:24
msgid "We couldn't find any tickets matching {0}"
msgstr "We couldn't find any tickets matching {0}"

#: src/components/common/ErrorLoadingMessage/index.tsx:13
msgid "We couldn't load the data. Please try again."
msgstr "We couldn't load the data. Please try again."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:94
msgid "We couldn't reorder the categories. Please try again."
msgstr "We couldn't reorder the categories. Please try again."

#: src/components/routes/event/HomepageDesigner/index.tsx:118
msgid "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"
msgstr "We recommend dimensions of 2160px by 1080px, and a maximum file size of 5MB"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:42
#~ msgid "We use Stripe to process payments. Connect your Stripe account to start receiving payments."
#~ msgstr "We use Stripe to process payments. Connect your Stripe account to start receiving payments."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:84
msgid "We were unable to confirm your payment. Please try again or contact support."
msgstr "We were unable to confirm your payment. Please try again or contact support."

#: src/components/routes/product-widget/PaymentReturn/index.tsx:79
msgid "We're processing your order. Please wait..."
msgstr "We're processing your order. Please wait..."

#: src/components/modals/CreateWebhookModal/index.tsx:46
msgid "Webhook created successfully"
msgstr "Webhook created successfully"

#: src/components/common/WebhookTable/index.tsx:53
msgid "Webhook deleted successfully"
msgstr "Webhook deleted successfully"

#: src/components/modals/WebhookLogsModal/index.tsx:151
msgid "Webhook Logs"
msgstr "Webhook Logs"

#: src/components/forms/WebhookForm/index.tsx:117
msgid "Webhook URL"
msgstr "Webhook URL"

#: src/components/forms/WebhookForm/index.tsx:27
msgid "Webhook will not send notifications"
msgstr "Webhook will not send notifications"

#: src/components/forms/WebhookForm/index.tsx:21
msgid "Webhook will send notifications"
msgstr "Webhook will send notifications"

#: src/components/layouts/Event/index.tsx:111
#: src/components/routes/event/Webhooks/index.tsx:30
msgid "Webhooks"
msgstr "Webhooks"

#: src/components/routes/auth/AcceptInvitation/index.tsx:76
msgid "Welcome aboard! Please login to continue."
msgstr "Welcome aboard! Please login to continue."

#: src/components/routes/auth/Login/index.tsx:55
msgid "Welcome back 👋"
msgstr "Welcome back 👋"

#: src/components/routes/event/EventDashboard/index.tsx:95
msgid "Welcome back{0} 👋"
msgstr "Welcome back{0} 👋"

#: src/components/routes/auth/Register/index.tsx:67
msgid "Welcome to Hi.Events 👋"
msgstr "Welcome to Hi.Events 👋"

#: src/components/routes/welcome/index.tsx:125
msgid "Welcome to Hi.Events, {0} 👋"
msgstr "Welcome to Hi.Events, {0} 👋"

#: src/components/forms/ProductForm/index.tsx:242
msgid "What are Tiered Products?"
msgstr "What are Tiered Products?"

#: src/components/forms/TicketForm/index.tsx:202
#~ msgid "What are Tiered Tickets?"
#~ msgstr "What are Tiered Tickets?"

#: src/components/forms/CheckInListForm/index.tsx:50
msgid "What date should this check-in list become active?"
msgstr "What date should this check-in list become active?"

#: src/components/modals/CreateProductCategoryModal/index.tsx:53
msgid "What is a Category?"
msgstr "What is a Category?"

#: src/components/forms/TicketForm/index.tsx:158
#~ msgid "What is a Tiered Ticketing?"
#~ msgstr "What is a Tiered Ticketing?"

#: src/components/routes/event/Webhooks/index.tsx:31
#~ msgid "What is a webhook?"
#~ msgstr "What is a webhook?"

#: src/components/forms/QuestionForm/index.tsx:159
msgid "What products does this code apply to?"
msgstr "What products does this code apply to?"

#: src/components/forms/PromoCodeForm/index.tsx:68
msgid "What products does this code apply to? (Applies to all by default)"
msgstr "What products does this code apply to? (Applies to all by default)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:48
msgid "What products should this capacity apply to?"
msgstr "What products should this capacity apply to?"

#: src/components/forms/PromoCodeForm/index.tsx:68
#~ msgid "What tickets does this code apply to? (Applies to all by default)"
#~ msgstr "What tickets does this code apply to? (Applies to all by default)"

#: src/components/forms/CapaciyAssigmentForm/index.tsx:47
#: src/components/forms/QuestionForm/index.tsx:159
#~ msgid "What tickets should this question be apply to?"
#~ msgstr "What tickets should this question be apply to?"

#: src/components/forms/QuestionForm/index.tsx:179
msgid "What time will you be arriving?"
msgstr "What time will you be arriving?"

#: src/components/forms/QuestionForm/index.tsx:170
msgid "What type of question is this?"
msgstr "What type of question is this?"

#: src/components/forms/WebhookForm/index.tsx:108
msgid "When a check-in is deleted"
msgstr "When a check-in is deleted"

#: src/components/forms/WebhookForm/index.tsx:84
msgid "When a new attendee is created"
msgstr "When a new attendee is created"

#: src/components/forms/WebhookForm/index.tsx:54
msgid "When a new order is created"
msgstr "When a new order is created"

#: src/components/forms/WebhookForm/index.tsx:36
msgid "When a new product is created"
msgstr "When a new product is created"

#: src/components/forms/WebhookForm/index.tsx:48
msgid "When a product is deleted"
msgstr "When a product is deleted"

#: src/components/forms/WebhookForm/index.tsx:42
msgid "When a product is updated"
msgstr "When a product is updated"

#: src/components/forms/WebhookForm/index.tsx:96
msgid "When an attendee is cancelled"
msgstr "When an attendee is cancelled"

#: src/components/forms/WebhookForm/index.tsx:102
msgid "When an attendee is checked in"
msgstr "When an attendee is checked in"

#: src/components/forms/WebhookForm/index.tsx:90
msgid "When an attendee is updated"
msgstr "When an attendee is updated"

#: src/components/forms/WebhookForm/index.tsx:78
msgid "When an order is cancelled"
msgstr "When an order is cancelled"

#: src/components/forms/WebhookForm/index.tsx:66
msgid "When an order is marked as paid"
msgstr "When an order is marked as paid"

#: src/components/forms/WebhookForm/index.tsx:72
msgid "When an order is refunded"
msgstr "When an order is refunded"

#: src/components/forms/WebhookForm/index.tsx:60
msgid "When an order is updated"
msgstr "When an order is updated"

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:182
msgid "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."
msgstr "When enabled, invoices will be generated for ticket orders. Invoices will sent along with the order confirmation email. Attendees can also download their invoices from the order confirmation page."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:141
msgid "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."
msgstr "When offline payments are enabled, users will be able to complete their orders and receive their tickets. Their tickets will clearly indicate the order is not paid, and the check-in tool will notify the check-in staff if an order requires payment."

#: src/components/forms/CheckInListForm/index.tsx:59
msgid "When should this check-in list expire?"
msgstr "When should this check-in list expire?"

#: src/components/forms/CheckInListForm/index.tsx:25
msgid "Which tickets should be associated with this check-in list?"
msgstr "Which tickets should be associated with this check-in list?"

#: src/components/modals/CreateEventModal/index.tsx:107
msgid "Who is organizing this event?"
msgstr "Who is organizing this event?"

#: src/components/modals/SendMessageModal/index.tsx:194
msgid "Who is this message to?"
msgstr "Who is this message to?"

#: src/components/forms/QuestionForm/index.tsx:151
msgid "Who should be asked this question?"
msgstr "Who should be asked this question?"

#: src/components/forms/QuestionForm/index.tsx:163
#~ msgid "Who type of question is this?"
#~ msgstr "Who type of question is this?"

#: src/components/routes/ticket-widget/CollectInformation/index.tsx:65
#~ msgid "Whoops! something went wrong. Please try again or contact support if the problem persists."
#~ msgstr "Whoops! something went wrong. Please try again or contact support if the problem persists."

#: src/components/layouts/Event/index.tsx:42
#~ msgid "Widget"
#~ msgstr "Widget"

#: src/components/routes/event/settings.tsx:29
#~ msgid "Widget Configuration"
#~ msgstr "Widget Configuration"

#: src/components/layouts/Event/index.tsx:110
msgid "Widget Embed"
msgstr "Widget Embed"

#: src/components/common/WidgetEditor/index.tsx:162
msgid "Widget Settings"
msgstr "Widget Settings"

#: src/components/modals/CreateAttendeeModal/index.tsx:196
msgid "Working"
msgstr "Working"

#: src/components/modals/CreateProductCategoryModal/index.tsx:60
#: src/components/modals/CreateProductModal/index.tsx:88
#: src/components/modals/CreatePromoCodeModal/index.tsx:56
#: src/components/modals/CreateQuestionModal/index.tsx:74
#: src/components/modals/DuplicateEventModal/index.tsx:142
#: src/components/modals/DuplicateProductModal/index.tsx:113
#: src/components/modals/EditProductCategoryModal/index.tsx:70
#: src/components/modals/EditProductModal/index.tsx:108
#: src/components/modals/EditPromoCodeModal/index.tsx:84
#: src/components/modals/EditQuestionModal/index.tsx:101
#: src/components/modals/ManageAttendeeModal/index.tsx:231
#: src/components/modals/ManageOrderModal/index.tsx:175
#: src/components/routes/auth/ForgotPassword/index.tsx:55
#: src/components/routes/auth/Register/index.tsx:129
#: src/components/routes/auth/ResetPassword/index.tsx:62
msgid "Working..."
msgstr "Working..."

#: src/components/common/ReportTable/index.tsx:51
msgid "Year to date"
msgstr "Year to date"

#: src/components/common/AttendeeDetails/index.tsx:32
msgid "Yes"
msgstr "Yes"

#: src/components/forms/ProductForm/index.tsx:376
msgid "Yes, remove them"
msgstr "Yes, remove them"

#: src/components/common/AttendeeCheckInTable/QrScanner.tsx:77
msgid "You already scanned this ticket"
msgstr "You already scanned this ticket"

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:53
msgid "You are changing your email to <0>{0}</0>."
msgstr "You are changing your email to <0>{0}</0>."

#: src/components/layouts/CheckIn/index.tsx:88
#: src/components/layouts/CheckIn/index.tsx:110
msgid "You are offline"
msgstr "You are offline"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:93
#~ msgid "You can connecting using this Zoom link..."
#~ msgstr "You can connecting using this Zoom link..."

#: src/components/forms/ProductForm/index.tsx:407
msgid "You can create a promo code which targets this product on the"
msgstr "You can create a promo code which targets this product on the"

#: src/components/forms/TicketForm/index.tsx:352
#~ msgid "You can create a promo code which targets this ticket on the"
#~ msgstr "You can create a promo code which targets this ticket on the"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:31
#~ msgid "You can now start receiving payments through Stripe."
#~ msgstr "You can now start receiving payments through Stripe."

#: src/components/forms/ProductForm/index.tsx:208
msgid "You cannot change the product type as there are attendees associated with this product."
msgstr "You cannot change the product type as there are attendees associated with this product."

#: src/components/forms/TicketForm/index.tsx:184
#~ msgid "You cannot change the ticket type as there are attendees associated with this ticket."
#~ msgstr "You cannot change the ticket type as there are attendees associated with this ticket."

#: src/components/forms/TicketForm/index.tsx:185
#~ msgid "You cannot change the ticket type because there are already tickets sold for this ticket."
#~ msgstr "You cannot change the ticket type because there are already tickets sold for this ticket."

#: src/components/layouts/CheckIn/index.tsx:119
#~ msgid "You cannot check in attendees with unpaid orders."
#~ msgstr "You cannot check in attendees with unpaid orders."

#: src/components/layouts/CheckIn/index.tsx:129
#: src/components/layouts/CheckIn/index.tsx:164
msgid "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."
msgstr "You cannot check in attendees with unpaid orders. This setting can be changed in the event settings."

#: src/components/common/ProductsTable/SortableCategory/index.tsx:40
msgid "You cannot delete the last category."
msgstr "You cannot delete the last category."

#: src/components/forms/ProductForm/index.tsx:57
msgid "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."
msgstr "You cannot delete this price tier because there are already products sold for this tier. You can hide it instead."

#: src/components/forms/TicketForm/index.tsx:54
#~ msgid "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."
#~ msgstr "You cannot delete this price tier because there are already tickets sold for this tier. You can hide it instead."

#: src/components/common/QuestionsTable/index.tsx:42
#~ msgid "You cannot edit a default question"
#~ msgstr "You cannot edit a default question"

#: src/components/modals/EditUserModal/index.tsx:88
msgid "You cannot edit the role or status of the account owner."
msgstr "You cannot edit the role or status of the account owner."

#: src/components/modals/RefundOrderModal/index.tsx:132
msgid "You cannot refund a manually created order."
msgstr "You cannot refund a manually created order."

#: src/components/common/QuestionsTable/index.tsx:252
msgid "You created a hidden question but disabled the option to show hidden questions. It has been enabled."
msgstr "You created a hidden question but disabled the option to show hidden questions. It has been enabled."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:77
#~ msgid "You do not have permission to access this page"
#~ msgstr "You do not have permission to access this page"

#: src/components/modals/ChooseAccountModal/index.tsx:14
msgid "You have access to multiple accounts. Please choose one to continue."
msgstr "You have access to multiple accounts. Please choose one to continue."

#: src/components/routes/auth/AcceptInvitation/index.tsx:57
msgid "You have already accepted this invitation. Please login to continue."
msgstr "You have already accepted this invitation. Please login to continue."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:29
#~ msgid "You have connected your Stripe account"
#~ msgstr "You have connected your Stripe account"

#: src/components/common/QuestionsTable/index.tsx:338
msgid "You have no attendee questions."
msgstr "You have no attendee questions."

#: src/components/common/QuestionsTable/index.tsx:149
#~ msgid "You have no attendee questions. Attendee questions are asked once per attendee."
#~ msgstr "You have no attendee questions. Attendee questions are asked once per attendee."

#: src/components/common/QuestionsTable/index.tsx:323
msgid "You have no order questions."
msgstr "You have no order questions."

#: src/components/common/QuestionsTable/index.tsx:140
#~ msgid "You have no order questions. Order questions are asked once per order."
#~ msgstr "You have no order questions. Order questions are asked once per order."

#: src/components/routes/profile/ConfirmEmailChange/index.tsx:41
msgid "You have no pending email change."
msgstr "You have no pending email change."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:39
#~ msgid "You have not completed your Stripe Connect setup"
#~ msgstr "You have not completed your Stripe Connect setup"

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:38
#~ msgid "You have not connected your Stripe account"
#~ msgstr "You have not connected your Stripe account"

#: src/components/layouts/Checkout/index.tsx:156
msgid "You have run out of time to complete your order."
msgstr "You have run out of time to complete your order."

#: src/components/forms/ProductForm/index.tsx:374
#~ msgid "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"
#~ msgstr "You have taxes and fees added to a Free Product. Would you like to remove or obscure them?"

#: src/components/forms/ProductForm/index.tsx:374
msgid "You have taxes and fees added to a Free Product. Would you like to remove them?"
msgstr "You have taxes and fees added to a Free Product. Would you like to remove them?"

#: src/components/forms/TicketForm/index.tsx:319
#~ msgid "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"
#~ msgstr "You have taxes and fees added to a Free Ticket. Would you like to remove or obscure them?"

#: src/components/common/MessageList/index.tsx:74
msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."
msgstr "You haven't sent any messages yet. You can send messages to all attendees, or to specific product holders."

#: src/components/common/MessageList/index.tsx:64
#~ msgid "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."
#~ msgstr "You haven't sent any messages yet. You can send messages to all attendees, or to specific ticket holders."

#: src/components/modals/SendMessageModal/index.tsx:108
msgid "You must acknowledge that this email is not promotional"
msgstr "You must acknowledge that this email is not promotional"

#: src/components/routes/auth/AcceptInvitation/index.tsx:33
msgid "You must agree to the terms and conditions"
msgstr "You must agree to the terms and conditions"

#: src/components/routes/event/GettingStarted/index.tsx:193
msgid "You must confirm your email address before your event can go live."
msgstr "You must confirm your email address before your event can go live."

#: src/components/modals/CreateAttendeeModal/index.tsx:98
msgid "You must create a ticket before you can manually add an attendee."
msgstr "You must create a ticket before you can manually add an attendee."

#: src/components/forms/ProductForm/index.tsx:60
msgid "You must have at least one price tier"
msgstr "You must have at least one price tier"

#: src/components/forms/TicketForm/index.tsx:84
#~ msgid "You must have at least one tier"
#~ msgstr "You must have at least one tier"

#: src/components/modals/SendMessageModal/index.tsx:136
#~ msgid "You need to verify your account before you can send messages."
#~ msgstr "You need to verify your account before you can send messages."

#: src/components/modals/SendMessageModal/index.tsx:151
msgid "You need to verify your account email before you can send messages."
msgstr "You need to verify your account email before you can send messages."

#: src/components/routes/event/Settings/Sections/PaymentSettings/index.tsx:143
msgid "You will have to mark an order as paid manually. This can be done on the manage order page."
msgstr "You will have to mark an order as paid manually. This can be done on the manage order page."

#: src/components/modals/CreateCheckInListModal/index.tsx:55
msgid "You'll need a ticket before you can create a check-in list."
msgstr "You'll need a ticket before you can create a check-in list."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
msgid "You'll need at a product before you can create a capacity assignment."
msgstr "You'll need at a product before you can create a capacity assignment."

#: src/components/modals/CreateCapacityAssignmentModal/index.tsx:51
#~ msgid "You'll need at a ticket before you can create a capacity assignment."
#~ msgstr "You'll need at a ticket before you can create a capacity assignment."

#: src/components/common/ProductsTable/ProductsBlankSlate/index.tsx:43
msgid "You'll need at least one product to get started. Free, paid or let the user decide what to pay."
msgstr "You'll need at least one product to get started. Free, paid or let the user decide what to pay."

#: src/components/common/TicketsTable/index.tsx:69
#~ msgid "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."
#~ msgstr "You'll need at least one ticket to get started. Free, paid or let the user decide what to pay."

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:93
msgid "You're going to {0}! 🎉"
msgstr "You're going to {0}! 🎉"

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:72
#~ msgid "Your account email in outgoing emails."
#~ msgstr "Your account email in outgoing emails."

#: src/components/routes/account/ManageAccount/sections/AccountSettings/index.tsx:64
msgid "Your account name is used on event pages and in emails."
msgstr "Your account name is used on event pages and in emails."

#: src/components/routes/event/attendees.tsx:44
msgid "Your attendees have been exported successfully."
msgstr "Your attendees have been exported successfully."

#: src/components/common/AttendeeTable/index.tsx:61
msgid "Your attendees will appear here once they have registered for your event. You can also manually add attendees."
msgstr "Your attendees will appear here once they have registered for your event. You can also manually add attendees."

#: src/components/common/WidgetEditor/index.tsx:329
msgid "Your awesome website 🎉"
msgstr "Your awesome website 🎉"

#: src/components/routes/product-widget/CollectInformation/index.tsx:276
msgid "Your Details"
msgstr "Your Details"

#: src/components/routes/auth/ForgotPassword/index.tsx:52
msgid "Your Email"
msgstr "Your Email"

#: src/components/routes/profile/ManageProfile/index.tsx:118
msgid "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"
msgstr "Your email request change to <0>{0}</0> is pending. Please check your email to confirm"

#: src/components/routes/event/EventDashboard/index.tsx:150
msgid "Your event must be live before you can sell tickets to attendees."
msgstr "Your event must be live before you can sell tickets to attendees."

#: src/components/routes/event/EventDashboard/index.tsx:164
#~ msgid "Your event must be live before you can sell tickets."
#~ msgstr "Your event must be live before you can sell tickets."

#: src/components/common/OrdersTable/index.tsx:88
msgid "Your message has been sent"
msgstr "Your message has been sent"

#: src/components/layouts/Checkout/index.tsx:84
msgid "Your Order"
msgstr "Your Order"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:94
msgid "Your order has been cancelled"
msgstr "Your order has been cancelled"

#: src/components/routes/product-widget/OrderSummaryAndProducts/index.tsx:96
msgid "Your order is awaiting payment 🏦"
msgstr "Your order is awaiting payment 🏦"

#: src/components/routes/ticket-widget/SelectTickets/index.tsx:242
#~ msgid "Your order is in progress"
#~ msgstr "Your order is in progress"

#: src/components/routes/event/orders.tsx:95
msgid "Your orders have been exported successfully."
msgstr "Your orders have been exported successfully."

#: src/components/common/OrdersTable/index.tsx:66
msgid "Your orders will appear here once they start rolling in."
msgstr "Your orders will appear here once they start rolling in."

#: src/components/routes/auth/Login/index.tsx:74
#: src/components/routes/auth/Register/index.tsx:107
msgid "Your password"
msgstr "Your password"

#: src/components/forms/StripeCheckoutForm/index.tsx:63
msgid "Your payment is processing."
msgstr "Your payment is processing."

#: src/components/forms/StripeCheckoutForm/index.tsx:66
msgid "Your payment was not successful, please try again."
msgstr "Your payment was not successful, please try again."

#: src/components/forms/StripeCheckoutForm/index.tsx:126
msgid "Your payment was unsuccessful. Please try again."
msgstr "Your payment was unsuccessful. Please try again."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#~ msgid "Your product for"
#~ msgstr "Your product for"

#: src/components/modals/RefundOrderModal/index.tsx:58
msgid "Your refund is processing."
msgstr "Your refund is processing."

#: src/components/routes/account/ManageAccount/sections/PaymentSettings/index.tsx:153
msgid "Your Stripe account is connected and ready to process payments."
msgstr "Your Stripe account is connected and ready to process payments."

#: src/components/routes/product-widget/AttendeeProductAndInformation/index.tsx:35
#: src/components/routes/product-widget/PrintProduct/index.tsx:42
msgid "Your ticket for"
msgstr "Your ticket for"

#: src/components/routes/event/settings.tsx:117
#~ msgid "Zip"
#~ msgstr "Zip"

#: src/components/routes/product-widget/CollectInformation/index.tsx:348
msgid "ZIP / Postal Code"
msgstr "ZIP / Postal Code"

#: src/components/common/CheckoutQuestion/index.tsx:170
#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:146
msgid "Zip or Postal Code"
msgstr "Zip or Postal Code"

#: src/components/routes/product-widget/CollectInformation/index.tsx:349
msgid "ZIP or Postal Code"
msgstr "ZIP or Postal Code"

#: src/components/routes/event/Settings/Sections/LocationSettings/index.tsx:92
#~ msgid "Zoom link, Google Meet link, etc."
#~ msgstr "Zoom link, Google Meet link, etc."
