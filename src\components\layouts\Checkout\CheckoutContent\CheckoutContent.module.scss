@use "../../../../styles/mixins.scss";

.main {
  position: relative;
  height: calc(100vh - 123px);
  overflow-y: auto;
  padding: 20px 60px;

  @include mixins.scrollbar();

  &.hasFooter {
    height: calc(100vh - 60px);

    @include mixins.respond-below(md) {
      height: calc(100vh - 110px);
    }
  }

  @include mixins.respond-below(md) {
    padding: 20px;
  }

  .innerContainer {
    max-width: 700px;
    margin: 0 auto;
  }
}

.footer {
  height: 100px;
  background-color: #ffffff;
}
